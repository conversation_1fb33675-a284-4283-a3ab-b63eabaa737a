# 单个员工班结小票打印功能

## 功能概述

在前一个班次报表弹框中，为每个员工添加了独立的打印按钮，允许用户打印指定员工的班结小票。

## 功能特点

### 1. 用户界面增强
- 在每个员工卡片的右上角添加了 "Print" 按钮
- 按钮采用 BP 品牌色彩设计，与整体UI风格保持一致
- 打印时显示加载状态，防止重复点击

### 2. 打印状态管理
- 使用 `_printingAttendantId` 状态变量跟踪当前正在打印的员工
- 打印过程中按钮变为灰色并显示 "Printing..." 文本
- 显示旋转的加载指示器

### 3. 打印内容
单个员工班结小票包含以下信息：

#### 头部信息
- BP OIL INDONESIA 品牌标识
- 加油站名称
- 分隔线

#### 班次和员工信息
- 报告类型：ATTENDANT SHIFT REPORT
- 班次编号
- 加油站名称
- 班次时间（开始-结束）
- 员工姓名和工号

#### 销售汇总
- 交易笔数
- 销售量（升）
- 销售金额
- 非油品收入
- 总计金额

#### 按油品分类的销售明细
- 油品等级名称
- 交易笔数
- 销售量
- 销售金额
- 单价

#### 支付方式统计
- 支付方式名称
- 交易笔数和百分比
- 金额

#### 页脚信息
- 报告结束标识
- 打印时间
- 感谢语

## 技术实现

### 1. UI 组件更新
```dart
// 在员工卡片中添加打印按钮
Container(
  margin: const EdgeInsets.only(left: 8),
  child: _buildAttendantPrintButton(attendant),
),
```

### 2. 状态管理
```dart
class _ShiftAttendantReportDialogState extends State<ShiftAttendantReportDialog> {
  bool _isPrinting = false;
  String? _printingAttendantId; // 跟踪正在打印的员工ID
  // ...
}
```

### 3. 打印按钮构建
```dart
Widget _buildAttendantPrintButton(Attendant attendant) {
  final bool isCurrentlyPrinting = _printingAttendantId == attendant.attendantInfo.staffCardId.toString();
  
  return ElevatedButton.icon(
    onPressed: isCurrentlyPrinting ? null : () => _printAttendantReport(attendant),
    icon: isCurrentlyPrinting ? CircularProgressIndicator() : Icon(Icons.print),
    label: Text(isCurrentlyPrinting ? 'Printing...' : 'Print'),
    // ... 样式配置
  );
}
```

### 4. 打印逻辑
```dart
Future<void> _printAttendantReport(Attendant attendant) async {
  setState(() {
    _printingAttendantId = attendant.attendantInfo.staffCardId.toString();
  });

  try {
    // 检查打印机连接
    final bool isConnected = await _printerService.isPrinterConnected();
    if (!isConnected) {
      throw Exception('Printer not connected');
    }

    // 执行打印
    await _printerService.enterPrinterBuffer(clearBuffer: true);
    await _printSingleAttendantReport(attendant);
    await _printerService.exitPrinterBuffer(commit: true);

    // 显示成功消息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${attendant.attendantInfo.attendantName} report printed successfully!')),
    );
  } catch (e) {
    // 显示错误消息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Print failed: $e')),
    );
  } finally {
    setState(() {
      _printingAttendantId = null;
    });
  }
}
```

## 使用方法

1. 打开班次管理页面
2. 点击 "View Previous Shift Report" 按钮
3. 在弹出的报表对话框中，找到需要打印的员工
4. 点击该员工卡片右上角的 "Print" 按钮
5. 等待打印完成，系统会显示成功或失败的提示消息

## 错误处理

- **打印机未连接**：显示 "Printer not connected" 错误消息
- **打印过程中断**：显示具体的错误信息
- **重复点击保护**：打印过程中按钮被禁用，防止重复操作

## 注意事项

1. 确保打印机已正确连接并处于就绪状态
2. 打印过程中请勿关闭应用或切换页面
3. 每次只能打印一个员工报告，需要等待当前打印完成后才能开始下一个
4. 打印内容针对58mm热敏纸张进行了优化

## 相关文件

- `lib/widgets/shift_attendant_report_dialog.dart` - 主要实现文件
- `lib/services/native/sunmi_printer_service.dart` - 打印服务
- `lib/models/shift_attendant_model.dart` - 数据模型
- `lib/utils/format_utils.dart` - 格式化工具

## 更新日志

- **v1.0.0** - 初始实现，支持单个员工班结小票打印功能 