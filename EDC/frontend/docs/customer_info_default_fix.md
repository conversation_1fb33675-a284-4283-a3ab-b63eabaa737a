# 客户信息默认值问题修复

## 问题描述

用户反馈：如果用户没输入姓名和手机，目前的默认内容好像有问题。

经过检查发现，客户信息默认值设置在两个地方存在不一致的问题：

1. **创建订单时**（`cash_payment_page.dart`）
2. **小票显示时**（`auto_print_service.dart`）

## 发现的问题

### 1. 小票显示逻辑缺失默认值设置

**问题位置**: `EDC/frontend/lib/services/auto_print_service.dart` 的 `_getCustomerInfo` 方法

**问题描述**: 
- 创建订单时会根据支付方式设置默认的客户信息（ANONIM/1010101010）
- 但在小票显示时，如果从订单中获取不到客户信息，没有相应的默认值设置逻辑
- 导致小票上可能显示空的客户信息

### 2. 小票显示过滤逻辑不完整

**问题位置**: `EDC/frontend/lib/services/auto_print_service.dart` 的打印逻辑

**问题描述**:
- 小票显示时没有正确过滤默认的电话号码 `1010101010`
- 导致默认电话号码可能显示在小票上

## 修复方案

### 1. 统一默认值设置逻辑

在 `auto_print_service.dart` 的 `_getCustomerInfo` 方法中添加默认值设置逻辑：

```dart
// 如果客户名字和电话都为空，根据支付方式设置默认值
if (customerName.isEmpty && customerPhone.isEmpty) {
  final String paymentMethod = order.paymentMethod.toLowerCase();
  
  if (paymentMethod.contains('tera')) {
    // TERA 支付方式
    customerName = 'ANONIM';
    customerPhone = '1010101010';
  } else {
    // 其他支付方式
    customerName = 'ANONIM';
    customerPhone = '1010101010';
  }
}
```

### 2. 完善小票显示过滤逻辑

修改小票打印逻辑，确保默认值不会显示在小票上：

```dart
// 显示客户电话（如果不为空且不是默认值）
if (customerPhone.isNotEmpty && customerPhone != '1010101010') {
  await printer.printText('Telepon: $customerPhone\n');
}

// 显示客户名字（如果不为空且不是默认值）
if (customerName.isNotEmpty && customerName.toUpperCase() != 'ANONIM') {
  await printer.printText('$customerName\n');
}
```

## 修复后的行为

### 创建订单时
- **TERA支付**: 无论是否有客户信息，都设置为 `ANONIM` / `1010101010`
- **其他支付**: 只有当客户姓名和手机号都为空时，才设置为 `ANONIM` / `1010101010`

### 小票显示时
- **有真实客户信息**: 正常显示客户姓名和电话
- **只有默认值**: 不显示客户信息行（因为 `ANONIM` 和 `1010101010` 被过滤）
- **部分信息为空**: 只显示有效的信息行

## 测试验证

创建了 `customer_info_consistency_test.dart` 测试文件，包含以下测试用例：

1. **TERA支付方式一致性测试**: 验证创建订单和小票显示使用相同的默认值
2. **非TERA支付方式一致性测试**: 验证空客户信息时的默认值设置
3. **小票显示过滤测试**: 验证默认值不会显示在小票上
4. **真实信息显示测试**: 验证真实客户信息正常显示
5. **边界情况测试**: 验证各种空值和null值的处理

所有测试用例都通过，确保修复的正确性。

## 影响范围

### 修改的文件
- `EDC/frontend/lib/services/auto_print_service.dart`
- `EDC/frontend/test/customer_info_consistency_test.dart` (新增)

### 影响的功能
- 小票打印中的客户信息显示
- 客户信息默认值的一致性

### 向后兼容性
- ✅ 完全向后兼容
- ✅ 不影响现有的订单创建逻辑
- ✅ 不影响现有的客户信息处理逻辑

## 验证步骤

1. **运行测试**:
   ```bash
   flutter test test/customer_info_consistency_test.dart
   ```

2. **手动测试场景**:
   - 创建没有客户信息的订单（CASH支付）
   - 创建没有客户信息的订单（TERA支付）
   - 创建有客户信息的订单
   - 检查小票上的客户信息显示

3. **预期结果**:
   - 没有客户信息时，小票上不显示客户信息行
   - 有真实客户信息时，小票上正常显示
   - 不会出现 "ANONIM" 或 "1010101010" 在小票上

## 总结

这次修复解决了客户信息默认值设置的一致性问题，确保：

1. **数据一致性**: 创建订单和小票显示使用相同的默认值逻辑
2. **用户体验**: 小票上不会显示无意义的默认值
3. **代码质量**: 通过测试确保修复的正确性和稳定性

修复后，当用户没有输入姓名和手机时，系统会正确处理默认值，并且小票上不会显示这些默认值，提供更好的用户体验。
