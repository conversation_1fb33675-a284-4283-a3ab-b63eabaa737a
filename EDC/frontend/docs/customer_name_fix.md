# Customer Name 字段修改说明

## 问题描述

在 cash payment 页面的下单接口中，`customerName` 字段之前使用的是会员的姓名（`memberInfo['name']`），这个姓名可能包含 "Customer" 或 "Business Customer" 前缀，例如：
- "Customer B1234ABC"
- "Business Customer B5678XYZ"

## 修改内容

### 1. 修改下单请求中的 customerName 字段

**文件**: `EDC/frontend/lib/screens/payment/cash_payment_page.dart`

**修改前**:
```dart
customerName: memberInfo['name'] as String?,
```

**修改后**:
```dart
// Prepare customer name - use vehicle plate number instead of member name
String? customerNameForOrder;
if (memberInfo.isNotEmpty && memberInfo['vehicle_plate'] != null && memberInfo['vehicle_plate'].toString().isNotEmpty) {
  customerNameForOrder = memberInfo['vehicle_plate'] as String;
  debugPrint('✅ Using vehicle plate as customer name: $customerNameForOrder');
} else {
  customerNameForOrder = null;
  debugPrint('⚠️ No vehicle plate available, customer name will be null');
}

// 在 CreateOrderRequest 中使用
customerName: customerNameForOrder,
```

### 2. 添加调试信息

添加了详细的调试信息来跟踪 customerName 字段的赋值过程：

```dart
debugPrint('   Customer Name (vehicle plate): $customerNameForOrder');
debugPrint('   Customer ID: ${memberInfo.isNotEmpty ? memberInfo['member_id'] : 'null'}');
```

## 修改效果

### 修改前
- `customerName`: "Customer B1234ABC"
- `customerId`: "B2C_1234567890"

### 修改后
- `customerName`: "B1234ABC"  （纯车牌号）
- `customerId`: "B2C_1234567890"  （保持不变）

## 数据流说明

1. **会员信息缓存**: 会员注册时，车牌号存储在 `metadata['plateNumbers']` 中
2. **会员信息构建**: 在 cash payment 页面，从缓存会员信息构建 `memberInfo`，其中包含：
   - `name`: 可能包含 "Customer" 前缀的姓名
   - `vehicle_plate`: 纯净的车牌号
3. **下单请求**: 现在使用 `vehicle_plate` 而不是 `name` 作为 `customerName`

## 兼容性说明

- 如果没有车牌号信息，`customerName` 将为 `null`
- `customerId` 字段保持不变，仍然使用会员ID
- 不影响其他现有功能，只修改下单接口中的 `customerName` 字段

## 测试建议

1. 测试有车牌号的会员下单，确认 `customerName` 为纯车牌号
2. 测试没有车牌号的情况，确认系统正常处理 `null` 值
3. 检查后端接收到的数据格式是否符合预期 