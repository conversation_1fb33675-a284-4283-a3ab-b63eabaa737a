# 基于支付方式的客户信息默认值设置

## 概述

根据用户需求，当客户姓名和电话都为空时，系统会根据支付方式自动设置默认的客户信息，以便在小票上显示相应的信息。

## 实现逻辑

### 触发条件
- 客户姓名为空（或为 "ANONIM"）
- 客户电话为空

### 判断规则
通过检查 `order.paymentMethod` 字段来确定支付方式：

```dart
// 如果客户名字和电话都为空，根据支付方式设置默认值
if (customerName.isEmpty && customerPhone.isEmpty) {
  final String paymentMethod = order.paymentMethod.toLowerCase();
  
  if (paymentMethod.contains('tera')) {
    // TERA 支付方式
    customerName = 'TERA';
    customerPhone = '**********';
  } else {
    // 其他支付方式
    customerName = 'ANONIM';
    customerPhone = '**********';
  }
}
```

## 支付方式分类

### TERA 支付
- **识别条件**：`paymentMethod.contains('tera')`（不区分大小写）
- **默认客户名**：`TERA`
- **默认电话**：`**********`

### 其他支付方式
- **包含**：CASH、BANK_CARD、WECHAT、ALIPAY、POINTS、COUPON、VOUCHER 等
- **默认客户名**：`ANONIM`
- **默认电话**：`**********`

## 电话号码格式化

为了确保默认电话号码正确显示，修改了 `_formatPhoneNumber` 方法：

```dart
/// 格式化电话号码
static String _formatPhoneNumber(String? phone) {
  if (phone == null || phone.isEmpty) return '';
  
  // 对于特殊的默认电话号码，直接返回
  if (phone == '**********' || phone == '**********') {
    return phone;
  }
  
  // 其他电话号码的常规格式化逻辑
  final String cleanPhone = phone.replaceAll(RegExp(r'[^\\d]'), '');
  
  if (cleanPhone.length >= 5) {
    return '**${cleanPhone.substring(cleanPhone.length - 5)}';
  } else if (cleanPhone.isNotEmpty) {
    return '**${cleanPhone.padLeft(5, '0')}';
  }
  
  return '';
}
```

## 小票显示效果

### TERA 支付方式（无客户信息）
```
Invoice No: INV-001
Date: 2024-01-15 14:30:00
Pump No: 1
TERA
Telepon: **********
```

### 现金支付方式（无客户信息）
```
Invoice No: INV-002
Date: 2024-01-15 14:35:00
Pump No: 2
ANONIM
Telepon: **********
```

### 银行卡支付方式（无客户信息）
```
Invoice No: INV-003
Date: 2024-01-15 14:40:00
Pump No: 3
ANONIM
Telepon: **********
```

## 优先级顺序

客户信息的获取优先级如下：

1. **最高优先级**：`member_info` 中的真实客户信息
2. **次优先级**：`order.customerName` 和 `order.memberPhone` 中的信息
3. **最低优先级**：根据支付方式设置的默认值

## 修改的文件

1. **`lib/services/auto_print_service.dart`**
   - 修改 `_getCustomerInfo` 方法，添加支付方式判断逻辑
   - 修改 `_formatPhoneNumber` 方法，处理默认电话号码

## 测试场景

### 测试用例 1：TERA 支付，无客户信息
- **输入**：`paymentMethod = "TERA"`，无客户姓名和电话
- **预期输出**：显示 "TERA" 和 "**********"

### 测试用例 2：现金支付，无客户信息
- **输入**：`paymentMethod = "CASH"`，无客户姓名和电话
- **预期输出**：显示 "ANONIM" 和 "**********"

### 测试用例 3：银行卡支付，无客户信息
- **输入**：`paymentMethod = "BANK_CARD"`，无客户姓名和电话
- **预期输出**：显示 "ANONIM" 和 "**********"

### 测试用例 4：有真实客户信息
- **输入**：任何支付方式，有真实客户姓名和电话
- **预期输出**：显示真实的客户信息，忽略默认值

## 注意事项

1. **大小写不敏感**：支付方式判断使用 `toLowerCase()` 进行大小写不敏感匹配
2. **包含匹配**：使用 `contains('tera')` 而不是精确匹配，以适应可能的变体
3. **电话格式**：默认电话号码不进行格式化，直接显示完整号码
4. **向后兼容**：不影响现有的客户信息显示逻辑，只在无客户信息时才使用默认值

## 业务意义

这个功能的业务意义在于：

1. **区分支付方式**：通过小票上的客户信息可以快速识别使用的支付方式
2. **审计追踪**：有助于财务和运营团队分析不同支付方式的使用情况
3. **用户体验**：避免小票上出现空白的客户信息区域
4. **数据一致性**：确保所有小票都有客户信息显示，便于后续处理和分析 