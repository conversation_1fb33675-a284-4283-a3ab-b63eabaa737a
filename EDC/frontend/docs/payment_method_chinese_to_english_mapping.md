# 支付方式中文到英文映射实现

## 问题描述

在班次员工报告打印中，支付方式显示存在不一致的问题：
- "现金" 显示为中文
- 其他支付方式（如 "Mandiri"）显示为英文

## 解决方案

### 1. 数据模型层映射

在 `lib/models/shift_attendant_model.dart` 中的 `PaymentMethodData` 类添加了映射函数：

```dart
factory PaymentMethodData.fromJson(Map<String, dynamic> json) {
  return PaymentMethodData(
    paymentMethod: json['payment_method'] as String,
    paymentMethodName: _mapPaymentMethodName(json['payment_method_name'] as String),
    totalAmount: (json['total_amount'] as num).toDouble(),
    transactionCount: json['transaction_count'] as int,
    percentage: (json['percentage'] as num).toDouble(),
  );
}

static String _mapPaymentMethodName(String methodName) {
  switch (methodName) {
    case '现金':
      return 'Cash';
    case '银行卡':
      return 'Bank Card';
    case '信用卡':
      return 'Credit Card';
    case '借记卡':
      return 'Debit Card';
    case '电子钱包':
      return 'E-Wallet';
    case '代金券':
      return 'Voucher';
    case '车队卡':
      return 'Fleet Card';
    default:
      return methodName; // 保持原值，如 "Mandiri"
  }
}
```

### 2. 演示代码修复

在 `lib/services/native/sunmi_printer_service.dart` 中的演示方法中，将硬编码的"现金"修改为"Cash"：

```dart
// 修改前
await printColumnsString(
  <String>['支付方式:', '现金', ''],
  <int>[2, 1, 1],
  <int>[0, 1, 0],
);

// 修改后
await printColumnsString(
  <String>['支付方式:', 'Cash', ''],
  <int>[2, 1, 1],
  <int>[0, 1, 0],
);
```

### 3. 映射规则

| 中文支付方式 | 英文映射 |
|-------------|----------|
| 现金 | Cash |
| 银行卡 | Bank Card |
| 信用卡 | Credit Card |
| 借记卡 | Debit Card |
| 电子钱包 | E-Wallet |
| 代金券 | Voucher |
| 车队卡 | Fleet Card |
| 其他（如Mandiri） | 保持原值 |

### 4. 工作流程

1. **API 返回数据**: 包含中文支付方式名称 `payment_method_name: "现金"`
2. **数据模型解析**: `PaymentMethodData.fromJson()` 调用 `_mapPaymentMethodName()`
3. **映射转换**: 中文"现金"映射为英文"Cash"
4. **打印输出**: 班次报告中显示统一的英文支付方式名称

### 5. 影响范围

- ✅ **班次员工报告打印**: 所有支付方式统一显示为英文
- ✅ **个人员工报告打印**: 支付方式名称一致性
- ✅ **数据一致性**: 确保所有打印输出的支付方式名称统一

### 6. 测试验证

创建了测试文件 `test/payment_method_mapping_test.dart` 来验证映射功能：

```dart
test('PaymentMethodData should map Chinese payment methods to English', () {
  final Map<String, dynamic> testData = {
    'payment_method': 'cash',
    'payment_method_name': '现金',
    'total_amount': 100000.0,
    'transaction_count': 5,
    'percentage': 50.0,
  };

  final PaymentMethodData paymentMethod = PaymentMethodData.fromJson(testData);

  expect(paymentMethod.paymentMethodName, equals('Cash'));
});
```

### 7. 注意事项

1. **向后兼容**: 未知的支付方式名称保持原值，确保不会破坏现有功能
2. **数据源**: 映射发生在数据模型层，确保所有使用该数据的地方都能获得一致的结果
3. **性能**: 映射函数简单高效，不会影响性能
4. **扩展性**: 可以轻松添加新的支付方式映射规则

## 实现状态

- ✅ 数据模型层映射函数已实现
- ✅ 演示代码硬编码问题已修复
- ✅ 测试用例已创建
- ✅ 文档已更新

## 验证方法

1. 运行测试: `flutter test test/payment_method_mapping_test.dart`
2. 查看班次报告打印输出，确认支付方式显示为英文
3. 验证不同支付方式的映射是否正确 