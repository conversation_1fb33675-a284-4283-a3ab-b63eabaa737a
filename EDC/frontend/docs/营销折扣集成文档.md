# 营销折扣集成文档

## 概述

本文档描述了EDC前庭支付系统中营销折扣计算功能的集成实现。该功能通过调用 `http://localhost:8080/api/v1/calculator/process` 接口计算折扣，并将折扣信息应用到订单中。

## 架构设计

### 核心组件

1. **MarketingDiscountService** - 营销折扣计算服务
2. **PromotionApi** - 促销API接口
3. **PromotionResponse** - 折扣响应模型
4. **支付页面集成** - 现金支付和银行卡支付页面

### 数据流程

```
支付页面 → MarketingDiscountService → PromotionApi → 折扣计算接口
                ↓
      应用折扣到订单请求 → 创建订单 → 订单包含折扣信息
```

## API接口规范

### 请求接口
- **URL**: `http://localhost:8080/api/v1/calculator/process`
- **方法**: POST
- **Content-Type**: application/json

### 请求格式
```json
{
  "userId": "M10001",
  "orderAmount": 450000,
  "orderTime": "2024-01-01T10:00:00.000Z",
  "items": [
    {
      "itemId": "bp-92-fuel",
      "name": "BP 92",
      "category": "fuel",
      "price": 14000,
      "quantity": 28
    }
  ]
}
```

### 响应格式
```json
{
  "success": true,
  "message": "折扣计算成功",
  "originalAmount": 450000,
  "discountedAmount": 435000,
  "discountAmount": 15000,
  "items": [
    {
      "itemId": "bp-92-fuel",
      "name": "BP 92",
      "originalPrice": 14000,
      "discountedPrice": 13533.************,
      "quantity": 28
    }
  ]
}
```

## 代码实现

### 1. 营销折扣服务 (MarketingDiscountService)

位置：`lib/services/marketing_discount_service.dart`

主要功能：
- 根据支付交易数据和会员信息计算折扣
- 将折扣信息应用到订单请求中
- 构建API请求数据

```dart
// 计算折扣
final promotionResponse = await _discountService.calculateDiscount(
  paymentData: paymentData,
  memberInfo: memberInfo,
);

// 应用折扣到订单
if (promotionResponse != null) {
  orderRequest = _discountService.applyDiscountToOrder(
    orderRequest: orderRequest,
    promotionResponse: promotionResponse,
  );
}
```

### 2. 促销API (PromotionApi)

位置：`lib/services/api/promotion_api.dart`

更新内容：
- 端点从 `/api/v1/calculator/calculate` 更新为 `/api/v1/calculator/process`
- 添加类型安全的响应处理

### 3. 支付页面集成

#### 现金支付页面
位置：`lib/screens/payment/cash_payment_page.dart`

集成点：
- 在订单创建前计算折扣
- 应用折扣到订单请求
- 记录折扣信息到订单metadata

#### 银行卡支付页面
位置：`lib/screens/payment/bank_card_payment_page.dart`

集成点：
- 与现金支付页面相同的集成模式
- 支持所有支付方式的折扣计算

## 订单数据结构

### 折扣信息在订单metadata中的结构

```json
{
  "metadata": {
    "discount_info": {
      "original_amount": 450000,
      "discounted_amount": 435000,
      "discount_amount": 15000,
      "discount_items": [
        {
          "item_id": "bp-92-fuel",
          "name": "BP 92",
          "original_price": 14000,
          "discounted_price": 13533.************,
          "quantity": 28
        }
      ],
      "promotion_applied_at": "2024-01-01T10:00:00.000Z"
    },
    "member_info": {
      "member_id": "M10001",
      "name": "John Doe",
      "membership_level": "Gold Member"
    }
  }
}
```

## 使用方式

### 1. 自动折扣计算

系统在支付确认前自动执行以下步骤：

1. 获取支付交易数据和会员信息
2. 调用营销折扣计算接口
3. 如果有可用折扣，自动应用到订单中
4. 创建包含折扣信息的订单
5. 显示折扣后的最终金额给用户

### 2. 会员折扣支持

- 优先使用缓存的会员信息
- 支持会员等级相关的折扣计算
- 记录会员信息到订单中

### 3. 异常处理

- 折扣计算失败时不影响正常支付流程
- 按原价创建订单，确保支付流程稳定性
- 详细的错误日志记录

## 测试

### 测试文件
位置：`test/marketing_discount_integration_test.dart`

测试内容：
- 有会员信息的折扣计算
- 无会员信息的折扣计算
- 折扣应用到订单的逻辑
- 数据结构验证

### 运行测试
```bash
flutter test test/marketing_discount_integration_test.dart
```

## 配置要求

### API服务配置
1. 确保营销折扣计算服务运行在 `localhost:8080`
2. 接口路径：`/api/v1/calculator/process`
3. 支持POST请求和JSON格式数据

### 应用配置
1. ApiService需要正确初始化
2. PromotionApi在ApiService中注册
3. 支付页面导入相关服务

## 调试和监控

### 日志输出
系统提供详细的调试日志：

```
🎯 开始计算营销折扣...
   交易金额: 450000.0
   燃油类型: Gasoline 92
   油量: 28.0L
   会员ID: M10001
   会员等级: Gold Member

✅ 折扣计算成功:
   原价: 450000.0
   折后价: 435000.0
   折扣金额: 15000.0
   折扣商品: 1个

🎁 应用折扣到订单...
   原始金额: 450000.0
   折扣金额: 15000.0
   最终金额: 435000.0

✅ 折扣已应用到订单请求
```

### 错误处理
```
❌ 营销折扣计算异常: [错误详情]
💰 无可用折扣，按原价支付
⚠️ 折扣计算失败: [失败原因]
```

## 扩展性

### 未来功能扩展
1. 支持多种促销规则
2. 支持优惠券和代金券
3. 支持积分抵扣
4. 支持时间限制的促销活动

### 接口扩展
1. 支持更多商品类型的折扣计算
2. 支持复杂的折扣组合规则
3. 支持实时库存和促销状态查询

## 注意事项

1. **性能考虑**：折扣计算是同步操作，可能增加支付确认时间
2. **网络异常**：确保网络问题不影响正常支付流程
3. **数据一致性**：折扣金额计算需要与后端服务保持一致
4. **用户体验**：折扣计算过程中显示适当的加载指示器

## 总结

营销折扣集成功能为EDC前庭支付系统提供了灵活的促销支持，通过标准化的API接口实现折扣计算，并将折扣信息完整地保存到订单中，为后续的财务对账和营销分析提供数据支持。 