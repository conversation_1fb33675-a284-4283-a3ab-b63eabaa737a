# 客户信息显示简化修改

## 修改概述

根据用户需求，简化了打印小票中的客户信息显示逻辑：
- 如果 `order.customerName` 为空，则不显示客户名行
- 如果客户电话为空，则不显示电话行
- 删除了其他复杂的判断逻辑

## 修改详情

### 1. 简化客户信息显示逻辑

**文件**：`lib/services/auto_print_service.dart`

**修改前**：
```dart
if (customerName.isNotEmpty && customerName.toUpperCase() != 'ANONIM') {
  await printer.printText('$customerName\n');
  // 只有当手机号码不为空且不是默认值时才显示该行
  if (customerPhone.isNotEmpty && customerPhone != '*****01010' && customerPhone != '**01010') {
    await printer.printText('Telepon: $customerPhone\n');
  }
}
```

**修改后**：
```dart
// 显示客户姓名（如果不为空且不是默认值）
if (customerName.isNotEmpty && customerName.toUpperCase() != 'ANONIM') {
  await printer.printText('$customerName\n');
}

// 显示客户电话（如果不为空）
if (customerPhone.isNotEmpty) {
  await printer.printText('Telepon: $customerPhone\n');
}
```

### 2. 重构客户信息获取方法

**修改前**：
```dart
static Map<String, String> _getCustomerInfo(Order order) {
  // 复杂的嵌套判断逻辑
  if (memberInfo != null && memberInfo.isNotEmpty) {
    if (name != null && name.isNotEmpty && name.toUpperCase() != 'ANONIM') {
      return {
        'name': name,
        'phone': _formatPhoneNumber(phone),
      };
    }
  }
  
  if (order.customerName != null && order.customerName!.isNotEmpty && order.customerName!.toUpperCase() != 'ANONIM') {
    return {
      'name': order.customerName!,
      'phone': _formatPhoneNumber(order.memberPhone),
    };
  }
  
  return {
    'name': 'ANONIM',
    'phone': '**01010',
  };
}
```

**修改后**：
```dart
static Map<String, String> _getCustomerInfo(Order order) {
  // 优先从 member_info 获取客户信息
  final Map<String, dynamic>? memberInfo = order.extInfo?['member_info'] as Map<String, dynamic>?;
  
  String customerName = '';
  String customerPhone = '';
  
  if (memberInfo != null && memberInfo.isNotEmpty) {
    final String? name = memberInfo['name'] as String?;
    final String? phone = memberInfo['phone'] as String?;
    
    if (name != null && name.isNotEmpty && name.toUpperCase() != 'ANONIM') {
      customerName = name;
    }
    
    if (phone != null && phone.isNotEmpty) {
      customerPhone = _formatPhoneNumber(phone);
    }
  }
  
  // 如果 member_info 中没有有效信息，尝试从 order 直接获取
  if (customerName.isEmpty && order.customerName != null && order.customerName!.isNotEmpty && order.customerName!.toUpperCase() != 'ANONIM') {
    customerName = order.customerName!;
  }
  
  if (customerPhone.isEmpty && order.memberPhone.isNotEmpty) {
    customerPhone = _formatPhoneNumber(order.memberPhone);
  }
  
  // 如果客户名字和电话都为空，根据支付方式设置默认值
  if (customerName.isEmpty && customerPhone.isEmpty) {
    final String paymentMethod = order.paymentMethod.toLowerCase();
    
    if (paymentMethod.contains('tera')) {
      // TERA 支付方式
      customerName = 'TERA';
      customerPhone = '0202020202';
    } else {
      // 其他支付方式
      customerName = 'ANONIM';
      customerPhone = '1010101010';
    }
  }
  
  return {
    'name': customerName,
    'phone': customerPhone,
  };
}
```

### 3. 简化电话号码格式化

**修改前**：
```dart
static String _formatPhoneNumber(String? phone) {
  if (phone == null || phone.isEmpty) return '**01010';
  
  final String cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
  
  if (cleanPhone.length >= 5) {
    return '**${cleanPhone.substring(cleanPhone.length - 5)}';
  } else if (cleanPhone.isNotEmpty) {
    return '**${cleanPhone.padLeft(5, '0')}';
  }
  
  return '**01010';
}
```

**修改后**：
```dart
static String _formatPhoneNumber(String? phone) {
  if (phone == null || phone.isEmpty) return '';
  
  final String cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
  
  if (cleanPhone.length >= 5) {
    return '**${cleanPhone.substring(cleanPhone.length - 5)}';
  } else if (cleanPhone.isNotEmpty) {
    return '**${cleanPhone.padLeft(5, '0')}';
  }
  
  return '';
}
```

## 显示规则

### 客户姓名显示规则
- ✅ 只有当客户姓名不为空且不是 "ANONIM" 时才显示
- ✅ 优先从 `member_info` 获取，其次从 `order.customerName` 获取
- ✅ 如果客户名字和电话都为空，根据支付方式设置默认值：
  - TERA 支付：显示 `TERA`
  - 其他支付：显示 `ANONIM`

### 客户电话显示规则
- ✅ 只有当客户电话不为空时才显示
- ✅ 优先从 `member_info` 获取，其次从 `order.memberPhone` 获取
- ✅ 如果客户名字和电话都为空，根据支付方式设置默认值：
  - TERA 支付：显示 `0202020202`
  - 其他支付：显示 `1010101010`

### 车牌号显示规则
- ✅ 只有当 `order.customerName` 不为空时才显示
- ✅ 显示格式：`NomorKendaraan: [车牌号]`

## 小票显示示例

### 有完整客户信息的情况
```
Invoice No: INV-001
Date: 2024-01-15 14:30:00
Pump No: 1
John Doe
Telepon: **12345
NomorKendaraan: B1234ABC
```

### 只有客户姓名的情况
```
Invoice No: INV-002
Date: 2024-01-15 14:35:00
Pump No: 2
Jane Smith
NomorKendaraan: B5678DEF
```

### 只有车牌号的情况
```
Invoice No: INV-003
Date: 2024-01-15 14:40:00
Pump No: 3
NomorKendaraan: B9012GHI
```

### 没有客户信息的情况（非 TERA 支付）
```
Invoice No: INV-004
Date: 2024-01-15 14:45:00
Pump No: 4
ANONIM
Telepon: 1010101010
```

### 没有客户信息的情况（TERA 支付）
```
Invoice No: INV-005
Date: 2024-01-15 14:50:00
Pump No: 5
TERA
Telepon: 0202020202
```

## 优势

1. **简化逻辑**：移除了复杂的嵌套判断和默认值处理
2. **清晰显示**：只在有实际数据时才显示相应信息
3. **减少冗余**：不再显示无意义的默认值
4. **易于维护**：代码逻辑更加清晰和易于理解
5. **用户友好**：小票上只显示有用的信息

## 测试建议

1. **测试有完整客户信息的订单**：验证客户姓名、电话和车牌号都正确显示
2. **测试部分客户信息的订单**：验证只显示有数据的字段
3. **测试没有客户信息的订单**：验证不显示任何客户信息行
4. **测试匿名客户**：验证不显示 "ANONIM" 客户名
5. **测试空电话号码**：验证不显示电话行

## 相关文件

- `lib/services/auto_print_service.dart` - 主要修改文件
- `docs/print_reprint_configuration.md` - 更新的配置文档 