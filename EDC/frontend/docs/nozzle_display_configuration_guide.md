# Nozzle 界面显示配置指南

## 概述

本文档说明如何修改 EDC Nozzle 授权界面的显示内容字段。通过修改 `NozzleDisplayConfig` 类中的配置项，您可以轻松控制界面上显示哪些信息以及如何显示。

## 配置文件位置

配置类位于：`lib/screens/fuel/nozzle_authorization_page.dart` 文件顶部的 `NozzleDisplayConfig` 类。

## 配置项说明

### 1. 基本信息显示配置

```dart
// 基本信息显示配置
static const bool showPumpName = true;           // 显示泵名称
static const bool showNozzleName = true;        // 显示枪名称
static const bool showFuelGrade = true;         // 显示油品等级
static const bool showFuelType = true;          // 显示燃油类型（完整名称）
static const bool showDeviceName = true;        // 显示设备名称
static const bool showNozzleNumber = true;      // 显示枪号
```

**效果预览：**
- `showPumpName = true`：显示 "Pump Device01"
- `showNozzleName = true`：显示 "Nozzle 1"
- `showFuelGrade = true`：显示 "92#"

### 2. 价格信息显示配置

```dart
// 价格信息显示配置
static const bool showUnitPrice = true;         // 显示单价
static const bool showPriceInHeader = true;     // 在顶部信息栏显示价格
static const bool showPriceLoading = true;      // 显示价格加载状态
static const bool showPriceError = true;        // 显示价格错误信息
```

### 3. 状态信息显示配置

```dart
// 状态信息显示配置  
static const bool showNozzleStatus = true;      // 显示喷枪状态
static const bool showLastUpdateTime = false;   // 显示最后更新时间
static const bool showCurrentVolume = false;    // 显示当前体积
static const bool showCurrentAmount = false;    // 显示当前金额
```

### 4. 授权信息显示配置

```dart
// 授权信息显示配置
static const bool showEstimatedValue = true;    // 显示预估值
static const bool showValueCalculation = true;  // 显示值计算过程
static const bool showQuickPresets = true;      // 显示快捷预设按钮
```

### 5. 格式化配置

```dart
// 格式化配置
static const int priceDecimalPlaces = 0;        // 价格小数位数
static const int volumeDecimalPlaces = 3;       // 体积小数位数
static const int amountDecimalPlaces = 0;       // 金额小数位数
static const String currencySymbol = 'Rp';      // 货币符号
static const String volumeUnit = 'L';           // 体积单位
```

### 6. 自定义显示文本

```dart
// 自定义显示文本
static const Map<String, String> customLabels = {
  'pumpInfo': 'Pump Information',
  'currentValue': 'Current Value',
  'estimatedVolume': 'Estimated Volume', 
  'estimatedAmount': 'Estimated Amount',
  'authMode': 'Authorization Mode',
  'quickAdd': 'Quick Add',
  'unitPrice': 'Unit Price',
  'nozzleStatus': 'Status',
  'lastUpdate': 'Last Update',
};
```

## 常见修改示例

### 示例 1：隐藏价格信息

```dart
// 隐藏顶部价格显示
static const bool showPriceInHeader = false;

// 隐藏价格加载状态
static const bool showPriceLoading = false;
```

### 示例 2：显示更多技术信息

```dart
// 显示最后更新时间
static const bool showLastUpdateTime = true;

// 显示当前体积和金额
static const bool showCurrentVolume = true;
static const bool showCurrentAmount = true;
```

### 示例 3：自定义货币和单位

```dart
// 改为美元显示
static const String currencySymbol = '$';

// 改为加仑单位
static const String volumeUnit = 'Gal';
static const int volumeDecimalPlaces = 2;  // 加仑通常显示2位小数
```

### 示例 4：自定义界面文本（多语言支持）

```dart
// 中文界面
static const Map<String, String> customLabels = {
  'pumpInfo': '加油机信息',
  'currentValue': '当前值',
  'estimatedVolume': '预估体积', 
  'estimatedAmount': '预估金额',
  'authMode': '授权模式',
  'quickAdd': '快速添加',
  'unitPrice': '单价',
  'nozzleStatus': '状态',
  'lastUpdate': '最后更新',
};
```

### 示例 5：简化界面（仅显示核心信息）

```dart
// 基本信息 - 仅显示核心内容
static const bool showPumpName = true;
static const bool showNozzleName = true;
static const bool showFuelGrade = true;
static const bool showFuelType = false;        // 隐藏详细燃油类型
static const bool showDeviceName = false;      // 隐藏设备名称
static const bool showNozzleNumber = false;    // 隐藏枪号

// 状态信息 - 隐藏技术详情
static const bool showNozzleStatus = false;
static const bool showLastUpdateTime = false;
static const bool showCurrentVolume = false;
static const bool showCurrentAmount = false;

// 授权信息 - 保持核心功能
static const bool showEstimatedValue = true;
static const bool showQuickPresets = true;
```

## 配置生效方式

1. **实时生效**：修改配置后，重新启动应用即可看到效果
2. **热重载支持**：在开发模式下，大部分配置修改支持热重载
3. **编译时确定**：所有配置都是编译时常量，确保最佳性能

## 高级定制

### 自定义信息格式化

如果需要更复杂的信息显示格式，可以修改以下方法：

```dart
// 自定义主信息栏格式
static String formatNozzleInfo(Nozzle nozzle) {
  List<String> infoParts = [];
  
  // 您的自定义逻辑
  if (showPumpName && nozzle.deviceName != null) {
    infoParts.add('Pump ${nozzle.deviceName}');
  }
  
  // 添加更多自定义格式...
  
  return infoParts.join(' • ');
}
```

### 添加新的显示字段

1. 在 `getExtendedInfo` 方法中添加新的配置项
2. 在 Nozzle 模型中确保包含所需的数据字段
3. 添加相应的显示开关和格式化逻辑

## 注意事项

1. **性能考虑**：所有配置都是编译时常量，不会影响运行时性能
2. **数据可用性**：确保显示的字段在 Nozzle 模型中确实存在
3. **UI 布局**：某些字段的隐藏可能会影响界面布局，建议在不同配置下测试界面效果
4. **用户体验**：避免隐藏过多关键信息，确保操作员能够获得必要的信息

## 调试建议

1. **逐步修改**：一次只修改少量配置项，观察效果
2. **测试不同状态**：在 idle、auth、fuelling 等不同状态下测试界面
3. **检查数据源**：确保 FCC 服务提供了所需的数据字段
4. **错误处理**：测试网络异常、价格获取失败等异常情况下的界面表现

通过这个配置系统，您可以轻松地定制 Nozzle 界面以满足不同的业务需求和用户偏好。 