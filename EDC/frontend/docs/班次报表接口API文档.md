# 班次报表接口API文档

## 接口概述

### 📋 **基本信息**
- **接口名称**: 获取班次报表数据
- **接口路径**: `GET /shifts/report/{id}`
- **接口描述**: 根据班次ID获取班次基本信息和班结小票的完整数据
- **接口版本**: v1.0
- **开发状态**: 设计中

### 🎯 **功能说明**
该接口用于获取指定班次的完整报表数据，包括：
- 班次基本信息（班次号、时间、站点等）
- 支付方式汇总数据
- 油品销售汇总数据  
- 非油品销售汇总数据
- TERA分类汇总数据
- 小票格式化数据

## 请求规范

### 🔗 **请求URL**
```
GET /api/v1/shifts/report/{id}
```

### 📝 **路径参数**
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | integer | 是 | 班次ID | 123 |

### 🔑 **请求头**
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| Authorization | string | 是 | 认证令牌 | Bearer {token} |
| Content-Type | string | 是 | 内容类型 | application/json |
| Accept-Language | string | 否 | 语言偏好 | zh-CN, en-US, id-ID |

### ❓ **查询参数**
| 参数名 | 类型 | 必填 | 描述 | 默认值 | 示例 |
|--------|------|------|------|--------|------|
| format | string | 否 | 响应格式 | json | json, receipt |
| currency | string | 否 | 货币格式 | IDR | IDR, USD |
| timezone | string | 否 | 时区 | Asia/Jakarta | Asia/Jakarta |
| include_details | boolean | 否 | 是否包含明细数据 | true | true, false |

### 📋 **请求示例**
```http
GET /api/v1/shifts/report/123?format=json&include_details=true HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
Accept-Language: zh-CN
```

## 响应规范

### ✅ **成功响应 (200 OK)**

#### 📊 **响应结构**
```json
{
  "success": true,
  "message": "Shift report retrieved successfully",
  "data": {
    "shift_info": {
      "id": 123,
      "shift_number": "SHIFT-20240115-001",
      "station_id": 1,
      "station_name": "BP Station Jakarta Pusat",
      "staff_id": 456,
      "staff_name": "Ahmad Wijaya",
      "start_time": "2024-01-15T06:00:00+07:00",
      "end_time": "2024-01-15T14:00:00+07:00",
      "duration_hours": 8.0,
      "status": "completed",
      "created_at": "2024-01-15T06:00:00+07:00",
      "updated_at": "2024-01-15T14:05:00+07:00"
    },
         "payment_summary": {
       "total_sales": 17427500.00,
       "total_transactions": 111,
      "payment_methods": [
        {
          "method": "cash",
          "method_name": "Cash",
          "amount": 6300000.00,
          "transaction_count": 42,
          "percentage": 40.0
        },
        {
          "method": "credit_card", 
          "method_name": "Credit Card",
          "amount": 4725000.00,
          "transaction_count": 28,
          "percentage": 30.0
        },
        {
          "method": "debit_card",
          "method_name": "Debit Card", 
          "amount": 3150000.00,
          "transaction_count": 18,
          "percentage": 20.0
        },
                 {
           "method": "e_wallet",
           "method_name": "E-Wallet",
           "amount": 1575000.00,
           "transaction_count": 10,
           "percentage": 10.0
         },
         {
           "method": "voucher",
           "method_name": "Voucher",
           "amount": 315000.00,
           "transaction_count": 5,
           "percentage": 2.0
         },
         {
           "method": "fleet_card",
           "method_name": "Fleet Card",
           "amount": 787500.00,
           "transaction_count": 8,
           "percentage": 5.0
         }
      ]
    },
    "fuel_summary": {
      "total_volume": 4521.750,
      "total_gross_sales": 9432375.00,
      "total_discount": 157206.25,
      "total_net_sales": 9275168.75,
      "total_transactions": 105,
      "fuel_grades": [
        {
          "grade": "92",
          "type": "gasoline",
          "name": "Pertamax 92",
          "volume": 2500.500,
          "gross_amount": 5251050.00,
          "discount_amount": 87517.50,
          "net_amount": 5163532.50,
          "average_price": 15003.00,
          "transaction_count": 45,
          "volume_percentage": 55.3
        },
        {
          "grade": "95", 
          "type": "gasoline",
          "name": "Pertamax 95",
          "volume": 1521.250,
          "gross_amount": 3042500.00,
          "discount_amount": 50708.33,
          "net_amount": 2991791.67,
          "average_price": 16002.00,
          "transaction_count": 35,
          "volume_percentage": 33.7
        },
        {
          "grade": "diesel",
          "type": "diesel", 
          "name": "Solar",
          "volume": 500.000,
          "gross_amount": 1138825.00,
          "discount_amount": 18980.42,
          "net_amount": 1119844.58,
          "average_price": 15009.00,
          "transaction_count": 25,
          "volume_percentage": 11.0
        }
      ]
    },
    "merchandise_summary": {
      "total_quantity": 147,
      "total_gross_sales": 6317625.00,
      "total_discount": 105293.75,
      "total_net_sales": 6212331.25,
      "total_transactions": 52,
      "top_products": [
        {
          "product_id": 101,
          "product_name": "Aqua 600ml",
          "product_type": "beverage",
          "category": "drinks",
          "quantity": 48,
          "unit_price": 3000.00,
          "gross_amount": 144000.00,
          "discount_amount": 7200.00,
          "net_amount": 136800.00,
          "transaction_count": 12
        },
        {
          "product_id": 102,
          "product_name": "Indomie Goreng",
          "product_type": "food",
          "category": "instant_food",
          "quantity": 24,
          "unit_price": 2500.00,
          "gross_amount": 60000.00,
          "discount_amount": 3000.00,
          "net_amount": 57000.00,
          "transaction_count": 8
        }
      ]
    },
    "tera_summary": {
      "fuel": {
        "gross_sales": 9432375.00,
        "total_discount": 157206.25,
        "net_sales": 9275168.75,
        "percentage": 59.9
      },
      "merchandise": {
        "gross_sales": 6317625.00,
        "total_discount": 105293.75,
        "net_sales": 6212331.25,
        "percentage": 40.1
      },
             "total": {
         "gross_sales": 17427500.00,
         "total_discount": 262500.00,
         "net_sales": 17165000.00
       }
    },
    "receipt_info": {
      "print_time": "2024-01-15T14:05:30+07:00",
      "receipt_number": "RCPT-************",
      "currency": "IDR",
      "timezone": "Asia/Jakarta"
    }
  },
  "meta": {
    "generated_at": "2024-01-15T14:05:30+07:00",
    "processing_time_ms": 245,
    "data_source": "database",
    "version": "1.0"
  }
}
```

#### 🎫 **小票格式响应 (format=receipt)**
当查询参数 `format=receipt` 时，返回格式化的小票数据：

```json
{
  "success": true,
  "message": "Shift receipt generated successfully",
  "data": {
    "receipt": {
      "header": {
        "station_name": "BP Station Jakarta Pusat",
        "address": "Jl. Sudirman No. 123, Jakarta Pusat",
        "phone": "+62-21-1234567"
      },
      "shift_info": {
        "shift_number": "SHIFT-20240115-001",
        "staff_name": "Ahmad Wijaya",
        "start_time": "15/01/2024 06:00",
        "end_time": "15/01/2024 14:00"
      },
      "sales_summary": [
        "=================================",
        "         SALES SUMMARY          ",
        "=================================",
                 "Cash Sales         : Rp 6,300,000",
         "Credit Card        : Rp 4,725,000", 
         "Debit Card         : Rp 3,150,000",
         "E-Wallet           : Rp 1,575,000",
         "Voucher            : Rp 315,000",
         "Fleet Card         : Rp 787,500",
         "---------------------------------",
         "Total Sales        : Rp 17,427,500",
        "",
        "=================================",
        "         FUEL SUMMARY           ",
        "=================================",
        "Pertamax 92        : 2,500.5 L",
        "Pertamax 95        : 1,521.3 L", 
        "Solar              : 500.0 L",
        "---------------------------------",
        "Total Volume       : 4,521.8 L",
        "Fuel Sales         : Rp 9,275,169",
        "",
        "=================================",
        "      MERCHANDISE SUMMARY       ",
        "=================================",
        "Total Items        : 147",
        "Merchandise Sales  : Rp 6,212,331",
        "",
                 "=================================",
         "        TERA SUMMARY            ",
         "=================================",
         "FUEL CATEGORY:",
         "  Gross Sales      : Rp 9,432,375",
         "  Total Discount   : Rp 157,206",
         "  Net Sales        : Rp 9,275,169",
         "",
         "MERCHANDISE CATEGORY:",
         "  Gross Sales      : Rp 6,317,625",
         "  Total Discount   : Rp 105,294",
         "  Net Sales        : Rp 6,212,331",
         "",
         "=================================",
         "         GRAND TOTAL            ",
         "=================================",
         "Gross Sales        : Rp 17,427,500",
         "Total Discount     : Rp 262,500",
         "Net Sales          : Rp 17,165,000"
      ],
      "footer": {
        "print_time": "15/01/2024 14:05:30",
        "receipt_number": "RCPT-************",
        "thank_you_message": "Thank you for your business!"
      }
    }
  }
}
```

### ❌ **错误响应**

#### **400 Bad Request - 参数错误**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "Invalid shift ID format",
    "details": {
      "field": "id",
      "value": "abc",
      "expected": "positive integer"
    }
  },
  "timestamp": "2024-01-15T14:05:30+07:00"
}
```

#### **401 Unauthorized - 认证失败**
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED", 
    "message": "Authentication token is missing or invalid",
    "details": {
      "reason": "token_expired"
    }
  },
  "timestamp": "2024-01-15T14:05:30+07:00"
}
```

#### **403 Forbidden - 权限不足**
```json
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "Insufficient permissions to access shift report",
    "details": {
      "required_permission": "shift:read",
      "user_permissions": ["order:read"]
    }
  },
  "timestamp": "2024-01-15T14:05:30+07:00"
}
```

#### **404 Not Found - 班次不存在**
```json
{
  "success": false,
  "error": {
    "code": "SHIFT_NOT_FOUND",
    "message": "Shift with ID 123 not found",
    "details": {
      "shift_id": 123,
      "possible_reasons": [
        "Shift does not exist",
        "Shift has been deleted",
        "Access restricted"
      ]
    }
  },
  "timestamp": "2024-01-15T14:05:30+07:00"
}
```

#### **409 Conflict - 班次状态错误**
```json
{
  "success": false,
  "error": {
    "code": "SHIFT_NOT_COMPLETED",
    "message": "Cannot generate report for incomplete shift",
    "details": {
      "shift_id": 123,
      "current_status": "active",
      "required_status": "completed",
      "suggestion": "End the shift before generating report"
    }
  },
  "timestamp": "2024-01-15T14:05:30+07:00"
}
```

#### **500 Internal Server Error - 服务器错误**
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "Failed to generate shift report",
    "details": {
      "error_id": "err_20240115_140530_001",
      "suggestion": "Please try again later or contact support"
    }
  },
  "timestamp": "2024-01-15T14:05:30+07:00"
}
```

## 字段映射对照表

### 📋 **小票字段与API响应映射关系**

根据[班结小票设计文档](./班结小票设计文档.md)的字段定义，以下是API响应字段与小票显示字段的完整映射关系：

#### **基础信息字段映射**
| 小票字段 | 中文说明 | API响应字段路径 | 备注 |
|---------|---------|----------------|------|
| Station Name | 加油站名称 | `data.shift_info.station_name` | 通过station_id调用外部服务获取 |
| Staff Name | 员工姓名 | `data.shift_info.staff_name` | 从employees表获取 |
| Shift Name | 班次名称 | `data.shift_info.shift_number` | 对应shift_number字段 |
| Start Date & Time | 班次开始时间 | `data.shift_info.start_time` | ISO 8601格式 |
| End Date & Time | 班次结束时间 | `data.shift_info.end_time` | ISO 8601格式 |

#### **油品销量字段映射**
| 小票字段 | 中文说明 | API响应字段路径 | 备注 |
|---------|---------|----------------|------|
| Product1 Volume (L) | 产品1销量(升) | `data.fuel_summary.fuel_grades[0].volume` | 按fuel_grade分类 |
| Product2 Volume (L) | 产品2销量(升) | `data.fuel_summary.fuel_grades[1].volume` | 按fuel_grade分类 |
| Product3 Volume (L) | 产品3销量(升) | `data.fuel_summary.fuel_grades[2].volume` | 按fuel_grade分类 |
| Merchandise (Count) | 非油品数量 | `data.merchandise_summary.total_quantity` | 所有非油品商品总数量 |

#### **支付方式销售额字段映射**
| 小票字段 | 中文说明 | API响应字段路径 | 备注 |
|---------|---------|----------------|------|
| Cash Sales (Amount IDR) | 现金销售额 | `data.payment_summary.payment_methods[method="cash"].amount` | 筛选cash类型 |
| Card Sales (Amount IDR) | 刷卡销售额 | `data.payment_summary.payment_methods[method="credit_card"/"debit_card"].amount` | 合并信用卡和借记卡 |
| E-Wallet Sales (Amount IDR) | 电子钱包销售额 | `data.payment_summary.payment_methods[method="e_wallet"].amount` | 筛选e_wallet类型 |
| Voucher Sales (Amount IDR) | 代金券销售额 | `data.payment_summary.payment_methods[method="voucher"].amount` | 筛选voucher类型 |
| Fleet Sales (Amount IDR) | 车队销售额 | `data.payment_summary.payment_methods[method="fleet_card"].amount` | 筛选fleet_card类型 |
| Total Sales (Amount IDR) | 总销售额 | `data.payment_summary.total_sales` | 所有支付方式总和 |

#### **TERA分类汇总字段映射**
| 小票字段 | 中文说明 | API响应字段路径 | 备注 |
|---------|---------|----------------|------|
| Fuel Total Sales (Amount IDR) | 油品总销售额 | `data.tera_summary.fuel.gross_sales` | 优惠前总销售额 |
| Fuel Total Discount (Amount IDR) | 油品总优惠额 | `data.tera_summary.fuel.total_discount` | 油品优惠总额 |
| Fuel Sales (Amount IDR) | 油品净销售额 | `data.tera_summary.fuel.net_sales` | 优惠后净销售额 |
| Merchandise Total Sales (Amount IDR) | 非油品总销售额 | `data.tera_summary.merchandise.gross_sales` | 优惠前总销售额 |
| Merchandise Total Discount (Amount IDR) | 非油品总优惠额 | `data.tera_summary.merchandise.total_discount` | 非油品优惠总额 |
| Merchandise Net Sales (Amount IDR) | 非油品净销售额 | `data.tera_summary.merchandise.net_sales` | 优惠后净销售额 |

#### **小票底部信息字段映射**
| 小票字段 | 中文说明 | API响应字段路径 | 备注 |
|---------|---------|----------------|------|
| Station Name | 加油站名称(重复) | `data.shift_info.station_name` | 与顶部相同 |
| Receipt Print Date & Time | 小票打印时间 | `data.receipt_info.print_time` | 当前系统时间 |

### ✅ **字段覆盖率检查结果**
- ✅ **基础信息字段**: 5/5 完全覆盖
- ✅ **油品销量字段**: 4/4 完全覆盖  
- ✅ **支付方式字段**: 6/6 完全覆盖（已补充voucher和fleet_card）
- ✅ **TERA分类汇总**: 6/6 完全覆盖
- ✅ **小票底部信息**: 2/2 完全覆盖

**总覆盖率**: 23/23 (100%) ✅

## 业务逻辑说明

### 📊 **数据生成逻辑**
1. **班次验证**: 验证班次ID有效性和状态
2. **权限检查**: 验证用户是否有权限访问该班次数据
3. **数据聚合**: 从明细表或实时计算获取汇总数据
4. **格式化处理**: 根据查询参数格式化响应数据
5. **缓存策略**: 已完成班次的数据进行缓存

### 🔄 **数据源优先级**
1. **已生成明细**: 优先从班结明细表获取数据
2. **实时计算**: 明细表无数据时实时聚合计算
3. **缓存数据**: 使用Redis缓存提高响应速度

### ⏱️ **性能要求**
- **响应时间**: 正常情况下 < 500ms
- **并发支持**: 支持100+并发请求
- **缓存策略**: 已完成班次数据缓存24小时

## 安全考虑

### 🔐 **认证授权**
- 需要有效的JWT令牌
- 需要 `shift:read` 权限
- 支持站点级别的数据隔离

### 🛡️ **数据保护**
- 敏感数据脱敏处理
- 审计日志记录
- 防止SQL注入攻击

### 🚫 **访问限制**
- 频率限制: 每分钟最多60次请求
- IP白名单支持
- 跨域请求控制

## 使用示例

### 📝 **cURL示例**
```bash
# 获取基本报表数据
curl -X GET "https://api.example.com/api/v1/shifts/report/123" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: zh-CN"

# 获取小票格式数据
curl -X GET "https://api.example.com/api/v1/shifts/report/123?format=receipt" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"

# 获取简化数据（不包含明细）
curl -X GET "https://api.example.com/api/v1/shifts/report/123?include_details=false" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

### 💻 **JavaScript示例**
```javascript
// 使用fetch获取班次报表
async function getShiftReport(shiftId, options = {}) {
  const params = new URLSearchParams({
    format: options.format || 'json',
    include_details: options.includeDetails !== false,
    ...options
  });

  const response = await fetch(`/api/v1/shifts/report/${shiftId}?${params}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${getAuthToken()}`,
      'Content-Type': 'application/json',
      'Accept-Language': 'zh-CN'
    }
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// 使用示例
try {
  const report = await getShiftReport(123, { 
    format: 'json', 
    includeDetails: true 
  });
  console.log('班次报表:', report.data);
} catch (error) {
  console.error('获取报表失败:', error);
}
```

### 🐹 **Go示例**
```go
type ShiftReportClient struct {
    baseURL string
    token   string
    client  *http.Client
}

func (c *ShiftReportClient) GetShiftReport(shiftID int64, options *ReportOptions) (*ShiftReportResponse, error) {
    url := fmt.Sprintf("%s/api/v1/shifts/report/%d", c.baseURL, shiftID)
    
    req, err := http.NewRequest("GET", url, nil)
    if err != nil {
        return nil, err
    }
    
    // 添加查询参数
    q := req.URL.Query()
    if options != nil {
        if options.Format != "" {
            q.Add("format", options.Format)
        }
        q.Add("include_details", strconv.FormatBool(options.IncludeDetails))
    }
    req.URL.RawQuery = q.Encode()
    
    // 设置请求头
    req.Header.Set("Authorization", "Bearer "+c.token)
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Accept-Language", "zh-CN")
    
    resp, err := c.client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var result ShiftReportResponse
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return nil, err
    }
    
    return &result, nil
}
```

## 版本历史

### v1.0 (当前版本)
- 初始版本设计
- 支持基本班次报表数据获取
- 支持JSON和小票格式响应
- 包含完整的错误处理

### 计划功能
- v1.1: 支持PDF格式导出
- v1.2: 支持批量班次报表
- v1.3: 支持自定义报表模板

## 相关文档

- [班结小票设计文档](./班结小票设计文档.md)
- [班结小票数据存储设计](./班结小票数据存储设计.md)
- [班次汇总数据生成流程设计](./班次汇总数据生成流程设计.md)
- [认证授权API文档](../auth/认证授权API文档.md) 