# EDC 前庭支付系统 UI/UE 优化规划文档

## 1. 执行摘要

基于对 BP EDC 前端项目的深度代码审查和现状分析，本文档制定了系统性的 UI/UE 优化方案。当前系统已具备完整的核心业务功能（燃油交易、订单管理、支付处理），但在品牌标准化、国际化一致性和导航结构方面存在优化空间。

### 1.1 优化目标
- 🎯 **100% BP 品牌合规**：基于BP Logo标准的色彩体系和视觉标准
- 🌐 **国际化架构**：支持中英文切换，架构预留多语言能力，先完成英文
- 🚀 **角色导航优化**：实现基于员工权限的动态导航
- 📱 **SOW完整功能**：实现SOW文档中的28个功能点(F154-F181)
- 🔧 **接口预留设计**：使用mock数据，预留真实接口扩展能力

### 1.2 现状优势
- ✅ **核心功能完整**：燃油交易、订单、支付模块已完全实现
- ✅ **API 集成成熟**：FuelTransactionApi、OrderApi、PromotionApi 运行稳定
- ✅ **原生集成完善**：Sunmi 打印机功能完整可用
- ✅ **代码架构优秀**：使用现代 Flutter 架构模式 (Riverpod + GoRouter)

## 2. 品牌标准化优化

### 2.1 色彩体系统一 🔴 **高优先级**

#### 当前问题
- `AppTheme` 仍使用蓝色系主题 (`Colors.blue`)
- 各页面手动实现 BP 绿色 (`Color(0xFF2E8B57)`)
- 缺少集中化的 BP 色彩常量管理

#### 解决方案
```dart
// 创建 lib/constants/bp_colors.dart
class BPColors {
  // BP Logo 标准色彩 - 从提供的Logo图片中提取
  static const Color primary = Color(0xFF00A650);    // BP标准绿色 - 主色调
  static const Color secondary = Color(0xFF80C242);  // BP辅助绿色 - 次要元素
  static const Color accent = Color(0xFFFFD903);     // BP黄色 - 强调色和警告
  static const Color current = Color(0xFF2E8B57);    // 当前系统绿色 - 保持兼容
  
  // 功能色彩
  static const Color success = Color(0xFF80C242);    // 成功状态
  static const Color warning = Color(0xFFFFD903);    // 警告提示
  static const Color error = Color(0xFFCC0000);      // 错误状态
  static const Color neutral = Color(0xFF666666);    // 中性文本
  static const Color background = Color(0xFFFFFFFF); // 纯白背景
  
  static MaterialColor get primarySwatch => _createSwatch(primary);
  
  static Map<int, Color> _createSwatch(Color color) {
    // 基于BP主色创建Material样本
    final hsl = HSLColor.fromColor(color);
    return {
      50: hsl.withLightness((hsl.lightness + 0.4).clamp(0.0, 1.0)).toColor(),
      100: hsl.withLightness((hsl.lightness + 0.3).clamp(0.0, 1.0)).toColor(),
      200: hsl.withLightness((hsl.lightness + 0.2).clamp(0.0, 1.0)).toColor(),
      300: hsl.withLightness((hsl.lightness + 0.1).clamp(0.0, 1.0)).toColor(),
      400: hsl.withLightness((hsl.lightness + 0.05).clamp(0.0, 1.0)).toColor(),
      500: color, // 基础色
      600: hsl.withLightness((hsl.lightness - 0.1).clamp(0.0, 1.0)).toColor(),
      700: hsl.withLightness((hsl.lightness - 0.2).clamp(0.0, 1.0)).toColor(),
      800: hsl.withLightness((hsl.lightness - 0.3).clamp(0.0, 1.0)).toColor(),
      900: hsl.withLightness((hsl.lightness - 0.4).clamp(0.0, 1.0)).toColor(),
    };
  }
}

// 更新 lib/theme/app_theme.dart
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: BPColors.primarySwatch,
      primaryColor: BPColors.primary,
      scaffoldBackgroundColor: BPColors.background,
      appBarTheme: AppBarTheme(
        backgroundColor: BPColors.background,
        foregroundColor: BPColors.primary,
        elevation: 0,
        titleTextStyle: TextStyle(
          color: BPColors.primary,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: BPColors.primary,
          foregroundColor: BPColors.background,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      cardTheme: CardTheme(
        color: BPColors.background,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: BPColors.neutral.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      colorScheme: ColorScheme.light(
        primary: BPColors.primary,
        secondary: BPColors.secondary,
        error: BPColors.error,
        background: BPColors.background,
        surface: BPColors.background,
      ),
    );
  }
}
```

#### 实施步骤
1. 创建 `lib/constants/bp_colors.dart` 集中管理BP Logo标准色彩
2. 更新 `lib/theme/app_theme.dart` 使用 BP 品牌色彩体系
3. 逐步替换各页面中硬编码的颜色值，保持向下兼容
4. 统一所有组件使用BP色彩常量，确保品牌一致性

### 2.2 组件标准化

#### AppBar 标准化
```dart
// 创建标准 BP AppBar 组件
class BPAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool showBackButton;
  
  const BPAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.showBackButton = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Row(
        children: [
          Image.asset('assets/images/bp_logo.png', height: 32),
          const SizedBox(width: 12),
          Text(
            title, 
            style: TextStyle(
              color: BPColors.primary, 
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      backgroundColor: BPColors.background,
      foregroundColor: BPColors.primary,
      elevation: 0,
      automaticallyImplyLeading: showBackButton,
      actions: actions,
    );
  }
  
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
```

### 2.3 国际化架构设计 🔴 **高优先级**

#### 架构设计原则
```dart
// 创建 lib/l10n/app_localizations.dart
abstract class AppLocalizations {
  // 获取当前语言环境的本地化实例
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }
  
  // 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // 英文
    Locale('zh', 'CN'), // 中文
  ];
  
  // 抽象方法定义
  String get appTitle;
  String get memberManagement;
  String get quickActions;
  String get memberRegistration;
  // ... 其他国际化字符串
}

// 创建 lib/l10n/app_localizations_en.dart
class AppLocalizationsEn extends AppLocalizations {
  @override
  String get appTitle => 'BP Mobile EDC';
  
  @override
  String get memberManagement => 'Member Management';
  
  @override
  String get quickActions => 'Quick Actions';
  
  @override
  String get memberRegistration => 'Member Registration';
  // ... 其他英文字符串
}

// 创建 lib/l10n/app_localizations_zh.dart
class AppLocalizationsZh extends AppLocalizations {
  @override
  String get appTitle => 'BP 移动 EDC';
  
  @override
  String get memberManagement => '会员管理';
  
  @override
  String get quickActions => '快速操作';
  
  @override
  String get memberRegistration => '会员注册';
  // ... 其他中文字符串
}
```

#### 国际化集成方案
```dart
// 在 main.dart 中配置
MaterialApp(
  localizationsDelegates: const [
    AppLocalizationsDelegate(),
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ],
  supportedLocales: AppLocalizations.supportedLocales,
  locale: const Locale('en', 'US'), // 默认英文
  // ...
)

// 在组件中使用
class SomeWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Text(l10n.memberManagement);
  }
}
```

## 3. 导航结构优化

### 3.1 权限管理导航实现 🟡 **中优先级**

#### 当前状况
- `MainNavigationPage` 已实现5标签底部导航
- 缺少基于员工权限的动态导航

#### 设计文档要求
- **加油员导航**：🚗 Fueling、👥 Members、📊 Records、⚙️ Settings (4标签)
- **管理员导航**：📋 Shifts、📈 Analytics、🖥️ Devices、👥 Staff、⚙️ System (5标签)

#### 实施方案
```dart
// 更新 lib/screens/home/<USER>
class MainNavigationPage extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final employee = ref.watch(authServiceProvider).currentEmployee;
        final isManager = employee?.role == 'manager';
        
        return SafeScaffold(
          body: isManager ? _buildManagerNavigation() : _buildEmployeeNavigation(),
          bottomNavigationBar: isManager ? _buildManagerBottomNav() : _buildEmployeeBottomNav(),
        );
      },
    );
  }
}
```

#### 页面映射策略
- **保持现有功能模块**：利用已完善的 Fuel、Order、Payment 模块
- **Members标签**：保持使用 Member 模块，完善会员管理功能
- **Records标签**：复用 FuelTransactionListPage 和 Transaction 历史功能
- **Shifts/Analytics标签**：新开发管理员专用功能，使用mock数据预留接口
- **Mock数据设计**：所有新模块使用结构化mock数据，方便后续真实API集成

### 3.2 导航页面重新组织

#### 加油员导航 (4标签)
```dart
final List<EmployeeNavigationTab> employeeTabs = [
  EmployeeNavigationTab(
    icon: Icons.local_gas_station,
    label: 'Fueling',
    page: FuelTransactionListPage(), // 复用现有交易页面
  ),
  EmployeeNavigationTab(
    icon: Icons.people,
    label: 'Members', 
    page: MemberHomePage(), // 保持使用Member模块
  ),
  EmployeeNavigationTab(
    icon: Icons.history,
    label: 'Records',
    page: PersonalRecordsPage(), // 个人交易记录，基于现有Transaction功能
  ),
  EmployeeNavigationTab(
    icon: Icons.settings,
    label: 'Settings',
    page: EmployeeSettingsPage(), // 员工设置页面
  ),
];
```

#### 管理员导航 (5标签)
```dart
final List<ManagerNavigationTab> managerTabs = [
  ManagerNavigationTab(
    icon: Icons.schedule,
    label: 'Shifts',
    page: ShiftManagementPage(), // 新开发-使用mock数据
  ),
  ManagerNavigationTab(
    icon: Icons.analytics,
    label: 'Analytics', 
    page: DataAnalyticsPage(), // 新开发-使用mock数据
  ),
  ManagerNavigationTab(
    icon: Icons.devices,
    label: 'Devices',
    page: DeviceManagementPage(), // 新开发-使用mock数据
  ),
  ManagerNavigationTab(
    icon: Icons.group,
    label: 'Staff',
    page: StaffManagementPage(), // 新开发-使用mock数据
  ),
  ManagerNavigationTab(
    icon: Icons.admin_panel_settings,
    label: 'System',
    page: SystemSettingsPage(), // 新开发-使用mock数据
  ),
];
```

#### Mock数据架构设计
```dart
// 创建 lib/data/mock_data_service.dart
abstract class MockDataService {
  // 班次相关Mock数据
  static List<ShiftModel> getShiftData() => [
    ShiftModel(
      id: 'shift_001',
      startTime: DateTime.now().subtract(Duration(hours: 8)),
      endTime: null, // 当前班次
      employeeId: 'emp_001',
      employeeName: 'John Doe',
      totalTransactions: 45,
      totalAmount: 2340.50,
      status: ShiftStatus.active,
    ),
    // ... 更多mock数据
  ];

  // 数据分析Mock数据  
  static AnalyticsData getAnalyticsData() => AnalyticsData(
    todaySales: 15420.80,
    todayTransactions: 156,
    monthlyGrowth: 12.5,
    topFuelType: 'Premium 95',
    // ... 预留API接口字段
  );

  // 设备状态Mock数据
  static List<DeviceStatus> getDeviceStatuses() => [
    DeviceStatus(
      deviceId: 'pump_001',
      deviceType: DeviceType.fuelPump,
      status: DeviceHealthStatus.online,
      lastMaintenance: DateTime.now().subtract(Duration(days: 30)),
      // ... 预留真实设备监控字段
    ),
    // ... 更多设备数据
  ];
}

// Mock数据模型设计原则
// 1. 结构化设计，方便后续替换为真实API
// 2. 包含足够的字段模拟真实业务场景
// 3. 使用枚举和常量，便于维护
// 4. 预留扩展字段，支持功能迭代
```

## 4. 国际化完善

### 4.1 剩余中文UI文本修复 🔴 **高优先级**

#### 发现的需要修复的文本
基于代码审查，发现以下页面仍有中文UI文本：

1. **会员管理页面**：
   - `member_home_page.dart`: "会员管理", "快速操作", "注册会员", "会员查询", "会员积分", "会员分析", "最近会员", "会员总数", "本月新增", "今日活跃"

2. **营销页面**：
   - `marketing_home_page.dart`: "营销管理", "营销功能", "促销活动", "优惠券核销", "礼品兑换", "活动历史"

3. **登录页面**：
   - `login_page.dart`: "使用本地环境", "使用开发环境", "环境已切换，请重启应用使设置生效"

4. **状态和错误信息**：
   - Widget 组件中的各种中文状态文本
   - 错误处理页面的中文提示

#### 国际化实施原则
- **不使用静态变量**：采用 `AppLocalizations.of(context)` 动态获取
- **架构预留能力**：支持中英文切换，优先完成英文版本
- **统一管理方式**：所有文本通过国际化框架管理，避免硬编码

#### 修复方案 - 集成国际化架构
```dart
// 更新 lib/l10n/app_localizations.dart 扩展完整字符串
abstract class AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }
  
  // 导航相关
  String get appTitle;
  String get fueling;
  String get members;
  String get records;
  String get settings;
  String get shifts;
  String get analytics;
  String get devices;
  String get staff;
  String get system;
  
  // 会员管理
  String get memberManagement;
  String get quickActions;
  String get memberRegistration;
  String get memberQuery;
  String get memberPoints;
  String get memberAnalytics;
  String get recentMembers;
  String get totalMembers;
  String get monthlyNew;
  String get todayActive;
  
  // 营销模块
  String get marketingManagement;
  String get marketingFeatures;
  String get promotions;
  String get couponVerification;
  String get giftRedemption;
  String get activityHistory;
  
  // 班次管理
  String get shiftManagement;
  String get currentShift;
  String get shiftStart;
  String get shiftEnd;
  String get shiftReport;
  
  // 设备管理
  String get deviceManagement;
  String get deviceStatus;
  String get deviceMaintenance;
  String get deviceConfiguration;
  
  // 状态信息
  String get processing;
  String get completed;
  String get failed;
  String get connected;
  String get disconnected;
  // ... 其他状态
}

// 使用示例
class MemberHomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return BPAppBar(
      title: l10n.memberManagement, // 动态获取，支持语言切换
      // ...
    );
  }
}
```

#### 修复优先级
1. **第一批**：Member 和 Marketing 页面主要标签和按钮
2. **第二批**：错误信息和状态提示
3. **第三批**：Widget 组件中的描述性文本

## 5. 功能模块优化

### 5.1 会员管理模块完善 🟡 **中优先级**

#### 当前状况
- UI设计完整且美观 (`MemberHomePage`, `MemberQueryPage`)
- 使用模拟数据，功能展示完整
- 模型定义完善 (`MemberModel`, `PointModel`)
- 缺少真实API对接

#### 优化方案
1. **保持现有UI设计**：`MemberHomePage`, `MemberQueryPage` 等界面设计良好
2. **创建会员API服务**：
```dart
class MemberApi {
  Future<Member> getMemberByPhone(String phone);
  Future<Member> getMemberById(String id);
  Future<Member> getMemberByPlate(String plateNumber);
  Future<List<Member>> searchMembers(String query);
  Future<Member> createMember(CreateMemberRequest request);
}
```
3. **集成到现有导航**：保持使用Members标签，完善会员管理功能

### 5.2 营销活动模块实现 🟢 **低优先级**

#### 当前状况
- 已有优惠计算API完整实现 (`PromotionController`, `PromotionApi`)
- 营销活动页面为Demo状态
- 优惠组件 `PromotionWidget` 已实现

#### 优化方案
1. **利用现有优惠计算能力**：已有的promotion API可支持营销活动
2. **完善营销活动界面**：基于现有 `PromotionListPage` 优化
3. **集成到加油流程**：在支付环节自动应用可用优惠

### 5.3 打印功能优化 🟢 **低优先级**

#### 当前状况
- Sunmi打印机集成完整 (`SunmiPrinterService`)
- 原生集成成熟 (`SunmiPrintHelper.kt`)
- 缺少打印历史和高级设置

#### 优化方案
1. **保持现有打印能力**：不修改已完善的核心打印功能
2. **添加打印模板管理**：小票格式自定义
3. **完善打印历史**：记录和重打功能

## 6. SOW完整功能模块实现

### 6.1 功能点全覆盖规划

基于SOW文档中的28个功能点(F154-F181)，以下是完整的模块实现规划：

#### 6.1.1 用户权限管理模块 (F154-F155)
```dart
// F154 - 员工身份认证
class EmployeeAuthPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildRFIDScanArea(),      // RFID刷卡区域
        _buildManualInput(),       // 手动输入备用方案
        _buildAuthStatus(),        // 认证状态显示
      ],
    );
  }
}

// F155 - 分级权限控制  
class PermissionController extends StateNotifier<PermissionState> {
  // Mock数据结构，预留真实权限API
  List<Permission> getMockPermissions(String employeeRole) => [
    Permission(id: 'fuel_management', granted: employeeRole == 'manager'),
    Permission(id: 'shift_management', granted: employeeRole == 'manager'),
    // ... 更多权限配置
  ];
}
```

#### 6.1.2 交易处理模块 (F156-F158)
```dart
// F156 - 预设交易管理
class TransactionPresetPage extends StatelessWidget {
  final MockDataService _mockService = MockDataService();
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildPumpSelector(),      // 油枪选择
        _buildTransactionTypes(),  // 预设类型：金额/数量/加满
        _buildPriceCalculator(),   // 价格计算预览
      ],
    );
  }
}

// F157 - 多支付方式处理
class PaymentMethodsPage extends StatelessWidget {
  // 使用现有Payment模块，扩展功能
  final List<PaymentMethod> mockPaymentMethods = [
    PaymentMethod(type: 'cash', enabled: true),
    PaymentMethod(type: 'bank_card', enabled: true),
    PaymentMethod(type: 'digital_wallet', enabled: false), // 预留
    PaymentMethod(type: 'member_account', enabled: true),
  ];
}

// F158 - 多银行支付集成
class BankPaymentIntegrationService {
  // Mock多银行数据，预留BOS集成
  Future<PaymentResult> processBankPayment(BankPaymentRequest request) async {
    // 模拟银行网关响应
    await Future.delayed(Duration(seconds: 2));
    return PaymentResult(
      success: true,
      transactionId: 'bank_${DateTime.now().millisecondsSinceEpoch}',
      bankName: request.bankCode,
    );
  }
}
```

#### 6.1.3 车辆数据采集模块 (F159-F161)
```dart
// F159 - OCR车牌识别
class LicensePlateOCRService {
  Future<OCRResult> recognizePlate(Uint8List imageData) async {
    // Mock OCR识别结果
    return OCRResult(
      plateNumber: 'ABC123',
      confidence: 0.95,
      detectedRegion: Rect.fromLTWH(10, 10, 200, 50),
    );
  }
}

// F160 - 车型识别
class VehicleTypeRecognitionService {
  Future<VehicleType> recognizeVehicleType(Uint8List imageData) async {
    // Mock车型识别，预留AI模型集成
    return VehicleType(
      category: VehicleCategory.sedan,
      confidence: 0.88,
      estimatedTankCapacity: 55.0,
    );
  }
}

// F161 - 客户信息关联
class CustomerLinkingService {
  Future<CustomerInfo?> linkByPlateNumber(String plateNumber) async {
    // Mock客户关联数据
    final mockCustomers = MockDataService.getCustomerData();
    return mockCustomers.firstWhere(
      (customer) => customer.vehicles.any((v) => v.plateNumber == plateNumber),
      orElse: () => null,
    );
  }
}
```

#### 6.1.4 会员注册流程模块 (F168-F170)
```dart
// F168 - 会员注册管理
class MemberRegistrationService {
  Future<RegistrationResult> registerNewMember(MemberRegistrationData data) async {
    // Mock注册流程，预留BOS会员API
    return RegistrationResult(
      success: true,
      memberId: 'M${DateTime.now().millisecondsSinceEpoch}',
      memberCard: generateMockMemberCard(),
    );
  }
}

// F169 - OTP验证处理
class OTPVerificationService {
  Future<bool> sendOTP(String phoneNumber) async {
    // Mock OTP发送，预留BOS短信服务
    print('Mock OTP sent to: $phoneNumber');
    return true;
  }
  
  Future<bool> verifyOTP(String phoneNumber, String otp) async {
    // Mock验证逻辑
    return otp == '123456'; // 开发期间的测试OTP
  }
}

// F170 - 会员唯一标识管理
class MemberIdentityService {
  Future<MemberIdentity> createUniqueIdentity(String phoneNumber, List<String> plateNumbers) async {
    return MemberIdentity(
      primaryId: phoneNumber,
      secondaryIds: plateNumbers,
      membershipLevel: MembershipLevel.basic,
    );
  }
}
```

#### 6.1.5 班次管理模块 (F180-F181)
```dart
// F180 - 班次关闭处理
class ShiftClosureService {
  Future<ShiftReport> closeShift(String shiftId) async {
    final mockData = MockDataService.getShiftClosureData(shiftId);
    return ShiftReport(
      shiftId: shiftId,
      totalTransactions: mockData.transactions.length,
      totalAmount: mockData.calculateTotalAmount(),
      paymentBreakdown: mockData.getPaymentMethodBreakdown(),
      anomalies: mockData.detectAnomalies(),
    );
  }
}

// F181 - 日终处理
class DayEndProcessingService {
  Future<DayEndReport> processDayEnd(DateTime date) async {
    final allShifts = await MockDataService.getShiftsForDate(date);
    return DayEndReport(
      date: date,
      totalShifts: allShifts.length,
      totalRevenue: allShifts.fold(0.0, (sum, shift) => sum + shift.revenue),
      topPerformers: _calculateTopPerformers(allShifts),
      systemHealth: await _generateSystemHealthReport(),
    );
  }
}
```

### 6.2 新功能页面设计规范

#### 6.2.1 页面结构标准化
```dart
// 所有新页面继承标准结构
abstract class BaseSOWPage extends StatelessWidget {
  String get pageTitle;
  
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return SafeScaffold(
      appBar: BPAppBar(
        title: pageTitle,
        actions: buildAppBarActions(),
      ),
      body: buildPageContent(context),
    );
  }
  
  Widget buildPageContent(BuildContext context);
  List<Widget>? buildAppBarActions() => null;
}

// 示例实现
class ShiftManagementPage extends BaseSOWPage {
  @override
  String get pageTitle => 'Shift Management';
  
  @override
  Widget buildPageContent(BuildContext context) {
    return Column(
      children: [
        _buildCurrentShiftCard(),     // 当前班次信息
        _buildShiftActions(),         // 班次操作按钮
        _buildShiftStatistics(),      // 班次统计数据
        _buildShiftHistory(),         // 班次历史记录
      ],
    );
  }
}
```

#### 6.2.2 数据层架构
```dart
// 统一的数据服务接口
abstract class DataService<T> {
  Future<List<T>> getMockData();
  Future<T?> getMockById(String id);
  Future<T> createMock(T item);
  Future<T> updateMock(T item);
  Future<bool> deleteMock(String id);
  
  // 预留真实API方法
  Future<List<T>> getFromAPI();
  Future<T> postToAPI(T item);
  Future<T> putToAPI(T item);
  Future<bool> deleteFromAPI(String id);
}

// 具体实现示例
class ShiftDataService extends DataService<ShiftModel> {
  @override
  Future<List<ShiftModel>> getMockData() async {
    return MockDataService.getShiftData();
  }
  
  @override
  Future<List<ShiftModel>> getFromAPI() async {
    // 预留真实API调用
    throw UnimplementedError('API integration pending');
  }
}
```

### 6.3 Mock数据设计标准

#### 6.3.1 数据结构设计原则
1. **完整性**：包含所有业务字段
2. **扩展性**：预留API响应字段
3. **真实性**：模拟真实业务场景
4. **可维护性**：集中管理，便于更新

#### 6.3.2 Mock数据示例
```dart
class MockDataService {
  // 设备管理Mock数据
  static List<DeviceStatus> getDeviceStatuses() => [
    DeviceStatus(
      deviceId: 'PUMP_001',
      deviceType: DeviceType.fuelPump,
      status: DeviceHealthStatus.online,
      location: 'Island 1 - Position A',
      lastHeartbeat: DateTime.now().subtract(Duration(seconds: 30)),
      fuelTypes: ['Premium 95', 'Regular 92'],
      dailyTransactions: 45,
      dailyVolume: 1250.5,
      maintenanceSchedule: DateTime.now().add(Duration(days: 15)),
      alerts: [], // 当前无警报
      // 预留字段
      firmwareVersion: '2.1.4',
      networkLatency: 25, // ms
      temperatureSensors: [
        TemperatureSensor(id: 'T1', value: 23.5, status: 'normal'),
      ],
    ),
    // ... 更多设备数据
  ];
  
  // 数据分析Mock数据
  static AnalyticsData getTodayAnalytics() => AnalyticsData(
    date: DateTime.now(),
    totalSales: 28450.75,
    totalTransactions: 186,
    averageTransactionValue: 152.97,
    fuelVolumeByType: {
      'Premium 95': 1580.2,
      'Regular 92': 2240.8,
      'Diesel': 890.5,
    },
    hourlyBreakdown: List.generate(24, (hour) => 
      HourlyData(
        hour: hour,
        sales: Random().nextDouble() * 2000,
        transactions: Random().nextInt(20),
      ),
    ),
    topCustomers: MockDataService.getTopCustomers(),
    // 预留分析字段
    predictedDemand: PredictedDemand(
      nextHour: 15.2,
      restOfDay: 185.7,
      accuracy: 0.87,
    ),
  );
}
```

## 7. 实施计划

### 7.1 第一阶段：BP品牌标准化和国际化架构 (1-2周)
🔴 **高优先级**
- [ ] 基于BP Logo实现标准色彩体系 (`BPColors`)
- [ ] 建立国际化架构框架 (`AppLocalizations`)
- [ ] 创建BP标准化组件库 (`BPAppBar`, `BaseSOWPage`)
- [ ] 修复所有中文UI文本，完成英文版本
- [ ] 统一所有页面使用BP品牌标准

### 7.2 第二阶段：权限导航和Mock数据架构 (2-3周)
🟡 **中优先级**
- [ ] 实现基于员工权限的动态导航 (员工4标签 vs 管理员5标签)
- [ ] 建立Mock数据服务架构 (`MockDataService`)
- [ ] 开发权限管理模块 (F154-F155)
- [ ] 创建数据服务抽象层，预留API集成接口
- [ ] 完善Members模块功能 (保持Member命名)

### 7.3 第三阶段：SOW核心功能模块开发 (3-5周)
🟡 **中优先级**
- [ ] 交易处理模块 (F156-F158) - 基于现有Payment模块扩展
- [ ] 车辆数据采集模块 (F159-F161) - Mock OCR和车型识别
- [ ] 会员注册流程模块 (F168-F170) - 扩展现有Member功能
- [ ] 班次管理模块 (F180-F181) - 新开发管理员功能
- [ ] 促销管理模块 (F171-F173) - 基于现有Promotion扩展

### 7.4 第四阶段：高级功能和系统集成 (5-8周)
🟢 **低优先级**
- [ ] QR码扫描和客户数据模块 (F162-F163)
- [ ] 产品销售管理模块 (F164-F165)
- [ ] 支付网关集成模块 (F166-F167)
- [ ] 数据同步模块 (F174-F175)
- [ ] EDC配置管理模块 (F176-F177)
- [ ] 小票管理模块 (F178-F179)

### 7.5 第五阶段：优化和生产准备 (8-10周)
🟢 **低优先级**
- [ ] 性能优化和内存管理
- [ ] 全面测试 (单元测试、集成测试、UI测试)
- [ ] API接口预留验证和文档
- [ ] 中文国际化版本开发
- [ ] 生产环境部署准备

## 8. 风险评估与缓解

### 8.1 技术风险
- **风险**：大规模重构可能影响现有功能稳定性
- **缓解**：采用渐进式重构，保持现有API不变，逐步替换UI组件

### 8.2 进度风险
- **风险**：国际化修复工作量可能被低估
- **缓解**：优先修复高频使用的页面，建立完整的字符串常量体系

### 8.3 兼容风险
- **风险**：权限导航可能影响现有用户习惯
- **缓解**：保持功能位置相对稳定，提供用户引导

## 9. 成功标准

### 9.1 品牌合规性
- [ ] 100% 页面使用 BP Logo 标准色彩体系
- [ ] 所有 AppBar 统一使用 BPAppBar 组件
- [ ] 所有按钮和卡片符合 BP 设计规范
- [ ] 色彩使用符合BP品牌指南 (主色#00A650, 辅助色#80C242, 强调色#FFD903)

### 9.2 国际化架构完整性
- [ ] 建立完整的 AppLocalizations 框架
- [ ] 100% UI 文本通过国际化框架管理
- [ ] 支持中英文动态切换 (优先完成英文版本)
- [ ] 不使用静态变量，采用 context 动态获取

### 9.3 SOW功能完整性
- [ ] 28个功能点(F154-F181)全部规划完成
- [ ] 加油员4标签导航 (Fueling/Members/Records/Settings)
- [ ] 管理员5标签导航 (Shifts/Analytics/Devices/Staff/System)
- [ ] 所有新模块使用Mock数据，预留API接口
- [ ] 基础权限管理和班次管理功能可用

### 9.4 技术架构指标
- [ ] Mock数据服务架构完整 (`MockDataService`, `DataService<T>`)
- [ ] 所有新页面继承 BaseSOWPage 标准结构
- [ ] API接口预留设计清晰，便于后续集成
- [ ] 页面加载时间 < 2秒，导航切换 < 0.5秒
- [ ] 内存占用 < 512MB，支持长时间运行

### 9.5 代码质量标准
- [ ] 所有新功能遵循现有项目架构模式
- [ ] 统一的错误处理和状态管理
- [ ] 完整的代码注释和文档
- [ ] Mock数据结构化，便于维护和扩展

## 10. 文档和培训

### 10.1 技术文档
- [ ] 更新组件库文档
- [ ] 创建导航使用指南
- [ ] 编写部署和配置手册

### 10.2 用户培训
- [ ] 制作功能演示视频
- [ ] 准备用户操作手册
- [ ] 组织内部培训会议

---

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**负责团队**: BP EDC 前端开发团队  
**审批状态**: 待审批  

**关键里程碑**:
- 第一阶段完成：BP品牌标准化和国际化架构 (Week 2)
- 第二阶段完成：权限导航和Mock数据架构 (Week 5)  
- 第三阶段完成：SOW核心功能模块开发 (Week 10)
- 第四阶段完成：高级功能和系统集成 (Week 18)
- 最终发布：优化和生产环境部署 (Week 22)

**重要设计原则**:
- **BP品牌合规**：严格按照BP Logo色彩标准实施
- **保持Member命名**：不更改为Customer，保持现有会员管理概念
- **Mock数据优先**：新功能使用结构化Mock数据，预留真实API接口
- **国际化架构**：支持中英文，不使用静态变量，先完成英文版本
- **SOW完整覆盖**：实现全部28个功能点，确保系统功能完整性 