# 促销API集成总结

## 概述
在现金支付页面集成了新的促销计算接口 `/api/v1/calculator/process`，支持促销优惠计算并将 `appliedPromotions` 数据传递到下单接口的 metadata 中。页面已全部转换为英文。

## 主要更改

### 1. 更新促销响应模型 (PromotionResponse)
- **文件**: `lib/models/promotion_response.dart`
- **更改**: 
  - 添加了 `appliedPromotions` 字段
  - 创建了 `AppliedPromotion` 模型来表示应用的促销活动
  - 支持新的API响应格式

### 2. 更新促销请求模型 (PromotionRequest)
- **文件**: `lib/models/promotion_request.dart`
- **更改**:
  - 添加了 `orderId` 字段
  - 支持动态类型的 `orderAmount` 和 `price`（支持 double 和 int）
  - 调整了从燃油交易创建请求的逻辑

### 3. 修改现金支付页面 (CashPaymentPage)
- **文件**: `lib/screens/payment/cash_payment_page.dart`
- **更改**:
  - 集成新的促销计算API `/api/v1/calculator/process`
  - 在页面初始化时自动调用促销接口
  - 将 `appliedPromotions` 数据添加到订单创建请求的 metadata 中
  - 页面文本全部转换为英文
  - 支持直接传入促销响应数据

### 4. 更新订单详情组件 (OrderDetailWidget)
- **文件**: `lib/widgets/order_detail_widget.dart`
- **更改**:
  - 添加了 `promotionResponse` 和 `isLoadingPromotion` 参数
  - 支持直接显示传入的促销响应数据
  - 创建了新的促销信息显示组件
  - 页面文本全部转换为英文
  - 修复了方法调用错误

## API 接口详情

### 促销计算接口
- **端点**: `/api/v1/calculator/process`
- **方法**: POST
- **请求格式**:
```json
{
  "orderId": "WEB-2025-07-13T1814",
  "userId": "test_user_bp92_30L",
  "orderAmount": 450000,
  "orderTime": "2025-07-13T18:14:22.458Z",
  "items": [
    {
      "itemId": "BP_92",
      "name": "燃油",
      "category": "BP_92",
      "quantity": 30,
      "price": 16000
    }
  ]
}
```

- **响应格式**:
```json
{
  "success": true,
  "message": "折扣计算成功",
  "originalAmount": 450000,
  "discountedAmount": 434000,
  "discountAmount": 16000,
  "appliedPromotions": [
    {
      "promotionId": "f13583c0-5dca-49b6-bae4-ff3062c0de8a",
      "promotionName": "25升免费1升促销",
      "discountType": "FREE_ITEM",
      "discountValue": 16000,
      "discountAmount": 16000,
      "description": "促销活动 '25升免费1升促销' - 免费商品优惠",
      "applicableItems": ["BP_92"],
      "metadata": {
        "applicable_items": ["BP_92"],
        "applicable_items_count": 1,
        "applied_at": "2025-07-14T01:14:22+07:00",
        "discount_type": "FREE_ITEM",
        "original_discount_amount": 16000,
        "promotion_id": "f13583c0-5dca-49b6-bae4-ff3062c0de8a",
        "rounded_discount_amount": 16000
      }
    }
  ],
  "items": [
    {
      "itemId": "BP_92",
      "name": "燃油",
      "originalPrice": 16000,
      "discountedPrice": 15431,
      "quantity": 30
    }
  ]
}
```

### 订单创建接口更新
- **metadata 新增字段**: `appliedPromotions`
- **数据格式**: 将促销响应中的 `appliedPromotions` 数组转换为 JSON 格式存储

## 用户体验改进

### 1. 促销信息显示
- 在支付确认页面显示促销详情
- 显示原价、折扣价和节省金额
- 显示具体的促销活动名称和描述

### 2. 多语言支持
- 页面文本全部转换为英文
- 保持一致的用户界面语言

### 3. 加载状态
- 促销计算时显示加载状态
- 异步加载促销数据，不阻塞页面渲染

## 技术实现细节

### 1. 数据流程
1. 用户进入现金支付页面
2. 自动调用促销计算API
3. 显示促销信息和最终支付金额
4. 用户确认支付时，将促销数据传递到订单创建接口

### 2. 错误处理
- 促销API调用失败时，使用原价继续流程
- 显示适当的错误信息给用户
- 不影响正常的支付流程

### 3. 性能优化
- 异步加载促销数据
- 缓存促销计算结果
- 避免重复API调用

## 测试建议

### 1. 功能测试
- 测试有促销活动时的支付流程
- 测试无促销活动时的支付流程
- 测试促销API调用失败的情况

### 2. 界面测试
- 验证促销信息显示正确
- 验证金额计算准确
- 验证英文文本显示正确

### 3. 集成测试
- 验证促销数据正确传递到订单系统
- 验证订单创建成功
- 验证打印小票包含促销信息

## 部署注意事项

1. 确保促销计算API服务正常运行
2. 验证API接口地址配置正确
3. 检查网络连接和权限设置
4. 监控API调用性能和错误率

## 后续优化建议

1. 添加促销活动缓存机制
2. 支持更多促销类型
3. 优化促销信息显示样式
4. 添加促销活动统计功能 