# Logo打印功能升级文档

## 概述

本次升级将EDC前庭支付系统中的logo打印功能从文本方式升级为使用`printBitmapCustom`函数的灰度图片打印方式，以提供更好的视觉效果和品牌展示。

## 升级内容

### 1. 新增打印方法

在`SunmiPrinterService`中新增了以下方法：

#### `printBitmapCustom`
```dart
Future<void> printBitmapCustom(Uint8List bitmap, int type, {int? width})
```
- **参数说明**：
  - `bitmap`: 图片的bitmap数据
  - `type`: 打印方式
    - `0`: 同`printBitmap()`方法
    - `1`: 阈值200的黑白化图片
    - `2`: 灰度图片
  - `width`: 可选，目标宽度（保持宽高比）

#### `printImageFromAssetsCustom`
```dart
Future<void> printImageFromAssetsCustom(String assetPath, int type, {int? width})
```
- **参数说明**：
  - `assetPath`: assets中的图片路径
  - `type`: 打印方式（同上）
  - `width`: 可选，目标宽度

### 2. 修改AutoPrintService

#### 原有实现
```dart
// === 1. BP-AKR LOGO ===
await printer.setAlignment(PrintAlignment.center);
await printer.setFontSize(headerFontSize);
await printer.printText('BP-AKR\n');
await printer.setFontSize(smallFontSize);
await printer.printText('Fuels Retail\n\n');
```

#### 新实现
```dart
// === 1. BP-AKR LOGO ===
await printer.setAlignment(PrintAlignment.center);
// 使用灰度图片打印BP logo
try {
  await printer.printImageFromAssetsCustom('assets/images/bp_akr_logo_receipt.png', 2, width: 200);
  await printer.printText('\n'); // 添加换行
} catch (e) {
  // 如果图片打印失败，回退到文本方式
  await printer.setFontSize(headerFontSize);
  await printer.printText('BP-AKR\n');
  await printer.setFontSize(smallFontSize);
  await printer.printText('Fuels Retail\n\n');
}
```

### 3. 测试打印功能升级

测试打印方法也同步升级，增加了logo图片打印测试：

```dart
// 测试logo打印
try {
  await _printerService.printImageFromAssetsCustom('assets/images/bp_akr_logo_receipt.png', 2, width: 200);
  await _printerService.printText('\n');
} catch (e) {
  await _printerService.setFontSize(32.0);
  await _printerService.printText('BP-AKR\n');
}
```

## 技术规格

### 图片要求
- **最大宽度**: 384像素（58mm纸张）或576像素（80mm纸张）
- **文件大小**: 小于1MB
- **像素分辨率**: 小于200万像素
- **格式**: PNG/JPEG等常见格式

### 打印参数
- **打印方式**: 使用`type=2`（灰度图片）
- **目标宽度**: 200像素（适配58mm纸张）
- **对齐方式**: 居中对齐

## 错误处理

### 回退机制
如果图片打印失败，系统会自动回退到原有的文本打印方式，确保功能的可靠性。

### 异常类型
- `IMAGE_PROCESSING_ERROR`: 图片处理失败
- `IMAGE_PRINT_ERROR`: 图片打印失败
- `INVALID_ARGUMENT`: 参数无效

## 使用的资源文件

### BP Logo图片
- **文件路径**: `assets/images/bp_akr_logo_receipt.png`
- **用途**: 小票打印专用BP logo
- **特点**: 适合小票打印的尺寸和对比度

### 备用Logo
- **文件路径**: `assets/images/bp_akr_logo.png`
- **用途**: 通用BP logo（备用）

## 兼容性

### Android端
- 已在`MainActivity.kt`中实现`printBitmapCustom`方法
- 使用Sunmi打印机SDK的原生`printBitmapCustom`函数
- 支持Base64编码的图片数据传输

### Flutter端
- 在`SunmiPrinterService`中封装了对应的Dart方法
- 支持从assets加载图片并转换为bitmap
- 提供了完整的错误处理机制

## 测试

### 单元测试
创建了`test/logo_print_test.dart`文件，包含以下测试：
- 方法存在性测试
- 参数有效性测试
- 资源路径验证测试

### 集成测试
- 实际打印测试（通过测试打印功能）
- 错误回退测试
- 不同纸张尺寸适配测试

## 部署注意事项

1. **确保资源文件存在**: 部署前检查`assets/images/bp_akr_logo_receipt.png`文件是否存在
2. **打印机兼容性**: 确认目标设备支持`printBitmapCustom`函数
3. **性能考虑**: 图片打印比文本打印消耗更多资源，建议在低电量时使用文本模式
4. **纸张适配**: 根据实际使用的纸张宽度调整图片尺寸

## 未来改进

1. **缓存机制**: 实现图片缓存以提高打印速度
2. **动态调整**: 根据纸张类型自动调整图片尺寸
3. **多logo支持**: 支持不同场景下的不同logo
4. **质量优化**: 进一步优化图片质量和打印效果

## 总结

本次升级成功将logo打印从文本方式升级为灰度图片打印，在保持原有功能稳定性的同时，显著提升了小票的视觉效果和品牌形象。通过完善的错误处理和回退机制，确保了系统的可靠性和用户体验。 