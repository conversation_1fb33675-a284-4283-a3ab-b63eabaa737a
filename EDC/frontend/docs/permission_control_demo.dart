// 班次管理权限控制演示代码
// 这个文件展示了权限控制的核心实现逻辑

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// 模拟的权限服务
class PermissionService {
  // 模拟不同用户的权限级别
  static const Map<String, String> userPermissions = {
    'admin': 'manager',
    'supervisor1': 'supervisor', 
    'assistant1': 'assistant_manager',
    'operator1': 'operator',
    'cashier1': 'cashier',
  };

  // 检查是否有管理权限
  static bool hasManagementAccess(String accessLevel) {
    return accessLevel == 'manager' || 
           accessLevel == 'assistant_manager' || 
           accessLevel == 'supervisor';
  }

  // 获取用户权限级别
  static String? getUserAccessLevel(String username) {
    return userPermissions[username];
  }
}

// 权限控制演示Widget
class ShiftPermissionDemo extends StatefulWidget {
  final String currentUser;
  
  const ShiftPermissionDemo({
    super.key,
    required this.currentUser,
  });

  @override
  State<ShiftPermissionDemo> createState() => _ShiftPermissionDemoState();
}

class _ShiftPermissionDemoState extends State<ShiftPermissionDemo> {
  bool _hasManagementAccess = false;
  bool _isCheckingPermissions = true;
  bool _isShiftActive = false;

  @override
  void initState() {
    super.initState();
    _checkUserPermissions();
  }

  // 检查用户权限
  Future<void> _checkUserPermissions() async {
    // 模拟异步权限检查
    await Future.delayed(const Duration(milliseconds: 500));
    
    final String? accessLevel = PermissionService.getUserAccessLevel(widget.currentUser);
    final bool hasAccess = accessLevel != null && 
                          PermissionService.hasManagementAccess(accessLevel);
    
    if (mounted) {
      setState(() {
        _hasManagementAccess = hasAccess;
        _isCheckingPermissions = false;
      });
    }
    
    print('🔐 用户 ${widget.currentUser} 权限检查结果: $_hasManagementAccess (级别: $accessLevel)');
  }

  // 开班操作
  void _startShift() {
    setState(() {
      _isShiftActive = true;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('班次已开始')),
    );
  }

  // 结班操作
  void _endShift() {
    setState(() {
      _isShiftActive = false;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('班次已结束')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('班次管理 - ${widget.currentUser}'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前用户: ${widget.currentUser}',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '权限级别: ${PermissionService.getUserAccessLevel(widget.currentUser) ?? "未知"}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _hasManagementAccess ? Icons.check_circle : Icons.cancel,
                          color: _hasManagementAccess ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _hasManagementAccess ? '有管理权限' : '无管理权限',
                          style: TextStyle(
                            color: _hasManagementAccess ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 班次状态
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(
                      _isShiftActive ? Icons.play_circle : Icons.stop_circle,
                      color: _isShiftActive ? Colors.green : Colors.grey,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      _isShiftActive ? '班次进行中' : '班次未开始',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 操作按钮区域 - 权限控制的核心部分
            if (_isCheckingPermissions)
              // 权限检查中
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在检查权限...'),
                  ],
                ),
              )
            else if (_hasManagementAccess)
              // 有管理权限 - 显示操作按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isShiftActive ? _endShift : _startShift,
                  icon: Icon(_isShiftActive ? Icons.stop : Icons.play_arrow),
                  label: Text(_isShiftActive ? '结束班次' : '开始班次'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isShiftActive ? Colors.red : Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              )
            else
              // 无管理权限 - 显示权限提示
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.lock_outline, color: Colors.grey),
                    SizedBox(width: 8),
                    Text(
                      '需要管理权限才能操作班次',
                      style: TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 20),

            // 权限说明
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '权限说明:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('• manager (经理) - 有管理权限'),
                    Text('• assistant_manager (副经理) - 有管理权限'),
                    Text('• supervisor (主管) - 有管理权限'),
                    Text('• operator (操作员) - 无管理权限'),
                    Text('• cashier (收银员) - 无管理权限'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 演示应用
class PermissionDemoApp extends StatelessWidget {
  const PermissionDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '班次权限控制演示',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const UserSelectionPage(),
    );
  }
}

// 用户选择页面
class UserSelectionPage extends StatelessWidget {
  const UserSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选择用户进行演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: PermissionService.userPermissions.entries.map((entry) {
          final String username = entry.key;
          final String accessLevel = entry.value;
          final bool hasAccess = PermissionService.hasManagementAccess(accessLevel);
          
          return Card(
            child: ListTile(
              leading: Icon(
                hasAccess ? Icons.admin_panel_settings : Icons.person,
                color: hasAccess ? Colors.green : Colors.grey,
              ),
              title: Text(username),
              subtitle: Text('权限级别: $accessLevel'),
              trailing: Icon(
                hasAccess ? Icons.check_circle : Icons.cancel,
                color: hasAccess ? Colors.green : Colors.red,
              ),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ShiftPermissionDemo(currentUser: username),
                  ),
                );
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}

// 主函数 - 用于演示
void main() {
  runApp(const PermissionDemoApp());
}
