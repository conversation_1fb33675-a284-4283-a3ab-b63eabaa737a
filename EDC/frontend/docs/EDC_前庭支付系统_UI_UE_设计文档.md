# EDC前庭支付系统 UI/UE 设计文档

## 1. 系统概述

### 1.1 设计理念
基于石油行业SOP（标准操作流程）和BP品牌标准，EDC前庭支付系统UI设计遵循"安全第一、效率优先、服务至上"的原则。采用BP品牌标准色彩体系，体现环保、专业、可信赖的品牌形象，确保在快节奏的加油站环境中提供流畅、直观的操作体验。

### 1.2 用户群体
- **主要用户**: 加油站前庭服务员（持证上岗，接受过专业培训）
- **次要用户**: 加油站主管（具备更高权限，负责监督和管理）
- **辅助用户**: 客户（在引导下完成部分自助操作）

### 1.3 应用环境
- **硬件平台**: Android手持EDC终端设备
- **使用环境**: 加油站前庭区域，可能面临强光、低温、戴手套操作等挑战
- **网络条件**: 支持4G/WiFi，需考虑网络不稳定情况

## 2. UI设计风格指南

### 2.1 视觉风格

#### 色彩方案
基于BP品牌标准色彩体系设计，体现石油行业的专业性和环保理念：

- **主色调**: BP绿色（#00A650）- BP标志性绿色，体现专业性和环保理念
- **次要色**: BP黄色（#FFD903）- 用于重要操作按钮和重点提示信息
- **辅助色**: 
  - 辅助绿（#80C242）- 成功状态、设备正常、确认操作
  - 警告红（#CC0000）- 错误提示、紧急情况、危险操作
  - 中性灰（#666666）- 次要信息、禁用状态、说明文字
- **背景色**: 纯白色（#FFFFFF）- 清洁明亮，符合BP品牌形象，减少视觉疲劳

#### 字体规范
- **主标题**: 24px，加粗，BP绿色（#00A650）- 页面标题和重要信息
- **次标题**: 18px，半粗，深灰色（#333333）- 功能区域标题
- **正文**: 16px，常规，深灰色（#333333）- 一般信息展示
- **按钮文字**: 16px，加粗，白色 - 主要操作按钮；BP绿色 - 次要按钮
- **提示文字**: 14px，常规，中性灰（#666666）- 帮助和说明信息
- **强调文字**: 16px，加粗，BP黄色（#FFD903）- 重要提示和价格信息

#### 图标设计
- **风格**: 线性图标，简洁明了，2px线宽
- **尺寸**: 24px×24px（常规）、32px×32px（重要操作）
- **颜色**: 遵循整体色彩方案，保持一致性

### 2.2 布局原则

#### 分区布局
- **顶部状态栏**: 显示系统状态、当前用户、时间等关键信息
- **主要操作区**: 页面中央，展示核心功能和内容
- **底部导航栏**: 快速切换主要功能模块
- **侧边辅助区**: 次要信息和辅助功能（适用于横屏模式）

#### 层次结构
- **第一层级**: 主要功能入口，大按钮设计，易于识别
- **第二层级**: 详细操作页面，清晰的步骤指引
- **第三层级**: 确认和结果页面，重要信息突出显示

## 3. 页面层级关系

### 3.1 整体层级架构

EDC前庭支付系统采用底部导航栏的三层级页面架构设计，提供统一的用户体验和高效的功能切换：

```
第一层级（认证层）- 仅限未登录状态
├── 员工登录页
└── 权限验证页

第二层级（主导航层）- 底部导航栏 + 功能主页
├── 加油员导航栏
│   ├── 🚗 加油（加油服务功能入口）
│   ├── 👥 客户（客户服务和会员管理）
│   ├── 📊 查询（交易记录和个人数据）
│   └── ⚙️ 设置（个人设置和偏好）
└── 管理员导航栏
    ├── 📋 班次（班次管理和交接班）
    ├── 📈 数据（交易统计和报表分析）
    ├── 🖥️ 设备（设备监控和状态管理）
    ├── 👥 员工（员工管理和权限设置）
    └── ⚙️ 系统（系统配置和维护）

第三层级（业务操作层）
├── 加油流程页面组
│   ├── 交易配置页
│   ├── 客户识别页
│   ├── 促销管理页
│   └── 支付处理页
├── 客户服务页面组
│   ├── 客户信息查询页
│   ├── 会员注册页
│   ├── 客户档案管理页
│   └── 服务记录页
├── 数据查询页面组
│   ├── 交易记录查询页
│   ├── 个人操作历史页
│   ├── 客户消费分析页
│   └── 数据导出页
├── 班次管理页面组
│   ├── 班次概况页
│   ├── 交接班操作页
│   ├── 班次报告页
│   └── 异常处理页
├── 数据分析页面组
│   ├── 实时统计页
│   ├── 历史报表页
│   ├── 趋势分析页
│   └── 异常监控页
├── 设备管理页面组
│   ├── 设备状态页
│   ├── 故障诊断页
│   ├── 维护记录页
│   └── 设备配置页
└── 系统管理页面组
    ├── 用户管理页
    ├── 权限配置页
    ├── 系统参数页
    └── 日志查看页

第四层级（确认/结果层）
├── 交易确认页
├── 支付结果页
├── 小票打印页
├── 操作确认页
├── 错误处理页
└── 成功反馈页
```

### 3.2 页面导航逻辑

#### 底部导航栏设计

**统一导航结构**：所有用户使用相同的界面结构，但根据权限显示不同的导航标签

- **加油员导航栏**：顶部显示页面内容，底部显示四个导航标签（加油、客户、查询、设置）
- **管理员导航栏**：顶部显示页面内容，底部显示五个导航标签（班次、数据、设备、员工、系统）

#### 主流程导航路径

**加油员标准流程**：
```
员工登录 → 底部导航(加油) → 交易配置 → 客户识别 → 促销管理 → 支付处理 → 交易确认 → 完成
```

**管理员标准流程**：
```
管理员登录 → 底部导航(班次/数据) → 具体功能页 → 详细操作 → 确认保存 → 导航切换
```

#### 导航标签功能

**加油员导航标签**：
- **🚗 加油**：
  - 默认页面：交易配置页（快速开始新交易）
  - 快速操作：重复上次交易、常用油品选择
  - 流程引导：交易配置 → 客户识别 → 支付处理

- **👥 客户**：
  - 默认页面：客户信息查询页
  - 主要功能：会员注册、客户档案、服务记录
  - 快速操作：扫码识别、车牌查询

- **📊 查询**：
  - 默认页面：当班交易记录
  - 主要功能：交易历史、个人统计、异常记录
  - 筛选功能：时间、金额、客户、油品类型

- **⚙️ 设置**：
  - 默认页面：个人设置
  - 主要功能：个人信息、操作偏好、快捷设置
  - 帮助功能：操作手册、常见问题

**管理员导航标签**：
- **📋 班次**：
  - 默认页面：当前班次概况
  - 主要功能：班次管理、交接班、人员安排
  - 实时数据：在班人员、交易统计、异常情况

- **📈 数据**：
  - 默认页面：今日经营数据
  - 主要功能：销售统计、趋势分析、报表生成
  - 分析维度：时间、油品、客户、员工

- **🖥️ 设备**：
  - 默认页面：设备状态总览
  - 主要功能：设备监控、故障诊断、维护记录
  - 监控对象：油枪、EDC、网络、摄像头

- **👥 员工**：
  - 默认页面：员工在岗状态
  - 主要功能：员工管理、权限设置、考勤统计
  - 管理维度：权限级别、工作表现、培训记录

- **⚙️ 系统**：
  - 默认页面：系统状态监控
  - 主要功能：系统配置、参数设置、日志查看
  - 配置范围：业务参数、设备参数、安全设置

#### 导航切换机制

**标签切换**：
- 点击导航标签直接切换到对应功能模块
- 保持当前操作状态，可随时切换回来
- 未完成操作提示保存或暂存

**状态保持**：
- 各标签页保持独立的操作状态
- 切换时自动保存当前输入数据
- 返回时恢复到离开时的状态

**权限验证**：
- 敏感操作跳转前验证权限
- 超权限操作提示申请管理员授权
- 临时权限提升机制

#### 特殊导航场景

**加油流程中断处理**：
- 正在进行交易时切换标签给予警告提示
- 提供保存草稿和继续操作选项
- 异常中断时自动保存状态用于恢复

**跨权限操作**：
- 加油员遇到需要管理员权限的操作
- 在当前页面发起授权请求
- 管理员可远程授权或现场处理

**紧急操作**：
- 长按任意导航标签激活紧急模式
- 快速切换到相应的应急处理页面
- 记录紧急操作过程和处理结果

#### 导航优化特性

**快捷操作**：支持长按标签显示快捷菜单，双击返回默认页面，滑动手势快速切换。

**智能提示**：新消息和异常情况在对应标签显示提示，重要操作提供引导信息。

**个性化**：支持常用功能快捷设置，记忆用户操作习惯。

### 3.3 页面层级权限控制

#### 权限层级定义

**加油员权限**：
- 显示加油员导航栏（加油、客户、查询、设置）
- 标准交易处理权限
- 客户服务和会员管理权限
- 个人交易记录查询权限

**管理员权限**：
- 显示管理员导航栏（班次、数据、设备、员工、系统）
- 班次管理和数据查看权限
- 设备监控和员工管理权限
- 系统配置和参数设置权限

#### 权限控制实现

- **登录时权限识别**: 根据员工权限显示对应的导航栏
- **页面访问控制**: 根据权限控制页面和功能的访问
- **数据访问控制**: 限制不同权限用户的数据查看范围

### 3.4 页面状态管理

#### 页面状态类型
- **加载状态**: 数据获取和处理中的页面状态
- **就绪状态**: 页面完全加载完成，可以正常操作
- **处理状态**: 用户操作提交后的处理过程状态
- **错误状态**: 发生异常或错误时的页面状态
- **离线状态**: 网络中断时的离线模式状态

#### 状态切换逻辑
页面按加载状态、就绪状态、处理状态、错误状态、离线状态进行切换，确保用户在任何情况下都有明确的状态反馈。

#### 状态保持机制
- **会话保持**: 用户操作过程中保持登录会话状态
- **数据缓存**: 关键数据在本地缓存，离线时可继续操作
- **状态恢复**: 应用重启或异常恢复后能够恢复到上次操作状态
- **进度保存**: 多步骤操作中保存操作进度，避免重复操作

### 3.5 响应式层级适配

界面根据不同屏幕尺寸（7英寸、8英寸、10英寸）自动调整布局，支持竖屏和横屏模式切换，确保在各种设备上都有良好的显示效果。

## 4. 核心功能模块设计

### 4.1 底部导航栏界面设计

#### 4.1.1 统一界面结构
采用底部导航栏设计，为所有用户提供一致的界面体验，根据权限显示不同的导航标签。

**通用界面布局**：界面分为三个区域：顶部状态栏显示系统名称和用户信息，中间主内容区显示当前功能页面，底部导航栏显示功能标签。

#### 4.1.2 加油员导航界面
为一线加油员工优化的简洁导航，专注核心业务操作。

**加油员界面特点**：
- 顶部状态栏显示系统名称和员工信息
- 主内容区显示当前选中功能的操作界面，如加油标签页显示油枪选择、快捷金额按钮、上次交易记录等
- 底部导航栏显示四个功能标签：加油、客户、查询、设置

**导航标签说明**：
- **🚗 加油**：交易配置、油枪管理、快速开始交易
- **👥 客户**：客户服务、会员管理
- **📊 查询**：交易记录、个人统计、历史数据
- **⚙️ 设置**：个人设置、系统偏好、帮助支持

#### 4.1.3 管理员导航界面
为管理人员设计的综合管理界面，突出数据监控和决策支持。

**管理员界面特点**：
- 顶部状态栏显示系统名称和管理员信息
- 主内容区显示管理功能界面，如班次标签页显示当前班次概况、关键业务指标、在班员工状态和快捷操作按钮
- 底部导航栏显示五个管理标签：班次、数据、设备、员工、系统

**导航标签说明**：
- **📋 班次**：班次管理、交接班、人员调度
- **📈 数据**：销售统计、趋势分析、报表生成
- **🖥️ 设备**：设备监控、故障诊断、维护管理
- **👥 员工**：员工管理、权限设置、绩效统计
- **⚙️ 系统**：系统配置、参数设置

#### 4.1.4 界面适配特性

**响应式设计**：
- **导航标签宽度**：根据屏幕尺寸和标签数量自适应
- **图标与文字**：大屏显示图标+文字，小屏仅显示图标
- **内容区域**：主内容区域自动调整，确保最佳显示效果

**状态指示**：
- **活跃标签**：当前选中标签使用BP绿色高亮显示
- **消息提示**：有新消息或异常的标签显示红色数字徽章
- **加载状态**：数据加载时显示加载动画，避免界面卡顿

**交互优化**：
- **触摸反馈**：标签点击时提供震动和视觉反馈
- **滑动切换**：支持左右滑动手势在相邻标签间切换
- **长按菜单**：长按标签显示快捷操作菜单

**可访问性**：
- **语音提示**：切换标签时语音播报当前功能
- **色彩对比**：确保在强光环境下的可视性
- **字体缩放**：支持系统字体大小设置

### 4.2 员工身份认证模块（F154-F155）

#### 设计目标
参考石油行业标准，确保只有授权员工才能操作系统，严格执行分级权限管理。

#### 界面组件
- **RFID感应区域**: 
  - 视觉设计：中央圆形感应区域，动态波纹效果提示感应状态
  - 提示信息："请将员工卡靠近感应区"
  - 状态反馈：感应中（BP黄色脉冲 #FFD903）、识别成功（BP绿色确认 #00A650）、识别失败（红色警告 #CC0000）

- **手动输入区域**:
  - 设计原则：作为备用认证方式，界面简洁
  - 输入组件：员工号输入框（数字键盘）、确认按钮
  - 安全考虑：输入过程中显示星号，避免信息泄露

- **权限显示组件**:
  - 层级指示：主管（BP黄色徽章 #FFD903）、服务员（辅助绿徽章 #80C242）
  - 权限说明：清晰列出当前用户可执行的操作范围，使用BP绿色文字标识

#### 交互流程
1. **标准认证流程**：员工刷卡 → 系统识别 → 权限验证 → 班次确认 → 进入主界面
2. **异常处理流程**：识别失败 → 提示重试（最多3次）→ 手动输入选项 → 管理员授权
3. **权限切换流程**：不同权限级别显示对应功能菜单，敏感操作需二次确认

### 4.3 交易预设配置模块（F156）

#### 设计目标
遵循加油站SOP，支持三种标准交易类型：预设金额、预设数量、加满服务。

#### 界面组件
- **油枪选择器**:
  - 设计形式：横向排列的卡片式选择器
  - 状态显示：可用（BP绿色边框 #00A650）、占用（中性灰禁用 #666666）、故障（红色警告 #CC0000）
  - 信息展示：油枪编号（BP绿色）、油品类型、当前价格（BP黄色突出显示）

- **交易类型选择器**:
  - 布局方式：垂直排列的单选按钮组
  - 选项设计：
    - 预设金额：输入框（支持快捷金额按钮：100、200、300、500元）
    - 预设数量：输入框（支持快捷数量按钮：20、30、40、50升）
    - 加满服务：自动模式，显示预估金额范围

- **价格信息显示器**:
  - 实时价格：大字体显示当前油价，使用BP黄色（#FFD903）突出显示
  - 计算预览：根据输入自动计算预估金额或数量，金额使用BP绿色显示
  - 优惠提示：显示当前有效的价格优惠信息，优惠金额使用BP黄色强调

#### 交互流程
1. **配置流程**：选择油枪 → 选择交易类型 → 输入金额/数量 → 确认预设 → 进入客户识别
2. **验证流程**：检查油枪可用性 → 验证输入合理性 → 计算预估值 → 用户确认
3. **修改流程**：支持任意步骤返回修改，保持用户输入状态

### 4.4 客户识别与关联模块（F159-F163）

#### 设计目标
整合多种识别方式，建立完整的客户数据档案，支持个性化服务。

#### 界面组件
- **车牌识别显示器**:
  - 摄像头预览：实时显示摄像头捕获画面，边框使用BP绿色
  - 识别结果框：BP黄色背景突出显示识别出的车牌号码
  - 置信度指示：颜色编码显示识别准确度（BP绿色>90%，BP黄色70-90%，红色<70%）

- **客户信息卡片**:
  - 会员信息：头像、姓名、会员等级、有效期
  - 车辆信息：车牌号、车型、品牌、颜色
  - 历史记录：最近加油记录、偏好油品、平均消费

- **QR码扫描器**:
  - 扫描界面：全屏扫描框，引导线提示对准位置
  - 结果处理：自动解析客户信息并填充表单

#### 交互流程
1. **自动识别流程**：启动摄像头 → 实时车牌识别 → 匹配客户数据 → 显示关联信息
2. **手动补充流程**：识别失败或信息不完整 → 手动输入补充 → 系统验证 → 保存记录
3. **新客户注册流程**：未找到匹配记录 → 提示注册 → 收集基本信息 → 创建客户档案

### 4.5 促销管理模块（F168-F172）

#### 设计目标
智能匹配和应用促销活动，提升客户满意度和销售额。

#### 界面组件
- **促销活动展示器**:
  - 卡片式布局：每个促销活动一个卡片
  - 关键信息：活动名称、优惠内容、适用条件、有效期
  - 视觉标识：不同类型促销使用不同颜色和图标

- **优惠计算器**:
  - 实时计算：根据当前交易自动计算可享受的优惠
  - 对比显示：原价与优惠后价格的对比
  - 节省金额：突出显示为客户节省的金额

- **促销选择器**:
  - 多选支持：支持同时应用多个兼容的促销活动
  - 冲突提示：自动检测促销冲突并提示最优组合
  - 确认应用：选择后需确认才能应用到交易

#### 交互流程
1. **自动匹配流程**：获取客户信息 → 匹配适用促销 → 排序推荐 → 展示给用户
2. **手动选择流程**：浏览所有促销 → 选择适用活动 → 系统验证 → 计算优惠
3. **应用确认流程**：选择促销 → 预览优惠效果 → 用户确认 → 更新交易金额

### 4.6 支付处理模块（F157-F158）

#### 设计目标
支持多种支付方式，确保支付安全，提供良好的支付体验。

#### 界面组件
- **支付方式选择器**:
  - 图标式设计：银行卡、现金、数字钱包等清晰图标
  - 状态指示：可用、不可用、推荐等状态
  - 快捷选择：支持最近使用的支付方式快速选择

- **金额确认显示器**:
  - 分项明细：油品费用（BP绿色）、优惠金额（BP黄色）、实付金额（深灰色）
  - 大字体显示：实付金额使用大号字体和BP绿色突出显示
  - 计算过程：展示完整的计算过程，使用BP品牌色彩体系增加可读性

- **支付状态指示器**:
  - 进度指示：支付处理进度的可视化展示
  - 状态反馈：处理中、成功、失败等状态的明确指示
  - 错误处理：支付失败时的错误信息和解决建议

#### 交互流程
1. **支付准备流程**：确认交易信息 → 选择支付方式 → 输入支付密码 → 发起支付请求
2. **支付处理流程**：连接支付网关 → 处理支付请求 → 接收支付结果 → 更新交易状态
3. **结果处理流程**：支付成功 → 生成交易记录 → 打印小票 → 完成交易

### 4.7 班次管理模块（F176-F178）

#### 设计目标
规范班次操作，确保交接班的准确性和完整性。

#### 界面组件
- **班次信息面板**:
  - 当前班次：班次名称、开始时间、当前员工
  - 交易统计：交易笔数、总金额、平均单价
  - 设备状态：各设备的运行状态汇总

- **交接班操作器**:
  - 开班操作：验证员工身份、初始化设备状态、记录开班时间
  - 交班操作：生成班次报告、确认无未完成交易、移交权限
  - 异常处理：处理交接班过程中的异常情况

- **班次报告生成器**:
  - 数据汇总：自动汇总班次期间的所有交易数据
  - 报告格式：标准化的班次报告格式
  - 导出功能：支持打印和电子文档导出

#### 交互流程
1. **正常交接流程**：当班员工发起交班 → 系统生成报告 → 新员工接班确认 → 完成交接
2. **异常交接流程**：发现异常 → 记录异常信息 → 主管介入 → 异常处理 → 继续交接
3. **强制交接流程**：紧急情况 → 主管授权 → 强制结束当前班次 → 记录原因 → 启动新班次

## 5. 关键交互流程设计

### 5.1 标准加油服务流程

#### 流程概述
基于石油行业SOP标准，完整的加油服务流程包括：客户接待 → 需求确认 → 服务执行 → 支付结算 → 服务完成。

#### 详细步骤
1. **客户接待阶段**
   - 员工使用EDC系统登录确认身份
   - 客户车辆进入服务区域
   - 系统自动启动车牌识别
   - 员工主动问候并确认服务需求

2. **需求确认阶段**
   - 确认加油类型（92#、95#、98#、柴油）
   - 确认加油方式（金额、升数、加满）
   - 检查车辆基本信息
   - 查询客户会员信息

3. **服务执行阶段**
   - 在EDC系统中预设交易参数
   - 启动相应油枪开始加油
   - 实时监控加油进度
   - 确保加油安全和质量

4. **支付结算阶段**
   - 显示交易明细和应付金额
   - 应用适用的促销优惠
   - 客户选择支付方式
   - 完成支付处理

5. **服务完成阶段**
   - 打印交易小票
   - 提供增值服务信息
   - 收集客户反馈
   - 清理服务区域

### 5.2 异常情况处理流程

#### 设备故障处理
- **油枪故障**：自动检测 → 警告提示 → 切换备用油枪 → 记录故障信息
- **支付设备故障**：检测异常 → 提示备用支付方式 → 手动记录交易 → 后续补处理
- **网络中断**：离线模式 → 本地数据存储 → 网络恢复后同步 → 确认数据完整性

#### 客户服务异常
- **识别失败**：提供手动输入选项 → 人工辅助识别 → 建立临时档案 → 后续完善信息
- **支付失败**：分析失败原因 → 提供解决方案 → 尝试其他支付方式 → 记录处理过程
- **客户投诉**：及时响应 → 记录投诉内容 → 寻求解决方案 → 跟踪处理结果

### 5.3 安全操作流程

#### 操作安全
- **身份验证**：每次操作前验证员工身份
- **权限控制**：根据员工级别限制操作范围
- **操作记录**：记录所有关键操作和操作人员
- **异常监控**：实时监控异常操作并及时报警

#### 数据安全
- **数据加密**：敏感数据传输加密处理
- **访问控制**：严格控制数据访问权限
- **备份恢复**：定期备份关键数据
- **合规要求**：符合相关法规和行业标准

## 6. 响应式设计要求

### 6.1 屏幕适配

#### 主要屏幕尺寸
- **7英寸平板**：1024×600像素，主要设备规格
- **8英寸平板**：1280×800像素，高端设备配置
- **10英寸平板**：1920×1200像素，管理端设备

#### 布局适配原则
- **弹性布局**：使用相对单位，适应不同屏幕尺寸
- **内容优先**：重要内容优先显示，次要内容可收缩
- **触摸友好**：保证触摸目标足够大，间距合理

### 6.2 环境适配

#### 光照条件
- **强光环境**：提高对比度，使用深色背景浅色文字
- **弱光环境**：降低亮度，使用护眼色彩方案
- **自动调节**：根据环境光线自动调整显示参数

#### 操作条件
- **戴手套操作**：增大触摸目标，提高触摸敏感度
- **单手操作**：重要功能放在易触达区域
- **快速操作**：减少操作步骤，提供快捷方式

## 7. 性能优化要求

### 7.1 响应速度
- **页面加载**：主要页面加载时间不超过2秒
- **交互响应**：用户操作响应时间不超过0.5秒
- **数据同步**：数据提交和同步时间不超过3秒

### 7.2 资源优化
- **内存使用**：应用运行内存占用不超过512MB
- **存储空间**：应用安装包大小不超过100MB
- **网络流量**：合理控制数据传输量，支持离线操作

### 7.3 稳定性要求
- **连续运行**：支持24小时连续稳定运行
- **错误恢复**：系统异常后能够快速恢复正常
- **数据完整性**：确保交易数据的完整性和一致性

## 8. 可访问性设计

### 8.1 视觉辅助
- **字体大小**：支持字体大小调节
- **色彩对比**：确保足够的色彩对比度
- **图标设计**：提供文字标签辅助图标理解

### 8.2 操作辅助
- **语音提示**：关键操作提供语音确认
- **操作指导**：首次使用提供操作引导
- **帮助系统**：提供在线帮助和操作手册

### 8.3 多语言支持
- **界面语言**：支持中文、英文界面切换
- **数据本地化**：支持不同地区的数据格式
- **文化适配**：考虑不同文化的使用习惯

