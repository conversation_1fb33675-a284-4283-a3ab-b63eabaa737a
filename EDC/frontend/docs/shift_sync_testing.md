# Shift状态跨设备同步测试文档

## 概述

本文档描述了如何测试shift状态在多台EDC设备间的实时同步功能。

## 实现方案

### 核心改动

1. **独立的班次状态轮询**: 在 `BosTransactionPollingService` 中添加了独立的班次状态轮询定时器
2. **自动缓存更新**: 通过 `ShiftService.refreshCurrentShift()` 定期从后台获取最新状态
3. **UI实时更新**: 通过 `ShiftService.shiftStatusStream` 通知所有监听的UI组件

### 轮询机制

- **交易轮询**: 每5秒轮询一次待处理交易
- **班次状态轮询**: 每30秒轮询一次班次状态
- **状态变化检测**: 比较班次ID和状态，发现变化时立即更新

## 测试场景

### 场景1: 基本跨设备同步测试

**前置条件**:
- 两台EDC设备连接到同一个后台服务
- 两台设备都已登录并初始化完成

**测试步骤**:
1. 在设备A上启动班次
2. 等待30秒（班次状态轮询间隔）
3. 检查设备B上的班次状态是否更新为"ON SHIFT"
4. 在设备A上结束班次
5. 等待30秒
6. 检查设备B上的班次状态是否更新为"OFF SHIFT"

**预期结果**:
- 设备B应该在30秒内自动更新班次状态
- 所有相关UI组件（ShiftStatusIndicator、ShiftHomePage等）都应该正确更新

### 场景2: UI组件响应测试

**测试组件**:
- `ShiftStatusIndicator`: 顶部状态指示器
- `ShiftHomePage`: 班次管理页面
- `TransactionPresetHomePageV3`: 主页面的班次状态显示

**测试步骤**:
1. 在设备A上进行班次操作（开班/结班）
2. 在设备B上观察各个UI组件的状态变化
3. 验证状态变化的一致性和及时性

**预期结果**:
- 所有UI组件应该同步更新
- 状态变化应该在30秒内完成
- 不应该出现状态不一致的情况

### 场景3: 网络异常处理测试

**测试步骤**:
1. 设备A正常开班
2. 临时断开设备B的网络连接
3. 在设备A上结班
4. 恢复设备B的网络连接
5. 观察设备B的状态恢复

**预期结果**:
- 网络恢复后，设备B应该在下一个轮询周期内更新到正确状态
- 不应该出现状态永久不同步的情况

## 调试信息

### 日志标识

查看以下日志来监控同步过程：

```
✅ 班次状态轮询已启动，间隔: 30秒
📊 班次状态发生变化
   之前班次: [班次ID] ([状态])
   当前班次: [班次ID] ([状态])
📊 ShiftHomePage: 班次状态更新 - [班次ID] ([状态])
```

### 服务状态检查

可以通过以下方式检查服务状态：

```dart
final BosTransactionPollingService pollingService = 
    ref.read(bosTransactionPollingServiceProvider);
final Map<String, dynamic> status = pollingService.getServiceStatus();
debugPrint('服务状态: $status');
```

## 故障排除

### 常见问题

1. **状态不同步**
   - 检查网络连接
   - 确认两台设备连接到同一个后台服务
   - 查看轮询服务是否正常启动

2. **更新延迟过长**
   - 检查轮询间隔设置（默认30秒）
   - 确认后台API响应正常
   - 查看是否有网络延迟

3. **UI不更新**
   - 确认UI组件正确监听了状态流
   - 检查是否有异常中断了状态订阅
   - 验证 `notifyListeners()` 是否被正确调用

### 调试步骤

1. **启用详细日志**
   ```dart
   debugPrint('🔄 手动触发BOS交易轮询...');
   await pollingService.pollOnce();
   ```

2. **检查轮询状态**
   ```dart
   final Map<String, dynamic> status = pollingService.getServiceStatus();
   debugPrint('轮询状态: $status');
   ```

3. **手动刷新班次状态**
   ```dart
   await ShiftService().refreshCurrentShift();
   ```

## 性能考虑

### 轮询间隔优化

- **当前设置**: 30秒轮询间隔
- **建议**: 根据实际需求调整，平衡实时性和性能
- **可配置**: 可以通过构造函数参数调整间隔

### 资源使用

- **内存**: 轮询服务使用最小内存，主要存储上次已知状态
- **网络**: 每30秒一次轻量级API调用
- **CPU**: 轮询和状态比较的CPU开销很小

## 未来优化

### WebSocket支持

考虑实现WebSocket实时通知以获得更好的实时性：

```dart
// 未来可能的实现
class ShiftWebSocketService {
  void subscribeToShiftChanges() {
    // WebSocket连接和消息处理
  }
}
```

### 智能轮询

根据业务场景动态调整轮询频率：

```dart
// 活跃时间段更频繁轮询
Duration getPollingInterval() {
  final DateTime now = DateTime.now();
  if (isBusinessHours(now)) {
    return Duration(seconds: 15);
  }
  return Duration(seconds: 60);
}
```

## 总结

通过以上实现，我们成功解决了shift状态跨设备同步的问题：

1. **最小改动**: 只修改了现有的轮询服务，没有引入新的复杂性
2. **高效同步**: 30秒内完成状态同步，满足业务需求
3. **稳定可靠**: 利用现有的错误处理和重试机制
4. **易于维护**: 代码结构清晰，便于后续维护和扩展

测试时请按照上述场景进行验证，确保功能正常工作。 