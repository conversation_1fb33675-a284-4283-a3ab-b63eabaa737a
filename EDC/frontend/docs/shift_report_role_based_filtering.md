# Shift Report Role-Based Filtering Implementation

## Overview

This document describes the implementation of role-based filtering for the Shift Report functionality in the EDC Foreourt Payment System. The implementation ensures that employees with "Employee" role can only view and print their own shift data, while users with management roles can view all attendant data.

## Key Requirements

1. **Role-based Access Control**: Employees with "Employee" role should only see their own shift data
2. **Staff Name Matching**: The system checks if the staff name in receipt information matches the logged-in username
3. **Print Filtering**: When printing, employees only get their own data printed
4. **UI Adaptation**: The interface adapts to show appropriate labels and content based on user role

## Implementation Details

### File Modified
- `lib/widgets/shift_attendant_report_dialog.dart`

### Key Methods Added

#### 1. `_isEmployeeRole()`
```dart
bool _isEmployeeRole() {
  final AuthService authService = ref.read(authServiceProvider);
  final Employee? currentEmployee = authService.currentEmployee;
  
  if (currentEmployee?.roles != null) {
    return currentEmployee!.roles!.any((role) => role.toLowerCase() == 'employee');
  }
  return false;
}
```
- Checks if the current user has the "Employee" role
- Case-insensitive role matching
- Returns `false` if no roles are defined (treats as management)

#### 2. `_getCurrentUsername()`
```dart
String? _getCurrentUsername() {
  final AuthService authService = ref.read(authServiceProvider);
  final Employee? currentEmployee = authService.currentEmployee;
  return currentEmployee?.username ?? currentEmployee?.employeeNo;
}
```
- Retrieves the current logged-in user's username
- Falls back to employeeNo if username is not available

#### 3. `_getFilteredAttendants()`
```dart
List<Attendant> _getFilteredAttendants() {
  final List<Attendant> allAttendants = widget.shiftAttendantData.data.attendants;
  
  if (!_isEmployeeRole()) {
    // Non-Employee role, show all attendant data
    return allAttendants;
  }
  
  // Employee role, only show own data
  final String? currentUsername = _getCurrentUsername();
  if (currentUsername == null) {
    return [];
  }
  
  return allAttendants.where((attendant) {
    // Check if attendant name matches current logged-in username
    return attendant.attendantInfo.attendantName == currentUsername ||
           attendant.attendantInfo.staffCardId.toString() == currentUsername;
  }).toList();
}
```
- Filters attendant data based on user role
- For Employee role: only returns attendants whose name matches the current username
- For other roles: returns all attendants

#### 4. `_getFilteredShiftSummary()`
```dart
ShiftSummary _getFilteredShiftSummary() {
  final List<Attendant> filteredAttendants = _getFilteredAttendants();
  
  if (!_isEmployeeRole()) {
    // Non-Employee role, return original summary data
    return widget.shiftAttendantData.data.shiftSummary;
  }
  
  // Employee role, calculate personal summary data
  if (filteredAttendants.isEmpty) {
    return ShiftSummary(/* zero values */);
  }
  
  final Attendant attendant = filteredAttendants.first;
  // Calculate payment method totals...
  return ShiftSummary(/* calculated values */);
}
```
- Calculates filtered shift summary based on user role
- For Employee role: calculates personal summary from their own data
- For other roles: returns original shift summary

#### 5. `_isCurrentUserAttendant()`
```dart
bool _isCurrentUserAttendant(Attendant attendant) {
  final String? currentUsername = _getCurrentUsername();
  if (currentUsername == null) return false;
  
  return attendant.attendantInfo.attendantName == currentUsername ||
         attendant.attendantInfo.staffCardId.toString() == currentUsername;
}
```
- Checks if a specific attendant represents the current user
- Used to determine if individual print buttons should be shown

### UI Changes

#### 1. Dialog Title
- **Manager/Admin**: "Previous Shift Report"
- **Employee**: "My Shift Report"

#### 2. Section Headers
- **Shift Summary**: "Shift Summary" vs "My Summary"
- **Attendants**: "Attendants (X)" vs "My Details"

#### 3. Print Button Text
- **Manager/Admin**: "Print Report"
- **Employee**: "Print My Report"

#### 4. Individual Print Buttons
- Only shown for non-Employee roles or when viewing own data
- Controlled by: `if (!_isEmployeeRole() || _isCurrentUserAttendant(attendant))`

### Print Functionality

#### 1. Main Print Method
- Uses `_getFilteredShiftSummary()` and `_getFilteredAttendants()`
- Prints only filtered data based on user role
- Updates print headers and labels accordingly

#### 2. Individual Attendant Print
- Available to all roles for their own data
- Headers adapt based on user role

### Staff Name Matching Logic

The system matches staff names using two approaches:

1. **Direct Name Match**: `attendant.attendantInfo.attendantName == currentUsername`
2. **Staff Card ID Match**: `attendant.attendantInfo.staffCardId.toString() == currentUsername`

This ensures compatibility with different username formats and data structures.

### Role Hierarchy

The system recognizes the following role hierarchy:

1. **Employee Role**: 
   - Case-insensitive matching ("employee", "Employee", "EMPLOYEE")
   - Restricted to own data only
   - Limited print functionality

2. **Management Roles** (all others):
   - Manager, Admin, Supervisor, etc.
   - Full access to all attendant data
   - Complete print functionality

### Error Handling

1. **No Current User**: Returns empty data for Employee role
2. **No Matching Data**: Shows appropriate empty state messages
3. **No Roles Defined**: Treats as management role (full access)

### Testing

The implementation includes comprehensive tests covering:

1. Manager role viewing all attendants
2. Employee role viewing only own data
3. Employee role with no matching data
4. User with no roles (treated as management)
5. Unit tests for core filtering logic

## Usage Examples

### Manager/Admin User
```dart
// User with Manager role
Employee managerUser = Employee(
  username: 'admin',
  roles: ['Manager'],
  // ...
);

// Will see all attendants in the shift
// Can print complete shift report
// Individual print buttons available for all attendants
```

### Employee User
```dart
// User with Employee role
Employee employeeUser = Employee(
  username: 'john_doe',
  roles: ['Employee'],
  // ...
);

// Will only see their own data where:
// attendant.attendantInfo.attendantName == 'john_doe'
// Can only print their own report
// Individual print button only for their own data
```

## Security Considerations

1. **Client-side Filtering**: Current implementation filters on the client side
2. **Role Verification**: Relies on AuthService for role information
3. **Data Integrity**: Ensures employees cannot access other employees' data
4. **Print Security**: Prevents unauthorized printing of sensitive data

## Future Enhancements

1. **Server-side Filtering**: Move filtering logic to API level
2. **Audit Logging**: Track who accessed which data
3. **Fine-grained Permissions**: More detailed permission system
4. **Role-based API Endpoints**: Separate endpoints for different roles

## Migration Notes

This implementation is backward compatible and does not affect existing functionality for management users. The changes are additive and only restrict access for users with "Employee" role. 