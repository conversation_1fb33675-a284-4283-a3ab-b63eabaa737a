# 动态列宽调整实现文档

## 概述
根据订单金额(amount)的大小动态调整打印收据中产品表格的列宽，以确保不同金额范围的订单都能正确显示。

## 需求说明
- 当 `amount < 100,000` 时，使用列宽 `[11, 6, 7, 8]`
- 当 `amount >= 100,000` 时，使用列宽 `[11, 5, 7, 9]`

## 实现位置
文件：`lib/services/auto_print_service.dart`
方法：`printOrderReceipt()` 中的产品表格打印部分

## 实现代码

### 修改前
```dart
// === 4. PRODUCT TABLE ===
await printer.setAlignment(PrintAlignment.center);
await printer.printColumnsText(
  <String>['Product', 'Qty', 'Price', 'Amount'],
  <int>[11, 8, 7, 9],  // 固定列宽
  <int>[TableAlignment.center, TableAlignment.center, TableAlignment.center, TableAlignment.center],
);

// Product details
await printer.printColumnsText(
  <String>[productInfo['name'].toString(), volumeStr, priceStr, amountStr],
  <int>[11, 8, 7, 9],  // 固定列宽
  <int>[TableAlignment.left, TableAlignment.center, TableAlignment.right, TableAlignment.right],
);
```

### 修改后
```dart
// === 4. PRODUCT TABLE ===
await printer.setAlignment(PrintAlignment.center);

// 根据amount动态调整列宽
final double amount = order.amount;
final List<int> columnWidths = amount < 100000 ? [11, 6, 7, 8] : [11, 5, 7, 9];

await printer.printColumnsText(
  <String>['Product', 'Qty', 'Price', 'Amount'],
  columnWidths,  // 动态列宽
  <int>[TableAlignment.center, TableAlignment.center, TableAlignment.center, TableAlignment.center],
);

// Product details
await printer.printColumnsText(
  <String>[productInfo['name'].toString(), volumeStr, priceStr, amountStr],
  columnWidths,  // 动态列宽
  <int>[TableAlignment.left, TableAlignment.center, TableAlignment.right, TableAlignment.right],
);
```

## 列宽分配说明

### 小金额情况 (amount < 100,000)
- **列宽**: `[11, 6, 7, 8]`
- **总宽度**: 32 字符
- **适用场景**: 金额较小，Amount列不需要太宽，可以给Qty列更多空间

| 列名 | 宽度 | 百分比 | 用途 |
|------|------|--------|------|
| Product | 11 | 34.4% | 产品名称 |
| Qty | 6 | 18.8% | 数量，有更多空间显示小数 |
| Price | 7 | 21.9% | 单价 |
| Amount | 8 | 25.0% | 金额 |

### 大金额情况 (amount >= 100,000)
- **列宽**: `[11, 5, 7, 9]`
- **总宽度**: 32 字符
- **适用场景**: 金额较大，Amount列需要更宽，压缩Qty列宽度

| 列名 | 宽度 | 百分比 | 用途 |
|------|------|--------|------|
| Product | 11 | 34.4% | 产品名称 |
| Qty | 5 | 15.6% | 数量，压缩宽度 |
| Price | 7 | 21.9% | 单价 |
| Amount | 9 | 28.1% | 金额，更宽以容纳大数值 |

## 判断逻辑
```dart
final double amount = order.amount;
final List<int> columnWidths = amount < 100000 ? [11, 6, 7, 8] : [11, 5, 7, 9];
```

- **判断条件**: `amount < 100000`
- **数据类型**: `double`（订单金额）
- **阈值**: 100,000

## 测试场景

### 测试用例1: 小金额订单
- **订单金额**: 50,000
- **预期列宽**: `[11, 6, 7, 8]`
- **验证点**: Qty列有6个字符宽度，Amount列有8个字符宽度

### 测试用例2: 大金额订单
- **订单金额**: 150,000
- **预期列宽**: `[11, 5, 7, 9]`
- **验证点**: Qty列有5个字符宽度，Amount列有9个字符宽度

### 测试用例3: 临界值
- **订单金额**: 100,000
- **预期列宽**: `[11, 5, 7, 9]`
- **验证点**: 等于100,000时使用大金额列宽

## 注意事项

1. **数据一致性**: 表头和数据行使用相同的`columnWidths`变量
2. **总宽度保持**: 两种列宽配置的总宽度都是32字符
3. **字符宽度**: 使用`printColumnsText`方法，按字符宽度计算
4. **对齐方式**: 不同列使用不同的对齐方式（左对齐、居中、右对齐）

## 相关文件
- `lib/services/auto_print_service.dart` - 主要实现文件
- `docs/print_table_column_width_fix.md` - 之前的列宽修复文档
- `docs/print_reprint_configuration.md` - 打印重打印配置文档

## 更新日期
2024-12-19 