# 打印和重打功能配置说明

## 概述

本文档说明了 EDC 系统中打印和重打功能的配置，主要通过 `isReprint` 参数来区分首次打印和重新打印。

## 功能区别

### 首次打印（自动结账）
- **触发场景**：用户完成支付确认后的自动打印
- **参数设置**：`isReprint = false`（默认值）
- **小票底部显示**：`"Salinan Pelanggan"`（客户副本）
- **调用位置**：`AutoPrintService.autoPrintOrderReceipt()`

### 重新打印（手动重打）
- **触发场景**：用户从订单详情页面或打印预览页面手动打印
- **参数设置**：`isReprint = true`
- **小票底部显示**：`"Salinan Ulang Pelanggan"`（客户重打副本）
- **调用位置**：
  - `order_detail_page.dart` 的 `_printOrderReceipt()` 方法
  - `receipt_preview_page.dart` 的打印功能

## 代码修改

### 1. 订单详情页面重打功能

**文件**：`lib/screens/order/order_detail_page.dart`

```dart
// 修改前
await AutoPrintService.printOrderReceipt(_orderDetail!);

// 修改后
await AutoPrintService.printOrderReceipt(
  _orderDetail!,
  isReprint: true, // 从订单详情页面打印设置为重打模式
);
```

### 2. 打印预览页面重打功能

**文件**：`lib/screens/printing/receipt_preview_page.dart`

```dart
// 修改前
await AutoPrintService.printOrderReceipt(_order!);

// 修改后
await AutoPrintService.printOrderReceipt(
  _order!,
  isReprint: true, // 从打印预览页面打印设置为重打模式
);
```

### 3. 自动结账打印功能（保持不变）

**文件**：`lib/services/auto_print_service.dart`

```dart
// 自动结账打印 - 使用默认参数
await printOrderReceipt(order); // isReprint = false（默认值）
```

**文件**：`lib/screens/payment/cash_payment_page.dart`

```dart
// 支付确认后的自动打印
await _autoPrintService.autoPrintOrderReceipt(
  context,
  order,
  showSuccessMessage: false,
  showFailureMessage: true,
);
```

## 小票底部文字对比

| 打印类型 | isReprint 值 | 小票底部显示 | 说明 |
|---------|-------------|-------------|-----|
| 首次打印 | `false` | `"Salinan Pelanggan"` | 客户副本 |
| 重新打印 | `true` | `"Salinan Ulang Pelanggan"` | 客户重打副本 |

## 使用场景

### 自动打印场景（isReprint = false）
1. 用户完成支付确认后的自动打印
2. 系统后台的静默打印
3. 任何首次生成的小票

### 手动重打场景（isReprint = true）
1. 用户在订单详情页面点击打印按钮
2. 用户在打印预览页面点击打印按钮
3. 客服或管理员手动重新打印小票

## 技术实现

### printOrderReceipt 方法参数

```dart
static Future<void> printOrderReceipt(
  Order order, {
  bool isReprint = false,        // 是否为重打模式
  int reprintCount = 1,          // 重打次数（暂未使用）
  String? vehiclePlate,          // 车牌号
  String? vehicleType,           // 车辆类型
  String? stationName,           // 加油站名称
  String? stationAddress,        // 加油站地址
}) async {
  // 实现逻辑
}
```

### 小票底部文字逻辑

```dart
await printer.printText(
  isReprint ? 'Salinan Ulang Pelanggan\n' : 'Salinan Pelanggan\n'
);
```

## 验证方法

### 测试首次打印
1. 完成一笔加油交易
2. 进行支付确认
3. 查看自动打印的小票底部应显示："Salinan Pelanggan"

### 测试重新打印
1. 进入订单详情页面
2. 点击打印按钮
3. 查看打印的小票底部应显示："Salinan Ulang Pelanggan"

## 客户信息显示逻辑

### 客户姓名显示规则
- 只有当客户姓名不为空且不是 "ANONIM" 时才显示
- 优先从 `member_info` 获取，其次从 `order.customerName` 获取

### 客户电话显示规则
- 只有当客户电话不为空时才显示
- 不再有默认值或占位符
- 优先从 `member_info` 获取，其次从 `order.memberPhone` 获取

### 车牌号显示规则
- 只有当 `order.customerName` 不为空时才显示
- 显示格式：`NomorKendaraan: [车牌号]`

## 注意事项

1. **参数一致性**：确保所有手动打印的地方都设置 `isReprint = true`
2. **默认行为**：自动打印应该始终使用默认参数（`isReprint = false`）
3. **用户体验**：通过小票底部文字帮助用户和工作人员区分原始小票和重打小票
4. **审计追踪**：重打标识有助于财务审计和问题追踪
5. **客户信息简化**：移除了复杂的判断逻辑，只在有实际数据时才显示相应信息

## 相关文件

- `lib/services/auto_print_service.dart` - 核心打印服务
- `lib/screens/order/order_detail_page.dart` - 订单详情页面
- `lib/screens/printing/receipt_preview_page.dart` - 打印预览页面
- `lib/screens/payment/cash_payment_page.dart` - 支付确认页面 