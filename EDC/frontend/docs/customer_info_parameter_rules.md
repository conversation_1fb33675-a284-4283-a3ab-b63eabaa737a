# 客户信息参数调用规则文档

## 概述
本文档说明了收据打印服务中客户信息参数的调用规则和数据来源。

## 数据结构说明

### Order 对象中的客户信息字段
- `order.customerName` - 实际存储的是车牌号码
- `order.memberPhone` - 会员电话号码
- `order.extInfo['member_info']['name']` - 客户真实姓名
- `order.extInfo['member_info']['phone']` - 客户电话号码

## 当前参数调用规则

### 客户姓名获取逻辑
```dart
// 只从 member_info 获取客户姓名
final String? name = memberInfo['name'] as String?;
if (name != null && name.isNotEmpty) {
  customerName = name;
}
```

### 客户电话获取逻辑
```dart
// 只从 member_info 获取客户电话
final String? phone = memberInfo['phone'] as String?;
if (phone != null && phone.isNotEmpty) {
  customerPhone = phone;
}
```

### 车牌号获取逻辑
```dart
// 直接使用 order.customerName
if (order.customerName != null && order.customerName!.isNotEmpty) {
  await printer.printText('NomorKendaraan: ${order.customerName!}\n');
}
```

## 输出规则

### 输出顺序
1. 客户姓名（如果 `member_info.name` 不为空）
2. 客户电话（如果 `member_info.phone` 不为空，使用脱敏格式）
3. 车牌号码（如果 `order.customerName` 不为空）

### 脱敏规则
- 电话号码脱敏格式：`*****` + 后5位数字
- 例如：`081234567890` → `*****67890`

### 空值处理
- 如果相应字段为空，则不输出该行
- 不使用任何后备数据源

## 实际场景示例

### 场景1：完整会员信息
```dart
// 输入数据
extInfo: {
  'member_info': {
    'name': '张三',
    'phone': '081234567890'
  }
}
order.customerName = 'B1234ABC'

// 输出结果
张三
Telepon: *****67890
NomorKendaraan: B1234ABC
```

### 场景2：部分会员信息
```dart
// 输入数据
extInfo: {
  'member_info': {
    'name': '李四',
    'phone': ''  // 空电话
  }
}
order.customerName = 'B5678DEF'

// 输出结果
李四
NomorKendaraan: B5678DEF
```

### 场景3：无会员信息
```dart
// 输入数据
extInfo: {
  'member_info': null  // 或者为空
}
order.customerName = 'B9999XYZ'

// 输出结果
NomorKendaraan: B9999XYZ
```

### 场景4：所有信息为空
```dart
// 输入数据
extInfo: {
  'member_info': {
    'name': '',
    'phone': ''
  }
}
order.customerName = null

// 输出结果
（无客户信息输出）
```

## 技术实现细节

### 主要方法
```dart
static Map<String, String> _getCustomerInfo(Order order) {
  // 只从 member_info 获取客户信息
  final Map<String, dynamic>? memberInfo = order.extInfo['member_info'] as Map<String, dynamic>?;
  
  String customerName = '';
  String customerPhone = '';
  
  if (memberInfo != null && memberInfo.isNotEmpty) {
    final String? name = memberInfo['name'] as String?;
    final String? phone = memberInfo['phone'] as String?;
    
    if (name != null && name.isNotEmpty) {
      customerName = name;
    }
    
    if (phone != null && phone.isNotEmpty) {
      customerPhone = phone;
    }
  }
  
  return {
    'name': customerName,
    'phone': customerPhone,
  };
}
```

### 电话脱敏方法
```dart
static String _formatPhoneNumber(String phoneNumber) {
  if (phoneNumber.isEmpty) return '';
  
  final String digitsOnly = phoneNumber.replaceAll(RegExp(r'[^0-9]'), '');
  
  if (digitsOnly.length < 5) {
    return '*' * digitsOnly.length;
  }
  
  final String lastFiveDigits = digitsOnly.substring(digitsOnly.length - 5);
  return '*****$lastFiveDigits';
}
```

## 注意事项

1. **数据来源单一化**：客户姓名和电话只从 `member_info` 获取，不再使用任何后备数据源
2. **隐私保护**：电话号码采用脱敏显示，保护客户隐私
3. **空值处理**：空值不输出，避免无意义的空行
4. **输出顺序**：按照姓名、电话、车牌的顺序输出
5. **车牌号独立**：车牌号直接从 `order.customerName` 获取，不依赖会员信息

## 更新历史

- 2024-01-XX：移除后备逻辑，只从 member_info 获取客户信息
- 2024-01-XX：添加电话脱敏功能
- 2024-01-XX：调整输出顺序为姓名、电话、车牌

## 公共方法

### 供外部调用的方法

```dart
// 获取客户信息（名字和电话）
static Map<String, String> getCustomerIdAsCustomerInfo(Order order) {
  return _getCustomerInfo(order);
}

// 获取车牌号
static String getCustomerNameAsVehicleId(Order order) {
  return _getVehicleId(order, order.customerName);
}

// 格式化电话号码（供测试使用）
static String formatPhoneNumber(String phoneNumber) {
  return _formatPhoneNumber(phoneNumber);
}
```

## 注意事项

1. **数据优先级**：始终优先使用 `member_info` 中的数据
2. **回退机制**：当 `member_info` 中数据为空时，使用 `order` 中的直接字段
3. **空值处理**：所有空值都不会在小票上显示
4. **隐私保护**：电话号码自动进行脱敏处理
5. **字段用途**：
   - `order.customerName` → 车牌号
   - `extInfo['member_info']['name']` → 客户真实姓名
   - `extInfo['member_info']['phone']` → 客户电话号码

## 测试验证

可以通过以下方式测试参数调用规则：

```dart
// 测试客户信息获取
final Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
print('客户姓名: ${customerInfo['name']}');
print('客户电话: ${customerInfo['phone']}');

// 测试电话号码脱敏
final String maskedPhone = AutoPrintService.formatPhoneNumber('081234567890');
print('脱敏电话: $maskedPhone');  // 输出: *****67890
```

---

**文档版本**: 1.0  
**最后更新**: 2024-01-XX  
**维护人**: AI Assistant 