# Promotion Management 编译错误修复

## 问题描述

在更新 Promotion Management 页面后，出现了编译错误：

```
Error: '_buildDetailRow' is already declared in this scope.
```

## 问题原因

在代码中存在两个同名的 `_buildDetailRow` 方法：

1. **旧版本**：简单的两参数方法，用于卡片中的详情显示
   ```dart
   Widget _buildDetailRow(String label, String value)
   ```

2. **新版本**：带可选参数的方法，用于弹窗中的表格式显示
   ```dart
   Widget _buildDetailRow(String label, String value, {bool isFirst = false, bool isLast = false})
   ```

## 解决方案

### 1. 重命名方法

将旧版本的方法重命名为 `_buildSimpleDetailRow`：

```dart
Widget _buildSimpleDetailRow(String label, String value) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 6.0),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        SizedBox(
          width: 70,
          child: Text(
            '$label:',
            style: EDCTextStyles.hintText.copyWith(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: EDCTextStyles.bodyText.copyWith(
              fontSize: 12,
              color: Colors.grey[800],
            ),
          ),
        ),
      ],
    ),
  );
}
```

### 2. 更新方法调用

将卡片中的详情显示调用更新为使用新的方法名：

```dart
// 修改前
_buildDetailRow('Period', '${promotion['startDate']} - ${promotion['endDate']}'),
_buildDetailRow('Vehicle', promotion['vehicleType'] as String),
_buildDetailRow('Product', promotion['product'] as String),
_buildDetailRow('Coverage', promotion['coverageSite'] as String),

// 修改后
_buildSimpleDetailRow('Period', '${promotion['startDate']} - ${promotion['endDate']}'),
_buildSimpleDetailRow('Vehicle', promotion['vehicleType'] as String),
_buildSimpleDetailRow('Product', promotion['product'] as String),
_buildSimpleDetailRow('Coverage', promotion['coverageSite'] as String),
```

### 3. 保留表格式方法

保留新的 `_buildDetailRow` 方法用于弹窗中的表格式显示：

```dart
Widget _buildDetailRow(String label, String value, {bool isFirst = false, bool isLast = false}) {
  return Container(
    decoration: BoxDecoration(
      border: Border(
        bottom: isLast ? BorderSide.none : BorderSide(color: Colors.grey.shade300),
      ),
      borderRadius: BorderRadius.only(
        topLeft: isFirst ? const Radius.circular(8) : Radius.zero,
        topRight: isFirst ? const Radius.circular(8) : Radius.zero,
        bottomLeft: isLast ? const Radius.circular(8) : Radius.zero,
        bottomRight: isLast ? const Radius.circular(8) : Radius.zero,
      ),
    ),
    child: Row(
      children: <Widget>[
        Expanded(
          flex: 2,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              border: Border(
                right: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 13,
              ),
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 13,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
```

## 修改文件

- `EDC/frontend/lib/screens/home/<USER>

## 验证结果

修复后：
- ✅ 编译错误已解决
- ✅ 卡片中的详情显示正常（使用 `_buildSimpleDetailRow`）
- ✅ 弹窗中的表格式显示正常（使用 `_buildDetailRow`）
- ✅ 所有功能保持不变

## 方法用途区分

### `_buildSimpleDetailRow`
- **用途**: 活动卡片中的简单详情显示
- **样式**: 简单的标签-值对，左对齐布局
- **位置**: 活动卡片内容区域

### `_buildDetailRow`
- **用途**: 弹窗中的表格式详情显示
- **样式**: 表格行样式，带边框和背景色
- **位置**: 活动详情弹窗中的详情表格

## 注意事项

1. **方法命名**: 使用清晰的命名区分不同用途的方法
2. **代码复用**: 避免重复的方法名称导致编译冲突
3. **功能分离**: 不同的显示需求使用不同的方法实现

这次修复确保了代码的清晰性和可维护性，同时保持了所有功能的正常运行。
