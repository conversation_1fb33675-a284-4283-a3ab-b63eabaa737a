# Promotion Management 页面更新

## 更新概述

根据用户需求，对 Promotion Management 页面进行了全面更新，包括活动数据、车辆类型限制、品牌名称规范化和详情展示样式优化。

## 主要修改内容

### 1. 活动数据更新

**文件**: `EDC/frontend/lib/screens/home/<USER>

#### 新增活动列表

根据提供的图片内容，更新了完整的活动列表：

**绿色系列（Gratis 活动）**：
- Gratis 1L BP 92 July 2025
- Gratis 1L BP Ultimate July 2025  
- Gratis 1L BP Ultimate Diesel July 2025

**蓝色系列（Extra Hemat 活动）**：
- Extra Hemat 28L – BP 92
- Extra Hemat 28L – BP Ultimate
- Extra Hemat 28L – BP Ultimate Diesel
- Extra Hemat 3L – BP 92
- Extra Hemat 3L – BP Ultimate
- Extra Hemat 3L – BP Ultimate Diesel

### 2. 车辆类型统一

- **修改前**: 各活动有不同的车辆类型限制（如 "4W", "4W & 2W"）
- **修改后**: 所有活动统一设置为 `"All Type"`，不限制车辆类型

### 3. 品牌名称规范化

- **修改前**: 使用小写 "bp"
- **修改后**: 统一使用大写 "BP"
- **影响范围**: 活动名称、产品名称、活动描述

### 4. 活动时间更新

- **修改前**: 各活动有不同的时间范围
- **修改后**: 所有活动统一设置为 `"1 July 2025"` 到 `"31 July 2025"`

### 5. 详情展示样式优化

#### 表格样式设计

参考提供的图片样式，实现了类似的表格布局：

- **表头设计**: 使用绿色/蓝色背景，白色文字
- **内容区域**: 使用浅色背景，清晰的边框分隔
- **双列布局**: "Name of Promotion" 和 "Promotion Rules"

#### 颜色主题

- **Gratis 活动**: 绿色主题 (`#4CAF50`)
- **Extra Hemat 活动**: 蓝色主题 (`#2196F3`)

#### 详情表格

新增了完整的活动详情表格，包含：
- Period（活动期间）
- Vehicle Type（车辆类型）
- Product（产品）
- Coverage Site（覆盖站点）
- Minimum Purchase（最低购买量）
- Discount（折扣）

## 代码结构

### 活动数据结构

```dart
<String, dynamic>{
  'id': '1',
  'name': 'Gratis 1L BP 92 July 2025',
  'type': 'Direct Discount',
  'description': 'Minimum purchase 25 Liter BP 92 gets discount 1 Liter / trx',
  'startDate': '1 July 2025',
  'endDate': '31 July 2025',
  'vehicleType': 'All Type',
  'product': 'BP 92',
  'coverageSite': 'All Site',
  'minPurchase': '25 Liter',
  'discount': '1 Liter / trx',
  'icon': Icons.local_gas_station,
  'color': const Color(0xFF4CAF50),
}
```

### 新增方法

- `_buildDetailTable()`: 构建详情表格
- `_buildDetailRow()`: 构建表格行
- 更新了 `_showPromotionDetails()`: 优化详情显示

## 视觉效果

### 活动卡片

- 保持原有的卡片式布局
- 根据活动类型使用不同的图标和颜色
- Gratis 活动使用加油站图标和绿色
- Extra Hemat 活动使用储蓄图标和蓝色

### 详情弹窗

- 表格式布局，类似 Excel 表格
- 清晰的表头和内容分离
- 响应式设计，适配不同屏幕尺寸

## 测试验证

创建了完整的单元测试 `promotion_management_test.dart`：

- ✅ 车辆类型统一性测试
- ✅ BP 品牌名称大写测试
- ✅ 活动时间一致性测试
- ✅ 活动名称格式测试
- ✅ 活动描述完整性测试
- ✅ 覆盖范围统一性测试

## 业务影响

### 用户体验提升

1. **统一的车辆类型**: 简化了用户理解，所有活动适用于所有车辆类型
2. **规范的品牌展示**: 统一使用大写 BP，提升品牌形象
3. **清晰的时间范围**: 所有活动都在 7月份，便于用户记忆
4. **优化的详情展示**: 表格式布局更直观，信息层次清晰

### 维护便利性

1. **数据结构统一**: 便于后续维护和扩展
2. **样式组件化**: 详情展示组件可复用
3. **测试覆盖**: 确保修改的正确性和稳定性

## 相关文件

- `EDC/frontend/lib/screens/home/<USER>
- `EDC/frontend/test/promotion_management_test.dart` - 单元测试
- `EDC/frontend/docs/promotion_management_update.md` - 本文档

## 注意事项

1. **数据同步**: 如果后端有相应的活动数据，需要同步更新
2. **图标资源**: 确保使用的图标在所有平台上都能正确显示
3. **颜色一致性**: 保持与整体 BP 品牌色彩的一致性
4. **响应式设计**: 确保在不同设备上都有良好的显示效果

## 后续优化建议

1. **动态数据**: 考虑从后端 API 获取活动数据
2. **国际化**: 支持多语言显示
3. **动画效果**: 添加适当的过渡动画
4. **筛选功能**: 添加按活动类型、时间等筛选功能
