# EDC 日志服务使用指南

## 概述

EDC 日志服务提供了统一的日志管理功能，支持控制台和文件双重输出。在 debug 模式下，日志保存到电脑上；在生产模式下，日志保存到设备上。

## 功能特性

- ✅ 控制台和文件双重输出
- ✅ 自动日志文件轮转（超过5MB自动备份）
- ✅ 支持多种日志级别（DEBUG、INFO、WARNING、ERROR）
- ✅ 日志文件自动管理（最多保留3个备份文件）
- ✅ 专门的API、交易、设备、打印日志记录方法
- ✅ 日志查看器界面
- ✅ 日志文件清理和导出功能
- ✅ **自动覆盖 debugPrint**：现有代码无需修改，所有 debugPrint 调用自动保存到文件

## 初始化

日志服务已在 `main.dart` 中自动初始化：

```dart
// 在 main() 函数中
await LogService.instance.initialize();
```

## 重要特性：debugPrint 自动覆盖

**无需修改现有代码！** 日志服务会自动覆盖 Flutter 的 `debugPrint` 函数，使所有现有的 `debugPrint` 调用都自动保存到文件。

### 现有代码自动生效

```dart
// 现有代码无需修改，这些调用现在会自动保存到日志文件
debugPrint('用户登录成功');
debugPrint('API 请求开始: GET /api/users');
debugPrint('❌ 发生错误: ${error.toString()}');

// 你的现有代码仍然可以正常工作，不需要任何修改！
void someExistingFunction() {
  debugPrint('🚀 函数开始执行');
  // 业务逻辑...
  debugPrint('✅ 函数执行完成');
}
```

### 覆盖功能特点

- ✅ **保持原有行为**：在 debug 模式下仍然输出到控制台
- ✅ **自动文件记录**：所有 debugPrint 调用都自动保存到日志文件
- ✅ **零代码修改**：现有项目无需修改任何 debugPrint 调用
- ✅ **完全兼容**：支持 wrapWidth 参数和所有原有功能
- ✅ **自动清理**：应用退出时自动恢复原始 debugPrint 函数

## 使用方法

### 1. 基础日志记录

```dart
import 'package:edc_app/services/log_service.dart';

// 使用全局便捷方法
logDebug('MyTag', 'This is a debug message');
logInfo('MyTag', 'This is an info message');
logWarning('MyTag', 'This is a warning message');
logError('MyTag', 'This is an error message', error, stackTrace);

// 或者使用服务实例
LogService.instance.debug('MyTag', 'Debug message');
LogService.instance.info('MyTag', 'Info message');
LogService.instance.warning('MyTag', 'Warning message');
LogService.instance.error('MyTag', 'Error message', error, stackTrace);
```

### 2. API 日志记录

```dart
// API 请求日志
LogService.instance.apiRequest('GET', '/api/users', {'page': 1});

// API 响应日志
LogService.instance.apiResponse('GET', '/api/users', 200, responseData);

// API 错误日志
LogService.instance.apiError('GET', '/api/users', error, stackTrace);
```

### 3. 业务专用日志

```dart
// 交易日志
LogService.instance.transaction('CREATE', 'TX123456', '创建交易成功');

// 设备日志
LogService.instance.device('Scanner', 'SCAN', '扫描二维码成功');

// 打印日志
LogService.instance.printer('PRINT', '打印小票成功');
```

### 4. 在现有代码中替换日志

将现有的 `debugPrint` 和 `print` 替换为新的日志服务：

```dart
// 旧代码
debugPrint('🚀 请求开始: GET /api/data');
print('响应状态: 200');

// 新代码
logInfo('API', '🚀 请求开始: GET /api/data');
logInfo('API', '响应状态: 200');

// 或者使用专用方法
LogService.instance.apiRequest('GET', '/api/data', null);
LogService.instance.apiResponse('GET', '/api/data', 200, data);
```

## 日志文件位置

### Debug 模式（开发时）
- **Windows**: `%USERPROFILE%\Documents\edc_logs\edc_app.log`
- **macOS**: `~/Documents/edc_logs/edc_app.log`
- **Linux**: `~/Documents/edc_logs/edc_app.log`

### 生产模式（设备上）
- **Android**: `/data/data/com.example.edc_app/app_flutter/logs/edc_app.log`
- **iOS**: `App Support Directory/logs/edc_app.log`

## 日志查看器

应用内提供了日志查看器界面，可以：

1. 查看最近的日志内容
2. 调整显示的日志行数
3. 复制日志到剪贴板
4. 清理日志文件
5. 生成测试日志

要访问日志查看器，导航到：
```dart
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const LogViewerPage(),
  ),
);
```

## 在不同模块中的应用示例

### 1. API 服务中的使用

在 `ApiClient` 中已经集成了日志服务：

```dart
class ApiClient {
  void _printRequestLog(RequestOptions options) {
    LogService.instance.apiRequest(
      options.method, 
      options.uri.toString(), 
      requestData
    );
  }
}
```

### 2. 交易流程中的使用

```dart
class TransactionService {
  Future<void> createTransaction(String orderId) async {
    try {
      logInfo('Transaction', '开始创建交易: $orderId');
      
      // 业务逻辑...
      
      LogService.instance.transaction('CREATE', orderId, '交易创建成功');
    } catch (e) {
      logError('Transaction', '创建交易失败: $orderId', e);
      rethrow;
    }
  }
}
```

### 3. 设备操作中的使用

```dart
class PrinterService {
  Future<void> printReceipt() async {
    try {
      LogService.instance.printer('INIT', '初始化打印机');
      
      // 打印逻辑...
      
      LogService.instance.printer('PRINT', '小票打印成功');
    } catch (e) {
      logError('Printer', '打印失败', e);
      rethrow;
    }
  }
}
```

### 4. 页面生命周期中的使用

```dart
class MyPage extends StatefulWidget {
  @override
  State<MyPage> createState() => _MyPageState();
}

class _MyPageState extends State<MyPage> {
  @override
  void initState() {
    super.initState();
    logDebug('MyPage', '页面初始化');
  }

  @override
  void dispose() {
    logDebug('MyPage', '页面销毁');
    super.dispose();
  }

  void _handleButtonPress() {
    logInfo('MyPage', '用户点击按钮');
    // 业务逻辑...
  }
}
```

## 日志管理

### 获取日志信息

```dart
// 获取日志文件路径
String? logPath = LogService.instance.logFilePath;

// 获取日志文件大小
int fileSize = await LogService.instance.getLogFileSize();

// 获取最近的日志内容
String recentLogs = await LogService.instance.getRecentLogs(lines: 200);
```

### 清理日志

```dart
// 清理所有日志文件
await LogService.instance.clearLogs();
```

## 性能考虑

1. **异步写入**: 日志写入是异步的，不会阻塞主线程
2. **文件轮转**: 超过5MB自动备份，避免单个文件过大
3. **日志级别**: 生产模式下自动过滤DEBUG级别日志
4. **缓存策略**: 使用文件流缓存，提高写入效率

## 最佳实践

### 1. 使用合适的日志级别

```dart
// DEBUG: 详细的调试信息
logDebug('Module', '进入方法: getUserData()');

// INFO: 一般信息
logInfo('Module', '用户登录成功: userID=${user.id}');

// WARNING: 警告信息
logWarning('Module', '网络连接不稳定，正在重试');

// ERROR: 错误信息
logError('Module', '登录失败', exception, stackTrace);
```

### 2. 使用有意义的标签

```dart
// 好的标签
logInfo('UserAuth', '用户登录成功');
logInfo('PaymentAPI', '支付请求发送');
logInfo('DatabaseService', '数据保存完成');

// 不推荐的标签
logInfo('Main', '操作完成');
logInfo('Utils', '处理数据');
```

### 3. 记录关键业务流程

```dart
class OrderService {
  Future<void> processOrder(Order order) async {
    // 记录开始
    logInfo('OrderService', '开始处理订单: ${order.id}');
    
    try {
      // 业务步骤1
      await validateOrder(order);
      logInfo('OrderService', '订单验证通过: ${order.id}');
      
      // 业务步骤2
      await processPayment(order);
      LogService.instance.transaction('PAYMENT', order.id, '支付处理完成');
      
      // 业务步骤3
      await updateInventory(order);
      logInfo('OrderService', '库存更新完成: ${order.id}');
      
      // 记录成功
      LogService.instance.transaction('COMPLETE', order.id, '订单处理完成');
      
    } catch (e) {
      // 记录失败
      logError('OrderService', '订单处理失败: ${order.id}', e);
      rethrow;
    }
  }
}
```

### 4. 避免敏感信息泄露

```dart
// 好的做法 - 不记录敏感信息
logInfo('Auth', '用户登录: userID=${user.id}');

// 不好的做法 - 可能泄露密码
logInfo('Auth', '登录请求: ${loginRequest.toString()}'); // 可能包含密码
```

## 故障排查

### 1. 日志文件未创建

检查应用是否有文件写入权限：

```dart
// 在日志服务初始化后检查
String? logPath = LogService.instance.logFilePath;
if (logPath == null) {
  logWarning('Main', '日志文件路径为空，可能是权限问题');
}
```

### 2. 日志内容缺失

确保日志服务已正确初始化：

```dart
// 在 main() 函数中确保初始化
await LogService.instance.initialize();
```

### 3. 性能问题

如果日志过多影响性能，可以调整日志级别：

```dart
// 在生产环境中只记录重要信息
Logger.root.level = Level.WARNING; // 只记录警告和错误
```

## 与现有代码的迁移

### 1. debugPrint 自动迁移 ✅

**好消息！** 不需要替换任何 `debugPrint` 调用，它们已经自动保存到文件了！

```dart
// 这些代码无需修改，已经自动记录到文件
debugPrint('🚀 请求开始: GET /api/data');
debugPrint('✅ 响应成功: 200');
debugPrint('❌ 发生错误: ${error.toString()}');
```

### 2. 可选：替换 print 语句

如果有使用 `print` 的地方，可以考虑替换为 `debugPrint` 或直接使用日志服务：

```dart
// 旧代码
print('Processing started');

// 选项1：改为 debugPrint（推荐，因为会自动记录到文件）
debugPrint('Processing started');

// 选项2：使用日志服务
logInfo('Module', 'Processing started');
```

### 3. 增强错误日志（可选）

在 catch 块中，可以选择使用更强大的错误日志：

```dart
// 现有的 debugPrint 方式（已自动记录到文件）
try {
  // 业务逻辑
} catch (e) {
  debugPrint('❌ 操作失败: $e');
}

// 或者使用增强的错误日志（可选）
try {
  // 业务逻辑
} catch (e, stackTrace) {
  logError('Module', '操作失败', e, stackTrace);
  rethrow;
}
```

### 4. 测试覆盖功能

可以使用测试页面验证 debugPrint 覆盖功能：

```dart
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const DebugPrintTestPage(),
  ),
);
```

这样，您就可以在整个应用中使用统一的日志服务，提高调试效率和问题排查能力。 