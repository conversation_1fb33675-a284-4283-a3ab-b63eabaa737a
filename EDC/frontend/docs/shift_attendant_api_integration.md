# 班次员工API集成文档

## 概述

本文档描述了如何将新的班次员工API (`/api/v1/shifts/{id}/attendants`) 集成到EDC前庭支付系统中。

## 集成内容

### 1. 数据模型 (`lib/models/shift_attendant_model.dart`)

创建了完整的数据模型来处理班次员工API的响应：

- `ShiftAttendantResponse`: 主响应模型
- `ShiftAttendantData`: 数据容器
- `ShiftInfo`: 班次基本信息
- `Attendant`: 员工详细信息
- `AttendantInfo`: 员工基本信息
- `FuelSales`: 油品销售数据
- `FuelGradeData`: 按油品等级分组的销售数据
- `FuelSalesTotal`: 油品销售汇总
- `PaymentSummary`: 支付方式汇总
- `PaymentMethodData`: 支付方式明细
- `ShiftSummary`: 班次汇总信息
- `ShiftAttendantMeta`: 元数据信息
- `ShiftAttendantQueryParams`: 查询参数

### 2. API服务 (`lib/services/api/shift_attendant_api.dart`)

创建了专门的API服务来处理班次员工数据：

- `ShiftAttendantApi`: 主API服务类
- `getShiftAttendants()`: 获取班次所有员工数据
- `getShiftAttendantByName()`: 按员工姓名筛选
- `getShiftAttendantsByFuelGrade()`: 按油品等级筛选
- `getShiftAttendantsByPaymentMethod()`: 按支付方式筛选

### 3. 更新的页面 (`lib/screens/shift/shift_home_page.dart`)

修改了班次管理页面以使用新的API：

- 替换了原有的统计数据加载逻辑
- 添加了员工详细信息对话框
- 增强了UI显示，支持点击查看员工详情
- 保持了原有的按产品和按员工分类的统计视图

### 4. 集成到ApiService (`lib/services/api/api_service.dart`)

将新的ShiftAttendantApi集成到主API服务中。

## API功能特点

### 智能数据源选择
- 已结束班次优先使用汇总表数据
- 进行中班次使用实时数据

### 员工姓名准确性
- 所有员工姓名均从users表获取真实姓名

### 支付数据完整性
- 提供详细的支付方式统计和分类
- 包含交易笔数和百分比信息

### 多维度筛选
- 支持按员工姓名、油品等级、支付方式筛选
- 支持模糊匹配员工姓名

## 使用示例

### 基本用法

```dart
// 获取班次所有员工数据
final response = await _shiftAttendantApi.getShiftAttendants(shiftId);

// 按员工姓名筛选
final response = await _shiftAttendantApi.getShiftAttendantByName(
  shiftId, 
  'Jayson'
);

// 按油品等级筛选
final response = await _shiftAttendantApi.getShiftAttendantsByFuelGrade(
  shiftId, 
  'BP Ultimate'
);

// 按支付方式筛选
final response = await _shiftAttendantApi.getShiftAttendantsByPaymentMethod(
  shiftId, 
  'cash'
);
```

### 高级筛选

```dart
// 使用查询参数进行多条件筛选
final queryParams = ShiftAttendantQueryParams(
  attendantName: 'Jay',
  fuelGrade: 'BP Ultimate',
  paymentMethod: 'cash',
);

final response = await _shiftAttendantApi.getShiftAttendants(
  shiftId, 
  queryParams: queryParams
);
```

## UI增强功能

### 员工详细信息对话框

- 点击员工统计项目可查看详细信息
- 显示员工总览统计（交易数、油量、销售额、非油品收入）
- 按油品等级分组显示销售明细
- 按支付方式分组显示支付统计

### 支付方式图标和颜色

- 每种支付方式都有对应的图标和颜色
- 支持现金、银行卡、代金券、B2B、Tera等多种支付方式

### 响应式设计

- 对话框支持不同屏幕尺寸
- 滚动支持，适应内容长度

## 错误处理

### API异常处理

- 标准化的错误响应处理
- 友好的用户错误消息
- 详细的调试日志

### 网络连接检查

- 提供连接状态检查方法
- 超时处理和重试机制

## 性能优化

### 数据缓存

- 班次员工数据在班次期间缓存
- 避免重复API调用

### 懒加载

- 员工详细信息按需加载
- 减少初始加载时间

## 测试建议

### 单元测试

1. 测试数据模型的序列化和反序列化
2. 测试API服务的各种查询方法
3. 测试错误处理逻辑

### 集成测试

1. 测试与真实API的交互
2. 测试不同班次状态的数据获取
3. 测试筛选功能的正确性

### UI测试

1. 测试员工详细信息对话框的显示
2. 测试统计数据的正确展示
3. 测试响应式布局

## 部署注意事项

### API环境配置

确保API环境配置正确，包括：
- 基础URL设置
- 认证配置
- 超时设置

### 权限检查

确保应用有访问班次员工数据的权限。

### 数据一致性

确保API返回的数据格式与模型定义一致。

## 未来改进建议

1. 添加实时数据更新功能
2. 支持更多的筛选条件
3. 添加数据导出功能
4. 优化大数据量的处理
5. 添加数据可视化图表

## 相关文件

- `lib/models/shift_attendant_model.dart` - 数据模型
- `lib/services/api/shift_attendant_api.dart` - API服务
- `lib/screens/shift/shift_home_page.dart` - 班次管理页面
- `lib/services/api/api_service.dart` - 主API服务

## 总结

通过集成新的班次员工API，我们为EDC前庭支付系统提供了更详细、更准确的班次员工数据展示功能。用户现在可以：

1. 查看每个员工的详细加油情况
2. 了解不同油品等级的销售情况
3. 分析各种支付方式的使用情况
4. 通过多维度筛选快速找到所需信息

这些功能增强了系统的数据分析能力，为管理决策提供了更好的支持。 