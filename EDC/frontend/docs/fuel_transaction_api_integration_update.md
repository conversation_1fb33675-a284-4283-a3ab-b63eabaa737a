# 燃油交易API集成更新文档

## 概述

本文档记录了对 `auto_print_service.dart` 文件的修改，以便更好地集成燃油交易API (`http://192.168.8.114:8080/api/v1/fuel-transactions`) 返回的数据字段。

**重要更新**: 已删除所有数据生成逻辑，现在**仅允许从API数据中获取**，不允许捏造任何数据。

## API数据结构

根据燃油交易API返回的数据结构，我们主要使用以下字段：

### 关键字段
- `transaction_date_time`: 实际交易时间 (格式: "2025-07-15T04:12:32.648728+07:00")
- `order_serial_no`: 订单序列号 (格式: "ORD12507150413038094")
- `pump_no`: 油泵编号 (格式: 2)
- `metadata`: 包含额外的交易元数据

### 示例数据结构
```json
{
  "id": 1282,
  "transaction_number": "13629656-9b5a-499e-b3ee-db63e44f9741",
  "station_id": 1,
  "amount": 69000,
  "attendant_name": "<PERSON><PERSON><PERSON>",
  "created_at": "2025-07-15T04:12:32.648728+07:00",
  "transaction_date_time": "2025-07-15T04:12:32.648728+07:00",
  "order_serial_no": "ORD12507150413038094",
  "pump_no": 2,
  "metadata": {
    "fcc_completed_at": "2025-07-15T04:12:32Z",
    "fcc_controller_id": "device_com7_pump02",
    "method_of_payment": "Cash",
    "net_amount": 69000,
    "nozzle_hangup_time": "2025-07-15T04:12:32.555205+07:00",
    "payment_time": "2025-07-15T04:13:03.203746+07:00"
  }
}
```

## 修改内容

### 1. 交易日期时间获取优化

#### 修改方法: `_getTransactionDateTime`

**修改前**:
- 从多个数据源搜索交易时间
- 包含订单创建时间等生成逻辑

**修改后**:
- **仅从API数据获取**: 只从 `metadata['transaction_date_time']` 或相关API字段获取
- **不允许生成**: 如果API数据不存在，返回 `null` 并显示 "NO DATE DATA"
- **严格验证**: 只接受来自燃油交易API的真实数据

#### 代码示例（交易时间）
```dart
// 优先使用FCC交易的completed_at字段
final String? completedAtStr = fccData['completed_at'] as String?;
if (completedAtStr != null && completedAtStr.isNotEmpty) {
  try {
    final DateTime completedAt = DateTime.parse(completedAtStr);
    debugPrint('   ✅ 从fcc_transaction[completed_at]获取交易完成时间: $completedAt');
    return completedAt;
  } catch (e) {
    debugPrint('   ❌ 解析fcc_transaction[completed_at]时间失败: $e');
  }
}

// 在打印时的使用
final DateTime? transactionDateTime = _getTransactionDateTime(order);
final String dateStr = transactionDateTime != null 
    ? dateFormat.format(transactionDateTime)
    : 'NO DATE DATA';  // 如果API数据不存在，显示错误信息
```

### 2. 发票号码获取优化

#### 修改方法: `_getInvoiceNumber` (原 `_generateInvoiceNumber`)

**修改前**:
- 使用算法生成发票号码: `INV/Site No./DateTime(Hex)/Running No.`

**修改后**:
- **仅从API数据获取**: 只从 `metadata['order_serial_no']` 或相关API字段获取
- **不允许生成**: 如果API数据不存在，返回 "NO INVOICE DATA"
- **严格验证**: 只接受来自燃油交易API的真实数据

#### 代码示例（发票号码）
```dart
// 仅从API数据获取，不允许生成
final String invoiceNumber = _getInvoiceNumber(order);
// 如果API数据不存在，返回 "NO INVOICE DATA"
```

### 3. 油泵编号获取优化

#### 修改方法: `_extractPumpNumber`

**修改前**:
- 从订单的 `pumpId` 字段中提取数字部分

**修改后**:
- **优先级1**: 从 `metadata['pump_no']` 获取（燃油交易API标准字段）
- **优先级2**: 从 `extInfo['pump_no']` 获取（直接字段）
- **备选方案**: 保持原有的从 `pumpId` 提取数字的逻辑

#### 代码示例
```dart
// 从metadata中获取order_serial_no（燃油交易API标准字段）
final Map<String, dynamic>? metadata = extInfo['metadata'] as Map<String, dynamic>?;
if (metadata != null) {
  final String? orderSerialNo = metadata['order_serial_no'] as String?;
  if (orderSerialNo != null && orderSerialNo.isNotEmpty) {
    debugPrint('   ✅ 从metadata[order_serial_no]获取发票号码: $orderSerialNo');
    return orderSerialNo;
  }
}
```

#### 代码示例（油泵编号）
```dart
// 从metadata中获取pump_no（燃油交易API标准字段）
final Map<String, dynamic>? metadata = extInfo['metadata'] as Map<String, dynamic>?;
if (metadata != null) {
  final dynamic pumpNo = metadata['pump_no'];
  if (pumpNo != null) {
    final String pumpNoStr = pumpNo.toString();
    if (pumpNoStr.isNotEmpty) {
      debugPrint('   ✅ 从metadata[pump_no]获取Pump编号: $pumpNoStr');
      return pumpNoStr;
    }
  }
}
```

## 数据源优先级

### 交易时间 (`transaction_date_time`)
1. `metadata['transaction_date_time']` - 燃油交易API标准字段
2. `extInfo['transaction_date_time']` - 直接字段
3. 其他API时间字段 (`transaction_datetime`, `transactionDateTime`, 等)
4. **`fcc_transaction['completed_at']`** - FCC交易完成时间（对应FccTransaction模型）
5. FCC交易其他时间字段 (`started_at`, `created_at`)
6. 燃油交易数据时间字段（API相关）
7. **如果无API数据**: 返回 `null`，显示 "NO DATE DATA"

### 发票号码 (`order_serial_no`)
1. `metadata['order_serial_no']` - 燃油交易API标准字段
2. `extInfo['order_serial_no']` - 直接字段
3. 其他序列号字段 (`orderSerialNo`, `serial_no`, `invoiceNo`, 等)
4. **如果无API数据**: 返回 "NO INVOICE DATA"

### 油泵编号 (`pump_no`)
1. `metadata['pump_no']` - 燃油交易API标准字段
2. `extInfo['pump_no']` - 直接字段
3. 其他油泵编号字段 (`pumpNo`, `pump_number`, `pump_id`, `dispenser_no`, 等)
4. 从订单 `pumpId` 字段提取数字部分（原有逻辑）
5. 直接使用 `pumpId` 字段值
6. 默认值 "N/A"

## 调试信息

两个修改的方法都包含详细的调试输出：

```dart
debugPrint('🔍 开始生成发票号码...');
debugPrint('   订单ID: ${order.orderId}');
debugPrint('   检查extInfo中的order_serial_no字段...');
debugPrint('   ✅ 从metadata[order_serial_no]获取发票号码: $orderSerialNo');
```

## 兼容性

这些修改保持了向后兼容性：
- 如果新的API字段不存在，系统会回退到原有的逻辑
- 保留了所有原有的数据源搜索机制
- 错误处理机制保持不变

## 测试建议

1. **正常情况测试**: 确保从燃油交易API获取的 `transaction_date_time` 和 `order_serial_no` 正确显示
2. **备选方案测试**: 测试当API字段不存在时，系统是否正确回退到原有逻辑
3. **时间格式测试**: 确保各种时间格式都能正确解析
4. **调试输出测试**: 验证调试信息是否正确显示数据获取过程

## 相关文件

- `lib/services/auto_print_service.dart` - 主要修改文件
- `assets/docs/fuel-transaction-api-documentation.md` - API文档
- `lib/constants/api_constants.dart` - API配置

## 数据来源对应关系

### UI界面 vs 小票打印的数据来源对比

| 字段 | UI界面 (`order_detail_page.dart`) | 小票打印 (`auto_print_service.dart`) | 数据来源差异 |
|------|-----------------------------------|--------------------------------------|-------------|
| **Invoice Number** | `order.orderId` (来自API `order_number`) | 优先 `metadata['order_serial_no']`，备选 `order.orderId` | API专用字段 vs 订单编号 |
| **Date & Time** | `order.createTime` (来自API `created_at`) | 优先 `metadata['transaction_date_time']` 或 `fcc_transaction['completed_at']` | 订单创建时间 vs 实际交易时间 |
| **Pump Number** | 从 `order.items[0]['pump_id']` 提取 | 优先 `metadata['pump_no']`，备选从 `pumpId` 提取数字 | 相同来源，不同获取方式 |

### 关键差异说明

1. **Invoice Number**: 
   - UI显示订单编号 (`order_number`)
   - 小票优先显示序列号 (`order_serial_no`)，如果没有则回退到订单编号

2. **Date & Time**:
   - UI显示订单创建时间 (`created_at`)
   - 小票优先显示实际交易时间 (`transaction_date_time` 或 `fcc_transaction.completed_at`)

3. **数据完整性**:
   - UI使用Order模型的标准字段
   - 小票使用燃油交易API的扩展字段 (`metadata` 和 `extInfo`)

## 更新日期

2025-01-21 - 初始版本，集成燃油交易API的 `transaction_date_time` 和 `order_serial_no` 字段
2025-01-21 - 添加 `pump_no` 字段集成，优化油泵编号获取逻辑
2025-01-21 - 删除所有数据生成逻辑，仅允许从API获取数据
2025-01-21 - 优先使用FCC交易模型的 `completed_at` 字段作为交易时间
2025-01-21 - 分析UI界面与小票打印的数据来源对应关系，添加备选方案 