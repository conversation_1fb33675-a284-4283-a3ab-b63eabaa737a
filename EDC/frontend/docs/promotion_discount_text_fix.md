# Promotion 页面折扣文字显示修复

## 问题描述

在 Promotion 页面的活动卡片中，折扣信息文字"Min. 25 liters above → Get 1 liter/transaction"在一行显示时会越界，影响用户体验。

## 问题位置

**文件**: `lib/screens/home/<USER>
**方法**: `_buildDiscountInfo(String minPurchase, String discount)`
**问题行**: 第380行的文字显示

## 解决方案

将原来的单行文字显示改为两行显示，使用 Column 布局替代原来的单个 Text 组件。

### 修改前

```dart
child: Row(
  mainAxisSize: MainAxisSize.min,
  children: <Widget>[
    const Icon(
      Icons.local_offer,
      color: BPColors.primary,
      size: 16,
    ),
    const SizedBox(width: 8),
    Text(
      'Min. $minPurchase → Get $discount',  // 单行显示，容易越界
      style: EDCTextStyles.bodyText.copyWith(
        fontSize: 13,
        fontWeight: FontWeight.w600,
        color: BPColors.primary,
      ),
    ),
  ],
),
```

### 修改后

```dart
child: Row(
  children: <Widget>[
    const Icon(
      Icons.local_offer,
      color: BPColors.primary,
      size: 16,
    ),
    const SizedBox(width: 8),
    Expanded(                              // 添加 Expanded 包装
      child: Column(                       // 使用 Column 分行显示
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            'Min. $minPurchase',           // 第一行：最低购买量
            style: EDCTextStyles.bodyText.copyWith(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: BPColors.primary,
            ),
          ),
          const SizedBox(height: 2),       // 行间距
          Text(
            'Get $discount',               // 第二行：获得折扣
            style: EDCTextStyles.bodyText.copyWith(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: BPColors.primary,
            ),
          ),
        ],
      ),
    ),
  ],
),
```

## 主要改进

### 1. 布局优化
- **从 Row 改为 Row + Column 组合**：保持图标在左侧，文字在右侧分两行显示
- **添加 Expanded 包装**：确保文字区域能够充分利用可用空间
- **移除 mainAxisSize.min**：允许 Row 占用更多空间

### 2. 文字分离
- **第一行**：`"Min. $minPurchase"` - 显示最低购买要求
- **第二行**：`"Get $discount"` - 显示获得的折扣
- **行间距**：2px 的间距，保持紧凑但清晰的视觉效果

### 3. 样式保持
- **字体大小**：保持 13px
- **字体粗细**：保持 FontWeight.w600
- **颜色**：保持 BPColors.primary
- **图标**：保持原有的 local_offer 图标和位置

### 4. 容器调整
- **垂直内边距**：从 8px 增加到 10px，为两行文字提供更好的视觉空间
- **其他样式**：保持原有的背景色、边框和圆角

## 显示效果

### 修改前
```
🏷️ Min. 25 liters above → Get 1 liter/transaction  [越界]
```

### 修改后
```
🏷️ Min. 25 liters above
   Get 1 liter/transaction
```

## 适用场景

这个修复适用于所有使用 `_buildDiscountInfo` 方法的地方：

1. **Free 1 Liter 活动**：
   - 第一行：`"Min. 25 liters above"`
   - 第二行：`"Get 1 liter / transaction"`

2. **Promo IRIT 活动**：
   - 第一行：`"Min. Various"`
   - 第二行：`"Get Multiple rates"`

## 测试验证

- ✅ 编译无错误
- ✅ 所有单元测试通过
- ✅ 文字显示不再越界
- ✅ 视觉效果更清晰
- ✅ 功能保持正常

## 兼容性

- ✅ 保持原有的样式风格
- ✅ 图标位置和大小不变
- ✅ 容器背景和边框保持一致
- ✅ 不影响其他组件的布局

## 总结

通过将单行文字改为两行显示，成功解决了折扣信息越界的问题：

1. **解决了越界问题**：文字不再超出容器边界
2. **提升了可读性**：信息分层更清晰
3. **保持了一致性**：样式和功能完全保持
4. **改善了用户体验**：视觉效果更好

这个修复确保了在不同屏幕尺寸和文字长度下，折扣信息都能正确显示。
