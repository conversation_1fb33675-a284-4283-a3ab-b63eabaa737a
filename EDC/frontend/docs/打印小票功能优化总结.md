# 打印订单小票功能优化总结

## 问题分析与解决方案

### 1. 会员信息打印问题

**问题：** 会员信息未在小票中正确显示

**解决方案：**
- 修改 `AutoPrintService.printOrderReceipt()` 方法，从 `order.extInfo['member_info']` 中提取会员信息
- 优先显示会员姓名、电话和车牌号
- 如果没有会员信息，则显示普通客户名称

**代码位置：** `lib/services/auto_print_service.dart` 第144行

**实现细节：**
```dart
// 提取会员信息
final memberInfo = metadata['member_info'] as Map<String, dynamic>?;

// 优先显示会员信息，然后是客户名称
if (memberInfo != null && memberInfo.isNotEmpty) {
  final memberName = memberInfo['name'] as String?;
  final memberPhone = memberInfo['phone'] as String?;
  final plateNumbers = memberInfo['plateNumbers'] as List<dynamic>?;
  
  // 打印会员姓名、电话、车牌号
}
```

### 2. 燃油类型来源问题

**问题：** 燃油类型显示不准确，FCCNozzle 配置与 PaymentTransactionData 不一致

**解决方案：**
- 优先使用 FCCNozzle 的真实配置（`fuelType` 和 `grade`）
- 如果 FCCNozzle 不可用，使用 PaymentTransactionData 的交易数据
- 移除 "(Est.)" 估算标识，因为交易数据是实际使用的燃油类型

**代码位置：** `lib/widgets/order_detail_widget.dart` 第210-280行

**修复前：**
```dart
fuelType = '${widget.paymentData.fuelType} (Est.)';
fuelGrade = '${widget.paymentData.fuelGrade} (Est.)';
```

**修复后：**
```dart
fuelType = widget.paymentData.fuelType.isNotEmpty ? widget.paymentData.fuelType : 'Unknown Fuel';
fuelGrade = widget.paymentData.fuelGrade.isNotEmpty ? widget.paymentData.fuelGrade : 'Unknown Grade';
```

### 3. 印尼货币格式问题

**问题：** 货币格式不符合印尼习惯，使用小数点和逗号分隔符

**解决方案：**
- 修改为整数格式（无小数点）
- 使用点号（.）作为千位分隔符
- 格式：`Rp 12.370` 而不是 `Rp 12,370.00`

**修复位置：**
1. `lib/services/auto_print_service.dart` - 小票打印格式
2. `lib/models/order.dart` - Order 模型格式化方法

**修复前：**
```dart
final NumberFormat currencyFormat = NumberFormat('#,##0.00', 'id_ID');
```

**修复后：**
```dart
final NumberFormat currencyFormat = NumberFormat('#,##0', 'id_ID');
// 使用时: currencyFormat.format(amount).replaceAll(',', '.')
```

## 调用位置总结

### 自动打印调用
1. **现金支付页面** (`cash_payment_page.dart:307`)
2. **银行卡支付页面** (`bank_card_payment_page.dart:411`)
3. **会员支付页面** (`member_payment_page.dart`)

### 手动打印调用
1. **订单详情页面** (`order_detail_page.dart:149`)
2. **小票预览页面** (`receipt_preview_page.dart:83`)

## 会员信息传递流程

```
支付页面 → CreateOrderRequest.metadata['member_info'] → Order.extInfo['member_info'] → 小票打印
```

## 测试验证

### 会员信息测试
- [ ] 有会员信息的订单：显示会员姓名、电话、车牌号
- [ ] 无会员信息的订单：显示普通客户名称
- [ ] 会员信息不完整：正确处理缺失字段

### 燃油类型测试
- [ ] FCCNozzle 可用：显示真实配置的燃油类型
- [ ] FCCNozzle 不可用：显示交易数据的燃油类型
- [ ] 无燃油信息：显示 "Unknown Fuel" / "Unknown Grade"

### 货币格式测试
- [ ] 小票金额格式：`Rp 12.370`（整数，点号分隔）
- [ ] 订单详情格式：与小票保持一致
- [ ] 大额金额测试：`Rp 1.234.567`

## 相关文件

### 核心文件
- `lib/services/auto_print_service.dart` - 主要打印逻辑
- `lib/widgets/order_detail_widget.dart` - 订单详情显示
- `lib/models/order.dart` - 订单模型

### 支付页面
- `lib/screens/payment/cash_payment_page.dart`
- `lib/screens/payment/bank_card_payment_page.dart`
- `lib/screens/payment/member_payment_page.dart`

### 打印相关
- `lib/screens/order/order_detail_page.dart`
- `lib/screens/printing/receipt_preview_page.dart`

## 注意事项

1. **会员信息缓存：** 确保会员信息正确传递到订单元数据中
2. **燃油类型映射：** FCCNozzle 和 PaymentTransactionData 的燃油类型字段需要保持一致
3. **货币格式一致性：** 确保整个应用的货币格式保持统一
4. **打印机兼容性：** 验证不同型号打印机的格式兼容性

## 后续优化建议

1. **燃油类型标准化：** 建立统一的燃油类型映射表
2. **小票模板化：** 考虑使用模板系统管理不同类型的小票格式
3. **多语言支持：** 为小票添加多语言支持
4. **打印质量优化：** 根据不同打印机型号优化字体和布局 