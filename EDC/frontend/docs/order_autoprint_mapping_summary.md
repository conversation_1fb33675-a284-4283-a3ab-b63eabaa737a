# Order 字段到 AutoPrintService 映射方法总结

## 概述

本文档总结了所有 `Order.dart` 字段到 `AutoPrintService.dart` 方法的映射关系，提供统一的调用接口。

## 映射关系总览

| Order字段 | AutoPrintService方法 | 返回类型 | 功能描述 |
|-----------|---------------------|---------|----------|
| `customerName` | `getCustomerNameAsVehicleId(order)` | `String` | 获取车辆ID/车牌号 |
| `customerId` | `getCustomerIdAsCustomerInfo(order)` | `Map<String, String>` | 获取客户信息 |

## 详细映射说明

### 1. customerName → 车辆ID映射

```dart
// Order.dart中的访问方式
String customerName = order.customerName ?? '';

// AutoPrintService中的等效方法
String vehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
```

**功能**: 从客户名称中提取车辆信息，支持多种格式和数据源

### 2. customerId → 客户信息映射

```dart
// Order.dart中的访问方式
int? customerId = order.customerId;

// AutoPrintService中的等效方法
Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
```

**功能**: 获取完整的客户信息，包括姓名和电话

## 使用示例

### 综合使用示例

```dart
class OrderProcessor {
  void processOrder(Order order) {
    // 获取车辆信息
    String vehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
    
    // 获取客户信息
    Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
    
    print('处理订单: ${order.orderId}');
    print('车辆ID: $vehicleId');
    print('客户姓名: ${customerInfo['name']}');
    print('客户电话: ${customerInfo['phone']}');
  }
}
```

### 打印服务中的使用

```dart
class PrintService {
  void printReceipt(Order order) {
    // 使用映射方法获取所有需要的信息
    String vehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
    Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
    
    // 打印小票
    print('=== 小票 ===');
    print('订单号: ${order.orderId}');
    print('车辆: $vehicleId');
    print('客户: ${customerInfo['name']}');
    print('电话: ${customerInfo['phone']}');
    print('金额: ${order.finalAmount}');
  }
}
```

### 数据对比示例

```dart
void compareOrderData(Order order) {
  print('=== 原始Order字段 ===');
  print('customerName: ${order.customerName}');
  print('customerId: ${order.customerId}');
  
  print('=== 映射后的增强信息 ===');
  String vehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
  Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
  
  print('车辆ID: $vehicleId');
  print('客户信息: $customerInfo');
}
```

## 数据处理优势

### 1. 智能数据提取
- **原始方式**: 直接访问字段值
- **映射方式**: 智能从多个数据源提取和处理

### 2. 格式化和验证
- **原始方式**: 原始数据，可能需要额外处理
- **映射方式**: 自动格式化和验证，返回标准格式

### 3. 错误处理
- **原始方式**: 可能返回null或空值
- **映射方式**: 提供默认值和错误处理

### 4. 多数据源支持
- **原始方式**: 仅限于Order对象的直接字段
- **映射方式**: 支持从extInfo、metadata、member_info等多个数据源获取

## 错误处理策略

### 车辆ID获取失败
```dart
String vehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
// 失败时返回: order.customerName ?? 'UNKNOWN VEHICLE'
```

### 客户信息获取失败
```dart
Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
// 失败时返回: {'name': 'ANONIM', 'phone': '**01010'}
```

## 调试和日志

两个映射方法都提供详细的调试信息：

```
🔄 映射Order.customerName到车辆ID...
   Order.customerName: Customer B1234ABC
   映射结果: B1234ABC

🔄 映射Order.customerId到客户信息...
   Order.customerId: 12345
   映射结果: {name: 张三, phone: **67890}
```

## 性能考虑

- 映射方法会进行多层数据查找，比直接字段访问稍慢
- 建议在需要增强处理的场景使用
- 对于简单的字段访问，可以继续使用原始方式

## 兼容性保证

- 所有映射方法都是静态方法，不影响现有代码
- 原有的Order字段访问方式保持不变
- 可以根据需要选择使用原始方式或映射方式

## 扩展性

此映射模式可以扩展到其他Order字段：

```dart
// 未来可能的扩展
static String getPaymentMethodAsText(Order order) { ... }
static List<String> getItemsAsProductList(Order order) { ... }
static DateTime getCreateTimeAsLocalTime(Order order) { ... }
```

## 最佳实践

1. **选择合适的方法**: 根据需要的数据处理复杂度选择
2. **错误处理**: 始终检查返回值，特别是默认值情况
3. **性能优化**: 避免在循环中重复调用映射方法
4. **调试支持**: 利用内置的调试信息进行问题排查
5. **文档更新**: 使用新方法时更新相关文档

## 相关文档

- [Order.customerName 到车辆ID映射](customer_name_vehicle_mapping.md)
- [Order.customerId 到客户信息映射](customer_id_info_mapping.md)
- [AutoPrintService API文档](auto_print_service_api.md) 