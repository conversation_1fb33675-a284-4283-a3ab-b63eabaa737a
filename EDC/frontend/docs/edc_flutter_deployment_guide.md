# EDC Flutter 应用部署和运行指南

## 环境准备

### 1. 确保开发环境正常
```bash
flutter doctor
```
确保所有项目都显示 ✅，特别是：
- Flutter SDK
- Android toolchain
- Connected device

### 2. 检查连接的设备
```bash
flutter devices
```
应该能看到类似这样的 EDC 设备：
```
P2 EU (mobile)    • PB10212120315 • android-arm    • Android 7.1.1 (API 25)
```

## 运行应用

### 方法 1：直接运行到 EDC 设备
```bash
# 使用设备 ID 运行
flutter run -d PB10212120315

# 或者使用设备名称
flutter run -d "P2 EU"
```

### 方法 2：交互式选择设备
```bash
flutter run
```
然后从列表中选择 EDC 设备

### 方法 3：调试模式运行
```bash
# 启用详细日志
flutter run -d PB10212120315 --verbose

# 或者使用 debug 模式
flutter run -d PB10212120315 --debug
```

## 热重载和调试

### 热重载
应用运行后，在终端中按：
- `r` - 热重载 (hot reload)
- `R` - 热重启 (hot restart)
- `q` - 退出应用

### 实时日志查看
```bash
# 查看设备日志
flutter logs -d PB10212120315

# 或者使用 adb 查看详细日志
adb logcat | grep flutter
```

## 常见问题和解决方案

### 问题 1：设备未检测到
**解决方案：**
```bash
# 检查 ADB 连接
adb devices

# 重启 ADB 服务
adb kill-server
adb start-server

# 重新检测设备
flutter devices
```

### 问题 2：构建失败
**解决方案：**
```bash
# 清理构建缓存
flutter clean

# 重新获取依赖
flutter pub get

# 重新构建
flutter build apk --debug
```

### 问题 3：权限问题
**解决方案：**
确保 EDC 设备已启用：
- USB 调试模式
- 安装未知来源应用
- 开发者选项已开启

### 问题 4：网络连接问题
**解决方案：**
```bash
# 检查网络配置
adb shell ping 8.8.8.8

# 检查应用网络权限
adb shell pm list permissions | grep network
```

## 性能优化

### 1. 构建优化版本
```bash
# 构建 release 版本
flutter build apk --release

# 安装到设备
flutter install -d PB10212120315
```

### 2. 分析包大小
```bash
flutter build apk --analyze-size
```

### 3. 性能分析
```bash
# 启用性能分析
flutter run -d PB10212120315 --profile
```

## 特定于 EDC 的配置

### 1. 检查 EDC 设备规格
```bash
# 查看设备信息
adb shell getprop ro.product.model
adb shell getprop ro.build.version.release

# 查看可用存储空间
adb shell df -h
```

### 2. EDC 特定设置
确保在 `android/app/src/main/AndroidManifest.xml` 中配置：
```xml
<!-- 网络权限 -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

<!-- 打印机权限 -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

<!-- 存储权限 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

### 3. 检查应用运行状态
```bash
# 查看应用进程
adb shell ps | grep com.bp.edc

# 查看应用日志
adb logcat | grep "BP_EDC"
```

## 开发工作流

### 1. 代码修改后的标准流程
```bash
# 1. 保存当前状态
git add .
git commit -m "feat: 更新 nozzle 界面显示配置"

# 2. 运行到设备
flutter run -d PB10212120315

# 3. 测试修改效果
# 使用热重载进行快速迭代

# 4. 如果需要完整重启
# 按 'R' 或者重新运行应用
```

### 2. 调试特定功能
```bash
# 启用调试模式
flutter run -d PB10212120315 --debug

# 在代码中添加调试输出
debugPrint('🔍 Nozzle info: ${nozzle.toString()}');
```

### 3. 测试不同配置
修改 `NozzleDisplayConfig` 后：
1. 保存文件
2. 按 `r` 进行热重载
3. 检查界面变化
4. 如果热重载不生效，按 `R` 进行热重启

## 部署到生产环境

### 1. 构建生产版本
```bash
# 构建 release APK
flutter build apk --release --split-per-abi

# 或者构建 AAB (推荐)
flutter build appbundle --release
```

### 2. 安装到多个 EDC 设备
```bash
# 批量安装脚本
for device in $(adb devices | grep -v "List" | awk '{print $1}'); do
    echo "Installing to device: $device"
    adb -s $device install build/app/outputs/flutter-apk/app-release.apk
done
```

## 监控和日志

### 1. 实时监控
```bash
# 监控应用性能
flutter run -d PB10212120315 --profile

# 查看内存使用
adb shell dumpsys meminfo com.bp.edc
```

### 2. 日志收集
```bash
# 收集应用日志
adb logcat -s flutter > edc_app_logs.txt

# 收集系统日志
adb logcat > edc_system_logs.txt
```

## 快速参考命令

```bash
# 基本命令
flutter devices                    # 查看设备
flutter run -d DEVICE_ID          # 运行到指定设备
flutter logs -d DEVICE_ID         # 查看日志
flutter clean                     # 清理项目
flutter pub get                   # 获取依赖

# 调试命令
flutter run --debug               # 调试模式
flutter run --profile             # 性能分析模式
flutter run --release             # 发布模式

# 构建命令
flutter build apk --debug         # 构建调试版本
flutter build apk --release       # 构建发布版本
flutter build appbundle          # 构建 AAB 包
```

记住：修改 nozzle 界面配置后，通常只需要热重载 (`r`) 就能看到效果！ 