# 表格格式列宽修复文档

## 问题描述
用户反馈打印收据时，Amount列中的金额会发生换行，例如"172.816"中的"16"换到了下一行。经过分析发现，当金额超过列宽限制时，会发生字符串截断和换行。

## 问题根因分析

### 原始问题
- **原列宽设置**: `[11, 5, 7, 9]` (Product, Qty, Price, Amount)
- **Amount列宽度**: 9个字符
- **问题金额示例**: 100172816 格式化为 "100.172.816" (11个字符)
- **换行结果**: 前9个字符 "100.172.8" 在第一行，"16" 在第二行

### 格式化规则
使用 `NumberFormat('#,##0', 'id_ID')` 进行格式化：
- 172816 → "172.816" (7个字符)
- 1728160 → "1.728.160" (9个字符)
- 17281600 → "17.281.600" (10个字符) ❌ 超出9字符限制
- 100172816 → "100.172.816" (11个字符) ❌ 超出9字符限制

## 解决方案

### 修复策略
将整个打印收据改为统一的表格格式，为不同部分分配合适的列宽：

1. **产品表格**: 增加Amount列宽度到12个字符
2. **促销信息**: 使用独立的列宽设置
3. **总计部分**: 使用更宽的列宽设置

### 具体修改

#### 1. 产品表格列宽
```dart
// 修改前
final List<int> columnWidths = [11, 5, 7, 9];

// 修改后
final List<int> productColumnWidths = [11, 5, 7, 12];
```

#### 2. 促销信息列宽
```dart
// 修改前
<int>[22, 13]

// 修改后
final List<int> promotionColumnWidths = [20, 15];
```

#### 3. 总计部分列宽
```dart
// 修改前
<int>[22, 13]

// 修改后
final List<int> totalsColumnWidths = [20, 15];
```

## 测试结果

### 支持的金额范围
| 金额 | 格式化结果 | 长度 | 产品表格(12字符) | 总计部分(15字符) |
|------|------------|------|------------------|------------------|
| 172,816 | "172.816" | 7 | ✅ 正常 | ✅ 正常 |
| 1,728,160 | "1.728.160" | 9 | ✅ 正常 | ✅ 正常 |
| 17,281,600 | "17.281.600" | 10 | ✅ 正常 | ✅ 正常 |
| 100,172,816 | "100.172.816" | 11 | ✅ 正常 | ✅ 正常 |
| 1,000,000,000 | "1.000.000.000" | 13 | ❌ 会换行 | ✅ 正常 |

### 极端情况
- **产品表格**: 支持高达999,999,999 (9.99亿) 的金额
- **总计部分**: 支持高达999,999,999,999 (9999亿) 的金额

## 代码修改位置

### 文件：`lib/services/auto_print_service.dart`

#### 产品表格部分 (行158-175)
```dart
// 产品表格列宽设置 - 为Amount列分配更多空间
final List<int> productColumnWidths = [11, 5, 7, 12];

await printer.printColumnsText(
  <String>['Product', 'Qty', 'Price', 'Amount'],
  productColumnWidths,
  <int>[TableAlignment.center, TableAlignment.center, TableAlignment.center, TableAlignment.center],
);

await printer.printColumnsText(
  <String>[productInfo['name'].toString(), volumeStr, priceStr, amountStr],
  productColumnWidths,
  <int>[TableAlignment.left, TableAlignment.center, TableAlignment.right, TableAlignment.right],
);
```

#### 促销信息部分 (行180-190)
```dart
// 促销信息表格列宽设置
final List<int> promotionColumnWidths = [20, 15];

await printer.printColumnsText(
  <String>[promotionName, '-Rp $discountStr'],
  promotionColumnWidths,
  <int>[TableAlignment.left, TableAlignment.right],
);
```

#### 总计部分 (行195-230)
```dart
// 总计部分表格列宽设置 - 为金额列分配更多空间
final List<int> totalsColumnWidths = [20, 15];

// 所有总计相关的printColumnsText调用都使用totalsColumnWidths
```

## 优势

1. **统一格式**: 整个收据使用一致的表格格式
2. **自适应宽度**: 不同部分使用适合的列宽
3. **更好的可读性**: 金额不再换行，提升用户体验
4. **易于维护**: 列宽设置集中管理，便于后续调整

## 注意事项

1. **总宽度**: 确保所有列宽总和不超过打印纸张宽度
2. **对齐方式**: 保持金额右对齐，文本左对齐
3. **极端情况**: 对于超大金额，可能需要进一步优化或使用科学记数法
4. **测试**: 建议在实际设备上测试不同金额范围的打印效果

## 更新日期
2024-12-19 