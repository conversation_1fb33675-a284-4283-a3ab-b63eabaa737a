# 全屏员工详情弹窗功能

## 功能概述

将班次管理中的员工详细信息弹框改为全屏显示，并采用小票打印样式的专业排版，提供更好的用户体验和信息展示效果。

## 主要改进

### 1. 全屏显示
- 从小型对话框改为全屏弹窗
- 使用 `Dialog.fullscreen()` 和 `Scaffold` 结构
- 提供更大的显示空间和更好的阅读体验

### 2. 专业AppBar设计
- 使用BP品牌色彩的AppBar
- 显示员工头像、姓名和工号
- 左侧返回按钮，符合移动端操作习惯

### 3. 小票样式布局
- 采用热敏打印小票的经典样式
- 使用等宽字体（Courier）确保对齐
- 分隔线和标题居中显示
- 左右对齐的数据行布局

## 界面结构

### AppBar区域
```dart
AppBar(
  title: Row(
    children: [
      CircleAvatar(员工头像),
      员工姓名和工号信息,
    ],
  ),
  backgroundColor: BPColors.primary,
  leading: 返回按钮,
)
```

### 内容区域

#### 1. 业绩概览部分 (PERFORMANCE SUMMARY)
- **样式**: 小票样式居中标题
- **分隔线**: 等号分隔线 (================================)
- **内容**: 关键指标的小票行布局
  - Transactions (交易笔数)
  - Volume (销售量)
  - Sales Amount (销售金额)
  - Dry Income (非油品收入)
  - GRAND TOTAL (总计金额) - 粗体显示

#### 2. 油品销售明细部分 (FUEL SALES BY GRADE)
- **样式**: 小票样式居中标题
- **分隔线**: 等号分隔线 (================================)
- **内容**: 按油品等级分类的销售数据
  - 油品名称居中显示
  - 缩进的详细信息（Trans, Volume, Amount, Price）
  - 虚线分隔不同油品 (- - - - - - - - - - - - - - - -)

#### 3. 支付方式统计部分 (PAYMENT METHODS)
- **样式**: 小票样式居中标题
- **分隔线**: 等号分隔线 (================================)
- **内容**: 支付方式列表
  - 英文支付方式名称（自动转换）
  - 左右对齐的金额显示
  - 缩进的交易笔数和百分比

## 设计特点

### 1. 小票样式设计
- 简洁的白色背景，细边框
- 统一的圆角设计 (8px)
- 模拟热敏打印小票的视觉效果

### 2. 排版系统
- 使用 Courier 等宽字体确保对齐
- 居中的标题和分隔线
- 左右对齐的数据行
- 一致的行间距和缩进

### 3. 分隔线设计
- 等号分隔线 (================================) 用于主要部分
- 虚线分隔 (- - - - - - - - - - - - - - - -) 用于子项目
- 短横线分隔 (--------------------------------) 用于小计

### 4. 文本层次
- 标题使用大写字母和加粗
- 数据行使用等宽字体
- 总计行使用粗体强调
- 子项目使用缩进显示

## 技术实现

### 1. 全屏弹窗结构
```dart
Dialog.fullscreen(
  child: Scaffold(
    appBar: AppBar(...),
    body: SingleChildScrollView(
      child: Column(
        children: [
          _buildCompactOverviewCards(attendant),
          _buildCompactFuelSalesSection(attendant),
          _buildCompactPaymentMethodsSection(attendant),
        ],
      ),
    ),
  ),
)
```

### 2. 紧凑型卡片组件
```dart
Widget _buildCompactStatCard({
  required IconData icon,
  required String label,
  required String value,
  required Color color,
  bool isHighlight = false,
}) {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: isHighlight ? color.withOpacity(0.1) : Colors.grey[50],
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: ...),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            Text(label, style: ...),
          ],
        ),
        Text(value, style: ...),
      ],
    ),
  );
}
```

### 3. 响应式布局
- 使用 `Expanded` 和 `Row` 实现响应式网格
- 自动适应不同屏幕尺寸
- 保持一致的间距和比例

## 用户体验改进

### 1. 更好的信息展示
- 全屏显示提供更多空间
- 分类清晰，信息层次分明
- 重要数据突出显示

### 2. 移动端优化
- 符合移动端操作习惯的导航
- 适合触摸操作的按钮和间距
- 流畅的滚动体验

### 3. 视觉改进
- 专业的商务风格设计
- 一致的视觉语言
- 清晰的数据呈现

## 使用方法

1. 在班次管理页面，确保有活跃班次
2. 在统计模式中选择 "By Employee"
3. 点击任意员工的统计卡片
4. 查看全屏的员工详细信息
5. 使用左上角的返回按钮关闭弹窗

## 兼容性

- 适用于所有Android设备
- 支持不同屏幕尺寸
- 保持与现有功能的兼容性

## 性能优化

- 使用 `SingleChildScrollView` 支持长内容滚动
- 条件渲染减少不必要的组件
- 优化的布局结构减少重绘

## 相关文件

- `lib/screens/shift/shift_home_page.dart` - 主要实现文件
- `lib/constants/bp_colors.dart` - 颜色定义
- `lib/utils/format_utils.dart` - 数据格式化
- `lib/models/shift_attendant_model.dart` - 数据模型

## 字体大小规范

### AppBar区域
- 员工姓名：20px (Bold)
- 工号信息：14px (Regular)

### 卡片标题
- 所有卡片标题：18px (Bold)

### 内容区域
- 统计卡片标签：13px (Medium)
- 统计卡片数值：16px (Bold)
- 油品名称：17px (Bold)
- 油品详细信息标签：12px (Regular)
- 油品详细信息数值：14px (Semi-bold)
- 支付方式名称：16px (Semi-bold)
- 支付方式详细信息：13px (Regular)
- 金额显示：16px (Bold)

### 空状态提示
- 提示文本：16px (Regular)

## 支付方式英文转换

系统会自动将支付方式名称转换为英文显示：

- `tunai` / `cash` → `CASH`
- `mandiri` → `MANDIRI`
- `bca` → `BCA`
- `bni` → `BNI`
- `bri` → `BRI`
- `cimb` → `CIMB`
- `voucher` → `VOUCHER`
- `b2b` → `B2B`
- `tera` → `TERA`
- `debit` → `DEBIT CARD`
- `credit` → `CREDIT CARD`

## 更新日志

- **v1.0.0** - 初始实现全屏员工详情弹窗
- **v1.1.0** - 添加紧凑型排版和专业设计
- **v1.2.0** - 优化字体大小，提高可读性，确保所有内容为英文
- **v1.3.0** - 重新设计为小票打印样式，添加支付方式英文转换功能 