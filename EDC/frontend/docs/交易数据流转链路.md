# 交易数据流转链路

## 概述

本文档详细描述了 EDC 前庭支付系统中交易数据的完整流转链路，以及从松散的 `Map<String, dynamic>` 迁移到强类型 `PaymentTransactionData` DTO 的重构过程。

## 系统架构图

```mermaid
graph TD
    A[FCC设备] --> B[BOS API]
    B --> C[FuelTransaction Model]
    C --> D[TransactionNavigationService]
    D --> E[PaymentTransactionData DTO]
    E --> F[GoRouter]
    F --> G[PaymentMethodSelectionPage]
    G --> H[CashPaymentPage]
    
    I[FuelTransactionDetailPage] --> J[员工信息 SharedPreferences]
    J --> E
    
    K[Legacy Map Data] --> L[fromLegacyMap Factory]
    L --> E
    
    style E fill:#e1f5fe
    style C fill:#f3e5f5
    style H fill:#e8f5e8
```

## 数据流转链路详解

### 1. 数据源阶段

#### 1.1 FCC 设备数据
```dart
// FCC设备完成加油，生成交易数据
final fccTransaction = {
  'pump_id': 'FCC-PUMP-001',
  'nozzle_id': '01',
  'amount': 150000.0,
  'volume': 10.5,
  'fuel_type': 'Pertamax',
  // ...
};
```

#### 1.2 BOS API 数据处理
```dart
// BOS系统将FCC数据转换为FuelTransaction
final fuelTransaction = FuelTransaction(
  id: 12345,
  transactionNumber: 'TXN-2024-001',
  stationId: 1,
  pumpId: 'FCC-PUMP-001',
  nozzleId: '01',
  amount: 150000.0,
  volume: 10.5,
  fuelType: 'Pertamax',
  fuelGrade: 'Premium',
  status: 'pending',
  // ...
);
```

### 2. 业务逻辑层

#### 2.1 TransactionNavigationService
负责处理 Nozzle 点击事件和交易数据查询：

```dart
class TransactionNavigationService {
  /// 导航到单笔交易结算页面
  Future<void> _navigateToSingleTransactionSettlement(
    Nozzle nozzle, 
    FuelTransaction transaction
  ) async {
    // 获取员工信息
    final prefs = await SharedPreferences.getInstance();
    final employeeId = prefs.getString('employee_id') ?? 'unknown';
    final employeeNo = prefs.getString('employee_no') ?? 'unknown';
    
    // 🔄 数据转换：FuelTransaction → PaymentTransactionData
    final paymentData = PaymentTransactionData.fromFuelTransaction(
      transaction: transaction,
      employeeId: employeeId,
      employeeNo: employeeNo,
    );
    
    // 导航到支付页面
    if (context.mounted) {
      await context.push('/payment', extra: paymentData);
    }
  }
}
```

#### 2.2 FuelTransactionDetailPage
从交易详情页发起支付：

```dart
// 确认交易按钮点击
onPressed: () async {
  // 获取员工信息
  final prefs = await SharedPreferences.getInstance();
  final employeeId = prefs.getString('employee_id') ?? 'unknown';
  final employeeNo = prefs.getString('employee_no') ?? 'unknown';
  
  // 🔄 数据转换：FuelTransaction → PaymentTransactionData  
  final paymentData = PaymentTransactionData.fromFuelTransaction(
    transaction: transaction,
    employeeId: employeeId,
    employeeNo: employeeNo,
  );

  // 导航到支付页面
  if (context.mounted) {
    context.push('/payment', extra: paymentData);
  }
}
```

### 3. 路由层 (GoRouter)

#### 3.1 统一的路由处理
```dart
GoRoute(
  path: '/payment',
  builder: (context, state) {
    final extra = state.extra;
    PaymentTransactionData? paymentData;
    
    if (extra is PaymentTransactionData) {
      // ✅ 新版本：直接使用
      paymentData = extra;
    } else if (extra is Map<String, dynamic>) {
      // 🔄 兼容性：旧版本Map数据转换
      paymentData = PaymentTransactionData.fromLegacyMap(extra);
    }
    
    return PaymentHomePage(paymentData: paymentData);
  },
  routes: [
    GoRoute(
      path: 'methods',
      builder: (context, state) {
        final paymentData = state.extra as PaymentTransactionData?;
        return PaymentMethodSelectionPage(paymentData: paymentData);
      },
    ),
    GoRoute(
      path: 'cash',
      builder: (context, state) {
        final paymentData = state.extra as PaymentTransactionData?;
        return CashPaymentPage(paymentData: paymentData);
      }
    ),
  ],
),
```

### 4. 展示层

#### 4.1 PaymentMethodSelectionPage
支付方式选择：

```dart
void _handlePaymentMethodSelected(PaymentMethod method) {
  if (widget.paymentData == null) return;
  
  // 🔄 数据增强：添加支付方式ID
  final paymentDataWithMethod = widget.paymentData!.copyWith(
    paymentMethodId: method.id,
  );
  
  // 根据支付方式类型导航
  switch (method.type) {
    case 'CASH':
      context.push('/payment/cash', extra: paymentDataWithMethod);
      break;
    case 'BANK_CARD':
      context.push('/payment/bank_card', extra: paymentDataWithMethod);
      break;
  }
}
```

#### 4.2 CashPaymentPage
现金支付页面：

```dart
class CashPaymentPage extends ConsumerStatefulWidget {
  final PaymentTransactionData? paymentData;

  @override
  void initState() {
    super.initState();
    // ✅ 直接从PaymentTransactionData获取数据
    _amountDue = widget.paymentData?.totalAmount ?? 0.0;
    _nozzleNumber = '${widget.paymentData?.pumpId ?? '-'}/${widget.paymentData?.nozzleId ?? '-'}';
    _fuelType = widget.paymentData?.fuelType ?? '-';
    _fuelGrade = widget.paymentData?.fuelGrade ?? '-';
    
    // 格式化单价
    final double? unitPriceDouble = widget.paymentData?.unitPrice;
    if (unitPriceDouble != null && unitPriceDouble > 0) {
        _unitPrice = _currencyFormatter.format(unitPriceDouble);
    } else {
        _unitPrice = '-';
    }
  }
}
```

## 数据模型对比

### 迁移前：Map<String, dynamic>

#### 问题：
1. **类型不安全**：需要大量的类型转换和空值检查
2. **字段名不统一**：不同数据源使用不同的字段名
3. **维护困难**：每个页面都需要知道所有可能的字段结构
4. **容易出错**：拼写错误、数据结构变化导致运行时错误

#### 代码示例：
```dart
// ❌ 复杂的数据解析
final double totalAmount = double.tryParse(
  transactionData?['payment']?['totalAmount']?.toString() ?? '0'
) ?? 0.0;

// ❌ 需要处理多种可能的字段名
final candidates = [
  transactionData['pumpId'],
  transactionData['dispenserId'], 
  transactionData['pump_id'],
  transactionData['dispenser_id'],
  transactionData['pumpNumber'],
  transactionData['dispenserNumber'],
  transactionData['metadata']?['pump_id'],
];

for (final candidate in candidates) {
  if (candidate != null && candidate.toString().isNotEmpty) {
    return candidate.toString();
  }
}
```

### 迁移后：PaymentTransactionData

#### 优势：
1. **类型安全**：编译时错误检查
2. **统一结构**：明确的字段定义
3. **代码提示**：IDE 完整支持
4. **易于维护**：集中的数据结构管理

#### 代码示例：
```dart
// ✅ 直接访问，类型安全
final double totalAmount = paymentData.totalAmount;
final String pumpId = paymentData.pumpId;
final String nozzleId = paymentData.nozzleId;

// ✅ 清晰的数据创建
final paymentData = PaymentTransactionData.fromFuelTransaction(
  transaction: transaction,
  employeeId: employeeId,
  employeeNo: employeeNo,
);

// ✅ 便捷的数据修改
final updatedData = paymentData.copyWith(
  paymentMethodId: methodId,
);
```

## PaymentTransactionData 结构

```dart
class PaymentTransactionData {
  // 交易基本信息
  final String transactionId;        // 交易ID
  final int stationId;              // 站点ID
  final String transactionRef;      // 交易参考号
  
  // 设备信息
  final String pumpId;              // 加油机ID
  final String nozzleId;            // 油枪ID
  
  // 燃油信息
  final String fuelType;            // 燃油类型
  final String fuelGrade;           // 燃油等级
  
  // 金额信息
  final double totalAmount;         // 总金额
  final double volume;              // 数量（升）
  final double unitPrice;           // 单价
  
  // 支付信息
  final String status;              // 状态
  final int? paymentMethodId;       // 支付方式ID（可选）
  
  // 会员信息
  final Member? memberInfo;         // 会员信息（可选）
  
  // 员工信息
  final String employeeId;          // 员工ID
  final String employeeNo;          // 员工编号
  
  // 时间信息
  final DateTime createdAt;         // 创建时间
  
  // 额外数据
  final Map<String, dynamic> metadata; // 元数据
}
```

## 工厂方法

### 1. fromFuelTransaction
```dart
PaymentTransactionData.fromFuelTransaction({
  required FuelTransaction transaction,
  required String employeeId,
  required String employeeNo,
  Member? memberInfo,
  int? paymentMethodId,
})
```
**用途**：从 BOS FuelTransaction 对象创建支付数据

### 2. fromLegacyMap
```dart
PaymentTransactionData.fromLegacyMap(Map<String, dynamic> data)
```
**用途**：兼容旧版本的 Map 数据，智能提取各种可能的字段名

### 3. fromJson / toJson
```dart
PaymentTransactionData.fromJson(Map<String, dynamic> json)
Map<String, dynamic> toJson()
```
**用途**：JSON 序列化/反序列化

## 兼容性策略

### 智能字段映射
`fromLegacyMap` 方法处理各种可能的字段名：

```dart
// 处理加油机ID的多种命名
final String pumpId = _extractField(data, [
  'pumpId', 'dispenserId', 'pump_id', 'dispenser_id', 
  'pumpNumber', 'dispenserNumber', 'metadata.pump_id'
]) ?? '1';

// 处理金额的多种结构
final double totalAmount = _extractDoubleField(data, [
  'payment.totalAmount', 'totalAmount', 'amount', 
  'settlement.totalAmount', 'transaction.amount'
]) ?? 0.0;
```

### 路由层兼容
```dart
builder: (context, state) {
  final extra = state.extra;
  PaymentTransactionData? paymentData;
  
  if (extra is PaymentTransactionData) {
    // 新版本数据
    paymentData = extra;
  } else if (extra is Map<String, dynamic>) {
    // 旧版本数据兼容
    paymentData = PaymentTransactionData.fromLegacyMap(extra);
  }
  
  return PaymentPage(paymentData: paymentData);
}
```

## 数据流转时序图

```mermaid
sequenceDiagram
    participant FCC as FCC设备
    participant BOS as BOS API
    participant TN as TransactionNavigationService
    participant SP as SharedPreferences
    participant Router as GoRouter
    participant PMPage as PaymentMethodPage
    participant CashPage as CashPaymentPage
    
    FCC->>BOS: 上传交易数据
    BOS->>TN: FuelTransaction对象
    TN->>SP: 获取员工信息
    SP-->>TN: employeeId, employeeNo
    TN->>TN: 创建PaymentTransactionData
    TN->>Router: context.push('/payment', extra: paymentData)
    Router->>PMPage: 传递PaymentTransactionData
    PMPage->>PMPage: 选择支付方式
    PMPage->>Router: context.push('/payment/cash', extra: updatedData)
    Router->>CashPage: 传递PaymentTransactionData
    CashPage->>CashPage: 显示支付界面
```

## 最佳实践

### 1. 数据创建
```dart
// ✅ 推荐：使用工厂方法
final paymentData = PaymentTransactionData.fromFuelTransaction(
  transaction: transaction,
  employeeId: employeeId,
  employeeNo: employeeNo,
);

// ❌ 不推荐：手动构造Map
final transactionData = {
  'id': transaction.id,
  'amount': transaction.amount,
  // ... 大量手动映射
};
```

### 2. 数据传递
```dart
// ✅ 推荐：类型安全的传递
context.push('/payment/cash', extra: paymentData);

// ❌ 不推荐：松散的Map传递
context.push('/payment/cash', extra: {'amount': 100, 'pumpId': '1'});
```

### 3. 数据访问
```dart
// ✅ 推荐：直接属性访问
final amount = paymentData.totalAmount;
final pumpId = paymentData.pumpId;

// ❌ 不推荐：复杂的Map解析
final amount = double.tryParse(data?['payment']?['totalAmount']?.toString() ?? '0') ?? 0.0;
```

### 4. 数据修改
```dart
// ✅ 推荐：使用copyWith
final updatedData = paymentData.copyWith(
  paymentMethodId: newMethodId,
  status: 'processing',
);

// ❌ 不推荐：修改Map
data['paymentMethodId'] = newMethodId;
data['status'] = 'processing';
```

## 总结

通过引入 `PaymentTransactionData` DTO，我们实现了：

1. **类型安全**：编译时错误检查，减少运行时错误
2. **代码质量**：更清晰、更易维护的代码
3. **开发效率**：IDE 支持和代码提示
4. **系统稳定性**：统一的数据结构和验证
5. **向后兼容**：支持旧版本数据的平滑迁移

这种重构是从"快速原型"向"生产级代码"演进的重要步骤，为系统的长期维护和扩展奠定了坚实基础。 