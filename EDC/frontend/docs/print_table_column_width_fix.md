# 打印表格列宽修复

## 问题描述

用户反馈在打印小票时，数量(Qty)列中的数字（如 7.425）会出现换行问题，影响小票的美观性和可读性。

## 问题原因

原代码使用了 `printColumnsString` 方法，该方法使用**比例宽度**而不是固定字符宽度：

```dart
await printer.printColumnsString(
  <String>['Product', 'Qty', 'Price', 'Amount'],
  <int>[11, 5, 7, 9],  // 比例宽度：11:5:7:9
  <int>[TableAlignment.center, TableAlignment.center, TableAlignment.center, TableAlignment.center],
);
```

在这种设置下：
- Qty 列占总宽度的 5/(11+5+7+9) = 5/32 ≈ 15.6%
- 对于较长的数字（如 7.425），在这个宽度下容易换行

## 解决方案

将 `printColumnsString` 替换为 `printColumnsText`，并调整列宽设置：

### 修改前后对比

**修改前（使用比例宽度）：**
```dart
await printer.printColumnsString(
  <String>['Product', 'Qty', 'Price', 'Amount'],
  <int>[11, 5, 7, 9],  // 比例宽度
  <int>[TableAlignment.center, TableAlignment.center, TableAlignment.center, TableAlignment.center],
);
```

**修改后（使用固定字符宽度）：**
```dart
await printer.printColumnsText(
  <String>['Product', 'Qty', 'Price', 'Amount'],
  <int>[11, 8, 7, 9],  // 固定字符宽度
  <int>[TableAlignment.center, TableAlignment.center, TableAlignment.center, TableAlignment.center],
);
```

## 方法差异

### printColumnsString vs printColumnsText

| 方法 | 宽度类型 | 说明 | 适用场景 |
|------|----------|------|----------|
| `printColumnsString` | 比例宽度 | 根据比例分配总宽度 | 需要响应式布局的场景 |
| `printColumnsText` | 固定字符宽度 | 每列固定字符数 | 需要精确控制列宽的场景 |

### 列宽调整说明

| 列 | 修改前宽度 | 修改后宽度 | 调整原因 |
|----|-----------|-----------|----------|
| Product | 11 | 11 | 保持不变 |
| **Qty** | **5** | **8** | **增加宽度防止数字换行** |
| Price | 7 | 7 | 保持不变 |
| Amount | 9 | 9 | 保持不变 |

## 修改范围

### 1. 产品表格
- 表格头部
- 产品详情行

### 2. 促销信息
- 促销名称和折扣金额显示

### 3. 总计部分
- 所有金额汇总行（Total、Discount、Net Amount 等）

### 4. 统一列宽设置
- 双列布局统一使用 `[22, 13]` 的字符宽度
- 四列布局使用 `[11, 8, 7, 9]` 的字符宽度

## 预期效果

### 修改前
```
Product     Qty   Price   Amount
--------------------------------
BP92        7.42  16.350  121.395
            5
```

### 修改后
```
Product     Qty      Price   Amount
--------------------------------
BP92        7.425    16.350  121.395
```

## 技术细节

### printColumnsText 特点
1. **固定字符宽度**：每列占用固定的字符数
2. **精确控制**：可以准确控制每列的显示宽度
3. **不换行**：在指定宽度内，内容不会自动换行
4. **字符计算**：1个中文字符 = 2个英文字符

### 宽度计算
- 总宽度约为 32 个字符（基于 Sunmi 打印机的标准宽度）
- Product: 11 字符
- Qty: 8 字符（足够显示 "7.425" 这样的数字）
- Price: 7 字符
- Amount: 9 字符
- 总计: 11+8+7+9 = 35 字符（略超但在可接受范围内）

## 测试验证

建议测试以下场景：
1. 短数字：1.0, 5.5
2. 中等数字：12.345, 67.890
3. 长数字：123.456, 999.999
4. 边界情况：0.001, 1000.000

## 注意事项

1. **兼容性**：确保在不同 Sunmi 打印机型号上测试
2. **字符编码**：注意中文字符的宽度计算
3. **数据格式**：确保数字格式化后不会超出列宽
4. **对齐方式**：数字列使用右对齐，文本列使用左对齐

## 相关文件

- `lib/services/auto_print_service.dart` - 主要修改文件
- `lib/services/native/sunmi_printer_service.dart` - 打印服务接口
- `docs/print_reprint_configuration.md` - 打印功能配置文档 