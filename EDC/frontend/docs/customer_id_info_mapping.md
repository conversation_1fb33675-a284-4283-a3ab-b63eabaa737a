# Order.customerId 到 AutoPrintService 客户信息映射

## 功能概述

此功能将 `Order.dart` 中的 `customerId` 字段映射到 `AutoPrintService.dart` 中的 `_getCustomerInfo` 方法，提供统一的客户信息获取接口。

## 使用方法

### 1. 在Order.dart中访问customerId

```dart
// 直接访问Order对象的customerId字段
int? customerId = order.customerId;
```

### 2. 在AutoPrintService中获取相同的客户信息

```dart
// 使用映射方法获取客户信息
Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
```

## 方法详情

### `getCustomerIdAsCustomerInfo(Order order)`

**功能**: 将Order.customerId字段映射到客户信息获取逻辑

**参数**: 
- `order`: Order对象

**返回值**: 
- `Map<String, String>`: 客户信息Map，包含以下字段：
  - `name`: 客户姓名
  - `phone`: 客户电话（格式化后的后5位）

**处理逻辑**:
1. 调用内部的_getCustomerInfo方法进行处理
2. 利用_getCustomerInfo中的所有逻辑，包括：
   - 从member_info中获取会员信息
   - 从customer_info中获取客户信息
   - 从订单直接字段获取信息
   - 从metadata中获取客户相关信息
   - 从erp_info中获取ERP信息

## 数据处理优先级

1. **member_info中的会员信息** (最高优先级)
2. **customer_info中的客户信息**
3. **订单直接字段** (customerName, memberPhone)
4. **metadata中的客户信息**
5. **erp_info中的ERP信息**
6. **返回默认值** (ANONIM, **01010)

## 示例代码

```dart
// 示例1: 基本使用
Order order = getOrderFromAPI();
Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
print('客户姓名: ${customerInfo['name']}');
print('客户电话: ${customerInfo['phone']}');

// 示例2: 在打印服务中使用
class PrintService {
  void printReceipt(Order order) {
    Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
    String customerName = customerInfo['name'] ?? 'ANONIM';
    String customerPhone = customerInfo['phone'] ?? '**01010';
    
    // 使用客户信息进行打印处理
    print('打印客户: $customerName ($customerPhone)');
  }
}

// 示例3: 与原有方法对比
void compareResults(Order order) {
  // 原有方法
  int? originalCustomerId = order.customerId;
  
  // 新映射方法
  Map<String, String> mappedCustomerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
  
  print('原始customerId: $originalCustomerId');
  print('映射后客户信息: $mappedCustomerInfo');
}

// 示例4: 处理客户信息
void processCustomerInfo(Order order) {
  Map<String, String> customerInfo = AutoPrintService.getCustomerIdAsCustomerInfo(order);
  
  if (customerInfo['name'] != 'ANONIM') {
    print('找到客户: ${customerInfo['name']}');
    print('联系电话: ${customerInfo['phone']}');
  } else {
    print('匿名客户');
  }
}
```

## 返回数据格式

```dart
Map<String, String> {
  'name': String,    // 客户姓名，默认为 'ANONIM'
  'phone': String,   // 客户电话（后5位），默认为 '**01010'
}
```

## 电话号码格式化

- 显示后5位数字，前面加 `**`
- 例如：`081234567890` → `**67890`
- 如果电话号码少于5位，会用0填充
- 如果没有电话号码，返回 `**01010`

## 调试信息

方法执行时会输出以下调试信息：
- `🔄 映射Order.customerId到客户信息...`
- `Order.customerId: [值]`
- `映射结果: [结果Map]`

## 错误处理

如果映射过程中发生错误：
- 输出错误信息: `❌ 映射Order.customerId到客户信息失败: [错误]`
- 返回默认值: `{'name': 'ANONIM', 'phone': '**01010'}`

## 数据来源详情

### 1. member_info (会员信息)
```dart
{
  'name': '会员姓名',
  'phone': '会员电话'
}
```

### 2. customer_info (客户信息)
```dart
{
  'name': '客户姓名',
  'phone': '客户电话'
}
```

### 3. 订单直接字段
- `order.customerName`: 客户姓名
- `order.memberPhone`: 会员电话

### 4. metadata (元数据)
```dart
{
  'customer_name': '客户姓名',
  'customer_phone': '客户电话'
}
```

### 5. erp_info (ERP信息)
```dart
{
  'customer_Name': '客户姓名',
  'customer_Phone_No': '客户电话'
}
```

## 兼容性

- 与现有的Order.dart模型完全兼容
- 不影响原有的customerId字段访问
- 提供增强的客户信息提取功能
- 支持多种数据源格式
- 自动处理客户名称格式化（如从"Customer B1234ABC"提取友好名称）

## 与其他方法的关系

此方法与以下方法配合使用：
- `getCustomerNameAsVehicleId(order)`: 获取车辆信息
- `getCustomerInfoFromOrder(order)`: 获取完整客户信息
- `getVehiclePlateFromOrder(order)`: 获取车牌信息

## 更新历史

- **2024-01-XX**: 初始版本，实现基本映射功能
- **2024-01-XX**: 添加错误处理和调试信息
- **2024-01-XX**: 完善文档和示例代码
- **2024-01-XX**: 添加电话号码格式化功能 