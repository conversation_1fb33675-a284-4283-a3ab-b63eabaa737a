# 客户信息车牌号问题修复

## 问题描述

用户反馈：为什么没有客户姓名和手机的时候，下单会把customer姓名和手机号传递为车牌号和0000000000，这个是错误的。应该是ANONIM和1010101010。

## 问题分析

经过仔细检查，发现了以下问题：

### 1. 历史遗留问题

从文档 `customer_name_fix.md` 可以看到，之前确实有人修改过代码，将车牌号作为客户姓名：

```dart
// Prepare customer name - use vehicle plate number instead of member name
String? customerNameForOrder;
if (memberInfo.isNotEmpty && memberInfo['vehicle_plate'] != null && memberInfo['vehicle_plate'].toString().isNotEmpty) {
  customerNameForOrder = memberInfo['vehicle_plate'] as String;
  debugPrint('✅ Using vehicle plate as customer name: $customerNameForOrder');
} else {
  customerNameForOrder = null;
  debugPrint('⚠️ No vehicle plate available, customer name will be null');
}
```

### 2. 默认手机号问题

在 `member_registration_page.dart` 第862行，当用户没有输入手机号时，系统会设置为 `'0000000000'`：

```dart
phone: phone.isEmpty
    ? (_selectedCustomerType == 'B2B' ? '0000000000' : '0000000000')
    : phone,
```

### 3. 过滤逻辑缺失

在 `cash_payment_page.dart` 中，从会员信息获取手机号时，没有过滤掉默认的 `'0000000000'`，导致：

1. 如果用户没有输入手机号，会员注册时会设置为 `'0000000000'`
2. 在下单时，`customerPhone` 会被设置为 `'0000000000'`
3. 由于 `customerPhone` 不为空（虽然是默认值），所以不会触发设置 `1010101010` 的逻辑

## 修复方案

### 1. 过滤默认手机号

在 `cash_payment_page.dart` 的客户手机号提取逻辑中添加过滤：

```dart
// Extract customer phone from member info
String? customerPhone;
if (memberInfo.isNotEmpty && memberInfo['phone'] != null) {
  final String phoneFromMember = memberInfo['phone'].toString();
  // 过滤掉默认的手机号
  if (phoneFromMember.isNotEmpty && phoneFromMember != '0000000000') {
    customerPhone = phoneFromMember;
  }
} else if (widget.paymentData?.memberInfo?.phone != null) {
  final String phoneFromPaymentData = widget.paymentData!.memberInfo!.phone;
  // 过滤掉默认的手机号
  if (phoneFromPaymentData.isNotEmpty && phoneFromPaymentData != '0000000000') {
    customerPhone = phoneFromPaymentData;
  }
}
```

### 2. 确认客户姓名逻辑正确

当前的客户姓名逻辑已经是正确的，会过滤掉自动生成的姓名：

```dart
// Prepare customer name - use actual member name, not vehicle plate
String? customerNameForOrder;
if (memberInfo.isNotEmpty && memberInfo['name'] != null && memberInfo['name'].toString().isNotEmpty) {
  final String memberName = memberInfo['name'].toString();
  // Skip auto-generated names like "Customer B1234ABC" or "Business Customer B1234ABC"
  if (!memberName.startsWith('Customer ') && !memberName.startsWith('Business Customer ')) {
    customerNameForOrder = memberName;
  } else {
    customerNameForOrder = null;  // 自动生成的姓名被忽略
  }
}
```

## 修复后的行为

### 场景1：没有客户信息的情况
- **会员姓名**: `"Customer B1234ABC"` (自动生成)
- **会员手机号**: `"0000000000"` (默认值)
- **处理结果**:
  - `customerNameForOrder`: `null` (自动生成姓名被过滤)
  - `customerPhone`: `null` (默认手机号被过滤)
- **最终设置**: `ANONIM` / `1010101010`

### 场景2：有真实客户信息的情况
- **会员姓名**: `"John Doe"` (真实姓名)
- **会员手机号**: `"081234567890"` (真实手机号)
- **处理结果**:
  - `customerNameForOrder`: `"John Doe"`
  - `customerPhone`: `"081234567890"`
- **最终设置**: 保持真实信息

### 场景3：TERA支付方式
- **无论是否有客户信息**: 强制设置为 `ANONIM` / `1010101010`

## 测试验证

创建了 `customer_info_vehicle_plate_test.dart` 测试文件，包含以下测试用例：

1. **客户姓名不应该是车牌号**: 验证自动生成的姓名被正确过滤
2. **手机号不应该是0000000000**: 验证默认手机号被正确过滤
3. **真实客户信息应该正常使用**: 验证真实信息正常处理
4. **空客户信息应该设置默认值**: 验证默认值设置逻辑
5. **TERA支付应该强制使用默认值**: 验证TERA支付的特殊逻辑
6. **边界情况测试**: 验证各种无效客户信息的处理

所有测试用例都通过，确保修复的正确性。

## 影响范围

### 修改的文件
- `EDC/frontend/lib/screens/payment/cash_payment_page.dart` (第369-383行)
- `EDC/frontend/test/customer_info_vehicle_plate_test.dart` (新增)

### 影响的功能
- 下单时的客户信息处理
- 客户信息默认值设置

### 向后兼容性
- ✅ 完全向后兼容
- ✅ 不影响现有的真实客户信息处理
- ✅ 修复了错误的默认值传递

## 验证步骤

1. **运行测试**:
   ```bash
   flutter test test/customer_info_vehicle_plate_test.dart
   ```

2. **手动测试场景**:
   - 注册新会员时不输入姓名和手机号
   - 使用该会员进行下单（CASH支付）
   - 检查订单中的客户信息是否为 `ANONIM` / `1010101010`
   - 使用TERA支付方式下单，验证强制默认值设置

3. **预期结果**:
   - 没有客户信息时，订单中的客户信息为 `ANONIM` / `1010101010`
   - 不会出现车牌号作为客户姓名
   - 不会出现 `0000000000` 作为客户手机号

## 总结

这次修复解决了客户信息处理中的关键问题：

1. **数据准确性**: 确保客户信息不会被错误设置为车牌号或默认手机号
2. **业务逻辑正确性**: 正确实现了默认值设置的业务规则
3. **代码质量**: 通过测试确保修复的正确性和稳定性

修复后，当用户没有输入姓名和手机时，系统会正确传递 `ANONIM` 和 `1010101010` 作为默认值，而不是车牌号和 `0000000000`。
