# 客户信息默认值设置功能

## 功能概述

在下单流程中，系统会根据选择的支付方式自动处理客户信息，确保订单数据的完整性和业务规则的正确执行。

## 业务规则

### TERA 支付方式
当支付方式为 TERA（回罐油）时：
- **触发条件**: 选择 TERA 支付方式（无论是否有客户信息）
- **处理逻辑**: 清空现有客户信息并设置为默认值
- **客户姓名**: `ANONIM`
- **客户手机号**: `**********`

### 其他支付方式
当支付方式为非 TERA（如 CASH、BANK_CARD、POINTS 等）时：
- **触发条件**: 客户姓名和手机号都为空（null 或空字符串）
- **处理逻辑**: 仅在客户信息为空时设置默认值，有客户信息时保持不变
- **客户姓名**: `ANONIM`
- **客户手机号**: `**********`

## 实现位置

**文件**: `EDC/frontend/lib/screens/payment/cash_payment_page.dart`

**方法**: `_confirmPayment()`

**代码位置**: 第377-393行

## 实现逻辑

```dart
// 根据支付方式设置客户信息
final bool isTeraPayment = widget.paymentData?.paymentMethodName?.toUpperCase() == 'TERA';

if (isTeraPayment) {
  // TERA 支付方式：无论是否有客户信息，都清空并设置为默认值
  customerNameForOrder = 'ANONIM';
  customerPhone = '**********';
  debugPrint('✅ TERA支付：清空客户信息并设置默认值 - 姓名: ANONIM, 手机: **********');
} else {
  // 其他支付方式：只有当客户姓名和手机号都为空时才设置默认值
  if ((customerNameForOrder == null || customerNameForOrder.trim().isEmpty) &&
      (customerPhone == null || customerPhone.trim().isEmpty)) {
    customerNameForOrder = 'ANONIM';
    customerPhone = '**********';
    debugPrint('✅ 其他支付：设置默认客户信息 - 姓名: ANONIM, 手机: **********');
  }
}
```

## 支付方式识别

系统通过以下方式识别 TERA 支付方式：
```dart
final bool isTeraPayment = widget.paymentData?.paymentMethodName?.toUpperCase() == 'TERA';
```

## 测试场景

### 场景1: TERA 支付 + 有客户信息
- **输入**: customerName = "John Doe", customerPhone = "************", paymentMethod = "TERA"
- **输出**: customerName = "ANONIM", customerPhone = "**********"
- **说明**: TERA支付会清空现有客户信息

### 场景2: TERA 支付 + 空客户信息
- **输入**: customerName = null, customerPhone = null, paymentMethod = "TERA"
- **输出**: customerName = "ANONIM", customerPhone = "**********"

### 场景3: 其他支付 + 空客户信息
- **输入**: customerName = null, customerPhone = null, paymentMethod = "CASH"
- **输出**: customerName = "ANONIM", customerPhone = "**********"

### 场景4: 其他支付 + 有客户信息
- **输入**: customerName = "John Doe", customerPhone = "************", paymentMethod = "CASH"
- **输出**: customerName = "John Doe", customerPhone = "************"
- **说明**: 非TERA支付保持原有客户信息

### 场景5: 其他支付 + 空字符串客户信息
- **输入**: customerName = "", customerPhone = "   ", paymentMethod = "BANK_CARD"
- **输出**: customerName = "ANONIM", customerPhone = "**********"

## 调试信息

当触发客户信息处理时，系统会输出相应的调试信息：

**TERA支付方式**：
```
✅ TERA支付：清空客户信息并设置默认值 - 姓名: ANONIM, 手机: **********
```

**其他支付方式**：
```
✅ 其他支付：设置默认客户信息 - 姓名: ANONIM, 手机: **********
```

## 数据流向

1. **客户信息提取**: 从 memberInfo 或 paymentData 中提取客户姓名和手机号
2. **空值检查**: 检查客户姓名和手机号是否都为空
3. **支付方式判断**: 识别当前选择的支付方式
4. **默认值设置**: 根据支付方式设置相应的默认值
5. **订单创建**: 将处理后的客户信息传递给 CreateOrderRequest
6. **API调用**: 将包含客户信息的订单数据发送到后端

## 相关文件

- `EDC/frontend/lib/screens/payment/cash_payment_page.dart` - 主要实现
- `EDC/frontend/lib/models/create_order_request.dart` - 订单请求模型
- `EDC/frontend/test/customer_info_default_test.dart` - 单元测试

## 注意事项

1. **TERA支付特殊处理**: TERA支付方式会无条件清空并重置客户信息，无论原来是否有客户信息
2. **其他支付方式**: 只有当客户姓名和手机号都为空时才会设置默认值，有客户信息时保持不变
3. **字符串处理**: 使用 `trim()` 方法处理空白字符
4. **大小写**: 支付方式名称比较时转换为大写
5. **调试**: 包含详细的调试输出便于问题排查
6. **业务逻辑**: TERA支付理论上不需要客户信息，因此统一使用匿名信息

## 测试验证

运行单元测试验证功能：
```bash
flutter test test/customer_info_default_test.dart
```

所有测试用例应该通过，确保功能正常工作。
