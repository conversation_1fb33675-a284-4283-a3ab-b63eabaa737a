# 收据格式修复总结

## 修复的问题

### 1. ✅ Product、Qty、Price、Amount 列对齐问题
**问题描述：** 产品信息表格的列属性不对齐，产品名称和数量/价格/金额没有正确对应

**修复方案：**
- 使用 `printColumnsString` 方法创建标准表格头部
- 产品名称占第一行，数量/价格/金额占第二行
- 使用列宽比例 `[2, 1, 1, 1]` 确保对齐
- 对齐方式：Product(左对齐)、Qty(居中)、Price(右对齐)、Amount(右对齐)

**修改前：**
```dart
await printer.printText('Product    Qty  Price Amount\n');
await printer.printText('${productInfo['name']}\n');
await printer.printColumnsString(
  <String>[volumeStr, priceStr, amountStr],
  <int>[1, 1, 1],
  <int>[TableAlignment.left, TableAlignment.right, TableAlignment.right],
);
```

**修改后：**
```dart
await printer.printColumnsString(
  <String>['Product', 'Qty', 'Price', 'Amount'],
  <int>[2, 1, 1, 1],
  <int>[TableAlignment.left, TableAlignment.center, TableAlignment.right, TableAlignment.right],
);
await printer.printText('${productInfo['name']}\n');
await printer.printColumnsString(
  <String>['', volumeStr, priceStr, amountStr],
  <int>[2, 1, 1, 1],
  <int>[TableAlignment.left, TableAlignment.center, TableAlignment.right, TableAlignment.right],
);
```

### 2. ✅ Invoice Number 到 Kendaraan 左对齐及分割线
**问题描述：** 发票号码到车牌号这部分没有明确左对齐，且缺少分割线

**修复方案：**
- 在发票号码部分明确设置 `PrintAlignment.left`
- 在车牌号后添加分割线 `$separator`

**修改前：**
```dart
await printer.printText('Kendaraan: $plateNumber\n');
await printer.printText('\n');
```

**修改后：**
```dart
await printer.setAlignment(PrintAlignment.left);
await printer.printText('Kendaraan: $plateNumber\n');
await printer.printText('$separator\n');
```

### 3. ✅ 显示 Promotion 名称及优惠金额
**问题描述：** 优惠信息显示不完整，没有正确显示优惠名称和金额

**修复方案：**
- 获取优惠名称和折扣金额
- 使用表格格式显示优惠名称和对应的折扣金额
- 只在有优惠且有折扣金额时显示

**修改前：**
```dart
if (promotionName.isNotEmpty) {
  await printer.printText('Extra Hemat 3L  $promotionName\n');
  await printer.printText('.00\n');
}
```

**修改后：**
```dart
final Map<String, double> discounts = _getDiscountBreakdown(order);
if (promotionName.isNotEmpty && (discounts['fuel']! > 0 || discounts['other']! > 0)) {
  final double totalDiscount = discounts['fuel']! + discounts['other']!;
  final String discountStr = currencyFormat.format(totalDiscount).replaceAll(',', '.');
  await printer.printColumnsString(
    <String>[promotionName, 'Rp $discountStr'],
    <int>[3, 2],
    <int>[TableAlignment.left, TableAlignment.right],
  );
}
```

### 4. ✅ Discount (Fuel) 冒号换行问题
**问题描述：** "Discount (Fuel):" 的冒号会换行到第二行

**修复方案：**
- 移除冒号，使用更宽的列宽比例
- 调整列宽为 `[3, 2]` 确保文本在一行显示

**修改前：**
```dart
<String>['Discount (Fuel):', 'Rp ${amount}'],
<int>[1, 1],
```

**修改后：**
```dart
<String>['Discount (Fuel)', 'Rp ${amount}'],
<int>[3, 2],
```

### 5. ✅ Total Quantity 下面添加分割线
**问题描述：** Total Quantity 下面缺少分割线

**修复方案：**
- 在 Total Quantity 后添加分割线
- 调整列宽保持一致性

**修改前：**
```dart
await printer.printColumnsString(
  <String>['Total Quantity:', '${productInfo['volume']}L'],
  <int>[1, 1],
  <int>[TableAlignment.left, TableAlignment.right],
);
await printer.printText('\n');
```

**修改后：**
```dart
await printer.printColumnsString(
  <String>['Total Quantity:', '${productInfo['volume']}L'],
  <int>[3, 2],
  <int>[TableAlignment.left, TableAlignment.right],
);
await printer.printText('$separator\n');
```

## 额外改进

### 统一列宽比例
为了保持整个收据的视觉一致性，将所有金额相关的显示都统一使用 `[3, 2]` 的列宽比例：

- Total Amount
- Discount (Fuel)
- Discount (Others)
- Net Amount
- Payment Method
- Total Quantity

## 技术细节

### 表格对齐方式
- **左对齐 (TableAlignment.left):** 用于文本标签和产品名称
- **居中 (TableAlignment.center):** 用于数量显示
- **右对齐 (TableAlignment.right):** 用于价格和金额显示

### 列宽比例说明
- `[2, 1, 1, 1]` - 产品表格头部，产品名称占2份，其他各占1份
- `[3, 2]` - 金额显示，标签占3份，金额占2份
- `[3, 2]` - 优惠信息，名称占3份，金额占2份

### 分割线使用
- 在关键信息块之间添加分割线 `$separator`
- 保持收据的层次结构清晰

## 预期效果

修复后的收据格式将具有：
1. **清晰的表格对齐** - 产品信息列对齐准确
2. **一致的左对齐** - 发票信息到车牌信息统一左对齐
3. **完整的优惠信息** - 显示优惠名称和具体金额
4. **避免文本换行** - 折扣标签保持在一行内
5. **清晰的分割线** - 在适当位置添加分割线分隔内容

## 测试建议

1. **表格对齐测试** - 验证产品信息的列对齐是否正确
2. **优惠信息测试** - 测试有优惠和无优惠的订单打印效果
3. **长文本测试** - 测试较长的产品名称和优惠名称是否正确显示
4. **多种金额测试** - 验证不同金额格式的显示效果
5. **分割线测试** - 确认分割线位置正确且一致

---

**修复状态：** ✅ 已完成  
**影响文件：** `lib/services/auto_print_service.dart`  
**修复时间：** 2024年12月19日 