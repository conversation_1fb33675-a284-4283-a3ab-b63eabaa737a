# Order.customerName 到 AutoPrintService 车辆ID映射

## 功能概述

此功能将 `Order.dart` 中的 `customer_name` 字段映射到 `AutoPrintService.dart` 中的 `_getVehicleId` 方法，提供统一的车辆信息获取接口。

## 使用方法

### 1. 在Order.dart中访问customer_name

```dart
// 直接访问Order对象的customerName字段
String customerName = order.customerName ?? '';
```

### 2. 在AutoPrintService中获取相同的车辆信息

```dart
// 使用映射方法获取车辆ID
String vehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
```

## 方法详情

### `getCustomerNameAsVehicleId(Order order)`

**功能**: 将Order.customerName字段映射到车辆ID获取逻辑

**参数**: 
- `order`: Order对象

**返回值**: 
- `String`: 车辆ID/车牌号

**处理逻辑**:
1. 首先使用Order.customerName作为vehiclePlate参数
2. 调用内部的_getVehicleId方法进行处理
3. 利用_getVehicleId中的所有逻辑，包括：
   - 从customerName中提取车牌信息
   - 从扩展信息中获取车辆数据
   - 从会员信息中获取车牌数组
   - 多层级数据源查找

## 数据处理优先级

1. **传入的customerName参数** (最高优先级)
2. **从customerName中提取车牌** (格式: "Customer B1234ABC")
3. **从扩展信息获取车辆数据**
4. **从会员信息获取车牌数组**
5. **从其他数据源获取**
6. **返回默认值** "UNKNOWN VEHICLE"

## 示例代码

```dart
// 示例1: 基本使用
Order order = getOrderFromAPI();
String vehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
print('车辆ID: $vehicleId');

// 示例2: 在打印服务中使用
class PrintService {
  void printReceipt(Order order) {
    String vehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
    // 使用vehicleId进行打印处理
  }
}

// 示例3: 与原有方法对比
void compareResults(Order order) {
  // 原有方法
  String originalCustomerName = order.customerName ?? '';
  
  // 新映射方法
  String mappedVehicleId = AutoPrintService.getCustomerNameAsVehicleId(order);
  
  print('原始customerName: $originalCustomerName');
  print('映射后vehicleId: $mappedVehicleId');
}
```

## 调试信息

方法执行时会输出以下调试信息：
- `🔄 映射Order.customerName到车辆ID...`
- `Order.customerName: [值]`
- `映射结果: [结果]`

## 错误处理

如果映射过程中发生错误：
- 输出错误信息: `❌ 映射Order.customerName到车辆ID失败: [错误]`
- 返回备用值: `order.customerName ?? 'UNKNOWN VEHICLE'`

## 兼容性

- 与现有的Order.dart模型完全兼容
- 不影响原有的customerName字段访问
- 提供增强的车辆信息提取功能
- 支持多种数据源格式

## 更新历史

- **2024-01-XX**: 初始版本，实现基本映射功能
- **2024-01-XX**: 添加错误处理和调试信息
- **2024-01-XX**: 完善文档和示例代码 