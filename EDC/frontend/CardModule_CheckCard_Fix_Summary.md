# CardModule CheckCard失败问题修复总结

## 问题描述
CardModule的checkCard方法失败，无法正常检测NFC卡片。

## 根本原因分析

### 1. 错误的API使用
- **问题**: 使用了`checkCardEx`方法，但参数配置不正确
- **原因**: `checkCardEx`方法的文档说明有误导性，实际应该使用标准的`checkCard`方法

### 2. 错误的卡类型常量
- **问题**: 使用了硬编码的数值 `1 or 4 or 16`
- **原因**: 应该使用正确的常量定义，但`AidlConstantsV2.CardType`类不存在

### 3. 编译错误
- **问题**: `AidlConstantsV2.CardType`无法解析
- **原因**: 商米SDK中没有这个类，需要使用直接的常量值

### 4. 缺乏参数验证
- **问题**: 没有验证SDK状态和常量值
- **原因**: 可能导致运行时错误

## 修复内容

### 1. 修正API调用
**修改前:**
```kotlin
val cardType = 1 or 4 or 16  // 硬编码值
currentReadCardOptV2.checkCardEx(cardType, ctrCode, stopOnError, checkCardCallback, timeout)
```

**修改后:**
```kotlin
// 定义卡类型常量
private const val CARD_TYPE_MAGNETIC = 1   // 1<<0 磁条卡
private const val CARD_TYPE_IC = 2         // 1<<1 IC卡
private const val CARD_TYPE_NFC = 4        // 1<<2 NFC卡

val cardType = CARD_TYPE_MAGNETIC or CARD_TYPE_IC or CARD_TYPE_NFC
currentReadCardOptV2.checkCard(cardType, checkCardCallback, timeout)
```

### 2. 使用正确的卡类型常量
- `CARD_TYPE_MAGNETIC = 1` - 磁条卡 (1<<0)
- `CARD_TYPE_IC = 2` - IC卡 (1<<1)
- `CARD_TYPE_NFC = 4` - NFC卡 (1<<2)
- `CARD_TYPE_MIFARE = 8` - MIFARE卡 (1<<3)

### 3. 增强错误处理
添加了详细的错误代码处理：
- `-20003`: 参数错误
- `-2001`: 没有检测到卡片
- `-2002`: 检测到多张卡片
- `-30003`: NFC检测失败
- `-30004`: IC卡检测失败
- `-30005`: 读卡超时

### 4. 添加SDK验证
新增`validateSDKAndConstants()`方法：
- 验证卡类型常量值
- 验证SDK实例状态
- 提供详细的诊断日志

### 5. 改进日志记录
- 添加卡类型常量值的日志输出
- 增强错误信息的可读性
- 提供更详细的诊断信息

### 6. 修正cardOff调用
**修改前:**
```kotlin
currentReadCardOptV2.cardOff(16)  // 硬编码值
```

**修改后:**
```kotlin
currentReadCardOptV2.cardOff(CARD_TYPE_NFC)  // 使用常量
```

### 7. 更新卡类型名称映射
更新`getCardTypeName()`方法以使用正确的常量值进行映射。

## 商米SDK卡类型常量说明

根据商米SDK文档，正确的卡类型常量定义：

```java
// 磁条卡
public static final int MAGNETIC = 1<<0;  // = 1

// IC卡
public static final int IC = 1<<1;        // = 2

// NFC卡
public static final int NFC = 1<<2;       // = 4

// MIFARE卡
public static final int MIFARE = 1<<3;    // = 8
```

## 使用建议

### 1. 检测所有类型的卡
```kotlin
val cardType = CARD_TYPE_MAGNETIC or CARD_TYPE_IC or CARD_TYPE_NFC
```

### 2. 只检测NFC卡
```kotlin
val cardType = CARD_TYPE_NFC
```

### 3. 错误处理
始终检查错误回调中的错误代码，并提供用户友好的错误信息。

### 4. 超时设置
建议使用60秒的超时时间，这是一个合理的平衡点。

## 测试建议

1. **基本功能测试**:
   - 测试NFC卡检测
   - 测试IC卡检测
   - 测试磁条卡检测

2. **错误场景测试**:
   - 无卡片时的超时处理
   - 多张卡片的错误处理
   - SDK未初始化的错误处理

3. **性能测试**:
   - 检测响应时间
   - 连续检测的稳定性

## 预期结果

修复后，checkCard方法应该能够：
1. 正确检测各种类型的卡片
2. 提供详细的错误信息
3. 稳定运行不崩溃
4. 快速响应卡片检测事件

## 注意事项

1. 确保商米SDK已正确初始化
2. 检查设备权限设置
3. 验证硬件支持情况
4. 关注日志输出以便调试
