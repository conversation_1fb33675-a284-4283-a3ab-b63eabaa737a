# API响应格式统一迁移计划

## 新的统一响应格式

```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        // 实际数据内容
        "items": [...],     // 对于列表接口
        "total": 100,
        "page": 1,
        "page_size": 10,
        "total_page": 10
    }
}
```

## 已实现的统一解析工具

已在 `lib/services/api/api_client.dart` 中添加了统一解析器：

### 1. `ApiResponseHandler.parseResponse<T>()`
用于解析单个对象的响应

### 2. `ApiResponseHandler.parsePaginatedResponse<T>()`
用于解析分页列表响应

### 3. `PaginatedResponse<T>` 数据模型
标准化的分页响应结构

## API文件迁移状态

### ✅ 已完成迁移

1. **employee_api.dart** - 已正确适配
   - `login()` 方法
   - `getEmployeeDetail()` 方法

2. **fuel_transaction_api.dart** - 智能兼容两种格式
   - `getFuelTransactions()` 方法  
   - `getFuelTransactionById()` 方法

3. **order_api.dart** - 已更新使用统一解析器
   - ✅ `getOrders()` - 已更新使用 `parsePaginatedResponse`
   - ✅ `getOrderDetail()` - 已更新使用 `parseResponse`
   - ✅ `createOrder()` - 之前已正确处理统一格式

### ⚠️ 需要迁移的API文件

#### 1. shift_api_service.dart - 高优先级
**文件路径**: `lib/services/api/shift_api_service.dart`

**需要修改的方法** (7个方法):
```dart
// 当前代码模式
return ShiftModel.fromJson(response.data as Map<String, dynamic>);

// 需要改为
return ApiResponseHandler.parseResponse<ShiftModel>(
  response,
  (json) => ShiftModel.fromJson(json),
);
```

**具体方法列表**:
- `startShift()` - Line 42
- `endShift()` - Line 70  
- `getCurrentShift()` - Line 98
- `getShiftById()` - Line 129
- `getShifts()` - Line 160 (分页接口，使用 `parsePaginatedResponse`)
- `getShiftByNumber()` - Line 188
- `ensureShift()` - Line 216

#### 2. fcc_device_api.dart - 中优先级
**文件路径**: `lib/services/api/fcc_device_api.dart`

**问题**: 使用原生HTTP客户端，直接解析JSON
```dart
// 当前代码模式
return json.decode(response.body) as Map<String, dynamic>;

// 需要先转换为Dio Response格式，或创建专门的解析方法
```

**需要修改的方法** (估计10+个方法):
- `getDevices()`
- `getDeviceById()`
- `updateDevice()`
- `getControllers()`
- 等等...

#### 3. shift_api.dart - 中优先级
**文件路径**: `lib/services/api/shift_api.dart`

**需要修改的方法** (6个方法):
- 多处使用 `response.data as Map<String, dynamic>`

#### 4. 其他API文件需要检查
- `promotion_api.dart`
- `payment_api.dart`
- `pump_api.dart`
- `enhanced_reports_api.dart`

## 迁移步骤建议

### 第一阶段：高优先级 (1-2天)

1. **shift_api_service.dart** - 立即修改
   ```bash
   # 修改命令示例
   # 将所有 ShiftModel.fromJson(response.data as Map<String, dynamic>)
   # 替换为 ApiResponseHandler.parseResponse<ShiftModel>(response, (json) => ShiftModel.fromJson(json))
   ```

### 第二阶段：中优先级 (3-5天)

2. **fcc_device_api.dart** - 需要重构
   - 选项1: 迁移到使用Dio客户端
   - 选项2: 创建兼容的解析方法

3. **shift_api.dart** - 标准化修改

### 第三阶段：完善检查 (1-2天)

4. 检查所有剩余API文件
5. 统一错误处理
6. 添加单元测试

## 具体修改示例

### 单个对象响应
```dart
// 修改前
if (response.statusCode == 200) {
  return ShiftModel.fromJson(response.data as Map<String, dynamic>);
}

// 修改后
if (response.statusCode == 200) {
  return ApiResponseHandler.parseResponse<ShiftModel>(
    response,
    (json) => ShiftModel.fromJson(json),
  );
}
```

### 分页列表响应
```dart
// 修改前
final responseData = response.data as Map<String, dynamic>;
return ShiftListResponse.fromJson(responseData);

// 修改后
final paginatedResponse = ApiResponseHandler.parsePaginatedResponse<ShiftModel>(
  response,
  (json) => ShiftModel.fromJson(json),
);
// 然后转换为原有的Response格式或直接使用PaginatedResponse
```

## 测试建议

1. **向后兼容测试**
   - 测试旧格式响应是否仍能正常解析
   - 测试新格式响应是否正确解析

2. **错误处理测试**  
   - 测试 `code != 0` 的错误响应
   - 测试网络错误情况

3. **边界情况测试**
   - 空数据响应
   - 格式错误的响应

## 注意事项

1. **保持向后兼容**: 统一解析器已支持新旧两种格式
2. **错误信息统一**: 所有API错误都通过 `ApiException` 抛出
3. **日志记录**: 保持详细的日志记录便于调试
4. **类型安全**: 所有类型转换都加入了安全检查

## 完成标准

- [ ] 所有API方法都使用统一解析器
- [ ] 移除所有直接的 `response.data as Map<String, dynamic>` 调用
- [ ] 错误处理统一使用 `ApiException`
- [ ] 通过单元测试验证新旧格式兼容性
- [ ] 更新相关文档 