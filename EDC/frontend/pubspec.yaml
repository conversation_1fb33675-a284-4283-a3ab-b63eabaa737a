name: edc_app
description: BP-AKR EDC手持终端系统
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # 状态管理
  flutter_riverpod: ^2.3.6
  
  # 路由管理  
  go_router: ^12.1.1
  
  # 网络请求
  dio: ^5.3.2
  
  # 持久化存储
  shared_preferences: ^2.2.0
  
  # 图标
  cupertino_icons: ^1.0.2
  
  # 工具库
  intl: ^0.20.2
  equatable: ^2.0.5
  
  # 日志系统
  logging: ^1.2.0
  
  # 文件路径
  path_provider: ^2.1.5
  
  # UI组件
  flutter_svg: ^2.0.7
  
  # 设备信息
  package_info_plus: ^8.3.0
  device_info_plus: ^11.5.0
  network_info_plus: ^6.1.4
  
  # 网络请求
  http: any

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^3.0.0
  flutter_launcher_icons: ^0.14.4

flutter:
  uses-material-design: true
  generate: true
  
  assets:
    - assets/images/

# 应用图标配置
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/bp_logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true # 移除iOS图标的alpha通道
  web:
    generate: true
    image_path: "assets/images/bp_logo.png"
    background_color: "#FFFFFF"
    theme_color: "#00A650"
  windows:
    generate: true
    image_path: "assets/images/bp_logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/bp_logo.png"
