<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <!-- 允许访问本地IP地址 -->
        <domain includeSubdomains="false">*************</domain>
        <domain includeSubdomains="false">*************</domain>
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">***********00</domain>
        <domain includeSubdomains="false">***********01</domain>
        <domain includeSubdomains="false">*************</domain>
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>
        <!-- 允许所有192.168.x.x网段 -->
        <domain includeSubdomains="true">***********</domain>
        <!-- 允许所有10.x.x.x网段 -->
        <domain includeSubdomains="true">10.0.0.0</domain>
        <!-- 允许所有172.16.x.x-172.31.x.x网段 -->
        <domain includeSubdomains="true">**********</domain>
    </domain-config>
    
    <!-- 允许所有HTTP流量用于开发和测试 -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config> 