package com.example.edc_app

import android.content.Context
import android.os.RemoteException
import android.util.Log
import com.sunmi.peripheral.printer.InnerPrinterCallback
import com.sunmi.peripheral.printer.InnerPrinterManager
import com.sunmi.peripheral.printer.InnerResultCallback
import com.sunmi.peripheral.printer.SunmiPrinterService

/**
 * Helper class for interacting with the Sunmi built-in printer.
 * <PERSON>les connecting to the printer service and provides methods for printing.
 */
class SunmiPrintHelper private constructor() { // Use Singleton pattern

    private var sunmiPrinterService: SunmiPrinterService? = null
    private var context: Context? = null
    private var isConnected: Boolean = false

    companion object {
        private const val TAG = "SunmiPrintHelper"
        // Singleton instance
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            SunmiPrintHelper()
        }
    }

    // Callback for service connection status
    private val innerPrinterCallback = object : InnerPrinterCallback() {
        override fun onConnected(service: SunmiPrinterService) {
            Log.d(TAG, "Sunmi Printer Service Connected.")
            sunmiPrinterService = service
            isConnected = true
            // You might want to add a callback here to notify the caller (e.g., MainActivity)
            // that the connection is successful.
        }

        override fun onDisconnected() {
            Log.e(TAG, "Sunmi Printer Service Disconnected.")
            sunmiPrinterService = null
            isConnected = false
            // Handle disconnection, maybe attempt to reconnect?
            // Also notify the caller about the disconnection.
        }
    }

    /**
     * Initializes and binds the Sunmi printer service.
     * Must be called before any printing operations.
     * @param appContext The application context.
     * @return Boolean indicating if the bindService call was initiated successfully.
     */
    fun initPrinter(appContext: Context): Boolean {
        this.context = appContext.applicationContext // Store application context
        Log.d(TAG, "Initializing Sunmi Printer...")
        try {
            val result = InnerPrinterManager.getInstance().bindService(this.context, innerPrinterCallback)
            if (!result) {
                Log.e(TAG, "Failed to initiate bindService.")
            }
            return result
        } catch (e: Exception) {
            Log.e(TAG, "Error binding Sunmi printer service: ${e.message}", e)
            return false
        }
    }

    /**
     * Disconnects from the Sunmi printer service.
     * Should be called when the printer is no longer needed (e.g., in onDestroy).
     */
    fun destroyPrinter() {
        Log.d(TAG, "Destroying Sunmi Printer connection...")
        if (sunmiPrinterService != null) {
            try {
                context?.let { InnerPrinterManager.getInstance().unBindService(it, innerPrinterCallback) }
                sunmiPrinterService = null
                isConnected = false
                context = null
            } catch (e: Exception) {
                Log.e(TAG, "Error unbinding Sunmi printer service: ${e.message}", e)
            }
        } else {
             Log.w(TAG, "Printer service was not bound or already destroyed.")
        }
    }

    /**
     * Checks if the printer service is connected.
     */
    fun isPrinterConnected(): Boolean {
        return isConnected && sunmiPrinterService != null
    }

    // --- Basic Printer Status and Info ---

    /**
     * Gets the current status of the printer.
     * Refer to the documentation for status codes.
     * 1: Normal, 2: Preparing, 3: Abnormal communication, 4: Out of paper,
     * 5: Overheated, 6: Cover open, 7: Cutter error, 8: Cutter recovered,
     * 9: Black mark not detected, 505: Printer not detected, 507: Firmware update failed.
     * @param callback Callback to receive the status code asynchronously.
     */
    fun getPrinterStatus(callback: InnerResultCallback) {
        if (!isPrinterConnected()) {
             handleNotConnectedError(callback, "getPrinterStatus")
             return
        }
        try {
            // Note: According to docs, updatePrinterState() itself might return a status directly
            // But let's try to use a callback consistently if possible, or adapt based on actual SDK behavior.
            // The SDK might have changed. Let's assume updatePrinterState returns directly as per docs.
             val status = sunmiPrinterService?.updatePrinterState() ?: -1 // Use -1 for error state
             callback.onRunResult(true) // Indicate the call itself succeeded
             callback.onReturnString(status.toString()) // Return status via onReturnString

        } catch (e: RemoteException) {
            handleRemoteException(e, callback, "getPrinterStatus")
        } catch (e: Exception) {
             handleGenericException(e, callback, "getPrinterStatus")
        }
    }

    fun getPrinterSerialNo(callback: InnerResultCallback) {
        executePrintCommand(callback, "getPrinterSerialNo") {
            callback.onReturnString(sunmiPrinterService?.printerSerialNo ?: "N/A")
        }
    }

    fun getPrinterModal(callback: InnerResultCallback) {
        executePrintCommand(callback, "getPrinterModal") {
             callback.onReturnString(sunmiPrinterService?.printerModal ?: "N/A")
        }
    }
    
    fun getPrinterVersion(callback: InnerResultCallback) {
        executePrintCommand(callback, "getPrinterVersion") {
             callback.onReturnString(sunmiPrinterService?.printerVersion ?: "N/A")
        }
    }

    fun getServiceVersion(callback: InnerResultCallback) {
        executePrintCommand(callback, "getServiceVersion") {
             callback.onReturnString(sunmiPrinterService?.serviceVersion ?: "N/A")
        }
    }

    fun getPrintedLength(callback: InnerResultCallback) {
        executePrintCommand(callback, "getPrintedLength") {
             sunmiPrinterService?.getPrintedLength(callback) // Uses callback
        }
    }

    fun getPrinterPaper(callback: InnerResultCallback) {
         executePrintCommand(callback, "getPrinterPaper") {
             val paperType = sunmiPrinterService?.printerPaper ?: -1
             callback.onReturnString(paperType.toString())
         }
    }
    
     fun printerSelfChecking(callback: InnerResultCallback) {
        executePrintCommand(callback, "printerSelfChecking") {
            sunmiPrinterService?.printerSelfChecking(callback)
        }
    }

    // --- ESC/POS Raw Data ---
    fun sendRAWData(data: ByteArray, callback: InnerResultCallback) {
        executePrintCommand(callback, "sendRAWData") {
             sunmiPrinterService?.sendRAWData(data, callback)
        }
    }
    
    // --- Printer Style ---
    fun setPrinterStyle(key: Int, value: Int, callback: InnerResultCallback) {
         executePrintCommand(callback, "setPrinterStyle") {
             sunmiPrinterService?.setPrinterStyle(key, value)
             callback.onRunResult(true) // Assume success if no exception
        }
    }

    // --- Printer Mode ---
     fun getPrinterMode(callback: InnerResultCallback) {
         executePrintCommand(callback, "getPrinterMode") {
             val mode = sunmiPrinterService?.printerMode ?: -1
             callback.onReturnString(mode.toString())
         }
    }
    
     fun getPrinterBBMDistance(callback: InnerResultCallback) {
         executePrintCommand(callback, "getPrinterBBMDistance") {
             val distance = sunmiPrinterService?.printerBBMDistance ?: -1
             callback.onReturnString(distance.toString())
         }
    }

    // --- Basic Printing Methods (Re-adding originals) ---
     /**
     * Prints plain text. Text should end with '\n' to ensure it's printed immediately.
     * @param text The text to print. Ensure it ends with '\n'.
     * @param callback Callback to receive the result of the print command execution.
     */
    fun printText(text: String, callback: InnerResultCallback) {
        executePrintCommand(callback, "printText") {
            sunmiPrinterService?.printText(text, callback)
        }
    }

    /**
     * Sets the alignment for subsequent print commands.
     * @param alignment 0 for left, 1 for center, 2 for right.
     * @param callback Callback to receive the result.
     */
    fun setAlignment(alignment: Int, callback: InnerResultCallback) {
        executePrintCommand(callback, "setAlignment") {
             sunmiPrinterService?.setAlignment(alignment, callback)
        }
    }

     /**
     * Sets the font size for subsequent print commands.
     * @param size Font size (e.g., 24.0f).
     * @param callback Callback to receive the result.
     */
    fun setFontSize(size: Float, callback: InnerResultCallback) {
        executePrintCommand(callback, "setFontSize") {
             sunmiPrinterService?.setFontSize(size, callback)
        }
    }

    /**
     * Prints a QR code.
     * @param data The data to encode in the QR code.
     * @param moduleSize Size of the QR code module (dots), e.g., 8. Range 4-16.
     * @param errorLevel Error correction level (0=L, 1=M, 2=Q, 3=H).
     * @param callback Callback to receive the result.
     */
    fun printQRCode(data: String, moduleSize: Int, errorLevel: Int, callback: InnerResultCallback) {
         executePrintCommand(callback, "printQRCode") {
            sunmiPrinterService?.printQRCode(data, moduleSize, errorLevel, callback)
        }
    }

    /**
     * Prints a 1D barcode.
     * @param data Barcode content.
     * @param symbology Barcode type (0-8, e.g., 8 for CODE128). See docs for types.
     * @param height Barcode height (1-255). Default 162.
     * @param width Barcode width (2-6). Default 2.
     * @param textPosition Position of human-readable text (0=None, 1=Above, 2=Below, 3=Both).
     * @param callback Callback to receive the result.
     */
    fun printBarCode(data: String, symbology: Int, height: Int, width: Int, textPosition: Int, callback: InnerResultCallback) {
         executePrintCommand(callback, "printBarCode") {
             sunmiPrinterService?.printBarCode(data, symbology, height, width, textPosition, callback)
        }
    }

    /**
     * Feeds the paper by n lines.
     * @param n Number of lines to feed.
     * @param callback Callback to receive the result.
     */
    fun lineWrap(n: Int, callback: InnerResultCallback) {
         executePrintCommand(callback, "lineWrap") {
             sunmiPrinterService?.lineWrap(n, callback)
        }
    }

    /**
     * Cuts the paper (for devices with cutters).
     * @param callback Callback to receive the result.
     */
    fun cutPaper(callback: InnerResultCallback) {
         executePrintCommand(callback, "cutPaper") {
             sunmiPrinterService?.cutPaper(callback)
        }
    }

    // --- Text Printing Additions ---
     fun setFontName(typeface: String, callback: InnerResultCallback) {
        executePrintCommand(callback, "setFontName") {
            sunmiPrinterService?.setFontName(typeface, callback)
        }
    }
    
    fun printTextWithFont(text: String, typeface: String, fontSize: Float, callback: InnerResultCallback) {
         executePrintCommand(callback, "printTextWithFont") {
            sunmiPrinterService?.printTextWithFont(text, typeface, fontSize, callback)
        }
    }

    fun printOriginalText(text: String, callback: InnerResultCallback) {
         executePrintCommand(callback, "printOriginalText") {
            sunmiPrinterService?.printOriginalText(text, callback)
        }
    }

    // --- Table Printing ---
    fun printColumnsText(colsTextArr: Array<String>, colsWidthArr: IntArray, colsAlign: IntArray, callback: InnerResultCallback) {
         executePrintCommand(callback, "printColumnsText") {
            sunmiPrinterService?.printColumnsText(colsTextArr, colsWidthArr, colsAlign, callback)
        }
    }
    
    fun printColumnsString(colsTextArr: Array<String>, colsWidthArr: IntArray, colsAlign: IntArray, callback: InnerResultCallback) {
         executePrintCommand(callback, "printColumnsString") {
            sunmiPrinterService?.printColumnsString(colsTextArr, colsWidthArr, colsAlign, callback)
        }
    }

    // --- Bitmap Printing ---
    fun printBitmap(bitmap: android.graphics.Bitmap, callback: InnerResultCallback) {
        executePrintCommand(callback, "printBitmap") {
            sunmiPrinterService?.printBitmap(bitmap, callback)
        }
    }
    
     fun printBitmapCustom(bitmap: android.graphics.Bitmap, type: Int, callback: InnerResultCallback) {
        executePrintCommand(callback, "printBitmapCustom") {
            sunmiPrinterService?.printBitmapCustom(bitmap, type, callback)
        }
    }
    
    // --- 2D Code Printing ---
    fun print2DCode(data: String, symbology: Int, modulesize: Int, errorlevel: Int, callback: InnerResultCallback) {
         executePrintCommand(callback, "print2DCode") {
            sunmiPrinterService?.print2DCode(data, symbology, modulesize, errorlevel, callback)
        }
    }

    // --- Cutter Info ---
    fun getCutPaperTimes(callback: InnerResultCallback) {
        executePrintCommand(callback, "getCutPaperTimes") {
            val times = sunmiPrinterService?.cutPaperTimes ?: -1
            callback.onReturnString(times.toString())
        }
    }
    
    // --- Cash Drawer ---
     fun openDrawer(callback: InnerResultCallback) {
        executePrintCommand(callback, "openDrawer") {
            sunmiPrinterService?.openDrawer(callback)
        }
    }

    fun getOpenDrawerTimes(callback: InnerResultCallback) {
         executePrintCommand(callback, "getOpenDrawerTimes") {
             val times = sunmiPrinterService?.openDrawerTimes ?: -1
             callback.onReturnString(times.toString())
        }
    }
    
    fun getDrawerStatus(callback: InnerResultCallback) {
         executePrintCommand(callback, "getDrawerStatus") {
             val status = sunmiPrinterService?.drawerStatus ?: -1
             callback.onReturnString(status.toString())
        }
    }
    
    // --- Label Printing ---
    fun labelLocate(callback: InnerResultCallback) {
        executePrintCommand(callback, "labelLocate") {
            sunmiPrinterService?.labelLocate()
             callback.onRunResult(true) // Assume success if no exception
        }
    }
    
     fun labelOutput(callback: InnerResultCallback) {
        executePrintCommand(callback, "labelOutput") {
            sunmiPrinterService?.labelOutput()
            callback.onRunResult(true) // Assume success if no exception
        }
    }

    // --- Transaction Printing --- (Optional, but recommended for receipts)

    /**
     * Enters transaction print mode. Commands are buffered until committed.
     * @param clearBuffer If true, clears any previous uncommitted buffer.
     * @param callback Callback to receive the result.
     */
    fun enterPrinterBuffer(clearBuffer: Boolean, callback: InnerResultCallback) {
        executePrintCommand(callback, "enterPrinterBuffer") {
            // Note: The SDK docs show enterPrinterBuffer doesn't take a callback directly.
            // We'll assume it's synchronous or handle errors differently if needed.
            // For now, simulate success/failure via callback.
            sunmiPrinterService?.enterPrinterBuffer(clearBuffer)
            callback.onRunResult(true) // Assume success if no exception
        }
    }

     /**
     * Commits the buffered print commands in transaction mode. The printer remains in transaction mode.
     * The actual print result (success/failure after printing) comes via onPrintResult in the callback.
     * @param callback Callback to receive the result (including onPrintResult).
     */
    fun commitPrinterBuffer(callback: InnerResultCallback) {
         executePrintCommand(callback, "commitPrinterBuffer") {
             sunmiPrinterService?.commitPrinterBufferWithCallback(callback)
        }
    }

    /**
     * Exits transaction print mode.
     * @param commit If true, prints any remaining buffered commands before exiting.
     * @param callback Callback to receive the result (including onPrintResult if commit is true).
     */
    fun exitPrinterBuffer(commit: Boolean, callback: InnerResultCallback) {
         executePrintCommand(callback, "exitPrinterBuffer") {
             sunmiPrinterService?.exitPrinterBufferWithCallback(commit, callback)
        }
    }


    // --- Private Helper Methods ---

    /**
     * Executes a print command safely, checking connection and handling exceptions.
     * @param callback The callback to notify of results or errors.
     * @param commandName The name of the command being executed (for logging).
     * @param command Lambda function representing the actual print command call.
     */
    private fun executePrintCommand(callback: InnerResultCallback, commandName: String, command: () -> Unit) {
        if (!isPrinterConnected()) {
            handleNotConnectedError(callback, commandName)
            return
        }
        try {
            Log.d(TAG, "Executing command: $commandName")
            command.invoke()
        } catch (e: RemoteException) {
            handleRemoteException(e, callback, commandName)
        } catch (e: Exception) {
             handleGenericException(e, callback, commandName)
        }
    }

     /**
     * Handles the case where the printer service is not connected.
     */
    private fun handleNotConnectedError(callback: InnerResultCallback, commandName: String) {
        Log.e(TAG, "Cannot execute '$commandName': Printer service not connected.")
        try {
             // You might define specific error codes
            callback.onRaiseException(PrinterError.SERVICE_DISCONNECTED.code, "Printer service not connected")
        } catch (re: RemoteException) {
             Log.e(TAG, "RemoteException while raising SERVICE_DISCONNECTED error", re)
        }
    }

    /**
     * Handles RemoteExceptions during command execution.
     */
    private fun handleRemoteException(e: RemoteException, callback: InnerResultCallback, commandName: String) {
        Log.e(TAG, "RemoteException executing '$commandName': ${e.message}", e)
        try {
            // You might define specific error codes
            callback.onRaiseException(PrinterError.REMOTE_EXCEPTION.code, e.message ?: "RemoteException occurred")
        } catch (re: RemoteException) {
            Log.e(TAG, "RemoteException while raising REMOTE_EXCEPTION error", re)
        }
    }

     /**
     * Handles generic Exceptions during command execution.
     */
    private fun handleGenericException(e: Exception, callback: InnerResultCallback, commandName: String) {
        Log.e(TAG, "Exception executing '$commandName': ${e.message}", e)
        try {
            // You might define specific error codes
            callback.onRaiseException(PrinterError.GENERIC_EXCEPTION.code, e.message ?: "An unexpected error occurred")
        } catch (re: RemoteException) {
             Log.e(TAG, "RemoteException while raising GENERIC_EXCEPTION error", re)
        }
    }

    /**
     * Defines potential printer error codes for easier handling in Flutter.
     */
    enum class PrinterError(val code: Int) {
        SERVICE_DISCONNECTED(-1001),
        REMOTE_EXCEPTION(-1002),
        GENERIC_EXCEPTION(-1003),
        COMMAND_FAILED(-1004) // Generic failure reported by onRunResult(false)
        // Add more specific codes if needed, based on onRaiseException codes from Sunmi docs
    }

} 