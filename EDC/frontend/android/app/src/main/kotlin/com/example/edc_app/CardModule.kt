package com.example.edc_app

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.RemoteException
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import sunmi.paylib.SunmiPayKernel
import com.sunmi.pay.hardware.aidlv2.readcard.ReadCardOptV2
import com.sunmi.pay.hardware.aidlv2.AidlErrorCodeV2
import com.sunmi.pay.hardware.aidlv2.readcard.CheckCardCallbackV2
import com.sunmi.pay.hardware.aidlv2.AidlConstantsV2
import java.util.*
import com.example.edc_app.EdcApplication

/**
 * 卡模块实现类
 * 负责调用商米SDK实现卡模块功能
 */
class CardModule(private val context: Context) {
    companion object {
        private const val TAG = "CardModule"
    }

    private val mainHandler = Handler(Looper.getMainLooper())
    
    private var checkCardCallback: CheckCardCallbackV2? = null
    private var resultCallback: MethodChannel.Result? = null
    private var channel: MethodChannel? = null
    
    /**
     * 设置 MethodChannel (如果需要回调 Flutter)
     */
    fun setChannel(channel: MethodChannel) {
        this.channel = channel
        Log.d(TAG, "MethodChannel set for CardModule")
    }
    
    /**
     * 开始检测NFC卡
     */
    fun startCheckNFCCard(result: MethodChannel.Result) {
        Log.d(TAG, "startCheckNFCCard called.")

        // 验证SDK状态和常量
        if (!validateSDKAndConstants()) {
            result.error("SDK_VALIDATION_FAILED", "SDK验证失败", null)
            return
        }

        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying to start NFC check. SDK might not be connected.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接或CardModule未正确初始化", null)
            return
        }

        try {
            Log.i(TAG, "Attempting to start NFC card check using $currentReadCardOptV2")

            // 检查 ReadCardOptV2 实例状态
            Log.d(TAG, "ReadCardOptV2 instance: $currentReadCardOptV2")
            Log.d(TAG, "ReadCardOptV2 class: ${currentReadCardOptV2.javaClass.name}")

            resultCallback = result
            
            checkCardCallback = object : CheckCardCallbackV2.Stub() {
                @Throws(RemoteException::class)
                override fun findMagCard(info: Bundle) {
                    Log.i(TAG, "🔍 findMagCard callback triggered: $info")
                }

                @Throws(RemoteException::class)
                override fun findICCardEx(info: Bundle) {
                    Log.i(TAG, "🔍 findICCardEx callback triggered: $info")
                }

                @Throws(RemoteException::class)
                override fun findRFCardEx(info: Bundle) {
                    Log.i(TAG, "🔍 findRFCardEx callback triggered from Binder thread: $info")
                    val cardType = info.getInt("cardType")
                    val uuid = info.getString("uuid") ?: ""
                    val ats = info.getString("ats") ?: ""
                    val cardCategory = info.getInt("cardCategory")
                    
                    val cardInfo = HashMap<String, Any>()
                    cardInfo["cardType"] = getCardTypeName(cardType)
                    cardInfo["cardCategory"] = getCardCategoryName(cardCategory)
                    cardInfo["uuid"] = uuid
                    cardInfo["ats"] = ats
                    
                    mainHandler.post {
                        Log.d(TAG, "Invoking onCardDetected on main thread")
                        channel?.invokeMethod("onCardDetected", cardInfo)
                    }
                }
                
                @Throws(RemoteException::class)
                override fun onErrorEx(info: Bundle) {
                    Log.i(TAG, "🔍 onErrorEx callback triggered from Binder thread: $info")
                    val code = info.getInt("code")
                    val message = info.getString("message") ?: "未知错误"
                    val cardType = info.getInt("cardType", -1)

                    Log.e(TAG, "Card check error - Code: $code, Message: $message, CardType: $cardType")

                    // 详细的错误分析
                    val detailedMessage = when (code) {
                        -20003 -> "参数错误: 检查卡类型参数是否正确"
                        -2001 -> "没有检测到卡片"
                        -2002 -> "检测到多张卡片"
                        -30003 -> "NFC检测失败"
                        -30004 -> "IC卡检测失败"
                        -30005 -> "读卡超时"
                        else -> message
                    }

                    val errorInfo = HashMap<String, Any>()
                    errorInfo["code"] = code
                    errorInfo["message"] = detailedMessage
                    errorInfo["originalMessage"] = message
                    if (cardType != -1) {
                        errorInfo["cardType"] = getCardTypeName(cardType)
                    }

                    mainHandler.post {
                        Log.d(TAG, "Invoking onCardError on main thread with detailed info")
                        channel?.invokeMethod("onCardError", errorInfo)
                    }
                }

                override fun findICCard(atr: String?) {
                    Log.i(TAG, "🔍 findICCard (legacy) callback triggered: $atr")
                }
                override fun findRFCard(uuid: String?) {
                    Log.i(TAG, "🔍 findRFCard (legacy) callback triggered: $uuid")
                }
                override fun onError(code: Int, message: String?) {
                    Log.i(TAG, "🔍 onError (legacy) callback triggered: code=$code, message=$message")
                }
            }
            
            // 使用正确的卡类型常量
            // 根据AidlConstantsV2定义：MAGNETIC=1, IC=2, NFC=4
            val cardType = AidlConstantsV2.CardType.MAGNETIC.getValue() or
                          AidlConstantsV2.CardType.IC.getValue() or
                          AidlConstantsV2.CardType.NFC.getValue()
            val timeout = 60   // 60秒超时

            Log.d(TAG, "Calling checkCard with cardType: $cardType (MAGNETIC|IC|NFC), timeout: ${timeout}s")
            Log.d(TAG, "CardType values - MAGNETIC: ${AidlConstantsV2.CardType.MAGNETIC.getValue()}, IC: ${AidlConstantsV2.CardType.IC.getValue()}, NFC: ${AidlConstantsV2.CardType.NFC.getValue()}")

            try {
                // 使用标准的checkCard方法而不是checkCardEx
                currentReadCardOptV2.checkCard(cardType, checkCardCallback, timeout)
                Log.d(TAG, "checkCard call completed successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Exception during checkCard call: ${e.message}", e)
                throw e
            }

            // 添加一个定时器来检查是否有响应
            mainHandler.postDelayed({
                Log.d(TAG, "⏰ 5秒后检查：NFC检测是否有响应...")
                Log.d(TAG, "   - checkCardCallback: $checkCardCallback")
                Log.d(TAG, "   - resultCallback: $resultCallback")

                // 测试回调是否工作 - 发送一个测试事件
                Log.d(TAG, "🧪 发送测试回调到Flutter...")
                val testInfo = HashMap<String, Any>()
                testInfo["cardType"] = "测试卡"
                testInfo["cardCategory"] = "测试类别"
                testInfo["uuid"] = "TEST-UUID-12345"
                testInfo["ats"] = "TEST-ATS"

                channel?.invokeMethod("onCardDetected", testInfo)
            }, 5000)

            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error starting NFC card check: ${e.message}")
            result.error("CHECK_CARD_ERROR", "检测NFC卡出错: ${e.message}", null)
        }
    }
    
    /**
     * 停止检测NFC卡
     */
    fun stopCheckNFCCard(result: MethodChannel.Result) {
        Log.d(TAG, "Stopping NFC card check")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying to stop NFC check.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }
        try {
            // 关闭NFC卡片检测
            currentReadCardOptV2.cardOff(AidlConstantsV2.CardType.NFC.getValue())
            currentReadCardOptV2.cancelCheckCard()
            checkCardCallback = null
            resultCallback = null

            Log.d(TAG, "NFC card check stopped successfully")
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping NFC card check: ${e.message}")
            result.error("CANCEL_CHECK_CARD_ERROR", "停止检测NFC卡出错: ${e.message}", null)
        }
    }
    
    /**
     * M1卡认证
     */
    fun m1Auth(sector: Int, keyType: Int, key: String, result: MethodChannel.Result) {
        Log.d(TAG, "M1 auth - sector: $sector, keyType: $keyType, key: $key")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            Log.e(TAG, "readCardOptV2 is null when trying M1 auth.")
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null)
            return
        }

        // 参数验证
        if (sector < 0 || sector > 15) {
            Log.e(TAG, "Invalid sector: $sector. Must be 0-15")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的扇区号: $sector，必须在0-15之间"
            result.success(response)
            return
        }

        if (keyType != 0 && keyType != 1) {
            Log.e(TAG, "Invalid keyType: $keyType. Must be 0 (KeyA) or 1 (KeyB)")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的密钥类型: $keyType，必须是0(KeyA)或1(KeyB)"
            result.success(response)
            return
        }

        if (key.replace(" ", "").length != 12) {
            Log.e(TAG, "Invalid key length: ${key.length}. Must be 12 hex characters")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的密钥长度: 必须是12位16进制字符"
            result.success(response)
            return
        }

        try {
            val keyBytes = hexStringToByteArray(key)
            if (keyBytes.size != 6) {
                Log.e(TAG, "Key bytes length is ${keyBytes.size}, expected 6")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "密钥长度错误: 必须是6字节"
                result.success(response)
                return
            }

            val block = sector * 4
            Log.d(TAG, "Calling mifareAuth with keyType: $keyType, block: $block, keyBytes: ${bytesToHexString(keyBytes)}")
            val code = currentReadCardOptV2.mifareAuth(keyType, block, keyBytes) ?: -1

            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号、密钥类型或密钥值"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    -2002 -> "检测到多张卡片"
                    else -> try {
                        AidlErrorCodeV2.valueOf(code).msg
                    } catch (e: Exception) {
                        "未知错误代码: $code"
                    }
                }

                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "认证失败: $errorMsg"
                response["code"] = code
                result.success(response)
            }
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "Parameter validation error in M1 auth: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "参数错误: ${e.message}"
            result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 auth: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "认证异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 读取M1卡数据块
     */
    fun m1ReadBlock(sector: Int, block: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 read block - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        if (sector < 0 || sector > 15) {
            Log.e(TAG, "Invalid sector: $sector. Must be 0-15")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的扇区号: $sector，必须在0-15之间"
            result.success(response)
            return
        }

        if (block < 0 || block > 3) {
            Log.e(TAG, "Invalid block: $block. Must be 0-3")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的块号: $block，必须在0-3之间"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            if (blockIndex < 0 || blockIndex > 63) {
                Log.e(TAG, "Invalid blockIndex: $blockIndex. Must be 0-63")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "无效的绝对块号: $blockIndex，必须在0-63之间"
                result.success(response)
                return
            }

            val blockData = ByteArray(16)
            Log.d(TAG, "Calling mifareReadBlock with blockIndex: $blockIndex")
            val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
            if (code >= 0 && code <= 16) {
                val hexStr = bytesToHexString(blockData.copyOf(code))
                val response = HashMap<String, Any>()
                response["success"] = true
                val data = HashMap<String, Any>()
                data["block$block"] = hexStr
                response["data"] = data
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号和块号"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    -30001 -> "读卡失败(未知原因)"
                    else -> try {
                        "读取失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "读取失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 read block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "读取异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 写入M1卡数据块
     */
    fun m1WriteBlock(sector: Int, block: Int, data: String, result: MethodChannel.Result) {
        Log.d(TAG, "M1 write block - sector: $sector, block: $block, data: $data")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        if (sector < 0 || sector > 15) {
            Log.e(TAG, "Invalid sector: $sector. Must be 0-15")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的扇区号: $sector，必须在0-15之间"
            result.success(response)
            return
        }

        if (block < 0 || block > 3) {
            Log.e(TAG, "Invalid block: $block. Must be 0-3")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的块号: $block，必须在0-3之间"
            result.success(response)
            return
        }

        // 检查是否尝试写入控制块（每个扇区的第3块）
        if (block == 3) {
            Log.w(TAG, "Warning: Attempting to write to control block (block 3) in sector $sector")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "不能写入控制块: 扇区 $sector 的块 3 是控制块"
            result.success(response)
            return
        }

        if (data.replace(" ", "").length != 32) {
            Log.e(TAG, "Invalid data length: ${data.length}. Must be 32 hex characters (16 bytes)")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的数据长度: 必须是32位16进制字符(16字节)"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            if (blockIndex < 0 || blockIndex > 63) {
                Log.e(TAG, "Invalid blockIndex: $blockIndex. Must be 0-63")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "无效的绝对块号: $blockIndex，必须在0-63之间"
                result.success(response)
                return
            }

            val blockData = hexStringToByteArray(data)
            if (blockData.size != 16) {
                Log.e(TAG, "Block data size is ${blockData.size}, expected 16")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "数据长度错误: 必须是16字节"
                result.success(response)
                return
            }

            Log.d(TAG, "Calling mifareWriteBlock with blockIndex: $blockIndex, data: ${bytesToHexString(blockData)}")
            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, blockData) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号、块号或数据格式"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    -30001 -> "写卡失败(未知原因)"
                    else -> try {
                        "写入失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "写入失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "Parameter validation error in M1 write block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "参数错误: ${e.message}"
            result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 write block: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "写入异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 初始化M1卡钱包
     */
    fun m1InitWallet(sector: Int, block: Int, result: MethodChannel.Result) {
        Log.d(TAG, "M1 init wallet - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        if (sector < 0 || sector > 15) {
            Log.e(TAG, "Invalid sector: $sector. Must be 0-15")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的扇区号: $sector，必须在0-15之间"
            result.success(response)
            return
        }

        if (block < 0 || block > 2) {
            Log.e(TAG, "Invalid block for wallet: $block. Must be 0-2 (not control block)")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的钱包块号: $block，钱包只能使用0-2块"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            if (blockIndex < 0 || blockIndex > 63) {
                Log.e(TAG, "Invalid blockIndex: $blockIndex. Must be 0-63")
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = "无效的绝对块号: $blockIndex，必须在0-63之间"
                result.success(response)
                return
            }

            val initData = getInitWalletData(blockIndex)
            Log.d(TAG, "Calling mifareWriteBlock for wallet init with blockIndex: $blockIndex, data: ${bytesToHexString(initData)}")
            val code = currentReadCardOptV2.mifareWriteBlock(blockIndex, initData) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号和块号"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "初始化钱包失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "初始化钱包失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 init wallet: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "初始化钱包异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 获取M1卡钱包余额
     */
    fun m1GetBalance(sector: Int, block: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 get balance - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateWalletParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            val blockData = ByteArray(16)
            Log.d(TAG, "Calling mifareReadBlock for balance with blockIndex: $blockIndex")
            val code = currentReadCardOptV2.mifareReadBlock(blockIndex, blockData) ?: -1
            if (code >= 0 && code <= 16) {
                val balance = getInt32FromBytes(blockData, 0, true)
                val response = HashMap<String, Any>()
                response["success"] = true
                response["balance"] = balance
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号和块号"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "获取余额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "获取余额失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 get balance: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "获取余额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 增加M1卡钱包金额
     */
    fun m1IncreaseValue(sector: Int, block: Int, amount: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 increase value - sector: $sector, block: $block, amount: $amount")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateWalletParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        if (amount <= 0) {
            Log.e(TAG, "Invalid amount: $amount. Must be positive")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的金额: $amount，必须大于0"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            val amountData = int32ToBytes(amount, true)
            Log.d(TAG, "Calling mifareIncValue with blockIndex: $blockIndex, amount: $amount")
            val code = currentReadCardOptV2.mifareIncValue(blockIndex, amountData) ?: -1
            if (code == 0) {
                m1GetBalance(sector, block, result)
                return
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号、块号或金额"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "增加金额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "增加金额失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 increase value: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "增加金额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 减少M1卡钱包金额
     */
    fun m1DecreaseValue(sector: Int, block: Int, amount: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 decrease value - sector: $sector, block: $block, amount: $amount")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
            result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateWalletParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        if (amount <= 0) {
            Log.e(TAG, "Invalid amount: $amount. Must be positive")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "无效的金额: $amount，必须大于0"
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            val amountData = int32ToBytes(amount, true)
            Log.d(TAG, "Calling mifareDecValue with blockIndex: $blockIndex, amount: $amount")
            val code = currentReadCardOptV2.mifareDecValue(blockIndex, amountData) ?: -1
            if (code == 0) {
                m1GetBalance(sector, block, result)
                return
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号、块号或金额"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "减少金额失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "减少金额失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 decrease value: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "减少金额异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * M1卡恢复操作
     */
    fun m1Restore(sector: Int, block: Int, result: MethodChannel.Result) {
         Log.d(TAG, "M1 restore - sector: $sector, block: $block")
        val currentReadCardOptV2 = EdcApplication.instance.readCardOptV2
        if (currentReadCardOptV2 == null) {
             result.error("SDK_NOT_INITIALIZED", "商米SDK未连接", null); return
        }

        // 参数验证
        val validationError = validateWalletParams(sector, block)
        if (validationError != null) {
            Log.e(TAG, "Parameter validation failed: $validationError")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = validationError
            result.success(response)
            return
        }

        try {
            val blockIndex = sector * 4 + block
            Log.d(TAG, "Calling mifareRestore with blockIndex: $blockIndex")
            val code = currentReadCardOptV2.mifareRestore(blockIndex) ?: -1
            if (code == 0) {
                val response = HashMap<String, Any>()
                response["success"] = true
                result.success(response)
            } else {
                val errorMsg = when (code) {
                    -1 -> "SDK调用失败"
                    -20003 -> "参数错误: 检查扇区号和块号"
                    -2000 -> "卡参数错误"
                    -2001 -> "没有卡片"
                    else -> try {
                        "恢复操作失败: ${AidlErrorCodeV2.valueOf(code).msg}"
                    } catch (e: Exception) {
                        "恢复操作失败: 未知错误代码 $code"
                    }
                }
                val response = HashMap<String, Any>()
                response["success"] = false
                response["message"] = errorMsg
                response["code"] = code
                result.success(response)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in M1 restore: ${e.message}")
            val response = HashMap<String, Any>()
            response["success"] = false
            response["message"] = "恢复操作异常: ${e.message}"
            result.success(response)
        }
    }
    
    /**
     * 验证SDK状态和常量
     */
    private fun validateSDKAndConstants(): Boolean {
        try {
            // 验证卡类型常量
            val magneticValue = AidlConstantsV2.CardType.MAGNETIC.getValue()
            val icValue = AidlConstantsV2.CardType.IC.getValue()
            val nfcValue = AidlConstantsV2.CardType.NFC.getValue()

            Log.d(TAG, "Card type constants - MAGNETIC: $magneticValue, IC: $icValue, NFC: $nfcValue")

            // 验证常量值是否合理
            if (magneticValue <= 0 || icValue <= 0 || nfcValue <= 0) {
                Log.e(TAG, "Invalid card type constants detected")
                return false
            }

            // 验证SDK实例
            val readCardOptV2 = EdcApplication.instance.readCardOptV2
            if (readCardOptV2 == null) {
                Log.e(TAG, "ReadCardOptV2 instance is null")
                return false
            }

            Log.d(TAG, "SDK validation passed")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "SDK validation failed: ${e.message}", e)
            return false
        }
    }

    /**
     * 验证钱包操作的通用参数
     */
    private fun validateWalletParams(sector: Int, block: Int): String? {
        if (sector < 0 || sector > 15) {
            return "无效的扇区号: $sector，必须在0-15之间"
        }

        if (block < 0 || block > 2) {
            return "无效的钱包块号: $block，钱包只能使用0-2块"
        }

        val blockIndex = sector * 4 + block
        if (blockIndex < 0 || blockIndex > 63) {
            return "无效的绝对块号: $blockIndex，必须在0-63之间"
        }

        return null // 验证通过
    }

    /**
     * 获取卡类型名称
     */
    private fun getCardTypeName(cardType: Int): String {
        return when (cardType) {
            AidlConstantsV2.CardType.MAGNETIC.getValue() -> "MAGNETIC" // 磁条卡
            AidlConstantsV2.CardType.IC.getValue() -> "IC" // IC卡
            AidlConstantsV2.CardType.NFC.getValue() -> "NFC" // NFC卡
            AidlConstantsV2.CardType.MIFARE.getValue() -> "MIFARE" // MIFARE卡
            AidlConstantsV2.CardType.FELICA.getValue() -> "FeliCa" // FeliCa卡
            AidlConstantsV2.CardType.MIFARE_UL.getValue() -> "MIFARE Ultralight" // MIFARE Ultralight
            AidlConstantsV2.CardType.MIFARE_PLUS.getValue() -> "MIFARE Plus" // MIFARE Plus
            AidlConstantsV2.CardType.MIFARE_DESFIRE.getValue() -> "MIFARE DESFire" // MIFARE DESFire
            else -> "未知卡类型($cardType)"
        }
    }
    
    /**
     * 获取卡类别名称
     */
    private fun getCardCategoryName(cardCategory: Int): String {
        return when (cardCategory.toChar()) {
            'A' -> "A"
            'B' -> "B"
            else -> "未知类别($cardCategory)"
        }
    }
    
    /**
     * 16进制字符串转字节数组
     */
    private fun hexStringToByteArray(hexString: String): ByteArray {
        val hexStr = hexString.replace(" ", "").uppercase()

        // 验证输入参数
        if (hexStr.isEmpty()) {
            throw IllegalArgumentException("Hex string cannot be empty")
        }

        if (hexStr.length % 2 != 0) {
            throw IllegalArgumentException("Hex string length must be even")
        }

        // 验证是否为有效的16进制字符
        if (!hexStr.matches(Regex("[0-9A-F]*"))) {
            throw IllegalArgumentException("Invalid hex string: contains non-hex characters")
        }

        val len = hexStr.length
        val result = ByteArray(len / 2)

        for (i in 0 until len step 2) {
            val high = Character.digit(hexStr[i], 16)
            val low = Character.digit(hexStr[i + 1], 16)
            if (high == -1 || low == -1) {
                throw IllegalArgumentException("Invalid hex character at position $i")
            }
            result[i / 2] = ((high shl 4) or low).toByte()
        }

        return result
    }
    
    /**
     * 字节数组转16进制字符串
     */
    private fun bytesToHexString(bytes: ByteArray): String {
        val hexChars = CharArray(bytes.size * 2)
        
        for (i in bytes.indices) {
            val v = bytes[i].toInt() and 0xFF
            hexChars[i * 2] = "0123456789ABCDEF"[v ushr 4]
            hexChars[i * 2 + 1] = "0123456789ABCDEF"[v and 0x0F]
        }
        
        return String(hexChars)
    }
    
    /**
     * 获取初始化钱包数据
     */
    private fun getInitWalletData(blockIndex: Int): ByteArray {
        val result = byteArrayOf(
            0x00, 0x00, 0x00, 0x00,
            0xFF.toByte(), 0xFF.toByte(), 0xFF.toByte(), 0xFF.toByte(),
            0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00
        )
        
        result[12] = (blockIndex and 0xFF).toByte()
        result[13] = (blockIndex and 0xFF).inv().toByte()
        result[14] = (blockIndex and 0xFF).toByte()
        result[15] = (blockIndex and 0xFF).inv().toByte()
        
        return result
    }
    
    /**
     * 字节数组转32位整数
     */
    private fun getInt32FromBytes(bytes: ByteArray, offset: Int, littleEndian: Boolean): Int {
        var result = 0
        
        if (littleEndian) {
            result = result or (bytes[offset].toInt() and 0xFF)
            result = result or ((bytes[offset + 1].toInt() and 0xFF) shl 8)
            result = result or ((bytes[offset + 2].toInt() and 0xFF) shl 16)
            result = result or ((bytes[offset + 3].toInt() and 0xFF) shl 24)
        } else {
            result = result or ((bytes[offset].toInt() and 0xFF) shl 24)
            result = result or ((bytes[offset + 1].toInt() and 0xFF) shl 16)
            result = result or ((bytes[offset + 2].toInt() and 0xFF) shl 8)
            result = result or (bytes[offset + 3].toInt() and 0xFF)
        }
        
        return result
    }
    
    /**
     * 32位整数转字节数组
     */
    private fun int32ToBytes(value: Int, littleEndian: Boolean): ByteArray {
        val bytes = ByteArray(4)
        
        if (littleEndian) {
            bytes[0] = (value and 0xFF).toByte()
            bytes[1] = ((value ushr 8) and 0xFF).toByte()
            bytes[2] = ((value ushr 16) and 0xFF).toByte()
            bytes[3] = ((value ushr 24) and 0xFF).toByte()
        } else {
            bytes[0] = ((value ushr 24) and 0xFF).toByte()
            bytes[1] = ((value ushr 16) and 0xFF).toByte()
            bytes[2] = ((value ushr 8) and 0xFF).toByte()
            bytes[3] = (value and 0xFF).toByte()
        }
        
        return bytes
    }
} 