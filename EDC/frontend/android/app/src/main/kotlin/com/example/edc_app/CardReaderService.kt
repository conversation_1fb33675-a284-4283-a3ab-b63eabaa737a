package com.example.edc_app

import android.os.Bundle
import android.util.Log
import com.sunmi.pay.hardware.aidlv2.readcard.CheckCardCallbackV2
import com.sunmi.pay.hardware.aidlv2.readcard.ReadCardOptV2

/**
 * 读卡服务类
 * 提供详细的读卡日志和诊断功能
 */
class CardReaderService {
    companion object {
        private const val TAG = "CardReaderService"
        
        // 卡片类型常量
        const val CARD_TYPE_MAGNETIC = 0x01
        const val CARD_TYPE_IC = 0x02
        const val CARD_TYPE_NFC = 0x04
        const val CARD_TYPE_ALL = CARD_TYPE_MAGNETIC or CARD_TYPE_IC or CARD_TYPE_NFC
        
        // 读卡超时时间（秒）
        const val READ_CARD_TIMEOUT = 60
    }
    
    private var readCardOptV2: ReadCardOptV2? = null
    private var isReading = false
    
    /**
     * 初始化读卡服务
     */
    fun initialize() {
        try {
            Log.i(TAG, "Initializing CardReaderService...")
            
            val app = EdcApplication.instance
            if (!app.isConnectPaySDK()) {
                Log.e(TAG, "PaySDK not connected, cannot initialize CardReader")
                return
            }
            
            readCardOptV2 = app.readCardOptV2
            
            if (readCardOptV2 != null) {
                Log.i(TAG, "CardReaderService initialized successfully")
                logCardReaderCapabilities()
            } else {
                Log.e(TAG, "ReadCardOptV2 is null, initialization failed")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing CardReaderService: ${e.message}", e)
        }
    }
    
    /**
     * 记录读卡器能力信息
     */
    private fun logCardReaderCapabilities() {
        try {
            Log.i(TAG, "=== Card Reader Capabilities ===")
            Log.i(TAG, "Magnetic Card Support: Available")
            Log.i(TAG, "IC Card Support: Available") 
            Log.i(TAG, "NFC Card Support: Available")
            Log.i(TAG, "ReadCardOptV2 Instance: $readCardOptV2")
            Log.i(TAG, "================================")
        } catch (e: Exception) {
            Log.e(TAG, "Error logging card reader capabilities: ${e.message}", e)
        }
    }
    
    /**
     * 开始读卡
     */
    fun startReadCard(
        cardType: Int = CARD_TYPE_ALL,
        timeout: Int = READ_CARD_TIMEOUT,
        callback: CardReadCallback? = null
    ) {
        try {
            Log.i(TAG, "=== Starting Card Read Operation ===")
            Log.i(TAG, "Card Type: ${getCardTypeString(cardType)}")
            Log.i(TAG, "Timeout: ${timeout}s")
            Log.i(TAG, "Current Reading Status: $isReading")
            
            if (isReading) {
                Log.w(TAG, "Card reading already in progress, ignoring new request")
                callback?.onError("Card reading already in progress")
                return
            }
            
            if (readCardOptV2 == null) {
                Log.e(TAG, "ReadCardOptV2 is null, cannot start reading")
                callback?.onError("Card reader not initialized")
                return
            }
            
            isReading = true
            Log.i(TAG, "Setting reading status to true")
            
            // 创建读卡参数
            val bundle = Bundle().apply {
                putInt("cardType", cardType)
                putInt("timeout", timeout)
            }
            
            Log.d(TAG, "Created read card bundle: $bundle")
            
            // 创建读卡回调
            val checkCardCallback = object : CheckCardCallbackV2.Stub() {
                override fun findMagCard(info: Bundle?) {
                    Log.i(TAG, "🔍 Magnetic card detected!")
                    logCardInfo("Magnetic", info)
                    isReading = false
                    callback?.onMagneticCardDetected(info)
                }

                override fun findICCard(atr: String?) {
                    Log.i(TAG, "🔍 IC card detected!")
                    Log.d(TAG, "IC Card ATR: $atr")
                    isReading = false
                    callback?.onICCardDetected(atr)
                }

                override fun findICCardEx(info: Bundle?) {
                    Log.i(TAG, "🔍 IC card detected (Extended)!")
                    logCardInfo("ICCardEx", info)
                    isReading = false
                    val atr = info?.getString("atr")
                    callback?.onICCardDetected(atr)
                }

                override fun findRFCard(uuid: String?) {
                    Log.i(TAG, "🔍 NFC/RF card detected!")
                    Log.d(TAG, "NFC Card UUID: $uuid")
                    isReading = false
                    callback?.onNFCCardDetected(uuid)
                }

                override fun findRFCardEx(info: Bundle?) {
                    Log.i(TAG, "🔍 NFC/RF card detected (Extended)!")
                    logCardInfo("RFCardEx", info)
                    isReading = false
                    val uuid = info?.getString("uuid")
                    callback?.onNFCCardDetected(uuid)
                }

                override fun onError(code: Int, message: String?) {
                    Log.e(TAG, "❌ Card read error - Code: $code, Message: $message")
                    isReading = false
                    callback?.onError("Error $code: $message")
                }

                override fun onErrorEx(info: Bundle?) {
                    Log.e(TAG, "❌ Card read error (Extended)!")
                    logCardInfo("ErrorEx", info)
                    val code = info?.getInt("code", -1) ?: -1
                    val message = info?.getString("message") ?: "Unknown error"
                    isReading = false
                    callback?.onError("Error $code: $message")
                }
            }
            
            Log.d(TAG, "Created CheckCardCallbackV2: $checkCardCallback")
            
            // 开始读卡
            Log.i(TAG, "Calling readCardOptV2.checkCard()...")
            readCardOptV2!!.checkCard(cardType, checkCardCallback, timeout)
            Log.i(TAG, "checkCard() call completed, waiting for callback...")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Exception in startReadCard: ${e.message}", e)
            isReading = false
            callback?.onError("Exception: ${e.message}")
        }
    }
    
    /**
     * 停止读卡
     */
    fun stopReadCard() {
        try {
            Log.i(TAG, "Stopping card read operation...")
            
            if (!isReading) {
                Log.d(TAG, "No card reading in progress")
                return
            }
            
            readCardOptV2?.let { reader ->
                Log.d(TAG, "Calling cancelCheckCard()...")
                reader.cancelCheckCard()
                Log.i(TAG, "Card reading cancelled")
            }
            
            isReading = false
            Log.i(TAG, "Reading status set to false")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping card read: ${e.message}", e)
            isReading = false
        }
    }
    
    /**
     * 获取卡片类型字符串
     */
    private fun getCardTypeString(cardType: Int): String {
        val types = mutableListOf<String>()
        if (cardType and CARD_TYPE_MAGNETIC != 0) types.add("Magnetic")
        if (cardType and CARD_TYPE_IC != 0) types.add("IC")
        if (cardType and CARD_TYPE_NFC != 0) types.add("NFC")
        return if (types.isEmpty()) "Unknown($cardType)" else types.joinToString("|")
    }
    
    /**
     * 记录卡片信息
     */
    private fun logCardInfo(cardType: String, info: Bundle?) {
        try {
            Log.i(TAG, "=== $cardType Card Information ===")
            info?.let { bundle ->
                for (key in bundle.keySet()) {
                    val value = bundle.get(key)
                    Log.d(TAG, "$key: $value")
                }
            } ?: Log.d(TAG, "No card information provided")
            Log.i(TAG, "================================")
        } catch (e: Exception) {
            Log.e(TAG, "Error logging card info: ${e.message}", e)
        }
    }
    
    /**
     * 获取读卡状态
     */
    fun getStatus(): String {
        return when {
            readCardOptV2 == null -> "Not initialized"
            isReading -> "Reading in progress"
            else -> "Ready"
        }
    }
    
    /**
     * 读卡回调接口
     */
    interface CardReadCallback {
        fun onMagneticCardDetected(info: Bundle?)
        fun onICCardDetected(atr: String?)
        fun onNFCCardDetected(uuid: String?)
        fun onError(message: String)
        fun onTimeout()
    }
}
