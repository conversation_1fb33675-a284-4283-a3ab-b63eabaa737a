package com.example.edc_app

import android.app.Application
import android.util.Log
import com.sunmi.pay.hardware.aidlv2.readcard.ReadCardOptV2
import com.sunmi.pay.hardware.aidlv2.system.BasicOptV2
import sunmi.paylib.SunmiPayKernel

class EdcApplication : Application() {
    companion object {
        private const val TAG = "EdcApplication"
        lateinit var instance: EdcApplication
    }

    // 支付SDK模块
    var readCardOptV2: ReadCardOptV2? = null
        private set // Make setter private to control assignment
    var basicOptV2: BasicOptV2? = null
        private set

    // 连接状态
    @Volatile private var connectPaySDK = false

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate called")
        instance = this
        
        // 初始化PaySDK
        bindPaySDKService()
    }

    /**
     * 检查是否已连接PaySDK
     */
    fun isConnectPaySDK(): Boolean {
        Log.d(TAG, "isConnectPaySDK called, status: $connectPaySDK")
        return connectPaySDK
    }

    /**
     * 初始化商米支付SDK
     */
    @Synchronized // Ensure thread safety during initialization
    fun bindPaySDKService() {
        if (connectPaySDK) {
            Log.d(TAG, "PaySDK already connected or connecting.")
            return
        }
        Log.d(TAG, "Attempting to bind PaySDK service...")
        try {
            val payKernel = SunmiPayKernel.getInstance()
            payKernel.initPaySDK(applicationContext, object : SunmiPayKernel.ConnectCallback {
                override fun onConnectPaySDK() {
                    try {
                        Log.i(TAG, "Successfully connected to SunmiPaySDK")
                        
                        // 获取各个功能模块
                        readCardOptV2 = payKernel.mReadCardOptV2
                        basicOptV2 = payKernel.mBasicOptV2
                        
                        Log.d(TAG, "readCardOptV2 assigned: $readCardOptV2")
                        Log.d(TAG, "basicOptV2 assigned: $basicOptV2")

                        // 更新连接状态
                        connectPaySDK = true
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing onConnectPaySDK: ${e.message}", e)
                        connectPaySDK = false
                        readCardOptV2 = null
                        basicOptV2 = null
                    }
                }
                
                override fun onDisconnectPaySDK() {
                    Log.e(TAG, "Disconnected from SunmiPaySDK")
                    connectPaySDK = false
                    readCardOptV2 = null
                    basicOptV2 = null
                }
            })
            Log.d(TAG, "SunmiPaySDK initialization sequence started")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start SunmiPaySDK initialization: ${e.message}", e)
            connectPaySDK = false
            readCardOptV2 = null
            basicOptV2 = null
        }
    }
}
