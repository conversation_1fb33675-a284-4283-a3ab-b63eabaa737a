# Flutter/Dart相关
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub/
.pub-cache/
/build/
pubspec.lock

# IDE相关
.idea/
.vscode/
*.iml
*.ipr
*.iws
.DS_Store

# iOS相关
**/ios/Flutter/.last_build_id
**/ios/Flutter/ephemeral
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/.symlinks/
**/ios/Pods/
**/ios/.generated/
**/ios/Runner/GeneratedPluginRegistrant.*

# Android相关
**/android/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/key.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/app/debug
**/android/app/profile
**/android/app/release

# Web相关
**/web/favicon.png
**/web/icons/
**/web/manifest.json

# 日志文件
*.log

# 环境变量文件
.env*

# 缓存文件
.fvm/
coverage/
doc/api/
.flutter-plugins-dependencies

# 编译文件
*.dill
*.dill.track.dill.sum
*.jsbundle
*.pyc
*.class

# 测试相关
/coverage/
.test_coverage.dart

# 其他可能需要忽略的文件
*.bak
*.swp
*.tmp
*~
.history/ 
