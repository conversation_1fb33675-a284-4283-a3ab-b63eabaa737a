# Authentication API v2 Integration - EDC System

## Overview

This document outlines the integration of the new Authentication API v2 with the EDC (Electronic Data Capture) Flutter application. The integration includes system-based access control and enhanced error handling.

## Changes Made

### 1. Updated Employee API (`lib/services/api/employee_api.dart`)

#### New Models Added:
- **SystemAccess**: Handles system-specific access information
  - `system`: Target system (EDC/BOS)
  - `accessLevel`: User's role level
  - `stationIds`: Accessible station IDs
  - `stationCount`: Number of accessible stations

#### Updated LoginResponse:
- Added `systemAccess` field to handle new API v2 response structure
- Maintains backward compatibility with existing response format

#### Updated Login Method:
- Now sends `system: 'EDC'` parameter in login requests
- Handles new API v2 response structure

#### New Methods Added:
- `refreshToken()`: Implements token refresh functionality

### 2. Enhanced AuthService (`lib/screens/auth/auth_service.dart`)

#### New Storage Keys:
- `_refreshTokenKey`: Stores refresh token
- `_systemKey`: Stores current system (EDC)
- `_accessLevelKey`: Stores user's access level

#### New Methods:
- `getCurrentSystem()`: Get current system
- `getCurrentAccessLevel()`: Get user's access level
- `canAccessSystem(String system)`: Check system access permissions
- `hasManagementAccess()`: Check if user has management permissions
- `refreshToken()`: Refresh access token using refresh token

#### Enhanced State Management:
- Properly stores and manages SystemAccess information
- Maintains backward compatibility with existing data structure
- Enhanced logout with server-side logout call

### 3. Updated Login Page (`lib/screens/login/login_page.dart`)

#### Enhanced Error Handling:
- Specific error messages for API v2 error codes:
  - `4001`: No station association found
  - `4002`: Insufficient permissions for EDC system
  - `4003`: System configuration error
  - `1001`: Invalid username or password
- Improved user experience with contextual error messages

## API v2 Integration Details

### Login Request Format
```json
{
  "username": "employee_id",
  "password": "password",
  "system": "EDC"
}
```

### Expected Response Format
```json
{
  "code": 0,
  "message": "Login successful",
  "data": {
    "accessToken": "jwt_token",
    "refreshToken": "refresh_token",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "user": {
      "id": "user_id",
      "username": "employee_id",
      "email": "<EMAIL>",
      "fullName": "User Name",
      "roles": ["role1", "role2"],
      "permissions": ["perm1", "perm2"]
    },
    "systemAccess": {
      "system": "EDC",
      "accessLevel": "operator",
      "stationIds": [1, 2, 3],
      "stationCount": 3
    }
  }
}
```

### Error Response Handling
The application now handles specific error codes:
- **4001**: User has no station associations
- **4002**: Insufficient permissions for BOS system (shouldn't occur for EDC)
- **4003**: Invalid system parameter
- **1001**: Invalid credentials

## Testing Instructions

### 1. Prerequisites
- Ensure the backend API v2 is running and accessible
- Update API base URL in `lib/constants/api_constants.dart` if needed

### 2. Test Scenarios

#### Successful Login (EDC System)
1. Launch the app
2. Enter valid credentials (default: admin/Admin123)
3. Verify successful login with welcome message
4. Check that user is redirected to main screen

#### Error Handling Tests
1. **Invalid Credentials**: Enter wrong username/password
   - Expected: "Invalid username or password. Please try again."

2. **No Station Association**: Use account with no station assignments
   - Expected: "No station association found. Please contact administrator."

3. **Network Error**: Disconnect network during login
   - Expected: "Network error. Please check your connection and try again."

### 3. Debug Information
The application provides extensive debug logging:
- Login flow tracking
- SystemAccess information parsing
- Token management
- Error handling details

Check Flutter console for debug output prefixed with:
- `🔄` - Initialization
- `💾` - State saving
- `🧹` - State clearing
- `✅` - Success operations
- `❌` - Error operations

## Backward Compatibility

The integration maintains full backward compatibility:
- Existing API responses without `systemAccess` are handled gracefully
- Station IDs are extracted from both new and old response formats
- All existing functionality continues to work

## Security Enhancements

1. **Token Management**: Proper storage and handling of both access and refresh tokens
2. **System Access Control**: Validates user permissions for EDC system
3. **Secure Logout**: Calls server-side logout endpoint before clearing local state
4. **Error Information**: Sanitized error messages that don't expose sensitive information

## Future Enhancements

1. **Automatic Token Refresh**: Implement automatic token refresh on API calls
2. **Role-Based UI**: Show/hide features based on user's access level
3. **Station Filtering**: Filter data based on user's accessible stations
4. **Session Management**: Enhanced session timeout handling

## Files Modified

1. `lib/services/api/employee_api.dart` - API integration
2. `lib/screens/auth/auth_service.dart` - Authentication service
3. `lib/screens/login/login_page.dart` - Login UI and error handling

## Configuration

No additional configuration is required. The system automatically:
- Sends `system: 'EDC'` in login requests
- Handles both API v1 and v2 response formats
- Stores system access information locally
- Provides appropriate error messages

The integration is ready for production use and testing.