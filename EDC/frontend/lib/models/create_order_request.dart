// 创建订单请求模型
// 用于向API发送创建订单的请求数据

class CreateOrderRequest {
  // 构造函数
  CreateOrderRequest({
    required this.fuelTransactionId,
    required this.paymentMethod,
    required this.stationId,
    this.paymentType, // 支付类型：CASH, BANK_CARD, POINTS, VOUCHER, TERA等
    this.allocatedAmount,
    this.customerId,
    this.customerName,
    this.customerPhone, // 新增：客户手机号
    this.vehicleType, // 新增：车型（Car, Motorbike, Truck等）
    this.licensePlate, // 新增：车牌号
    this.employeeNo,
    this.terminalId, // 新增：终端ID
    this.metadata,
  });

  final String fuelTransactionId; // 必须：关联的燃油交易ID
  final int paymentMethod; // 必须：支付方式ID，匹配后端API期望的数字类型
  final int stationId; // 必须：加油站ID
  final String? paymentType; // 可选但重要：支付类型，后端用于业务逻辑验证
  final double? allocatedAmount; // 可选：分配金额
  final int? customerId; // 可选：客户ID
  final String? customerName; // 可选：客户姓名
  final String? customerPhone; // 可选：客户手机号
  final String? vehicleType; // 可选：车型（Car, Motorbike, Truck等）
  final String? licensePlate; // 可选：车牌号
  final String? employeeNo; // 可选：员工编号
  final String? terminalId; // 可选：终端ID
  final Map<String, dynamic>? metadata; // 可选：元数据

  // 转换为JSON的方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'fuel_transaction_id': fuelTransactionId,
      'payment_method': paymentMethod,
      'station_id': stationId,
    };

    // 可选字段
    if (paymentType != null) {
      json['payment_type'] = paymentType;
      // 添加B2B标识
      json['is_b2b'] = paymentType == 'B2B';
    }
    if (allocatedAmount != null) json['allocated_amount'] = allocatedAmount;
    if (customerId != null) json['customer_id'] = customerId;
    if (customerName != null) json['customer_name'] = customerName;
    if (customerPhone != null) json['customer_phone'] = customerPhone;
    if (vehicleType != null) json['vehicle_type'] = vehicleType;
    if (licensePlate != null) json['license_plate'] = licensePlate;
    if (employeeNo != null) json['employee_no'] = employeeNo;
    if (terminalId != null) json['terminal_id'] = terminalId;
    if (metadata != null) {
      json['metadata'] = {
        ...metadata!,
        // 在metadata中也添加B2B信息，便于后端处理
        if (paymentType == 'B2B') 'is_b2b_payment': true,
      };
    } else if (paymentType == 'B2B') {
      json['metadata'] = {'is_b2b_payment': true};
    }

    return json;
  }

  /// 验证必需字段是否完整
  String? validate() {
    if (fuelTransactionId.isEmpty) {
      return 'fuel_transaction_id不能为空';
    }
    if (paymentMethod <= 0) {
      return 'payment_method必须大于0';
    }
    if (stationId <= 0) {
      return 'station_id必须大于0';
    }
    
    return null; // 验证通过
  }
}
