/// 班次报表数据模型
///
/// 根据班次报表接口API文档定义的完整数据结构
/// API路径: GET /shifts/report/{id}
library;

// 班次基本信息
class ShiftInfo {
  ShiftInfo({
    required this.id,
    required this.shiftNumber,
    required this.stationId,
    required this.stationName,
    this.staffId,
    this.staffName,
    required this.startTime,
    this.endTime,
    required this.durationHours,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ShiftInfo.fromJson(Map<String, dynamic> json) {
    return ShiftInfo(
      id: json['id'] as int,
      shiftNumber: json['shift_number'] as String,
      stationId: json['station_id'] as int,
      stationName: json['station_name'] as String,
      staffId: json['staff_id'] as int?,
      staffName: json['staff_name'] as String?,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'] as String)
          : null,
      durationHours: (json['duration_hours'] as num).toDouble(),
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }
  final int id;
  final String shiftNumber;
  final int stationId;
  final String stationName;
  final int? staffId;
  final String? staffName;
  final DateTime startTime;
  final DateTime? endTime;
  final double durationHours;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'shift_number': shiftNumber,
      'station_id': stationId,
      'station_name': stationName,
      if (staffId != null) 'staff_id': staffId,
      if (staffName != null) 'staff_name': staffName,
      'start_time': startTime.toIso8601String(),
      if (endTime != null) 'end_time': endTime!.toIso8601String(),
      'duration_hours': durationHours,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

// 支付方式汇总
class PaymentMethodSummary {
  PaymentMethodSummary({
    required this.method,
    required this.methodName,
    required this.amount,
    required this.transactionCount,
    required this.percentage,
  });

  factory PaymentMethodSummary.fromJson(Map<String, dynamic> json) {
    return PaymentMethodSummary(
      method: json['method'] as String? ?? '',
      methodName: json['method_name'] as String? ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      transactionCount: json['transaction_count'] as int? ?? 0,
      percentage: (json['percentage'] as num?)?.toDouble() ?? 0.0,
    );
  }
  final String method;
  final String methodName;
  final double amount;
  final int transactionCount;
  final double percentage;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'method': method,
      'method_name': methodName,
      'amount': amount,
      'transaction_count': transactionCount,
      'percentage': percentage,
    };
  }
}

// 支付汇总数据
class PaymentSummary {
  PaymentSummary({
    required this.totalSales,
    required this.totalTransactions,
    this.paymentMethods,
  });

  factory PaymentSummary.fromJson(Map<String, dynamic> json) {
    return PaymentSummary(
      totalSales: (json['total_sales'] as num?)?.toDouble() ?? 0.0,
      totalTransactions: json['total_transactions'] as int? ?? 0,
      paymentMethods: json['payment_methods'] != null
          ? (json['payment_methods'] as List)
              .map((e) =>
                  PaymentMethodSummary.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }
  final double totalSales;
  final int totalTransactions;
  final List<PaymentMethodSummary>? paymentMethods;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'total_sales': totalSales,
      'total_transactions': totalTransactions,
      'payment_methods':
          paymentMethods?.map((PaymentMethodSummary e) => e.toJson()).toList(),
    };
  }
}

// 油品等级信息
class FuelGrade {
  FuelGrade({
    required this.grade,
    required this.type,
    required this.name,
    required this.volume,
    required this.grossAmount,
    required this.discountAmount,
    required this.netAmount,
    required this.averagePrice,
    required this.transactionCount,
    required this.volumePercentage,
  });

  factory FuelGrade.fromJson(Map<String, dynamic> json) {
    return FuelGrade(
      grade: json['grade'] as String? ?? '',
      type: json['type'] as String? ?? '',
      name: json['name'] as String? ?? '',
      volume: (json['volume'] as num?)?.toDouble() ?? 0.0,
      grossAmount: (json['gross_amount'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      netAmount: (json['net_amount'] as num?)?.toDouble() ?? 0.0,
      averagePrice: (json['average_price'] as num?)?.toDouble() ?? 0.0,
      transactionCount: json['transaction_count'] as int? ?? 0,
      volumePercentage: (json['volume_percentage'] as num?)?.toDouble() ?? 0.0,
    );
  }
  final String grade;
  final String type;
  final String name;
  final double volume;
  final double grossAmount;
  final double discountAmount;
  final double netAmount;
  final double averagePrice;
  final int transactionCount;
  final double volumePercentage;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'grade': grade,
      'type': type,
      'name': name,
      'volume': volume,
      'gross_amount': grossAmount,
      'discount_amount': discountAmount,
      'net_amount': netAmount,
      'average_price': averagePrice,
      'transaction_count': transactionCount,
      'volume_percentage': volumePercentage,
    };
  }
}

// 油品销售汇总
class FuelSummary {
  FuelSummary({
    required this.totalVolume,
    required this.totalGrossSales,
    required this.totalDiscount,
    required this.totalNetSales,
    required this.totalTransactions,
    this.fuelGrades,
  });

  factory FuelSummary.fromJson(Map<String, dynamic> json) {
    return FuelSummary(
      totalVolume: (json['total_volume'] as num?)?.toDouble() ?? 0.0,
      totalGrossSales: (json['total_gross_sales'] as num?)?.toDouble() ?? 0.0,
      totalDiscount: (json['total_discount'] as num?)?.toDouble() ?? 0.0,
      totalNetSales: (json['total_net_sales'] as num?)?.toDouble() ?? 0.0,
      totalTransactions: json['total_transactions'] as int? ?? 0,
      fuelGrades: json['fuel_grades'] != null
          ? (json['fuel_grades'] as List)
              .map((e) => FuelGrade.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }
  final double totalVolume;
  final double totalGrossSales;
  final double totalDiscount;
  final double totalNetSales;
  final int totalTransactions;
  final List<FuelGrade>? fuelGrades;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'total_volume': totalVolume,
      'total_gross_sales': totalGrossSales,
      'total_discount': totalDiscount,
      'total_net_sales': totalNetSales,
      'total_transactions': totalTransactions,
      'fuel_grades': fuelGrades?.map((FuelGrade e) => e.toJson()).toList(),
    };
  }
}

// 热销商品信息
class TopProduct {
  TopProduct({
    required this.productId,
    required this.productName,
    required this.productType,
    required this.category,
    required this.quantity,
    required this.unitPrice,
    required this.grossAmount,
    required this.discountAmount,
    required this.netAmount,
    required this.transactionCount,
  });

  factory TopProduct.fromJson(Map<String, dynamic> json) {
    return TopProduct(
      productId: json['product_id'] as int? ?? 0,
      productName: json['product_name'] as String? ?? '',
      productType: json['product_type'] as String? ?? '',
      category: json['category'] as String? ?? '',
      quantity: json['quantity'] as int? ?? 0,
      unitPrice: (json['unit_price'] as num?)?.toDouble() ?? 0.0,
      grossAmount: (json['gross_amount'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      netAmount: (json['net_amount'] as num?)?.toDouble() ?? 0.0,
      transactionCount: json['transaction_count'] as int? ?? 0,
    );
  }
  final int productId;
  final String productName;
  final String productType;
  final String category;
  final int quantity;
  final double unitPrice;
  final double grossAmount;
  final double discountAmount;
  final double netAmount;
  final int transactionCount;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'product_id': productId,
      'product_name': productName,
      'product_type': productType,
      'category': category,
      'quantity': quantity,
      'unit_price': unitPrice,
      'gross_amount': grossAmount,
      'discount_amount': discountAmount,
      'net_amount': netAmount,
      'transaction_count': transactionCount,
    };
  }
}

// 非油品销售汇总
class MerchandiseSummary {
  MerchandiseSummary({
    required this.totalQuantity,
    required this.totalGrossSales,
    required this.totalDiscount,
    required this.totalNetSales,
    required this.totalTransactions,
    this.topProducts,
  });

  factory MerchandiseSummary.fromJson(Map<String, dynamic> json) {
    return MerchandiseSummary(
      totalQuantity: json['total_quantity'] as int? ?? 0,
      totalGrossSales: (json['total_gross_sales'] as num?)?.toDouble() ?? 0.0,
      totalDiscount: (json['total_discount'] as num?)?.toDouble() ?? 0.0,
      totalNetSales: (json['total_net_sales'] as num?)?.toDouble() ?? 0.0,
      totalTransactions: json['total_transactions'] as int? ?? 0,
      topProducts: json['top_products'] != null
          ? (json['top_products'] as List)
              .map((e) => TopProduct.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
    );
  }
  final int totalQuantity;
  final double totalGrossSales;
  final double totalDiscount;
  final double totalNetSales;
  final int totalTransactions;
  final List<TopProduct>? topProducts;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'total_quantity': totalQuantity,
      'total_gross_sales': totalGrossSales,
      'total_discount': totalDiscount,
      'total_net_sales': totalNetSales,
      'total_transactions': totalTransactions,
      'top_products': topProducts?.map((TopProduct e) => e.toJson()).toList(),
    };
  }
}

// TERA 分类数据
class TeraCategory {
  TeraCategory({
    required this.grossSales,
    required this.totalDiscount,
    required this.netSales,
    required this.percentage,
  });

  factory TeraCategory.fromJson(Map<String, dynamic> json) {
    return TeraCategory(
      grossSales: (json['gross_sales'] as num?)?.toDouble() ?? 0.0,
      totalDiscount: (json['total_discount'] as num?)?.toDouble() ?? 0.0,
      netSales: (json['net_sales'] as num?)?.toDouble() ?? 0.0,
      percentage: (json['percentage'] as num?)?.toDouble() ?? 0.0,
    );
  }
  final double grossSales;
  final double totalDiscount;
  final double netSales;
  final double percentage;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'gross_sales': grossSales,
      'total_discount': totalDiscount,
      'net_sales': netSales,
      'percentage': percentage,
    };
  }
}

// TERA 汇总数据
class TeraSummary {
  TeraSummary({
    required this.fuel,
    required this.merchandise,
    required this.total,
  });

  factory TeraSummary.fromJson(Map<String, dynamic> json) {
    return TeraSummary(
      fuel: TeraCategory.fromJson(json['fuel'] as Map<String, dynamic>),
      merchandise:
          TeraCategory.fromJson(json['merchandise'] as Map<String, dynamic>),
      total: TeraCategory.fromJson(json['total'] as Map<String, dynamic>),
    );
  }
  final TeraCategory fuel;
  final TeraCategory merchandise;
  final TeraCategory total;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'fuel': fuel.toJson(),
      'merchandise': merchandise.toJson(),
      'total': total.toJson(),
    };
  }
}

// 小票信息
class ReceiptInfo {
  ReceiptInfo({
    required this.printTime,
    required this.receiptNumber,
    required this.currency,
    required this.timezone,
  });

  factory ReceiptInfo.fromJson(Map<String, dynamic> json) {
    return ReceiptInfo(
      printTime: DateTime.parse(json['print_time'] as String),
      receiptNumber: json['receipt_number'] as String,
      currency: json['currency'] as String,
      timezone: json['timezone'] as String,
    );
  }
  final DateTime printTime;
  final String receiptNumber;
  final String currency;
  final String timezone;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'print_time': printTime.toIso8601String(),
      'receipt_number': receiptNumber,
      'currency': currency,
      'timezone': timezone,
    };
  }
}

// 元数据
class ShiftReportMeta {
  ShiftReportMeta({
    required this.generatedAt,
    required this.processingTimeMs,
    required this.dataSource,
    required this.version,
  });

  factory ShiftReportMeta.fromJson(Map<String, dynamic> json) {
    return ShiftReportMeta(
      generatedAt: DateTime.parse(json['generated_at'] as String),
      processingTimeMs: json['processing_time_ms'] as int,
      dataSource: json['data_source'] as String,
      version: json['version'] as String,
    );
  }
  final DateTime generatedAt;
  final int processingTimeMs;
  final String dataSource;
  final String version;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'generated_at': generatedAt.toIso8601String(),
      'processing_time_ms': processingTimeMs,
      'data_source': dataSource,
      'version': version,
    };
  }
}

// 班次报表完整数据
class ShiftReportData {
  ShiftReportData({
    required this.shiftInfo,
    required this.paymentSummary,
    required this.fuelSummary,
    required this.merchandiseSummary,
    required this.teraSummary,
    required this.receiptInfo,
  });

  factory ShiftReportData.fromJson(Map<String, dynamic> json) {
    return ShiftReportData(
      shiftInfo: ShiftInfo.fromJson(json['shift_info'] as Map<String, dynamic>),
      paymentSummary: PaymentSummary.fromJson(
          json['payment_summary'] as Map<String, dynamic>),
      fuelSummary:
          FuelSummary.fromJson(json['fuel_summary'] as Map<String, dynamic>),
      merchandiseSummary: MerchandiseSummary.fromJson(
          json['merchandise_summary'] as Map<String, dynamic>),
      teraSummary:
          TeraSummary.fromJson(json['tera_summary'] as Map<String, dynamic>),
      receiptInfo:
          ReceiptInfo.fromJson(json['receipt_info'] as Map<String, dynamic>),
    );
  }
  final ShiftInfo shiftInfo;
  final PaymentSummary paymentSummary;
  final FuelSummary fuelSummary;
  final MerchandiseSummary merchandiseSummary;
  final TeraSummary teraSummary;
  final ReceiptInfo receiptInfo;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'shift_info': shiftInfo.toJson(),
      'payment_summary': paymentSummary.toJson(),
      'fuel_summary': fuelSummary.toJson(),
      'merchandise_summary': merchandiseSummary.toJson(),
      'tera_summary': teraSummary.toJson(),
      'receipt_info': receiptInfo.toJson(),
    };
  }
}

// 班次报表响应模型
class ShiftReportResponse {
  ShiftReportResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.meta,
  });

  factory ShiftReportResponse.fromJson(Map<String, dynamic> json) {
    return ShiftReportResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: ShiftReportData.fromJson(json['data'] as Map<String, dynamic>),
      meta: ShiftReportMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );
  }
  final bool success;
  final String message;
  final ShiftReportData data;
  final ShiftReportMeta meta;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'success': success,
      'message': message,
      'data': data.toJson(),
      'meta': meta.toJson(),
    };
  }
}

// === 小票格式相关模型 ===

// 小票头部信息
class ReceiptHeader {
  ReceiptHeader({
    required this.stationName,
    required this.address,
    required this.phone,
  });

  factory ReceiptHeader.fromJson(Map<String, dynamic> json) {
    return ReceiptHeader(
      stationName: json['station_name'] as String,
      address: json['address'] as String,
      phone: json['phone'] as String,
    );
  }
  final String stationName;
  final String address;
  final String phone;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'station_name': stationName,
      'address': address,
      'phone': phone,
    };
  }
}

// 小票班次信息
class ReceiptShiftInfo {
  ReceiptShiftInfo({
    required this.shiftNumber,
    required this.staffName,
    required this.startTime,
    required this.endTime,
  });

  factory ReceiptShiftInfo.fromJson(Map<String, dynamic> json) {
    return ReceiptShiftInfo(
      shiftNumber: json['shift_number'] as String,
      staffName: json['staff_name'] as String,
      startTime: json['start_time'] as String,
      endTime: json['end_time'] as String,
    );
  }
  final String shiftNumber;
  final String staffName;
  final String startTime;
  final String endTime;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'shift_number': shiftNumber,
      'staff_name': staffName,
      'start_time': startTime,
      'end_time': endTime,
    };
  }
}

// 小票底部信息
class ReceiptFooter {
  ReceiptFooter({
    required this.printTime,
    required this.receiptNumber,
    required this.thankYouMessage,
  });

  factory ReceiptFooter.fromJson(Map<String, dynamic> json) {
    return ReceiptFooter(
      printTime: json['print_time'] as String,
      receiptNumber: json['receipt_number'] as String,
      thankYouMessage: json['thank_you_message'] as String,
    );
  }
  final String printTime;
  final String receiptNumber;
  final String thankYouMessage;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'print_time': printTime,
      'receipt_number': receiptNumber,
      'thank_you_message': thankYouMessage,
    };
  }
}

// 小票内容
class ReceiptContent {
  ReceiptContent({
    required this.header,
    required this.shiftInfo,
    required this.salesSummary,
    required this.footer,
  });

  factory ReceiptContent.fromJson(Map<String, dynamic> json) {
    return ReceiptContent(
      header: ReceiptHeader.fromJson(json['header'] as Map<String, dynamic>),
      shiftInfo:
          ReceiptShiftInfo.fromJson(json['shift_info'] as Map<String, dynamic>),
      salesSummary: (json['sales_summary'] as List).cast<String>(),
      footer: ReceiptFooter.fromJson(json['footer'] as Map<String, dynamic>),
    );
  }
  final ReceiptHeader header;
  final ReceiptShiftInfo shiftInfo;
  final List<String> salesSummary;
  final ReceiptFooter footer;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'header': header.toJson(),
      'shift_info': shiftInfo.toJson(),
      'sales_summary': salesSummary,
      'footer': footer.toJson(),
    };
  }
}

// 小票数据
class ReceiptData {
  ReceiptData({
    required this.receipt,
  });

  factory ReceiptData.fromJson(Map<String, dynamic> json) {
    return ReceiptData(
      receipt: ReceiptContent.fromJson(json['receipt'] as Map<String, dynamic>),
    );
  }
  final ReceiptContent receipt;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'receipt': receipt.toJson(),
    };
  }
}

// 小票响应模型
class ShiftReceiptResponse {
  ShiftReceiptResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory ShiftReceiptResponse.fromJson(Map<String, dynamic> json) {
    return ShiftReceiptResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: ReceiptData.fromJson(json['data'] as Map<String, dynamic>),
    );
  }
  final bool success;
  final String message;
  final ReceiptData data;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'success': success,
      'message': message,
      'data': data.toJson(),
    };
  }
}
