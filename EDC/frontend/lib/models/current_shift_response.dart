import 'shift_model.dart';

/// 当前班次查询响应模型
class CurrentShiftResponse {
  final bool hasActiveShift;
  final ShiftModel? shift;
  final String message;

  CurrentShiftResponse({
    required this.hasActiveShift,
    this.shift,
    required this.message,
  });

  /// 从JSON创建实例
  factory CurrentShiftResponse.fromJson(Map<String, dynamic> json) {
    return CurrentShiftResponse(
      hasActiveShift: json['has_active_shift'] as bool? ?? false,
      shift: json['shift'] != null ? ShiftModel.fromJson(json['shift'] as Map<String, dynamic>) : null,
      message: json['message'] as String? ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'has_active_shift': hasActiveShift,
      'shift': shift?.toJson(),
      'message': message,
    };
  }

  @override
  String toString() {
    return 'CurrentShiftResponse{hasActiveShift: $hasActiveShift, shift: $shift, message: $message}';
  }
}

/// 班次元数据模型
class ShiftMetadata {
  final int? employeeId;
  final String? notes;

  ShiftMetadata({
    this.employeeId,
    this.notes,
  });

  /// 从JSON创建实例
  factory ShiftMetadata.fromJson(Map<String, dynamic> json) {
    return ShiftMetadata(
      employeeId: json['employee_id'] as int?,
      notes: json['notes'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'employee_id': employeeId,
      'notes': notes,
    };
  }

  @override
  String toString() {
    return 'ShiftMetadata{employeeId: $employeeId, notes: $notes}';
  }
}
