import 'package:flutter/foundation.dart';
import 'member_model.dart';
import 'fuel_transaction.dart';

/// 支付交易数据传输对象
/// 统一规范支付流程中的数据结构，确保类型安全
class PaymentTransactionData {
  const PaymentTransactionData({
    required this.transactionId,
    required this.stationId,
    required this.transactionRef,
    required this.pumpId,
    required this.nozzleId,
    required this.fuelType,
    required this.fuelGrade,
    required this.totalAmount,
    required this.volume,
    required this.unitPrice,
    required this.status,
    required this.employeeId,
    required this.employeeNo,
    required this.createdAt,
    this.paymentMethodId,
    this.paymentMethodName,
    this.paymentMethodType,
    this.paymentMethodConfig,
    this.memberInfo,
    this.metadata = const <String, dynamic>{},
  });

  /// 从 JSON Map 创建实例
  factory PaymentTransactionData.fromJson(Map<String, dynamic> json) {
    return PaymentTransactionData(
      transactionId: json['transactionId'] as String,
      stationId: json['stationId'] as int,
      transactionRef: json['transactionRef'] as String,
      pumpId: json['pumpId'] as String,
      nozzleId: json['nozzleId'] as String,
      fuelType: json['fuelType'] as String,
      fuelGrade: json['fuelGrade'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      volume: (json['volume'] as num).toDouble(),
      unitPrice: (json['unitPrice'] as num).toDouble(),
      status: json['status'] as String,
      employeeId: json['employeeId'] as String,
      employeeNo: json['employeeNo'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      paymentMethodId: _safeParseInt(json['paymentMethodId']),
      paymentMethodName: json['paymentMethodName'] as String?,
      paymentMethodType: json['paymentMethodType'] as String?,
      paymentMethodConfig: json['paymentMethodConfig'] as Map<String, dynamic>?,
      memberInfo: json['memberInfo'] != null
          ? Member.fromJson(json['memberInfo'] as Map<String, dynamic>)
          : null,
      metadata:
          (json['metadata'] as Map<String, dynamic>?) ?? <String, dynamic>{},
    );
  }

  /// 安全地解析整数字段
  /// 支持 String 和 int 类型的输入
  static int? _safeParseInt(dynamic value) {
    if (value == null) return null;
    
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      if (value.isEmpty || value == 'null') return null;
      return int.tryParse(value);
    }
    
    // 尝试转换其他类型
    try {
      if (value is num) {
        return value.toInt();
      }
      // 尝试转换为字符串再解析
      final String stringValue = value.toString();
      if (stringValue.isEmpty || stringValue == 'null') return null;
      return int.tryParse(stringValue);
    } catch (e) {
      // 转换失败，返回 null
      return null;
    }
  }

  /// 从燃油交易创建实例
  factory PaymentTransactionData.fromFuelTransaction({
    required FuelTransaction transaction,
    required String employeeId,
    required String employeeNo,
    Member? memberInfo,
    int? paymentMethodId,
    String? paymentMethodName,
    String? paymentMethodType,
  }) {
    try {
      debugPrint('🔍 PaymentTransactionData字段检查:');
      debugPrint(
          '  - transaction.id: ${transaction.id} (${transaction.id.runtimeType})');
      debugPrint('  - employeeId: $employeeId (${employeeId.runtimeType})');
      debugPrint('  - employeeNo: $employeeNo (${employeeNo.runtimeType})');

      // 构建增强的metadata，包含会员车辆类型信息
      Map<String, dynamic> enhancedMetadata = Map<String, dynamic>.from(transaction.metadata);
      
      // 如果有会员信息，提取车辆类型并添加到metadata
      if (memberInfo != null) {
        final String? memberVehicleType = memberInfo.metadata['vehicle'] as String?;
        if (memberVehicleType != null && memberVehicleType.isNotEmpty) {
          // 将会员注册页面的车辆类型转换为API标准格式
          String apiVehicleType;
          switch (memberVehicleType) {
            case 'Motorbike':
              apiVehicleType = 'MOTORBIKE';
              break;
            case 'Car':
              apiVehicleType = 'CAR';
              break;
            case 'Truck':
              apiVehicleType = 'TRUCK';
              break;
            default:
              apiVehicleType = 'CAR'; // 默认为汽车
          }
          
          enhancedMetadata['vehicle_type'] = apiVehicleType;
          enhancedMetadata['member_vehicle_original'] = memberVehicleType;
          
          debugPrint('🚗 添加车辆类型信息到PaymentTransactionData:');
          debugPrint('   原始车辆类型: $memberVehicleType');
          debugPrint('   API格式车辆类型: $apiVehicleType');
        }
        
        // 同时添加会员的其他关键信息
        enhancedMetadata['member_id'] = memberInfo.id;
        enhancedMetadata['member_name'] = memberInfo.name;
        if (memberInfo.metadata['plateNumbers'] != null) {
          enhancedMetadata['vehicle_plate'] = memberInfo.metadata['plateNumbers'];
        }
      }

      return PaymentTransactionData(
        transactionId: transaction.id.toString(),
        stationId: transaction.stationId,
        transactionRef: transaction.transactionNumber,
        pumpId: transaction.pumpId,
        nozzleId: transaction.nozzleId,
        fuelType: transaction.fuelType,
        fuelGrade: transaction.fuelGrade,
        totalAmount: transaction.amount,
        volume: transaction.volume,
        unitPrice: transaction.unitPrice,
        status: transaction.status,
        employeeId: employeeId,
        employeeNo: employeeNo,
        createdAt: transaction.createdAt,
        paymentMethodId: paymentMethodId,
        paymentMethodName: paymentMethodName,
        paymentMethodType: paymentMethodType,
        memberInfo: memberInfo,
        metadata: enhancedMetadata, // 使用增强的metadata
      );
    } catch (e) {
      debugPrint('❌ PaymentTransactionData.fromFuelTransaction 字段转换错误: $e');
      debugPrint('❌ 详细字段信息:');
      debugPrint(
          '  transaction.id: ${transaction.id} (${transaction.id.runtimeType})');
      debugPrint('  employeeId: $employeeId (${employeeId.runtimeType})');
      debugPrint('  employeeNo: $employeeNo (${employeeNo.runtimeType})');
      rethrow;
    }
  }

  /// 从旧版本的 transactionData Map 创建 (用于兼容迁移)
  factory PaymentTransactionData.fromLegacyMap(Map<String, dynamic> data) {
    // 智能提取字段，处理各种可能的数据结构
    final String transactionId =
        _extractField(data, <String>['id', 'transaction.id']) ?? 'unknown';
    final int stationId =
        _extractIntField(data, <String>['stationId', 'station_id']) ?? 0;
    final String transactionRef = _extractField(data, <String>[
          'transactionRef',
          'transaction_ref',
          'transactionNumber'
        ]) ??
        '';

    final String pumpId = _extractField(data, <String>[
          'pumpId',
          'dispenserId',
          'pump_id',
          'dispenser_id',
          'pumpNumber',
          'dispenserNumber',
          'metadata.pump_id'
        ]) ??
        '1';

    final String nozzleId = _extractField(data, <String>[
          'nozzleId',
          'nozzle_id',
          'nozzleNumber',
          'metadata.nozzle_id'
        ]) ??
        '1';

    final String fuelType = _extractField(data, <String>[
          'fuelType',
          'fuel_type',
          'transaction.fuelType',
          'nozzle.fuelType'
        ]) ??
        '';

    final String fuelGrade = _extractField(data, <String>[
          'fuelGrade',
          'fuel_grade',
          'transaction.fuelGrade',
          'nozzle.fuelGrade'
        ]) ??
        '';

    final double totalAmount = _extractDoubleField(data, <String>[
          'payment.totalAmount',
          'totalAmount',
          'amount',
          'settlement.totalAmount',
          'transaction.amount'
        ]) ??
        0.0;

    final double volume = _extractDoubleField(
            data, <String>['payment.volume', 'volume', 'transaction.volume']) ??
        0.0;

    final double unitPrice = _extractDoubleField(data, <String>[
          'payment.unitPrice',
          'unitPrice',
          'transaction.unitPrice',
          'nozzle.price'
        ]) ??
        0.0;

    final String status =
        _extractField(data, <String>['status', 'transaction.status']) ??
            'pending';
    final String employeeId = _extractField(data,
            <String>['employeeId', 'user_id', 'employee_id', 'transaction.staffId']) ??
        '';  // 优先使用 user_id
    final String employeeNo =
        _extractField(data, <String>['employeeNo', 'employee_no']) ?? '';

    final int? paymentMethodId = _extractIntField(
        data, <String>['paymentMethodId', 'payment_method_id']);
    final String? paymentMethodName = _extractField(data,
        <String>['paymentMethodName', 'payment_method_name', 'paymentMethod']);
    final String? paymentMethodType = _extractField(data,
        <String>['paymentMethodType', 'payment_method_type']);

    // 处理会员信息
    Member? memberInfo;
    final memberData = data['member'] ?? data['memberInfo'];
    if (memberData != null && memberData is Map<String, dynamic>) {
      try {
        memberInfo = Member.fromJson(memberData);
      } catch (e) {
        // 如果解析失败，忽略会员信息
      }
    }

    // 处理时间
    DateTime createdAt = DateTime.now();
    final String? createdAtStr = _extractField(
        data, <String>['createdAt', 'created_at', 'transaction.startTime']);
    if (createdAtStr != null) {
      try {
        createdAt = DateTime.parse(createdAtStr);
      } catch (e) {
        // 使用默认时间
      }
    }

    return PaymentTransactionData(
      transactionId: transactionId,
      stationId: stationId,
      transactionRef: transactionRef,
      pumpId: pumpId,
      nozzleId: nozzleId,
      fuelType: fuelType,
      fuelGrade: fuelGrade,
      totalAmount: totalAmount,
      volume: volume,
      unitPrice: unitPrice,
      status: status,
      employeeId: employeeId,
      employeeNo: employeeNo,
      createdAt: createdAt,
      paymentMethodId: paymentMethodId,
      paymentMethodName: paymentMethodName,
      paymentMethodType: paymentMethodType,
      memberInfo: memberInfo,
      metadata:
          (data['metadata'] as Map<String, dynamic>?) ?? <String, dynamic>{},
    );
  }
  // 交易基本信息
  final String transactionId;
  final int stationId;
  final String transactionRef;

  // 设备信息
  final String pumpId;
  final String nozzleId;

  // 燃油信息
  final String fuelType;
  final String fuelGrade;

  // 金额信息
  final double totalAmount;
  final double volume;
  final double unitPrice;

  // 支付信息
  final String status;
  final int? paymentMethodId; // 支付方式ID
  final String? paymentMethodName; // 支付方式名称
  final String? paymentMethodType; // 支付方式类型
    final Map<String, dynamic>? paymentMethodConfig; // 支付方式配置信息
  
  // 会员信息
  final Member? memberInfo;

  // 员工信息
  final String employeeId;
  final String employeeNo;

  // 时间信息
  final DateTime createdAt;

  // 额外数据
  final Map<String, dynamic> metadata;

  /// 转换为 JSON Map
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'transactionId': transactionId,
      'stationId': stationId,
      'transactionRef': transactionRef,
      'pumpId': pumpId,
      'nozzleId': nozzleId,
      'fuelType': fuelType,
      'fuelGrade': fuelGrade,
      'totalAmount': totalAmount,
      'volume': volume,
      'unitPrice': unitPrice,
      'status': status,
      'paymentMethodId': paymentMethodId,
      'paymentMethodName': paymentMethodName,
      'paymentMethodType': paymentMethodType,
      'paymentMethodConfig': paymentMethodConfig,
      'memberInfo': memberInfo?.toJson(),
      'employeeId': employeeId,
      'employeeNo': employeeNo,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  // 辅助方法：从多个可能的路径提取字符串字段
  static String? _extractField(Map<String, dynamic> data, List<String> paths) {
    for (final String path in paths) {
      final value = _getNestedValue(data, path);
      if (value != null &&
          value.toString().isNotEmpty &&
          value.toString() != 'null') {
        return value.toString();
      }
    }
    return null;
  }

  // 辅助方法：从多个可能的路径提取整数字段
  static int? _extractIntField(Map<String, dynamic> data, List<String> paths) {
    for (final String path in paths) {
      final value = _getNestedValue(data, path);
      if (value != null) {
        if (value is int) return value;
        if (value is double) return value.toInt();
        if (value is String) {
          final int? parsed = int.tryParse(value);
          if (parsed != null) return parsed;
        }
      }
    }
    return null;
  }

  // 辅助方法：从多个可能的路径提取双精度字段
  static double? _extractDoubleField(
      Map<String, dynamic> data, List<String> paths) {
    for (final String path in paths) {
      final value = _getNestedValue(data, path);
      if (value != null) {
        if (value is double) return value;
        if (value is int) return value.toDouble();
        if (value is String) {
          final double? parsed = double.tryParse(value);
          if (parsed != null) return parsed;
        }
      }
    }
    return null;
  }

  // 辅助方法：获取嵌套值 (支持 "payment.totalAmount" 这样的路径)
  static dynamic _getNestedValue(Map<String, dynamic> data, String path) {
    final List<String> keys = path.split('.');
    dynamic current = data;

    for (final String key in keys) {
      if (current is Map<String, dynamic> && current.containsKey(key)) {
        current = current[key];
      } else {
        return null;
      }
    }

    return current;
  }

  /// 创建副本
    PaymentTransactionData copyWith({
    String? transactionId,
    int? stationId,
    String? transactionRef,
    String? pumpId,
    String? nozzleId,
    String? fuelType,
    String? fuelGrade,
    double? totalAmount,
    double? volume,
    double? unitPrice,
    String? status,
    int? paymentMethodId,
    String? paymentMethodName,
    String? paymentMethodType,
    Map<String, dynamic>? paymentMethodConfig,
    Member? memberInfo,
    String? employeeId,
    String? employeeNo,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
}) {
    return PaymentTransactionData(
      transactionId: transactionId ?? this.transactionId,
      stationId: stationId ?? this.stationId,
      transactionRef: transactionRef ?? this.transactionRef,
      pumpId: pumpId ?? this.pumpId,
      nozzleId: nozzleId ?? this.nozzleId,
      fuelType: fuelType ?? this.fuelType,
      fuelGrade: fuelGrade ?? this.fuelGrade,
      totalAmount: totalAmount ?? this.totalAmount,
      volume: volume ?? this.volume,
      unitPrice: unitPrice ?? this.unitPrice,
      status: status ?? this.status,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      paymentMethodName: paymentMethodName ?? this.paymentMethodName,
      paymentMethodType: paymentMethodType ?? this.paymentMethodType,
      paymentMethodConfig: paymentMethodConfig ?? this.paymentMethodConfig,
      memberInfo: memberInfo ?? this.memberInfo,
      employeeId: employeeId ?? this.employeeId,
      employeeNo: employeeNo ?? this.employeeNo,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'PaymentTransactionData(id: $transactionId, pumpId: $pumpId, amount: $totalAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PaymentTransactionData &&
        other.transactionId == transactionId &&
        other.stationId == stationId &&
        other.pumpId == pumpId &&
        other.nozzleId == nozzleId;
  }

  @override
  int get hashCode {
    return transactionId.hashCode ^
        stationId.hashCode ^
        pumpId.hashCode ^
        nozzleId.hashCode;
  }
}
