import 'package:equatable/equatable.dart';

/// 会员模型
class Member extends Equatable {
  const Member({
    required this.id,
    required this.memberCardId,
    required this.name,
    required this.phone,
    required this.email,
    required this.level,
    required this.status,
    required this.balance,
    required this.points,
    required this.registrationDate,
    this.lastVisitDate,
    this.address,
    this.birthDate,
    this.idCard,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 从JSON创建Member对象
  factory Member.fromJson(Map<String, dynamic> json) {
    return Member(
      id: json['id'] as String,
      memberCardId: json['member_card_id'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String,
      level: MemberLevel.values.firstWhere(
        (MemberLevel e) => e.toString() == json['level'],
        orElse: () => MemberLevel.bronze,
      ),
      status: MemberStatus.values.firstWhere(
        (MemberStatus e) => e.toString() == json['status'],
        orElse: () => MemberStatus.active,
      ),
      balance: (json['balance'] as num).toDouble(),
      points: json['points'] as int,
      registrationDate: DateTime.parse(json['registration_date'] as String),
      lastVisitDate: json['last_visit_date'] != null
          ? DateTime.parse(json['last_visit_date'] as String)
          : null,
      address: json['address'] as String?,
      birthDate: json['birth_date'] != null
          ? DateTime.parse(json['birth_date'] as String)
          : null,
      idCard: json['id_card'] as String?,
      metadata:
          json['metadata'] as Map<String, dynamic>? ?? <String, dynamic>{},
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }
  final String id;
  final String memberCardId;
  final String name;
  final String phone;
  final String email;
  final MemberLevel level;
  final MemberStatus status;
  final double balance;
  final int points;
  final DateTime registrationDate;
  final DateTime? lastVisitDate;
  final String? address;
  final DateTime? birthDate;
  final String? idCard;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  @override
  List<Object?> get props => <Object?>[
        id,
        memberCardId,
        name,
        phone,
        email,
        level,
        status,
        balance,
        points,
        registrationDate,
        lastVisitDate,
        address,
        birthDate,
        idCard,
        metadata,
        createdAt,
        updatedAt,
      ];

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'member_card_id': memberCardId,
      'name': name,
      'phone': phone,
      'email': email,
      'level': level.toString(),
      'status': status.toString(),
      'balance': balance,
      'points': points,
      'registration_date': registrationDate.toIso8601String(),
      'last_visit_date': lastVisitDate?.toIso8601String(),
      'address': address,
      'birth_date': birthDate?.toIso8601String(),
      'id_card': idCard,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// 创建副本并更新指定字段
  Member copyWith({
    String? id,
    String? memberCardId,
    String? name,
    String? phone,
    String? email,
    MemberLevel? level,
    MemberStatus? status,
    double? balance,
    int? points,
    DateTime? registrationDate,
    DateTime? lastVisitDate,
    String? address,
    DateTime? birthDate,
    String? idCard,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Member(
      id: id ?? this.id,
      memberCardId: memberCardId ?? this.memberCardId,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      level: level ?? this.level,
      status: status ?? this.status,
      balance: balance ?? this.balance,
      points: points ?? this.points,
      registrationDate: registrationDate ?? this.registrationDate,
      lastVisitDate: lastVisitDate ?? this.lastVisitDate,
      address: address ?? this.address,
      birthDate: birthDate ?? this.birthDate,
      idCard: idCard ?? this.idCard,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 获取会员等级显示名称
  String get levelDisplayName {
    switch (level) {
      case MemberLevel.bronze:
        return 'Bronze Member';
      case MemberLevel.silver:
        return 'Silver Member';
      case MemberLevel.gold:
        return 'Gold Member';
      case MemberLevel.platinum:
        return 'Platinum Member';
      case MemberLevel.diamond:
        return 'Diamond Member';
    }
  }

  /// 获取会员状态显示名称
  String get statusDisplayName {
    switch (status) {
      case MemberStatus.active:
      case MemberStatus.normal:
        return 'Active';
      case MemberStatus.inactive:
        return 'Inactive';
      case MemberStatus.suspended:
      case MemberStatus.frozen:
        return 'Suspended';
      case MemberStatus.expired:
        return 'Expired';
      case MemberStatus.temporary:
        return 'Temporary';
    }
  }

  /// 检查会员是否为活跃状态
  bool get isActive =>
      status == MemberStatus.active || status == MemberStatus.normal;

  /// 检查会员是否为VIP等级（金卡及以上）
  bool get isVIP => level.index >= MemberLevel.gold.index;

  /// 计算积分价值（积分转换为余额的比率）
  double get pointsValue => points * 0.01; // 100积分 = 1元

  // 为了向后兼容，添加别名getter
  /// 会员ID（兼容性别名）
  String get memberId => id;

  /// 会员等级（兼容性别名）
  MemberLevel get memberLevel => level;

  /// 账户余额（兼容性别名）
  double get accountBalance => balance;

  /// 车牌号列表（从metadata中提取，兼容性别名）
  List<String> get plateNumbers {
    final plates = metadata['plateNumbers'];
    if (plates is List) {
      return plates.cast<String>();
    }
    return <String>[];
  }

  /// 获取下一等级所需积分
  int get pointsToNextLevel {
    switch (level) {
      case MemberLevel.bronze:
        return (1000 - points).clamp(0, 1000);
      case MemberLevel.silver:
        return (5000 - points).clamp(0, 5000);
      case MemberLevel.gold:
        return (10000 - points).clamp(0, 10000);
      case MemberLevel.platinum:
        return (20000 - points).clamp(0, 20000);
      case MemberLevel.diamond:
        return 0; // 已达到最高等级
    }
  }

  /// 获取示例数据用于UI预览和测试
  static List<Member> getDummyMembers() {
    return <Member>[
      Member(
        id: 'M10001',
        memberCardId: 'M10001',
        name: 'John Doe',
        phone: '***********',
        email: '<EMAIL>',
        level: MemberLevel.gold,
        status: MemberStatus.active,
        balance: 750000.0,
        points: 1000,
        registrationDate: DateTime(2022, 5, 10),
        lastVisitDate: DateTime(2022, 5, 15),
        address: 'Jl. Raya No. 1',
        birthDate: DateTime(1990, 5, 10),
        idCard: '1234567890',
        metadata: const <String, dynamic>{
          'vehicle': 'Toyota Avanza',
          'plateNumbers': <String>['B1234ABC'],
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Member(
        id: 'M10002',
        memberCardId: 'M10002',
        name: 'Jane Smith',
        phone: '08198765432',
        email: '<EMAIL>',
        level: MemberLevel.platinum,
        status: MemberStatus.normal,
        balance: 1250000.0,
        points: 5000,
        registrationDate: DateTime(2021, 3, 15),
        lastVisitDate: DateTime(2021, 3, 20),
        address: 'Jl. Raya No. 2',
        birthDate: DateTime(1995, 3, 15),
        idCard: '2345678901',
        metadata: const <String, dynamic>{
          'vehicle': 'Honda CRV',
          'plateNumbers': <String>['B5678XYZ', 'B9012DEF'],
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Member(
        id: 'M10003',
        memberCardId: 'M10003',
        name: 'Ahmad Rizal',
        phone: '08111223344',
        email: '<EMAIL>',
        level: MemberLevel.silver,
        status: MemberStatus.frozen,
        balance: 350000.0,
        points: 2000,
        registrationDate: DateTime(2023, 1, 20),
        lastVisitDate: DateTime(2023, 1, 25),
        address: 'Jl. Raya No. 3',
        birthDate: DateTime(1992, 1, 20),
        idCard: '3456789012',
        metadata: const <String, dynamic>{
          'vehicle': 'Daihatsu Xenia',
          'plateNumbers': <String>['B3456GHI'],
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Member(
        id: 'M10004',
        memberCardId: 'M10004',
        name: 'Siti Rahayu',
        phone: '08566778899',
        email: '<EMAIL>',
        level: MemberLevel.bronze,
        status: MemberStatus.temporary,
        balance: 125000.0,
        points: 500,
        registrationDate: DateTime(2023, 6, 5),
        lastVisitDate: null,
        address: 'Jl. Raya No. 4',
        birthDate: DateTime(2000, 6, 5),
        idCard: '4567890123',
        metadata: const <String, dynamic>{
          'vehicle': 'Suzuki Ertiga',
          'plateNumbers': <String>['B7890JKL'],
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Member(
        id: 'M10005',
        memberCardId: 'M10005',
        name: 'Budi Santoso',
        phone: '08577889900',
        email: '<EMAIL>',
        level: MemberLevel.silver,
        status: MemberStatus.suspended,
        balance: 500000.0,
        points: 3000,
        registrationDate: DateTime(2022, 11, 30),
        lastVisitDate: DateTime(2022, 12, 5),
        address: 'Jl. Raya No. 5',
        birthDate: DateTime(1990, 11, 30),
        idCard: '5678901234',
        metadata: const <String, dynamic>{
          'vehicle': 'Toyota Rush',
          'plateNumbers': <String>['B2468MNO'],
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }
}

/// 会员等级枚举
enum MemberLevel {
  bronze, // 铜卡
  silver, // 银卡
  gold, // 金卡
  platinum, // 白金卡
  diamond, // 钻石卡
}

/// 会员状态枚举
enum MemberStatus {
  active, // 活跃
  normal, // 正常 (alias for active)
  inactive, // 不活跃
  suspended, // 暂停
  frozen, // 冻结 (alias for suspended)
  expired, // 过期
  temporary, // 临时
}

/// 会员查询条件
class MemberQueryCondition {
  const MemberQueryCondition({
    this.memberCardId,
    this.name,
    this.phone,
    this.email,
    this.level,
    this.status,
    this.registrationDateFrom,
    this.registrationDateTo,
    this.lastVisitDateFrom,
    this.lastVisitDateTo,
    this.minBalance,
    this.maxBalance,
    this.minPoints,
    this.maxPoints,
  });
  final String? memberCardId;
  final String? name;
  final String? phone;
  final String? email;
  final MemberLevel? level;
  final MemberStatus? status;
  final DateTime? registrationDateFrom;
  final DateTime? registrationDateTo;
  final DateTime? lastVisitDateFrom;
  final DateTime? lastVisitDateTo;
  final double? minBalance;
  final double? maxBalance;
  final int? minPoints;
  final int? maxPoints;

  /// 转换为查询参数
  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = <String, dynamic>{};

    if (memberCardId != null) params['member_card_id'] = memberCardId;
    if (name != null) params['name'] = name;
    if (phone != null) params['phone'] = phone;
    if (email != null) params['email'] = email;
    if (level != null) params['level'] = level.toString();
    if (status != null) params['status'] = status.toString();
    if (registrationDateFrom != null)
      params['registration_date_from'] =
          registrationDateFrom!.toIso8601String();
    if (registrationDateTo != null)
      params['registration_date_to'] = registrationDateTo!.toIso8601String();
    if (lastVisitDateFrom != null)
      params['last_visit_date_from'] = lastVisitDateFrom!.toIso8601String();
    if (lastVisitDateTo != null)
      params['last_visit_date_to'] = lastVisitDateTo!.toIso8601String();
    if (minBalance != null) params['min_balance'] = minBalance;
    if (maxBalance != null) params['max_balance'] = maxBalance;
    if (minPoints != null) params['min_points'] = minPoints;
    if (maxPoints != null) params['max_points'] = maxPoints;

    return params;
  }
}
