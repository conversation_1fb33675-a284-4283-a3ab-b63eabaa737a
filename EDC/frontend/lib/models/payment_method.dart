import 'package:flutter/material.dart';

// 支付方式模型类，用于解析API返回的支付方式数据

class PaymentMethod {
  PaymentMethod({
    required this.id,
    required this.type,
    required this.name,
    required this.displayName,
    this.description,
    this.icon,
    required this.gatewayType,
    this.gatewayConfig,
    required this.enabled,
    this.minAmount,
    this.maxAmount,
    this.dailyLimit,
    this.feeType,
    this.feeValue,
    this.availableTime,
    this.allowedStations,
    required this.sortOrder,
    this.groupName,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  // 从JSON构造方法
  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] as int? ?? 0,
      type: json['type'] as String? ?? '',
      name: json['name'] as String? ?? '',
      displayName: json['display_name'] as String? ?? '',
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      gatewayType: json['gateway_type'] as String? ?? '',
      gatewayConfig: json['gateway_config'] as Map<String, dynamic>?,
      enabled: json['enabled'] as bool? ?? false,
      minAmount: _safeParseDouble(json['min_amount']),
      maxAmount: _safeParseDouble(json['max_amount']),
      dailyLimit: _safeParseDouble(json['daily_limit']),
      feeType: json['fee_type'] as String?,
      feeValue: _safeParseDouble(json['fee_value']),
      availableTime: json['available_time'] as String?,
      allowedStations: json['allowed_stations'] != null
          ? List<int>.from(json['allowed_stations'] as List)
          : null,
      sortOrder: json['sort_order'] as int? ?? 0,
      groupName: json['group_name'] as String?,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      deletedAt: json['deleted_at'] as String?,
    );
  }
  final int id;
  final String type;
  final String name;
  final String displayName;
  final String? description;
  final String? icon;
  final String gatewayType;
  final Map<String, dynamic>? gatewayConfig;
  final bool enabled;
  final double? minAmount;
  final double? maxAmount;
  final double? dailyLimit;
  final String? feeType;
  final double? feeValue;
  final String? availableTime;
  final List<int>? allowedStations;
  final int sortOrder;
  final String? groupName;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  /// 安全的数字解析方法
  static double? _safeParseDouble(dynamic value) {
    if (value == null) return null;

    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }

    // 如果是其他类型，尝试转换为 num 再转换为 double
    try {
      if (value is num) {
        return value.toDouble();
      }
    } catch (e) {
      // 转换失败，返回 null
      return null;
    }

    return null;
  }

  // 转换为JSON方法
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'type': type,
      'name': name,
      'display_name': displayName,
      'description': description,
      'icon': icon,
      'gateway_type': gatewayType,
      'gateway_config': gatewayConfig,
      'enabled': enabled,
      'min_amount': minAmount,
      'max_amount': maxAmount,
      'daily_limit': dailyLimit,
      'fee_type': feeType,
      'fee_value': feeValue,
      'available_time': availableTime,
      'allowed_stations': allowedStations,
      'sort_order': sortOrder,
      'group_name': groupName,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
    };
  }

  // 根据支付方式类型获取图标
  IconData getIcon() {
    switch (type) {
      case 'CASH':
        return Icons.money;
      case 'BANK_CARD':
        return Icons.credit_card;
      case 'WECHAT':
        return Icons.wechat;
      case 'ALIPAY':
        return Icons.payment;
      case 'POINTS':
        return Icons.stars;
      case 'COUPON':
        return Icons.card_giftcard;
      case 'VOUCHER':
        return Icons.card_giftcard;
      case 'TERA':
        return Icons.local_gas_station;
      default:
        return Icons.payment;
    }
  }

  // 根据支付方式名称获取银行logo图片路径
  String? getBankLogo() {
    if (type != 'BANK_CARD') return null;

    // 根据name字段的前缀或gatewayConfig中的bank_type来映射银行logo
    String? bankType;

    // 首先尝试从gatewayConfig获取bank_type
    if (gatewayConfig != null && gatewayConfig!['bank_type'] != null) {
      bankType = gatewayConfig!['bank_type'].toString().toLowerCase();
    } else {
      // 如果没有bank_type，则从name中提取
      final String lowerName = name.toLowerCase();
      if (lowerName.contains('bca')) {
        bankType = 'bca';
      } else if (lowerName.contains('cimb')) {
        bankType = 'cimb';
      } else if (lowerName.contains('mandiri')) {
        bankType = 'mandiri';
      } else if (lowerName.contains('pvs')) {
        bankType = 'pvs';
      }
    }

    // 映射到对应的logo文件
    switch (bankType) {
      case 'bca':
        return 'assets/images/bca.png';
      case 'cimb':
        return 'assets/images/cimb.png';
      case 'mandiri':
        return 'assets/images/mandiri.png';
      case 'pvs':
        return 'assets/images/pvs.png';
      default:
        return null;
    }
  }

  // 检查是否有银行logo
  bool hasBankLogo() {
    return getBankLogo() != null;
  }
}

// 支付方式响应模型
class PaymentMethodResponse {
  PaymentMethodResponse({
    required this.data,
    required this.success,
  });

  factory PaymentMethodResponse.fromJson(Map<String, dynamic> json) {
    try {
      // 安全地解析 data 字段
      final List<PaymentMethod> paymentMethods = <PaymentMethod>[];
      final dynamic dataField = json['data'];

      if (dataField != null && dataField is List) {
        for (final item in dataField) {
          if (item != null && item is Map<String, dynamic>) {
            try {
              paymentMethods.add(PaymentMethod.fromJson(item));
            } catch (e) {
              // 如果单个支付方式解析失败，记录错误但继续处理其他项
              debugPrint('Failed to parse payment method: $e');
            }
          }
        }
      }

      return PaymentMethodResponse(
        data: paymentMethods,
        success: json['success'] as bool? ?? false,
      );
    } catch (e) {
      // 如果整个解析失败，返回一个空的响应
      debugPrint('Failed to parse PaymentMethodResponse: $e');
      return PaymentMethodResponse(
        data: <PaymentMethod>[],
        success: false,
      );
    }
  }
  final List<PaymentMethod> data;
  final bool success;
}
