import 'package:equatable/equatable.dart';

/// 班结预检查响应模型
class EndShiftValidationResponse extends Equatable {
  const EndShiftValidationResponse({
    required this.shiftInfo,
    required this.totalizerContinuity,
    required this.canEndDirectly,
  });

  final ShiftInfo shiftInfo;
  final TotalizerContinuity totalizerContinuity;
  final bool canEndDirectly;

  factory EndShiftValidationResponse.fromJson(Map<String, dynamic> json) {
    return EndShiftValidationResponse(
      shiftInfo: ShiftInfo.fromJson(json['shift_info'] as Map<String, dynamic>),
      totalizerContinuity: TotalizerContinuity.fromJson(
          json['totalizer_continuity'] as Map<String, dynamic>),
      canEndDirectly: json['can_end_directly'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'shift_info': shiftInfo.toJson(),
      'totalizer_continuity': totalizerContinuity.toJson(),
      'can_end_directly': canEndDirectly,
    };
  }

  @override
  List<Object?> get props => <Object?>[
        shiftInfo,
        totalizerContinuity,
        canEndDirectly,
      ];
}

/// 班次信息模型
class ShiftInfo extends Equatable {
  const ShiftInfo({
    required this.shiftNumber,
    required this.startTime,
    required this.currentTime,
    required this.durationHours,
  });

  final String shiftNumber;
  final DateTime startTime;
  final DateTime currentTime;
  final double durationHours;

  factory ShiftInfo.fromJson(Map<String, dynamic> json) {
    return ShiftInfo(
      shiftNumber: json['shift_number'] as String,
      startTime: DateTime.parse(json['start_time'] as String),
      currentTime: DateTime.parse(json['current_time'] as String),
      durationHours: (json['duration_hours'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'shift_number': shiftNumber,
      'start_time': startTime.toIso8601String(),
      'current_time': currentTime.toIso8601String(),
      'duration_hours': durationHours,
    };
  }

  @override
  List<Object?> get props => <Object?>[
        shiftNumber,
        startTime,
        currentTime,
        durationHours,
      ];
}

/// 泵码连续性信息模型
class TotalizerContinuity extends Equatable {
  const TotalizerContinuity({
    required this.hasAbnormal,
    required this.abnormalTransactionCount,
    required this.totalDiscrepancyAmount,
    required this.abnormalTransactions,
  });

  final bool hasAbnormal;
  final int abnormalTransactionCount;
  final double totalDiscrepancyAmount;
  final List<AbnormalTransaction> abnormalTransactions;

  factory TotalizerContinuity.fromJson(Map<String, dynamic> json) {
    return TotalizerContinuity(
      hasAbnormal: json['has_abnormal'] as bool,
      abnormalTransactionCount: json['abnormal_transaction_count'] as int,
      totalDiscrepancyAmount:
          (json['total_discrepancy_amount'] as num).toDouble(),
      abnormalTransactions: (json['abnormal_transactions'] as List<dynamic>)
          .map((dynamic item) =>
              AbnormalTransaction.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'has_abnormal': hasAbnormal,
      'abnormal_transaction_count': abnormalTransactionCount,
      'total_discrepancy_amount': totalDiscrepancyAmount,
      'abnormal_transactions':
          abnormalTransactions.map((AbnormalTransaction item) => item.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => <Object?>[
        hasAbnormal,
        abnormalTransactionCount,
        totalDiscrepancyAmount,
        abnormalTransactions,
      ];
}

/// 异常交易模型
class AbnormalTransaction extends Equatable {
  const AbnormalTransaction({
    required this.transactionId,
    required this.pumpId,
    required this.nozzleId,
    required this.discrepancyAmount,
    required this.transactionTime,
  });

  final String transactionId;
  final String pumpId;
  final String nozzleId;
  final double discrepancyAmount;
  final DateTime transactionTime;

  factory AbnormalTransaction.fromJson(Map<String, dynamic> json) {
    return AbnormalTransaction(
      transactionId: json['transaction_id'] as String,
      pumpId: json['pump_id'] as String,
      nozzleId: json['nozzle_id'] as String,
      discrepancyAmount: (json['discrepancy_amount'] as num).toDouble(),
      transactionTime: DateTime.parse(json['transaction_time'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'transaction_id': transactionId,
      'pump_id': pumpId,
      'nozzle_id': nozzleId,
      'discrepancy_amount': discrepancyAmount,
      'transaction_time': transactionTime.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => <Object?>[
        transactionId,
        pumpId,
        nozzleId,
        discrepancyAmount,
        transactionTime,
      ];
}
