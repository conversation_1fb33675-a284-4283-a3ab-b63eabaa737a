/// 促销响应模型
class PromotionResponse {
  PromotionResponse({
    required this.success,
    required this.message,
    this.orderId,
    this.vehicleType,
    required this.originalAmount,
    required this.discountedAmount,
    required this.discountAmount,
    required this.appliedPromotions,
    required this.items,
    this.calculationTime,
    this.totalItems,
    this.totalQuantity,
  });

  factory PromotionResponse.fromJson(Map<String, dynamic> json) {
    final List<AppliedPromotion> appliedPromotionsList = 
        (json['appliedPromotions'] as List?)
            ?.map((item) => AppliedPromotion.fromJson(item as Map<String, dynamic>))
            .toList() ?? <AppliedPromotion>[];

    final List<DiscountedItem> itemsList = (json['items'] as List?)
            ?.map((item) => DiscountedItem.fromJson(item as Map<String, dynamic>))
            .toList() ??
        <DiscountedItem>[];

    return PromotionResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      orderId: json['orderId'] as String?,
      vehicleType: json['vehicleType'] as String?,
      originalAmount: (json['originalAmount'] as num?)?.toDouble() ?? 0.0,
      discountedAmount: (json['discountedAmount'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discountAmount'] as num?)?.toDouble() ?? 0.0,
      appliedPromotions: appliedPromotionsList,
      items: itemsList,
      calculationTime: json['calculationTime'] as String?,
      totalItems: json['totalItems'] as int?,
      totalQuantity: json['totalQuantity'] as int?,
    );
  }
  
  final bool success;
  final String message;
  final String? orderId;
  final String? vehicleType;
  final double originalAmount;
  final double discountedAmount;
  final double discountAmount;
  final List<AppliedPromotion> appliedPromotions;
  final List<DiscountedItem> items;
  final String? calculationTime;
  final int? totalItems;
  final int? totalQuantity;
}

/// 应用的促销活动模型
class AppliedPromotion {
  AppliedPromotion({
    required this.promotionId,
    required this.promotionName,
    required this.discountType,
    required this.discountValue,
    required this.discountAmount,
    required this.description,
    required this.applicableItems,
    required this.metadata,
  });

  factory AppliedPromotion.fromJson(Map<String, dynamic> json) {
    return AppliedPromotion(
      promotionId: json['promotionId'] as String? ?? '',
      promotionName: json['promotionName'] as String? ?? '',
      discountType: json['discountType'] as String? ?? '',
      discountValue: (json['discountValue'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discountAmount'] as num?)?.toDouble() ?? 0.0,
      description: json['description'] as String? ?? '',
      applicableItems: (json['applicableItems'] as List?)
          ?.map((item) => item.toString())
          .toList() ?? <String>[],
      metadata: json['metadata'] as Map<String, dynamic>? ?? <String, dynamic>{},
    );
  }

  final String promotionId;
  final String promotionName;
  final String discountType;
  final double discountValue;
  final double discountAmount;
  final String description;
  final List<String> applicableItems;
  final Map<String, dynamic> metadata;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'promotionId': promotionId,
      'promotionName': promotionName,
      'discountType': discountType,
      'discountValue': discountValue,
      'discountAmount': discountAmount,
      'description': description,
      'applicableItems': applicableItems,
      'metadata': metadata,
    };
  }
}

/// 折扣后的商品项模型
class DiscountedItem {
  DiscountedItem({
    required this.itemId,
    required this.name,
    required this.originalPrice,
    required this.discountedPrice,
    required this.quantity,
    this.categoryIds,
    this.attributes,
  });

  factory DiscountedItem.fromJson(Map<String, dynamic> json) {
    return DiscountedItem(
      itemId: json['itemId'] as String? ?? '',
      name: json['name'] as String? ?? '',
      originalPrice: (json['originalPrice'] as num?)?.toDouble() ?? 0.0,
      discountedPrice: (json['discountedPrice'] as num?)?.toDouble() ?? 0.0,
      quantity: json['quantity'] as int? ?? 0,
      categoryIds: (json['categoryIds'] as List?)?.map((item) => item.toString()).toList(),
      attributes: json['attributes'] as Map<String, dynamic>?,
    );
  }
  
  final String itemId;
  final String name;
  final double originalPrice;
  final double discountedPrice;
  final int quantity;
  final List<String>? categoryIds;
  final Map<String, dynamic>? attributes;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'itemId': itemId,
      'name': name,
      'originalPrice': originalPrice,
      'discountedPrice': discountedPrice,
      'quantity': quantity,
      if (categoryIds != null) 'categoryIds': categoryIds,
      if (attributes != null) 'attributes': attributes,
    };
  }
}
