// Order data model
// Contains basic order information, status, and status logs

import 'package:intl/intl.dart';

/// Order status based on API documentation values
/// Mapping API status to internal enum
enum OrderStatus {
  created, // API returns 'new'
  processing, // API returns 'processing'
  completed, // API returns 'completed'
  cancelled, // API returns 'cancelled'
  cancelling, // Internal status for cancellation process
  failed // Internal status for failed order
}

/// Helper function to map API status string to OrderStatus enum
OrderStatus _mapApiStatus(String? apiStatus) {
  switch (apiStatus?.toLowerCase()) {
    case 'new':
      return OrderStatus.created;
    case 'processing':
      return OrderStatus.processing;
    case 'completed':
      return OrderStatus.completed;
    case 'cancelled':
      return OrderStatus.cancelled;
    default:
      return OrderStatus.failed; // Default for unknown status
  }
}

/// Order status log
/// Records every order status change
class StatusLog {
  StatusLog({
    required this.time,
    required this.status,
    required this.operator,
    this.reason = '',
  });

  /// Create StatusLog from API JSON response
  factory StatusLog.fromJson(Map<String, dynamic> json) {
    return StatusLog(
      // Assume API returns ISO 8601 format for time
      time: DateTime.parse((json['created_at'] ??
              json['time'] ??
              DateTime.now().toIso8601String())
          .toString()),
      status: _mapApiStatus(json['status'] as String?),
      // Assume operator ID is in 'operator_id', 'user_id', or 'employee_id'
      operator: (json['operator_id'] ?? json['user_id'] ?? json['employee_id'] ?? '').toString(),
      reason: json['reason'] as String? ?? '',
    );
  }

  /// Time of status change
  final DateTime time;

  /// Status after change
  final OrderStatus status;

  /// ID of operator who made the change
  final String operator;

  /// Reason for status change
  final String reason;

  /// Convert StatusLog to JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'time': time.toIso8601String(),
        'status': status.name,
        'operator': operator,
        'reason': reason,
      };
}

/// Main order model
/// Contains all order information including items, payments, and promotions
class Order {
  Order({
    required this.id,
    required this.orderId,
    required this.createTime,
    required this.updatedAt,
    this.completedAt,
    this.cancelledAt,
    required this.operatorId,
    required this.amount,
    required this.discountAmount,
    required this.finalAmount,
    required this.paidAmount,
    required this.status,
    this.customerId,
    this.customerName,
    this.customerPhone,
    this.vehicleType,
    this.licensePlate,
    this.memberPhone = '',
    required this.stationId,
    this.stationName,
    this.staffName,
    required this.pumpId,
    required this.fuelType,
    required this.volume,
    required this.paymentMethod,
    required this.items,
    required this.promotions,
    required this.payments,
    required this.statusLogs,
    this.extInfo = const <String, dynamic>{},
  });

  /// Membuat Order dari JSON response API
  factory Order.fromJson(Map<String, dynamic> json) {
    // Helper untuk parsing tanggal dengan aman，正确处理时区信息
    DateTime? parseDate(String? dateStr) {
      if (dateStr == null || dateStr.isEmpty) return null;
      try {
        //  打印日志
        print('dateStr: $dateStr');

        // 解析带时区信息的ISO 8601格式字符串
        // 例如: "2025-07-17T06:52:19.101178+07:00"
        DateTime parsed = DateTime.parse(dateStr);
        print('parsed: $parsed');
        
        // 检查原始字符串是否包含时区信息
        bool hasTimezoneInfo = dateStr.contains('+') || 
                              dateStr.contains('-', 10) || // 10是为了跳过日期中的'-'
                              dateStr.endsWith('Z');
        
        if (hasTimezoneInfo) {
          // 如果包含时区信息，确保返回UTC时间
          // DateTime.parse已经自动转换为UTC，这是正确的
          return parsed; // parsed已经是UTC时间了
        } else {
          // 如果没有时区信息，假设是本地时间
          return parsed;
        }
      } catch (e) {
        // 解析失败时输出详细错误信息以便调试
        print('Failed to parse date string: "$dateStr", error: $e');
        return null;
      }
    }

    // Helper untuk parsing list dinamis
    List<Map<String, dynamic>> parseDynamicList(String key) {
      if (json[key] != null && json[key] is List) {
        return List<Map<String, dynamic>>.from(json[key] as List);
      }
      return <Map<String, dynamic>>[];
    }

    // Ekstrak detail item pertama untuk field langsung
    final List<Map<String, dynamic>> items = parseDynamicList('items');
    final Map<String, dynamic> firstItem =
        items.isNotEmpty ? items.first : <String, dynamic>{};

    final List<Map<String, dynamic>> payments = parseDynamicList('payments');
    final Map<String, dynamic> firstPayment =
        payments.isNotEmpty ? payments.first : <String, dynamic>{};

    final Map<String, dynamic> metadata =
        json['metadata'] as Map<String, dynamic>? ?? <String, dynamic>{};

    return Order(
      id: json['id'] as String,
      orderId: json['order_number'] as String? ?? '',
      createTime: parseDate(json['created_at'] as String?) ?? DateTime.now(),
      updatedAt: parseDate(json['updated_at'] as String?) ?? DateTime.now(),
      completedAt: parseDate(json['completed_at'] as String?),
      cancelledAt: parseDate(json['cancelled_at'] as String?),

      // 安全地处理 operatorId - 优先从 metadata 中获取，然后从顶层字段获取
      operatorId: _safeParseOperatorId(
        metadata['user_id'] ?? 
        metadata['employee_id'] ?? 
        json['employee_no'] ?? 
        json['operator_id'] ?? 
        ''
      ),

      amount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      finalAmount: (json['final_amount'] as num?)?.toDouble() ?? 0.0,
      paidAmount: (json['paid_amount'] as num?)?.toDouble() ?? 0.0,
      status: _mapApiStatus(json['status'] as String?),

      // 安全地处理 customerId 字段 - 支持 String 和 int 类型
      customerId: _safeParseCustomerId(json['customer_id']),
      customerName: json['customer_name'] as String?,
      customerPhone: json['customer_phone'] as String?,
      vehicleType: json['vehicle_type'] as String?,
      licensePlate: json['license_plate'] as String?,
      memberPhone: json['member_phone'] as String? ?? '',

      stationId: json['station_id'] as int? ?? 0,
      stationName: json['station_name'] as String?,
      staffName: json['staff_name'] as String?,

      // Ambil detail dari item pertama
      pumpId: firstItem['pump_id'] as String? ?? '',
      fuelType: firstItem['product_name'] as String? ??
          firstItem['fuel_grade'] as String? ??
          '',
      volume: (firstItem['quantity'] as num?)?.toDouble() ?? 0.0,

      // Ambil metode pembayaran dari pembayaran pertama
      paymentMethod: firstPayment['payment_method'] as String? ?? '',

      items: items,
      promotions: parseDynamicList('promotions'),
      payments: payments,
      statusLogs: <StatusLog>[], // Log status tidak biasanya ada di list view
      extInfo: metadata,
    );
  }

  /// 安全地解析 operatorId 字段
  /// 支持 String 和 int 类型的输入
  static String _safeParseOperatorId(dynamic value) {
    if (value == null) return '';
    
    if (value is String) return value;
    if (value is int) return value.toString();
    if (value is double) return value.toInt().toString();
    
    // 尝试转换其他类型
    try {
      if (value is num) {
        return value.toString();
      }
      // 尝试转换为字符串
      final String stringValue = value.toString();
      if (stringValue.isEmpty || stringValue == 'null') return '';
      return stringValue;
    } catch (e) {
      // 转换失败，返回空字符串
      return '';
    }
  }

  /// 安全地解析 customerId 字段
  /// 支持 String 和 int 类型的输入
  static int? _safeParseCustomerId(dynamic value) {
    if (value == null) return null;
    
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      if (value.isEmpty || value == 'null') return null;
      return int.tryParse(value);
    }
    
    // 尝试转换其他类型
    try {
      if (value is num) {
        return value.toInt();
      }
      // 尝试转换为字符串再解析
      final String stringValue = value.toString();
      if (stringValue.isEmpty || stringValue == 'null') return null;
      return int.tryParse(stringValue);
    } catch (e) {
      // 转换失败，返回 null
      return null;
    }
  }
  // === Order Identity ===
  /// Unique order ID from database
  final String id;

  /// Human-readable order number
  final String orderId;

  /// Order creation time
  final DateTime createTime;

  /// Last updated time
  final DateTime updatedAt;

  /// Order completion time (if completed)
  final DateTime? completedAt;

  /// Order cancellation time (if cancelled)
  final DateTime? cancelledAt;

  // === Transaction Information ===
  /// Operator/cashier ID who processed the order
  final String operatorId;

  /// Total order amount before discount
  final double amount;

  /// Applied discount amount
  final double discountAmount;

  /// Final amount after discount
  final double finalAmount;

  /// Amount already paid
  final double paidAmount;

  /// Current order status
  final OrderStatus status;

  // === Customer Information ===
  /// Customer ID (optional)
  final int? customerId;

  /// Customer name (optional)
  final String? customerName;

  /// Customer phone number (optional)
  final String? customerPhone;

  /// Vehicle type (optional)
  final String? vehicleType;

  /// License plate number (optional)
  final String? licensePlate;

  /// Member phone number (for backward compatibility)
  final String memberPhone;

  // === Station Information ===
  /// Station ID where order was created
  final int stationId;

  /// Station name where order was created
  final String? stationName;

  /// Staff name who processed the order
  final String? staffName;

  /// Pump ID used (from first item)
  final String pumpId;

  /// Fuel type (from first item)
  final String fuelType;

  /// Fuel volume (from first item)
  final double volume;

  /// Primary payment method
  final String paymentMethod;

  // === Structured Data ===
  /// List of items in order
  final List<Map<String, dynamic>> items;

  /// List of applied promotions
  final List<Map<String, dynamic>> promotions;

  /// List of payments
  final List<Map<String, dynamic>> payments;

  /// Status change logs
  final List<StatusLog> statusLogs;

  /// Additional information/metadata
  final Map<String, dynamic> extInfo;

  /// Konversi Order ke JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'order_number': orderId,
        'created_at': createTime.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
        'completed_at': completedAt?.toIso8601String(),
        'cancelled_at': cancelledAt?.toIso8601String(),
        'total_amount': amount,
        'discount_amount': discountAmount,
        'final_amount': finalAmount,
        'paid_amount': paidAmount,
        'status': status.name,
        'customer_id': customerId,
        'customer_name': customerName,
        'customer_phone': customerPhone,
        'vehicle_type': vehicleType,
        'license_plate': licensePlate,
        'member_phone': memberPhone,
        'station_id': stationId,
        'station_name': stationName,
        'staff_name': staffName,
        'items': items,
        'promotions': promotions,
        'payments': payments,
        'metadata': extInfo,
      };

  // === Metode Status ===

  /// Get status text in English following Indonesian business habits
  String getStatusText() {
    switch (status) {
      case OrderStatus.created:
        return 'Created';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelling:
        return 'Cancelling';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.failed:
        return 'Failed';
    }
  }

  /// Get order status color
  int getStatusColor() {
    switch (status) {
      case OrderStatus.created:
        return 0xFF2196F3; // Blue
      case OrderStatus.processing:
        return 0xFFFFA000; // Orange
      case OrderStatus.completed:
        return 0xFF4CAF50; // Green
      case OrderStatus.cancelling:
        return 0xFFFF9800; // Orange
      case OrderStatus.cancelled:
        return 0xFF9E9E9E; // Grey
      case OrderStatus.failed:
        return 0xFFF44336; // Red
    }
  }

  /// Get order status icon
  String getStatusIcon() {
    switch (status) {
      case OrderStatus.created:
        return '📝';
      case OrderStatus.processing:
        return '⏳';
      case OrderStatus.completed:
        return '✅';
      case OrderStatus.cancelling:
        return '🔄';
      case OrderStatus.cancelled:
        return '❌';
      case OrderStatus.failed:
        return '⚠️';
    }
  }

  // === Validation Methods ===

  /// Check if order can be cancelled
  bool canBeCancelled() {
    return status == OrderStatus.created || status == OrderStatus.processing;
  }

  /// Check if order can be modified
  bool canBeModified() {
    return status == OrderStatus.created;
  }

  /// Check if order is fully paid
  bool isFullyPaid() {
    return paidAmount >= finalAmount;
  }

  /// Check if order is completed
  bool isCompleted() {
    return status == OrderStatus.completed;
  }

  /// Check if order is active (not cancelled or failed)
  bool isActive() {
    return status != OrderStatus.cancelled && status != OrderStatus.failed;
  }

  // === Information Methods ===

  /// Get remaining payment amount
  double getRemainingAmount() {
    return (finalAmount - paidAmount).clamp(0.0, double.infinity);
  }

  /// Get payment percentage
  double getPaymentPercentage() {
    if (finalAmount <= 0) return 0.0;
    return (paidAmount / finalAmount * 100).clamp(0.0, 100.0);
  }

  /// Get total items in order
  int getTotalItems() {
    return items.length;
  }

  /// Get total applied promotions
  int getTotalPromotions() {
    return promotions.length;
  }

  /// Get processing duration (if completed)
  Duration? getProcessingDuration() {
    if (completedAt == null) return null;
    return completedAt!.difference(createTime);
  }

  /// Get order age
  Duration getAge() {
    return DateTime.now().difference(createTime);
  }

  // === Formatting Methods ===

  /// 格式化金额为印尼盾格式
  String formatAmount(double amount) {
    // 使用印尼货币格式：整数，点号作为千位分隔符
    final NumberFormat formatter = NumberFormat('#,##0', 'id_ID');
    return 'Rp ${formatter.format(amount).replaceAll(',', '.')}';
  }

  /// Get order summary
  String getSummary() {
    return 'Order $orderId - ${getStatusText()} - ${formatAmount(finalAmount)}';
  }

  /// Get complete order description
  String getDescription() {
    final StringBuffer buffer = StringBuffer();
    buffer.writeln('Order: $orderId');
    buffer.writeln('Status: ${getStatusText()}');
    buffer.writeln('Total: ${formatAmount(finalAmount)}');
    buffer.writeln(
        'Created: ${DateFormat('dd/MM/yyyy HH:mm').format(createTime)}');

    if (customerName?.isNotEmpty == true) {
      buffer.writeln('Customer: $customerName');
    }

    if (items.isNotEmpty) {
      buffer.writeln('Items: ${items.length}');
    }

    return buffer.toString().trim();
  }

  // === Copy Methods ===

  /// Create a copy of the order with some modified fields
  Order copyWith({
    String? id,
    String? orderId,
    DateTime? createTime,
    DateTime? updatedAt,
    DateTime? completedAt,
    DateTime? cancelledAt,
    String? operatorId,
    double? amount,
    double? discountAmount,
    double? finalAmount,
    double? paidAmount,
    OrderStatus? status,
    int? customerId,
    String? customerName,
    String? memberPhone,
    int? stationId,
    String? stationName,
    String? staffName,
    String? pumpId,
    String? fuelType,
    double? volume,
    String? paymentMethod,
    List<Map<String, dynamic>>? items,
    List<Map<String, dynamic>>? promotions,
    List<Map<String, dynamic>>? payments,
    List<StatusLog>? statusLogs,
    Map<String, dynamic>? extInfo,
  }) {
    return Order(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      createTime: createTime ?? this.createTime,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      operatorId: operatorId ?? this.operatorId,
      amount: amount ?? this.amount,
      discountAmount: discountAmount ?? this.discountAmount,
      finalAmount: finalAmount ?? this.finalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      status: status ?? this.status,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      memberPhone: memberPhone ?? this.memberPhone,
      stationId: stationId ?? this.stationId,
      stationName: stationName ?? this.stationName,
      staffName: staffName ?? this.staffName,
      pumpId: pumpId ?? this.pumpId,
      fuelType: fuelType ?? this.fuelType,
      volume: volume ?? this.volume,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      items: items ?? this.items,
      promotions: promotions ?? this.promotions,
      payments: payments ?? this.payments,
      statusLogs: statusLogs ?? this.statusLogs,
      extInfo: extInfo ?? this.extInfo,
    );
  }

  @override
  String toString() => getSummary();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Order && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Query condition for order search
class OrderQueryCondition {
  OrderQueryCondition({
    this.startDate,
    this.endDate,
    this.orderId,
    this.status,
    this.paymentMethod,
    this.customerId,
    this.stationId,
    this.page = 1,
    this.pageSize = 20,
  });
  // Sentinel object to distinguish between explicit null and not providing value
  static const Object _UNDEFINED = Object();

  /// Search start date
  final DateTime? startDate;

  /// Search end date
  final DateTime? endDate;

  /// Order ID/number for search
  final String? orderId;

  /// Order status for filtering
  final OrderStatus? status;

  /// Payment method for filtering
  final String? paymentMethod;

  /// Customer ID for filtering
  final int? customerId;

  /// Station ID for filtering
  final int? stationId;

  /// Page number for pagination
  final int page;

  /// Page size for pagination
  final int pageSize;

  /// Create a copy with some modified fields
  OrderQueryCondition copyWith({
    Object? startDate = _UNDEFINED,
    Object? endDate = _UNDEFINED,
    Object? orderId = _UNDEFINED,
    Object? status = _UNDEFINED,
    Object? paymentMethod = _UNDEFINED,
    Object? customerId = _UNDEFINED,
    Object? stationId = _UNDEFINED,
    int? page,
    int? pageSize,
  }) {
    return OrderQueryCondition(
      startDate:
          startDate == _UNDEFINED ? this.startDate : startDate as DateTime?,
      endDate: endDate == _UNDEFINED ? this.endDate : endDate as DateTime?,
      orderId: orderId == _UNDEFINED ? this.orderId : orderId as String?,
      status: status == _UNDEFINED ? this.status : status as OrderStatus?,
      paymentMethod: paymentMethod == _UNDEFINED
          ? this.paymentMethod
          : paymentMethod as String?,
      customerId:
          customerId == _UNDEFINED ? this.customerId : customerId as int?,
      stationId: stationId == _UNDEFINED ? this.stationId : stationId as int?,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
    );
  }

  /// Convert to query parameters for API
  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = <String, dynamic>{
      'page': page,
      'limit': pageSize,
    };

    if (startDate != null) {
      params['date_from'] = DateFormat('yyyy-MM-dd').format(startDate!);
    }

    if (endDate != null) {
      params['date_to'] = DateFormat('yyyy-MM-dd').format(endDate!);
    }

    if (orderId != null && orderId!.isNotEmpty) {
      params['order_number'] = orderId;
    }

    if (status != null) {
      // Map status enum to API string
      switch (status!) {
        case OrderStatus.created:
          params['status'] = 'new';
          break;
        case OrderStatus.processing:
          params['status'] = 'processing';
          break;
        case OrderStatus.completed:
          params['status'] = 'completed';
          break;
        case OrderStatus.cancelled:
          params['status'] = 'cancelled';
          break;
        default:
          // Don't add parameter for internal status
          break;
      }
    }

    if (paymentMethod != null && paymentMethod!.isNotEmpty) {
      params['payment_method'] = paymentMethod;
    }

    if (customerId != null) {
      params['customer_id'] = customerId;
    }

    if (stationId != null) {
      params['station_id'] = stationId;
    }

    return params;
  }

  @override
  String toString() {
    return 'OrderQueryCondition(page: $page, pageSize: $pageSize, status: $status)';
  }
}

/// Order cancellation reason
enum CancelReason {
  operationError, // Operation error
  customerRequest, // Customer request
  deviceFailure, // Device failure
  paymentFailed, // Payment failed
  systemError, // System error
  other // Other reason
}

/// Cancellation request status
enum CancelStatus {
  pending, // Waiting for approval
  approved, // Approved
  rejected, // Rejected
  processing // Processing
}

/// Order cancellation request model
class CancelRequest {
  CancelRequest({
    required this.requestId,
    required this.orderId,
    required this.requestTime,
    required this.operatorId,
    required this.reason,
    required this.detail,
    required this.status,
    this.approver,
    this.approveTime,
    this.approverNotes,
  });

  /// Create CancelRequest from JSON
  factory CancelRequest.fromJson(Map<String, dynamic> json) {
    return CancelRequest(
      requestId: json['request_id'] as String? ?? '',
      orderId: json['order_id'] as String? ?? '',
      requestTime: DateTime.parse(
          (json['request_time'] ?? DateTime.now().toIso8601String())
              .toString()),
      operatorId: json['operator_id'] as String? ?? '',
      reason: _mapCancelReason(json['reason'] as String?),
      detail: json['detail'] as String? ?? '',
      status: _mapCancelStatus(json['status'] as String?),
      approver: json['approver'] as String?,
      approveTime: json['approve_time'] != null
          ? DateTime.parse(json['approve_time'].toString())
          : null,
      approverNotes: json['approver_notes'] as String?,
    );
  }

  /// Unique cancellation request ID
  final String requestId;

  /// Order ID to be cancelled
  final String orderId;

  /// Request submission time
  final DateTime requestTime;

  /// Operator ID who submitted the cancellation
  final String operatorId;

  /// Cancellation reason
  final CancelReason reason;

  /// Detailed explanation
  final String detail;

  /// Request status
  final CancelStatus status;

  /// Approver ID (if approved)
  final String? approver;

  /// Approval time (if approved)
  final DateTime? approveTime;

  /// Notes from approver
  final String? approverNotes;

  /// Convert to JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'request_id': requestId,
        'order_id': orderId,
        'request_time': requestTime.toIso8601String(),
        'operator_id': operatorId,
        'reason': reason.name,
        'detail': detail,
        'status': status.name,
        'approver': approver,
        'approve_time': approveTime?.toIso8601String(),
        'approver_notes': approverNotes,
      };

  /// Get cancellation reason text in English following Indonesian business habits
  String getReasonText() {
    switch (reason) {
      case CancelReason.operationError:
        return 'Operation Error';
      case CancelReason.customerRequest:
        return 'Customer Request';
      case CancelReason.deviceFailure:
        return 'Device Failure';
      case CancelReason.paymentFailed:
        return 'Payment Failed';
      case CancelReason.systemError:
        return 'System Error';
      case CancelReason.other:
        return 'Other Reason';
    }
  }

  /// Get cancellation status text in English following Indonesian business habits
  String getStatusText() {
    switch (status) {
      case CancelStatus.pending:
        return 'Waiting for Approval';
      case CancelStatus.approved:
        return 'Approved';
      case CancelStatus.rejected:
        return 'Rejected';
      case CancelStatus.processing:
        return 'Processing';
    }
  }

  /// Get status color
  int getStatusColor() {
    switch (status) {
      case CancelStatus.pending:
        return 0xFFFFA000; // Orange
      case CancelStatus.approved:
        return 0xFF4CAF50; // Green
      case CancelStatus.rejected:
        return 0xFFF44336; // Red
      case CancelStatus.processing:
        return 0xFF2196F3; // Blue
    }
  }

  /// Check if request is still pending
  bool isPending() => status == CancelStatus.pending;

  /// Check if request is approved
  bool isApproved() => status == CancelStatus.approved;

  /// Check if request is rejected
  bool isRejected() => status == CancelStatus.rejected;
}

/// Helper function to map string to CancelReason
CancelReason _mapCancelReason(String? reason) {
  switch (reason?.toLowerCase()) {
    case 'operation_error':
      return CancelReason.operationError;
    case 'customer_request':
      return CancelReason.customerRequest;
    case 'device_failure':
      return CancelReason.deviceFailure;
    case 'payment_failed':
      return CancelReason.paymentFailed;
    case 'system_error':
      return CancelReason.systemError;
    default:
      return CancelReason.other;
  }
}

/// Helper function to map string to CancelStatus
CancelStatus _mapCancelStatus(String? status) {
  switch (status?.toLowerCase()) {
    case 'pending':
      return CancelStatus.pending;
    case 'approved':
      return CancelStatus.approved;
    case 'rejected':
      return CancelStatus.rejected;
    case 'processing':
      return CancelStatus.processing;
    default:
      return CancelStatus.pending;
  }
}
