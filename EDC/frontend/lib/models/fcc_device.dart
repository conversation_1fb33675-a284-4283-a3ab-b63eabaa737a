import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

/// FCC Device Type Enums
enum FCCDeviceType {
  controller('controller'),
  fuelController('fuel_controller'),
  paymentController('payment_controller'),
  monitorController('monitor_controller'),
  dartLineController('dart_line_controller'),
  tcpDeviceController('tcp_device_controller'),
  serialPortController('serial_port_controller'),
  fuelPump('fuel_pump'),
  fuelNozzle('fuel_nozzle'),
  atg('atg'),
  display('display'),
  pos('pos'),
  edc('edc'),
  pump('pump'),
  dartPump('dart_pump'),
  dartTank('dart_tank'),
  dartDisplay('dart_display');

  const FCCDeviceType(this.value);
  final String value;

  static FCCDeviceType fromString(String value) {
    return FCCDeviceType.values.firstWhere(
      (FCCDeviceType e) => e.value == value,
      orElse: () => FCCDeviceType.pump,
    );
  }
}

/// FCC Device Status Enums
enum FCCDeviceStatus {
  online('online'),
  offline('offline'),
  error('error'),
  maintenance('maintenance'),
  unknown('unknown');

  const FCCDeviceStatus(this.value);
  final String value;

  static FCCDeviceStatus fromString(String value) {
    return FCCDeviceStatus.values.firstWhere(
      (FCCDeviceStatus e) => e.value == value,
      orElse: () => FCCDeviceStatus.unknown,
    );
  }
}

/// FCC Device Health Enums
enum FCCDeviceHealth {
  healthy('healthy'),
  good('good'),
  warning('warning'),
  critical('critical'),
  error('error'),
  unknown('unknown');

  const FCCDeviceHealth(this.value);
  final String value;

  static FCCDeviceHealth fromString(String value) {
    return FCCDeviceHealth.values.firstWhere(
      (FCCDeviceHealth e) => e.value == value,
      orElse: () => FCCDeviceHealth.unknown,
    );
  }
}

/// FCC Protocol Type Enums
enum FCCProtocolType {
  tcp('tcp'),
  udp('udp'),
  serial('serial'),
  modbus('modbus'),
  dart('dart'),
  wayneDart('wayne_dart');

  const FCCProtocolType(this.value);
  final String value;

  static FCCProtocolType fromString(String value) {
    return FCCProtocolType.values.firstWhere(
      (FCCProtocolType e) => e.value == value,
      orElse: () => FCCProtocolType.dart,
    );
  }
}

/// FCC Nozzle Status Enums - Based on Wayne DART Protocol
enum FCCNozzleStatus {
  idle('idle'),
  selected('selected'),
  authorized('authorized'),
  out('out'),
  filling('filling'),
  completed('completed'),
  suspended('suspended'),
  error('error'),
  maintenance('maintenance');

  const FCCNozzleStatus(this.value);
  final String value;

  static FCCNozzleStatus fromString(String value) {
    return FCCNozzleStatus.values.firstWhere(
      (FCCNozzleStatus e) => e.value == value,
      orElse: () => FCCNozzleStatus.idle,
    );
  }
}

/// FCC Address Range
class FCCAddressRange extends Equatable {
  const FCCAddressRange({
    required this.min,
    required this.max,
  });

  factory FCCAddressRange.fromJson(Map<String, dynamic> json) {
    return FCCAddressRange(
      min: json['min'] as int,
      max: json['max'] as int,
    );
  }
  final int min;
  final int max;

  @override
  List<Object?> get props => <Object?>[min, max];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'min': min,
      'max': max,
    };
  }
}

/// FCC Controller Configuration
class FCCControllerConfig extends Equatable {
  const FCCControllerConfig({
    this.host,
    this.port,
    this.serialPort,
    this.baudRate,
    this.dataBits,
    this.stopBits,
    this.parity,
    this.timeout,
    this.readTimeout,
    this.writeTimeout,
    this.maxRetries,
    this.addressRange,
    this.dleEnabled,
    this.crcEnabled,
    this.txSequenceStart,
    this.extraConfig,
  });

  factory FCCControllerConfig.fromJson(Map<String, dynamic> json) {
    return FCCControllerConfig(
      host: json['host'] as String?,
      port: json['port'] as int?,
      serialPort: json['serial_port'] as String?,
      baudRate: json['baud_rate'] as int?,
      dataBits: json['data_bits'] as int?,
      stopBits: json['stop_bits'] as int?,
      parity: json['parity'] as String?,
      timeout: json['timeout'] as int?,
      readTimeout: json['read_timeout'] as int?,
      writeTimeout: json['write_timeout'] as int?,
      maxRetries: json['max_retries'] as int?,
      addressRange: json['address_range'] != null
          ? FCCAddressRange.fromJson(
              json['address_range'] as Map<String, dynamic>)
          : null,
      dleEnabled: json['dle_enabled'] as bool?,
      crcEnabled: json['crc_enabled'] as bool?,
      txSequenceStart: json['tx_sequence_start'] as int?,
      extraConfig: json['extra_config'] as Map<String, dynamic>?,
    );
  }
  final String? host;
  final int? port;
  final String? serialPort;
  final int? baudRate;
  final int? dataBits;
  final int? stopBits;
  final String? parity;
  final int? timeout;
  final int? readTimeout;
  final int? writeTimeout;
  final int? maxRetries;
  final FCCAddressRange? addressRange;
  final bool? dleEnabled;
  final bool? crcEnabled;
  final int? txSequenceStart;
  final Map<String, dynamic>? extraConfig;

  @override
  List<Object?> get props => <Object?>[
        host,
        port,
        serialPort,
        baudRate,
        dataBits,
        stopBits,
        parity,
        timeout,
        readTimeout,
        writeTimeout,
        maxRetries,
        addressRange,
        dleEnabled,
        crcEnabled,
        txSequenceStart,
        extraConfig,
      ];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      if (host != null) 'host': host,
      if (port != null) 'port': port,
      if (serialPort != null) 'serial_port': serialPort,
      if (baudRate != null) 'baud_rate': baudRate,
      if (dataBits != null) 'data_bits': dataBits,
      if (stopBits != null) 'stop_bits': stopBits,
      if (parity != null) 'parity': parity,
      if (timeout != null) 'timeout': timeout,
      if (readTimeout != null) 'read_timeout': readTimeout,
      if (writeTimeout != null) 'write_timeout': writeTimeout,
      if (maxRetries != null) 'max_retries': maxRetries,
      if (addressRange != null) 'address_range': addressRange!.toJson(),
      if (dleEnabled != null) 'dle_enabled': dleEnabled,
      if (crcEnabled != null) 'crc_enabled': crcEnabled,
      if (txSequenceStart != null) 'tx_sequence_start': txSequenceStart,
      if (extraConfig != null) 'extra_config': extraConfig,
    };
  }
}

/// FCC Controller - Represents lower-level controller hardware
class FCCController extends Equatable {
  const FCCController({
    required this.id,
    required this.name,
    required this.type,
    this.model,
    this.vendor,
    required this.protocol,
    required this.address,
    required this.config,
    required this.stationId,
    this.location,
    required this.status,
    required this.health,
    this.lastSeen,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    required this.version,
  });

  factory FCCController.fromJson(Map<String, dynamic> json) {
    return FCCController(
      id: json['id'] as String,
      name: json['name'] as String,
      type: FCCDeviceType.fromString(json['type'] as String),
      model: json['model'] as String?,
      vendor: json['vendor'] as String?,
      protocol: FCCProtocolType.fromString(json['protocol'] as String),
      address: json['address'] as String,
      config:
          FCCControllerConfig.fromJson(json['config'] as Map<String, dynamic>),
      stationId: json['station_id'] as String,
      location: json['location'] as String?,
      status: FCCDeviceStatus.fromString(json['status'] as String),
      health: FCCDeviceHealth.fromString(json['health'] as String),
      lastSeen: json['last_seen'] != null
          ? DateTime.parse(json['last_seen'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      version: json['version'] as int,
    );
  }
  final String id;
  final String name;
  final FCCDeviceType type;
  final String? model;
  final String? vendor;
  final FCCProtocolType protocol;
  final String address;
  final FCCControllerConfig config;
  final String stationId;
  final String? location;
  final FCCDeviceStatus status;
  final FCCDeviceHealth health;
  final DateTime? lastSeen;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int version;

  @override
  List<Object?> get props => <Object?>[
        id,
        name,
        type,
        model,
        vendor,
        protocol,
        address,
        config,
        stationId,
        location,
        status,
        health,
        lastSeen,
        metadata,
        createdAt,
        updatedAt,
        version,
      ];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'type': type.value,
      if (model != null) 'model': model,
      if (vendor != null) 'vendor': vendor,
      'protocol': protocol.value,
      'address': address,
      'config': config.toJson(),
      'station_id': stationId,
      if (location != null) 'location': location,
      'status': status.value,
      'health': health.value,
      if (lastSeen != null) 'last_seen': lastSeen!.toIso8601String(),
      if (metadata != null) 'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'version': version,
    };
  }

  // Utility methods
  bool get isOnline => status == FCCDeviceStatus.online;
  bool get isHealthy => health == FCCDeviceHealth.healthy;
  bool get isDartProtocol => protocol == FCCProtocolType.dart;
}

/// FCC Device Capabilities
class FCCDeviceCapabilities extends Equatable {
  const FCCDeviceCapabilities({
    required this.supportedCommands,
    required this.supportedDataTypes,
    this.protocolVersion,
    this.firmwareVersion,
    this.hardwareVersion,
    this.features,
    this.performance,
  });

  factory FCCDeviceCapabilities.fromJson(Map<String, dynamic> json) {
    return FCCDeviceCapabilities(
      supportedCommands: json['supported_commands'] != null
          ? List<String>.from(json['supported_commands'] as List)
          : <String>[],
      supportedDataTypes: json['supported_data_types'] != null
          ? List<String>.from(json['supported_data_types'] as List)
          : <String>[],
      protocolVersion: json['protocol_version'] as String?,
      firmwareVersion: json['firmware_version'] as String?,
      hardwareVersion: json['hardware_version'] as String?,
      features: json['features'] != null
          ? Map<String, bool>.from(json['features'] as Map)
          : null,
      performance: json['performance'] as Map<String, dynamic>?,
    );
  }
  final List<String> supportedCommands;
  final List<String> supportedDataTypes;
  final String? protocolVersion;
  final String? firmwareVersion;
  final String? hardwareVersion;
  final Map<String, bool>? features;
  final Map<String, dynamic>? performance;

  @override
  List<Object?> get props => <Object?>[
        supportedCommands,
        supportedDataTypes,
        protocolVersion,
        firmwareVersion,
        hardwareVersion,
        features,
        performance,
      ];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'supported_commands': supportedCommands,
      'supported_data_types': supportedDataTypes,
      if (protocolVersion != null) 'protocol_version': protocolVersion,
      if (firmwareVersion != null) 'firmware_version': firmwareVersion,
      if (hardwareVersion != null) 'hardware_version': hardwareVersion,
      if (features != null) 'features': features,
      if (performance != null) 'performance': performance,
    };
  }
}

/// FCC Nozzle - Represents fuel nozzle hardware
class FCCNozzle extends Equatable {
  const FCCNozzle({
    required this.id,
    required this.deviceId,
    required this.deviceName,
    required this.number,
    required this.name,
    required this.fuelType,
    required this.grade,
    required this.price,
    required this.currency,
    required this.status,
    required this.isEnabled,
    required this.isSelected,
    this.dispenserId, // 优化：从接口获取的 dispenserId
    this.pumpGroupId, // 优化：从接口获取的 pumpGroupId
    this.currentVolume = 0.0, // 默认值为0.0
    this.currentAmount = 0.0, // 默认值为0.0
    this.stateData,
    this.preauthType, // 新增：预授权类型
    this.preauthNumber, // 新增：预授权数值
    this.preauthCreatedAt, // 新增：预授权创建时间
    this.preauthExpiresAt, // 新增：预授权过期时间
    required this.lastUpdateTime,
    this.errorMessage,
  });

  factory FCCNozzle.fromJson(Map<String, dynamic> json,
      {required String deviceId, required String deviceName}) {
    try {
      // Parse basic fields using known API response format
      final String id = json['id'] as String? ?? 'unknown_nozzle';
      final int number = json['nozzle_number'] as int? ?? 1;
      final String name = json['name'] as String? ?? 'Nozzle $number';

      // Fuel information
      final String fuelGradeName =
          json['fuel_grade_name'] as String? ?? 'Unknown Fuel';

      // Price handling - 安全解析价格字段
      final dynamic priceValue = json['current_price'];
      final double price = _parseDoubleValue(priceValue);

      // Status and flags
      final FCCNozzleStatus status =
          FCCNozzleStatus.fromString(json['status'] as String? ?? 'idle');
      final bool isEnabled = json['is_enabled'] as bool? ?? true;
      final bool isSelected = json['is_selected'] as bool? ?? false;

      // Volume and amount - 安全解析数字字段（支持字符串和数字类型）
      final currentVolumeValue = json['current_volume'];
      final double currentVolume = _parseDoubleValue(currentVolumeValue);

      final currentAmountValue = json['current_amount'];
      final double currentAmount = _parseDoubleValue(currentAmountValue);

      // Timestamp
      final String? lastUpdateStr = json['last_update'] as String?;
      final DateTime lastUpdateTime = lastUpdateStr != null
          ? DateTime.parse(lastUpdateStr)
          : DateTime.now();

      // 优化：从 metadata 中解析 dispenserId 和 pumpGroupId
      String? dispenserId;
      String? pumpGroupId;
      if (json['metadata'] != null) {
        final Map<String, dynamic> metadata = json['metadata'] as Map<String, dynamic>;
        dispenserId = metadata['dispenser_id'] as String?;
        pumpGroupId = metadata['pump_group_id'] as String?;
      }

      // 解析预授权信息
      final String? preauthType = json['preauth_type'] as String?;
      final String? preauthNumber = json['preauth_number'] as String?;
      final DateTime? preauthCreatedAt = json['preauth_created_at'] != null
          ? DateTime.tryParse(json['preauth_created_at'] as String)
          : null;
      final DateTime? preauthExpiresAt = json['preauth_expires_at'] != null
          ? DateTime.tryParse(json['preauth_expires_at'] as String)
          : null;

      return FCCNozzle(
        id: id,
        deviceId: deviceId, // Use the provided deviceId parameter
        deviceName: deviceName, // Use the provided deviceName parameter
        number: number,
        name: name,
        fuelType: fuelGradeName,
        grade: fuelGradeName,
        price: price,
        currency: 'IDR',
        status: status,
        isEnabled: isEnabled,
        isSelected: isSelected,
        dispenserId: dispenserId, // 优化：从 metadata 中获取 dispenserId
        pumpGroupId: pumpGroupId, // 优化：从 metadata 中获取 pumpGroupId
        currentVolume: currentVolume,
        currentAmount: currentAmount,
        stateData: json['state_data'] as Map<String, dynamic>?,
        preauthType: preauthType, // 新增：预授权字段
        preauthNumber: preauthNumber,
        preauthCreatedAt: preauthCreatedAt,
        preauthExpiresAt: preauthExpiresAt,
        lastUpdateTime: lastUpdateTime,
        errorMessage: json['error_message'] as String?,
      );
    } catch (e) {
      debugPrint('❌ FCCNozzle JSON parsing error: $e');
      rethrow;
    }
  }
  final String id;
  final String deviceId;
  final String deviceName; // 设备名称，从 FCCDevice.name 传递
  final int number; // Wayne protocol nozzle number (1-15)
  final String name;
  final String fuelType;
  final String grade;
  final double price;
  final String currency;
  final FCCNozzleStatus status;
  final bool isEnabled;
  final bool isSelected;
  final String? dispenserId; // 优化：从接口获取的 dispenserId
  final String? pumpGroupId; // 优化：从接口获取的 pumpGroupId
  final double currentVolume; // 当前油量 (新增)
  final double currentAmount; // 当前金额 (新增)
  final Map<String, dynamic>? stateData; // Wayne protocol state data
  final String? preauthType; // 新增：预授权类型
  final String? preauthNumber; // 新增：预授权数值
  final DateTime? preauthCreatedAt; // 新增：预授权创建时间
  final DateTime? preauthExpiresAt; // 新增：预授权过期时间
  final DateTime lastUpdateTime;
  final String? errorMessage;

  @override
  List<Object?> get props => <Object?>[
        id,
        deviceId,
        deviceName,
        number,
        name,
        fuelType,
        grade,
        price,
        currency,
        status,
        isEnabled,
        isSelected,
        dispenserId, // 优化：新增到props
        pumpGroupId, // 优化：新增到props
        currentVolume, // 新增到props
        currentAmount, // 新增到props
        stateData,
        preauthType, // 新增：预授权字段到props
        preauthNumber,
        preauthCreatedAt,
        preauthExpiresAt,
        lastUpdateTime,
        errorMessage,
      ];

  /// 安全解析double值，支持字符串和数字类型
  static double _parseDoubleValue(dynamic value) {
    if (value == null) return 0.0;
    if (value is num) return value.toDouble();
    if (value is String) {
      final double? parsed = double.tryParse(value);
      return parsed ?? 0.0;
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'device_id': deviceId,
      'device_name': deviceName,
      'number': number,
      'name': name,
      'fuel_type': fuelType,
      'grade': grade,
      'price': price,
      'currency': currency,
      'status': status.value,
      'is_enabled': isEnabled,
      'is_selected': isSelected,
      'current_volume': currentVolume, // 新增JSON序列化
      'current_amount': currentAmount, // 新增JSON序列化
      if (stateData != null) 'state_data': stateData,
      'last_update_time': lastUpdateTime.toIso8601String(),
      if (errorMessage != null) 'error_message': errorMessage,
    };
  }

  // Utility methods
  bool get isIdle => status == FCCNozzleStatus.idle;
  bool get isActive => !isIdle && status != FCCNozzleStatus.error;
  bool get canAuthorize => status == FCCNozzleStatus.idle && isEnabled;
  bool get isTransactionInProgress =>
      status == FCCNozzleStatus.authorized ||
      status == FCCNozzleStatus.filling ||
      status == FCCNozzleStatus.suspended;

  FCCNozzle copyWith({
    String? id,
    String? deviceId,
    String? deviceName,
    int? number,
    String? name,
    String? fuelType,
    String? grade,
    double? price,
    String? currency,
    FCCNozzleStatus? status,
    bool? isEnabled,
    bool? isSelected,
    double? currentVolume, // 新增copyWith参数
    double? currentAmount, // 新增copyWith参数
    Map<String, dynamic>? stateData,
    String? preauthType, // 新增：预授权copyWith参数
    String? preauthNumber,
    DateTime? preauthCreatedAt,
    DateTime? preauthExpiresAt,
    DateTime? lastUpdateTime,
    String? errorMessage,
  }) {
    return FCCNozzle(
      id: id ?? this.id,
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      number: number ?? this.number,
      name: name ?? this.name,
      fuelType: fuelType ?? this.fuelType,
      grade: grade ?? this.grade,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      isEnabled: isEnabled ?? this.isEnabled,
      isSelected: isSelected ?? this.isSelected,
      currentVolume: currentVolume ?? this.currentVolume, // 新增copyWith逻辑
      currentAmount: currentAmount ?? this.currentAmount, // 新增copyWith逻辑
      stateData: stateData ?? this.stateData,
      preauthType: preauthType ?? this.preauthType, // 新增：预授权copyWith逻辑
      preauthNumber: preauthNumber ?? this.preauthNumber,
      preauthCreatedAt: preauthCreatedAt ?? this.preauthCreatedAt,
      preauthExpiresAt: preauthExpiresAt ?? this.preauthExpiresAt,
      lastUpdateTime: lastUpdateTime ?? this.lastUpdateTime,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// FCC Device - Represents terminal device hardware (like Wayne DART pump)
class FCCDevice extends Equatable {
  const FCCDevice({
    required this.id,
    required this.name,
    required this.type,
    this.model,
    this.vendor,
    this.serialNumber,
    required this.controllerId,
    this.controller,
    required this.deviceAddress,
    required this.stationId,
    this.islandId,
    this.position,
    this.dispenserNumber, // 新增：Dispenser编号参数
    required this.status,
    required this.health,
    this.lastSeen,
    required this.capabilities,
    required this.nozzles,
    required this.maxNozzles,
    required this.supportedGrades,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    required this.version,
  });

  factory FCCDevice.fromJson(Map<String, dynamic> json) {
    final String deviceId = json['id'] as String;

    // 处理config中的pump_config获取nozzle_count
    int maxNozzlesFromConfig = 2; // 默认值
    if (json['config'] != null) {
      final Map<String, dynamic> config =
          json['config'] as Map<String, dynamic>;
      if (config['pump_config'] != null) {
        final Map<String, dynamic> pumpConfig =
            config['pump_config'] as Map<String, dynamic>;
        maxNozzlesFromConfig = pumpConfig['nozzle_count'] as int? ?? 2;
      }
    }

    // 处理config中的supported_fuels获取supported_grades
    List<String> supportedGradesFromConfig = <String>[
      'gasoline',
      'diesel'
    ]; // 默认值
    if (json['config'] != null) {
      final Map<String, dynamic> config =
          json['config'] as Map<String, dynamic>;
      if (config['pump_config'] != null) {
        final Map<String, dynamic> pumpConfig =
            config['pump_config'] as Map<String, dynamic>;
        if (pumpConfig['supported_fuels'] != null) {
          supportedGradesFromConfig =
              List<String>.from(pumpConfig['supported_fuels'] as List);
        }
      }
    }

    return FCCDevice(
      id: deviceId,
      name: json['name'] as String,
      type: FCCDeviceType.fromString(json['type'] as String),
      model: json['model'] as String?,
      vendor: json['vendor'] as String?,
      serialNumber: json['serial_number'] as String?,
      controllerId: json['controller_id'] as String,
      controller: json['controller'] != null
          ? FCCController.fromJson(json['controller'] as Map<String, dynamic>)
          : null,
      deviceAddress: json['device_address'] as int,
      stationId: json['station_id'] as String,
      islandId: json['island_id'] as String?,
      position: json['position'] as String?,
      dispenserNumber:
          json['dispenser_number'] as int? ?? 1, // 新增：从JSON解析dispenserNumber
      status: FCCDeviceStatus.fromString(json['status'] as String),
      health: FCCDeviceHealth.fromString(json['health'] as String),
      lastSeen: json['last_seen'] != null
          ? DateTime.parse(json['last_seen'] as String)
          : null,
      capabilities: json['capabilities'] != null
          ? FCCDeviceCapabilities.fromJson(
              json['capabilities'] as Map<String, dynamic>)
          : const FCCDeviceCapabilities(
              supportedCommands: <String>[], supportedDataTypes: <String>[]),
      nozzles: (json['nozzles'] as List<dynamic>?)
              ?.map((nozzle) => FCCNozzle.fromJson(
                  nozzle as Map<String, dynamic>,
                  deviceId: deviceId,
                  deviceName: json['name'] as String))
              .toList() ??
          <FCCNozzle>[],
      maxNozzles: json['max_nozzles'] as int? ?? maxNozzlesFromConfig,
      supportedGrades: json['supported_grades'] != null
          ? List<String>.from(json['supported_grades'] as List)
          : supportedGradesFromConfig,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      version: json['version'] as int? ?? 1,
    );
  }
  final String id;
  final String name;
  final FCCDeviceType type;
  final String? model;
  final String? vendor;
  final String? serialNumber;
  final String controllerId;
  final FCCController? controller;
  final int deviceAddress; // DART protocol device address
  final String stationId;
  final String? islandId;
  final String? position;
  final int? dispenserNumber; // 新增：Dispenser编号字段
  final FCCDeviceStatus status;
  final FCCDeviceHealth health;
  final DateTime? lastSeen;
  final FCCDeviceCapabilities capabilities;
  final List<FCCNozzle> nozzles;
  final int maxNozzles;
  final List<String> supportedGrades;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int version;

  @override
  List<Object?> get props => <Object?>[
        id,
        name,
        type,
        model,
        vendor,
        serialNumber,
        controllerId,
        controller,
        deviceAddress,
        stationId,
        islandId,
        position,
        dispenserNumber, // 新增：在props中包含dispenserNumber
        status,
        health,
        lastSeen,
        capabilities,
        nozzles,
        maxNozzles,
        supportedGrades,
        metadata,
        createdAt,
        updatedAt,
        version,
      ];

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'type': type.value,
      if (model != null) 'model': model,
      if (vendor != null) 'vendor': vendor,
      if (serialNumber != null) 'serial_number': serialNumber,
      'controller_id': controllerId,
      if (controller != null) 'controller': controller!.toJson(),
      'device_address': deviceAddress,
      'station_id': stationId,
      if (islandId != null) 'island_id': islandId,
      if (position != null) 'position': position,
      if (dispenserNumber != null)
        'dispenser_number': dispenserNumber, // 新增：序列化dispenserNumber
      'status': status.value,
      'health': health.value,
      if (lastSeen != null) 'last_seen': lastSeen!.toIso8601String(),
      'capabilities': capabilities.toJson(),
      'nozzles': nozzles.map((FCCNozzle nozzle) => nozzle.toJson()).toList(),
      'max_nozzles': maxNozzles,
      'supported_grades': supportedGrades,
      if (metadata != null) 'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'version': version,
    };
  }

  // Utility methods
  bool get isOnline => status == FCCDeviceStatus.online;
  bool get isHealthy => health == FCCDeviceHealth.healthy;
  bool get isPump =>
      type == FCCDeviceType.fuelPump || type == FCCDeviceType.dartPump;
  bool get isWaynePump =>
      type == FCCDeviceType.dartPump && controller?.isDartProtocol == true;

  int get nozzleCount => nozzles.length;
  int get activeNozzleCount =>
      nozzles.where((FCCNozzle n) => n.isActive).length;
  int get availableNozzleCount =>
      nozzles.where((FCCNozzle n) => n.canAuthorize).length;

  List<FCCNozzle> get activeNozzles =>
      nozzles.where((FCCNozzle n) => n.isActive).toList();
  List<FCCNozzle> get enabledNozzles =>
      nozzles.where((FCCNozzle n) => n.isEnabled).toList();

  FCCNozzle? getNozzleByNumber(int number) {
    try {
      return nozzles.firstWhere((FCCNozzle n) => n.number == number);
    } catch (e) {
      return null;
    }
  }

  FCCNozzle? getNozzleById(String nozzleId) {
    try {
      return nozzles.firstWhere((FCCNozzle n) => n.id == nozzleId);
    } catch (e) {
      return null;
    }
  }

  FCCNozzle? get selectedNozzle {
    try {
      return nozzles.firstWhere((FCCNozzle n) => n.isSelected);
    } catch (e) {
      return null;
    }
  }

  bool get hasActiveTransaction =>
      nozzles.any((FCCNozzle n) => n.isTransactionInProgress);

  bool isNozzleNumberValid(int number) {
    return number >= 1 && number <= 15 && number <= maxNozzles;
  }

  FCCDevice copyWith({
    String? id,
    String? name,
    FCCDeviceType? type,
    String? model,
    String? vendor,
    String? serialNumber,
    String? controllerId,
    FCCController? controller,
    int? deviceAddress,
    String? stationId,
    String? islandId,
    String? position,
    int? dispenserNumber, // 新增：copyWith参数
    FCCDeviceStatus? status,
    FCCDeviceHealth? health,
    DateTime? lastSeen,
    FCCDeviceCapabilities? capabilities,
    List<FCCNozzle>? nozzles,
    int? maxNozzles,
    List<String>? supportedGrades,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? version,
  }) {
    return FCCDevice(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      model: model ?? this.model,
      vendor: vendor ?? this.vendor,
      serialNumber: serialNumber ?? this.serialNumber,
      controllerId: controllerId ?? this.controllerId,
      controller: controller ?? this.controller,
      deviceAddress: deviceAddress ?? this.deviceAddress,
      stationId: stationId ?? this.stationId,
      islandId: islandId ?? this.islandId,
      position: position ?? this.position,
      dispenserNumber: dispenserNumber ?? this.dispenserNumber, // 新增：copyWith逻辑
      status: status ?? this.status,
      health: health ?? this.health,
      lastSeen: lastSeen ?? this.lastSeen,
      capabilities: capabilities ?? this.capabilities,
      nozzles: nozzles ?? this.nozzles,
      maxNozzles: maxNozzles ?? this.maxNozzles,
      supportedGrades: supportedGrades ?? this.supportedGrades,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      version: version ?? this.version,
    );
  }
}
