/// Staff Card data models for BOS API integration
library staff_card;

import '../widgets/staff_verification_dialog.dart' show NFCCardInfo;

class StaffCard {
  const StaffCard({
    required this.id,
    required this.cardNumber,
    required this.userId,
    required this.stationId,
    required this.cardType,
    required this.status,
    required this.validFrom,
    required this.validUntil,
    required this.permissions,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.fullname,
  });

  factory StaffCard.fromJson(Map<String, dynamic> json) {
    return StaffCard(
      id: json['id'] as String,
      cardNumber: json['card_number'] as String,
      userId: json['user_id'] as String,
      stationId: json['station_id'] as int,
      cardType: json['card_type'] as String,
      status: json['status'] as String,
      validFrom: DateTime.parse(json['valid_from'] as String),
      validUntil: json['valid_until'] != null 
          ? DateTime.parse(json['valid_until'] as String)
          : DateTime.now().add(const Duration(days: 365)), // Default to 1 year from now
      permissions: StaffCardPermissions.fromJson(
        json['permissions'] as Map<String, dynamic>? ?? <String, dynamic>{},
      ),
      metadata: Map<String, dynamic>.from(
        json['metadata'] as Map<String, dynamic>? ?? <String, dynamic>{},
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      fullname: json['fullname'] as String?,
    );
  }

  final String id;
  final String cardNumber;
  final String userId;
  final int stationId;
  final String cardType;
  final String status;
  final DateTime validFrom;
  final DateTime validUntil;
  final StaffCardPermissions permissions;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? fullname;

  /// Check if the card has fuel dispensing authorization
  bool get canAuthorizeFuel => permissions.fuelDispensing;

  /// Check if the card is currently valid
  bool get isValid {
    final DateTime now = DateTime.now();
    return status == 'active' && 
           now.isAfter(validFrom) && 
           now.isBefore(validUntil);
  }

  /// Get employee name from API response
  String get employeeName => 
      fullname ?? 
      metadata['original_employee_name']?.toString() ?? 
      metadata['name']?.toString() ?? 
      'Unknown Employee';

  /// Get employee department from metadata
  String get department => metadata['department']?.toString() ?? '';

  /// Get staff ID for FCC authorization (using original staff card ID)
  String get staffIdForFcc => id.toString();  // 使用原本的 staff card ID (int型转字符串)

  /// Validate card for fuel dispensing
  StaffCardValidationResult validateForFuelDispensing() {
    if (!isValid) {
      return StaffCardValidationResult.invalid(
        'Staff card is expired or inactive',
      );
    }

    // Skip permission check - accept all active cards
    // if (!canAuthorizeFuel) {
    //   return StaffCardValidationResult.invalid(
    //     'Staff card does not have fuel dispensing permission',
    //   );
    // }

    return StaffCardValidationResult.valid();
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'card_number': cardNumber,
      'user_id': userId,
      'station_id': stationId,
      'card_type': cardType,
      'status': status,
      'valid_from': validFrom.toIso8601String(),
      'valid_until': validUntil.toIso8601String(),
      'permissions': permissions.toJson(),
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'fullname': fullname,
    };
  }
}

/// Staff Card Permissions model
class StaffCardPermissions {
  const StaffCardPermissions({
    required this.fuelDispensing,
    required this.cashHandling,
  });

  factory StaffCardPermissions.fromJson(Map<String, dynamic> json) {
    return StaffCardPermissions(
      fuelDispensing: json['fuel_dispensing'] as bool? ?? false,
      cashHandling: json['cash_handling'] as bool? ?? false,
    );
  }

  final bool fuelDispensing;
  final bool cashHandling;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'fuel_dispensing': fuelDispensing,
      'cash_handling': cashHandling,
    };
  }
}

/// Staff Card Validation Result
class StaffCardValidationResult {
  const StaffCardValidationResult._({
    required this.isValid,
    this.errorMessage,
  });

  factory StaffCardValidationResult.valid() {
    return const StaffCardValidationResult._(isValid: true);
  }

  factory StaffCardValidationResult.invalid(String errorMessage) {
    return StaffCardValidationResult._(
      isValid: false,
      errorMessage: errorMessage,
    );
  }

  final bool isValid;
  final String? errorMessage;
}

/// Combined NFC and BOS verification result
class VerifiedStaffInfo {
  const VerifiedStaffInfo({
    required this.cardInfo,
    required this.staffCard,
  });

  final NFCCardInfo cardInfo;
  final StaffCard staffCard;

  /// Get staff ID for FCC authorization
  String get staffIdForFcc => staffCard.staffIdForFcc;

  /// Get employee display name
  String get displayName => staffCard.employeeName;

  /// Get employee department
  String get department => staffCard.department;

  /// Get card number from NFC
  String get nfcCardNumber => cardInfo.uuid;

  /// Check if staff can authorize fuel dispensing
  bool get canAuthorizeFuel => staffCard.canAuthorizeFuel;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'nfc_card_info': <String, dynamic>{
        'card_type': cardInfo.cardType,
        'card_category': cardInfo.cardCategory,
        'uuid': cardInfo.uuid,
        'ats': cardInfo.ats,
      },
      'staff_card': staffCard.toJson(),
    };
  }
}

// NFCCardInfo is imported from staff_verification_dialog.dart to avoid duplication 