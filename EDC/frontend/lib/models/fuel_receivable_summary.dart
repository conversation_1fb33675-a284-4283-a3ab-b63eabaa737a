class FuelReceivableSummary {
  // 优惠合计

  FuelReceivableSummary({
    required this.actualReceivedAmount,
    required this.actualRefundAmount,
    required this.couponDiscountAmount,
    required this.discountAmount,
    required this.fuelSalesAmount,
    required this.fuelSalesVolume,
    required this.pointsRedeemedAmount,
    required this.pointsUsed,
    required this.refundableAmount,
    required this.refundableDiscount,
    required this.totalDiscountAmount,
  });

  factory FuelReceivableSummary.fromJson(Map<String, dynamic> json) {
    return FuelReceivableSummary(
      actualReceivedAmount:
          double.tryParse(json['actualReceivedAmount']?.toString() ?? '0.0') ??
              0.0,
      actualRefundAmount:
          double.tryParse(json['actualRefundAmount']?.toString() ?? '0.0') ??
              0.0,
      couponDiscountAmount:
          double.tryParse(json['couponDiscountAmount']?.toString() ?? '0.0') ??
              0.0,
      discountAmount:
          double.tryParse(json['discountAmount']?.toString() ?? '0.0') ?? 0.0,
      fuelSalesAmount:
          double.tryParse(json['fuelSalesAmount']?.toString() ?? '0.0') ?? 0.0,
      fuelSalesVolume:
          double.tryParse(json['fuelSalesVolume']?.toString() ?? '0.0') ?? 0.0,
      pointsRedeemedAmount:
          double.tryParse(json['pointsRedeemedAmount']?.toString() ?? '0.0') ??
              0.0,
      pointsUsed: int.tryParse(json['pointsUsed']?.toString() ?? '0') ?? 0,
      refundableAmount:
          double.tryParse(json['refundableAmount']?.toString() ?? '0.0') ?? 0.0,
      refundableDiscount:
          double.tryParse(json['refundableDiscount']?.toString() ?? '0.0') ??
              0.0,
      totalDiscountAmount:
          double.tryParse(json['totalDiscountAmount']?.toString() ?? '0.0') ??
              0.0,
    );
  }
  final double actualReceivedAmount; // 实收金额
  final double actualRefundAmount; // 实退金额
  final double couponDiscountAmount; // 优惠券金额
  final double discountAmount; // 油品优惠数据
  final double fuelSalesAmount; // 油品销售金额
  final double fuelSalesVolume; // 油品销售数据
  final double pointsRedeemedAmount; // 积分抵现金额
  final int pointsUsed; // 使用积分
  final double refundableAmount; // 油品退款数据
  final double refundableDiscount; // 应退优惠
  final double totalDiscountAmount;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'actualReceivedAmount': actualReceivedAmount,
      'actualRefundAmount': actualRefundAmount,
      'couponDiscountAmount': couponDiscountAmount,
      'discountAmount': discountAmount,
      'fuelSalesAmount': fuelSalesAmount,
      'fuelSalesVolume': fuelSalesVolume,
      'pointsRedeemedAmount': pointsRedeemedAmount,
      'pointsUsed': pointsUsed,
      'refundableAmount': refundableAmount,
      'refundableDiscount': refundableDiscount,
      'totalDiscountAmount': totalDiscountAmount,
    };
  }
}
