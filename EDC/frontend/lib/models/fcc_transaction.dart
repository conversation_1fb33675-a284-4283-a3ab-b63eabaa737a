import 'package:equatable/equatable.dart';

/// FCC Transaction Type 枚举
enum FccTransactionType {
  fuel,
  payment,
  refund,
  voidTx;

  String get value {
    switch (this) {
      case FccTransactionType.fuel:
        return 'fuel';
      case FccTransactionType.payment:
        return 'payment';
      case FccTransactionType.refund:
        return 'refund';
      case FccTransactionType.voidTx:
        return 'void';
    }
  }

  static FccTransactionType fromValue(String value) {
    switch (value) {
      case 'fuel':
        return FccTransactionType.fuel;
      case 'payment':
        return FccTransactionType.payment;
      case 'refund':
        return FccTransactionType.refund;
      case 'void':
        return FccTransactionType.voidTx;
      default:
        return FccTransactionType.fuel;
    }
  }
}

/// FCC Transaction Status 枚举
enum FccTransactionStatus {
  pending,
  started,
  active,
  completed,
  cancelled,
  failed,
  refunded;

  String get value {
    switch (this) {
      case FccTransactionStatus.pending:
        return 'pending';
      case FccTransactionStatus.started:
        return 'started';
      case FccTransactionStatus.active:
        return 'active';
      case FccTransactionStatus.completed:
        return 'completed';
      case FccTransactionStatus.cancelled:
        return 'cancelled';
      case FccTransactionStatus.failed:
        return 'failed';
      case FccTransactionStatus.refunded:
        return 'refunded';
    }
  }

  static FccTransactionStatus fromValue(String value) {
    switch (value) {
      case 'pending':
        return FccTransactionStatus.pending;
      case 'started':
        return FccTransactionStatus.started;
      case 'active':
        return FccTransactionStatus.active;
      case 'completed':
        return FccTransactionStatus.completed;
      case 'cancelled':
        return FccTransactionStatus.cancelled;
      case 'failed':
        return FccTransactionStatus.failed;
      case 'refunded':
        return FccTransactionStatus.refunded;
      default:
        return FccTransactionStatus.pending;
    }
  }
}

/// FCC Transaction 模型 - 对应FCC service的Go Transaction结构
class FccTransaction extends Equatable {
  const FccTransaction({
    required this.id,
    required this.type,
    required this.status,
    required this.deviceId,
    required this.controllerId,
    this.nozzleId,
    this.fuelType,
    required this.unitPrice,
    required this.presetAmount,
    required this.presetVolume,
    required this.actualAmount,
    required this.actualVolume,
    required this.totalAmount,
    this.startPumpVolumeReading,
    this.endPumpVolumeReading,
    this.startPumpAmountReading,
    this.endPumpAmountReading,
    this.pumpReadingSource,
    this.pumpReadingQuality,
    required this.pumpReadingValidated,
    this.pumpReadingDiscrepancy,
    this.customerId,
    this.customerCard,
    this.vehicleId,
    this.paymentMethod,
    this.paymentRef,
    required this.paidAmount,
    required this.changeAmount,
    this.operatorId,
    this.operatorName,
    this.startedAt,
    this.completedAt,
    this.cancelledAt,
    required this.createdAt,
    required this.updatedAt,
    required this.version,
    this.extraData,
  });

  /// 从JSON创建FccTransaction
  factory FccTransaction.fromJson(Map<String, dynamic> json) {
    return FccTransaction(
      id: json['id'] as String,
      type: FccTransactionType.fromValue(json['type'] as String? ?? 'fuel'),
      status: FccTransactionStatus.fromValue(
          json['status'] as String? ?? 'pending'),
      deviceId: json['device_id'] as String,
      controllerId: json['controller_id'] as String,
      nozzleId: json['nozzle_id'] as String?,
      fuelType: json['fuel_type'] as String?,
      unitPrice: (json['unit_price'] as num?)?.toDouble() ?? 0.0,
      presetAmount: (json['preset_amount'] as num?)?.toDouble() ?? 0.0,
      presetVolume: (json['preset_volume'] as num?)?.toDouble() ?? 0.0,
      actualAmount: (json['actual_amount'] as num?)?.toDouble() ?? 0.0,
      actualVolume: (json['actual_volume'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      startPumpVolumeReading:
          (json['start_pump_volume_reading'] as num?)?.toDouble(),
      endPumpVolumeReading:
          (json['end_pump_volume_reading'] as num?)?.toDouble(),
      startPumpAmountReading:
          (json['start_pump_amount_reading'] as num?)?.toDouble(),
      endPumpAmountReading:
          (json['end_pump_amount_reading'] as num?)?.toDouble(),
      pumpReadingSource: json['pump_reading_source'] as String?,
      pumpReadingQuality: json['pump_reading_quality'] as String?,
      pumpReadingValidated: json['pump_reading_validated'] as bool? ?? false,
      pumpReadingDiscrepancy:
          (json['pump_reading_discrepancy'] as num?)?.toDouble(),
      customerId: json['customer_id'] as String?,
      customerCard: json['customer_card'] as String?,
      vehicleId: json['vehicle_id'] as String?,
      paymentMethod: json['payment_method'] as String?,
      paymentRef: json['payment_ref'] as String?,
      paidAmount: (json['paid_amount'] as num?)?.toDouble() ?? 0.0,
      changeAmount: (json['change_amount'] as num?)?.toDouble() ?? 0.0,
      operatorId: json['operator_id'] as String?,
      operatorName: json['operator_name'] as String?,
      startedAt: json['started_at'] != null
          ? DateTime.parse(json['started_at'] as String)
          : null,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'] as String)
          : null,
      cancelledAt: json['cancelled_at'] != null
          ? DateTime.parse(json['cancelled_at'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      version: json['version'] as int? ?? 1,
      extraData: json['extra_data'] != null
          ? Map<String, dynamic>.from(
              json['extra_data'] as Map<dynamic, dynamic>)
          : null,
    );
  }

  /// 基础标识信息
  final String id;
  final FccTransactionType type;
  final FccTransactionStatus status;

  /// 设备信息
  final String deviceId;
  final String controllerId;
  final String? nozzleId;

  /// 交易数据
  final String? fuelType;
  final double unitPrice;
  final double presetAmount;
  final double presetVolume;
  final double actualAmount;
  final double actualVolume;
  final double totalAmount;

  /// 泵码数据 - Wayne DART协议DC101累计计数器支持
  final double? startPumpVolumeReading;
  final double? endPumpVolumeReading;
  final double? startPumpAmountReading;
  final double? endPumpAmountReading;
  final String? pumpReadingSource;
  final String? pumpReadingQuality;
  final bool pumpReadingValidated;
  final double? pumpReadingDiscrepancy;

  /// 客户信息
  final String? customerId;
  final String? customerCard;
  final String? vehicleId;

  /// 支付信息
  final String? paymentMethod;
  final String? paymentRef;
  final double paidAmount;
  final double changeAmount;

  /// 操作员信息
  final String? operatorId;
  final String? operatorName;

  /// 时间信息
  final DateTime? startedAt;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  /// 版本和扩展数据
  final int version;
  final Map<String, dynamic>? extraData;

  @override
  List<Object?> get props => <Object?>[
        id,
        type,
        status,
        deviceId,
        controllerId,
        nozzleId,
        fuelType,
        unitPrice,
        presetAmount,
        presetVolume,
        actualAmount,
        actualVolume,
        totalAmount,
        startPumpVolumeReading,
        endPumpVolumeReading,
        startPumpAmountReading,
        endPumpAmountReading,
        pumpReadingSource,
        pumpReadingQuality,
        pumpReadingValidated,
        pumpReadingDiscrepancy,
        customerId,
        customerCard,
        vehicleId,
        paymentMethod,
        paymentRef,
        paidAmount,
        changeAmount,
        operatorId,
        operatorName,
        startedAt,
        completedAt,
        cancelledAt,
        createdAt,
        updatedAt,
        version,
        extraData,
      ];

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'type': type.value,
      'status': status.value,
      'device_id': deviceId,
      'controller_id': controllerId,
      'nozzle_id': nozzleId,
      'fuel_type': fuelType,
      'unit_price': unitPrice,
      'preset_amount': presetAmount,
      'preset_volume': presetVolume,
      'actual_amount': actualAmount,
      'actual_volume': actualVolume,
      'total_amount': totalAmount,
      'start_pump_volume_reading': startPumpVolumeReading,
      'end_pump_volume_reading': endPumpVolumeReading,
      'start_pump_amount_reading': startPumpAmountReading,
      'end_pump_amount_reading': endPumpAmountReading,
      'pump_reading_source': pumpReadingSource,
      'pump_reading_quality': pumpReadingQuality,
      'pump_reading_validated': pumpReadingValidated,
      'pump_reading_discrepancy': pumpReadingDiscrepancy,
      'customer_id': customerId,
      'customer_card': customerCard,
      'vehicle_id': vehicleId,
      'payment_method': paymentMethod,
      'payment_ref': paymentRef,
      'paid_amount': paidAmount,
      'change_amount': changeAmount,
      'operator_id': operatorId,
      'operator_name': operatorName,
      'started_at': startedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'cancelled_at': cancelledAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'version': version,
      'extra_data': extraData,
    };
  }
}

/// FCC Transaction响应模型
class FccTransactionResponse {
  const FccTransactionResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPage,
  });

  factory FccTransactionResponse.fromJson(Map<String, dynamic> json) {
    return FccTransactionResponse(
      items: (json['items'] as List? ?? <dynamic>[])
          .map((item) => FccTransaction.fromJson(item as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int? ?? 0,
      page: json['page'] as int? ?? 1,
      pageSize: json['page_size'] as int? ?? 10,
      totalPage: json['total_page'] as int? ?? 0,
    );
  }
  final List<FccTransaction> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPage;
}

/// FCC Transaction查询参数
class FccTransactionQueryParams {
  const FccTransactionQueryParams({
    this.deviceId,
    this.status,
    this.type,
    this.nozzleId,
    this.customerId,
    this.dateFrom,
    this.dateTo,
    this.page = 1,
    this.limit = 10,
    this.sortBy = 'created_at',
    this.sortDir = 'desc',
  });
  final String? deviceId;
  final String? status;
  final String? type;
  final String? nozzleId;
  final String? customerId;
  final String? dateFrom;
  final String? dateTo;
  final int page;
  final int limit;
  final String sortBy;
  final String sortDir;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> params = <String, dynamic>{
      'page': page,
      'limit': limit,
      'sort_by': sortBy,
      'sort_dir': sortDir,
    };

    if (deviceId != null) params['device_id'] = deviceId;
    if (status != null) params['status'] = status;
    if (type != null) params['type'] = type;
    if (nozzleId != null) params['nozzle_id'] = nozzleId;
    if (customerId != null) params['customer_id'] = customerId;
    if (dateFrom != null) params['date_from'] = dateFrom;
    if (dateTo != null) params['date_to'] = dateTo;

    return params;
  }
}
