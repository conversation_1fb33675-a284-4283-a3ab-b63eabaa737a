import 'order.dart';
import 'fuel_transaction.dart';
import 'package:flutter/foundation.dart'; // Added for debugPrint

/// 促销服务请求模型
class PromotionRequest {
  PromotionRequest({
    this.orderId,
    required this.userId,
    required this.orderAmount,
    required this.orderTime,
    this.vehicleType,
    required this.items,
  });

  /// 从订单创建促销请求
  factory PromotionRequest.fromOrder(Order order) {
    // 将订单项转换为API所需的格式
    final List<OrderItem> items = order.items.map((Map<String, dynamic> item) {
      final String itemId = item['product_id'] as String? ?? '';
      final String name = item['product_name'] as String? ?? '';
      final String category = item['category'] as String? ?? 'default';
      final double price = (item['unit_price'] as num?)?.toDouble() ?? 0.0;
      final int quantity = (item['quantity'] as num?)?.toInt() ?? 0;

      return OrderItem(
        itemId: itemId,
        name: name,
        category: category,
        price: price,
        quantity: quantity,
      );
    }).toList();

    return PromotionRequest(
      orderId: order.orderId,
      userId: order.customerId?.toString() ?? 'anonymous',
      orderAmount: order.amount,
      orderTime: DateTime.now().toUtc(),
      vehicleType: 'CAR', // 默认车辆类型
      items: items,
    );
  }

  /// 从燃油交易创建促销请求
  factory PromotionRequest.fromFuelTransaction(FuelTransaction transaction) {
    // 尝试从交易metadata中获取车辆类型，如果没有则使用默认值
    String vehicleType = 'CAR';
    if (transaction.metadata['vehicle_type'] != null) {
      vehicleType = transaction.metadata['vehicle_type'].toString().toUpperCase();
      debugPrint('🚗 从燃油交易metadata获取车辆类型: $vehicleType');
    } else {
      debugPrint('⚠️ 燃油交易metadata中未找到vehicle_type，使用默认值: $vehicleType');
      debugPrint('   可用的metadata字段: ${transaction.metadata.keys.toList()}');
    }

    // 创建单个燃油商品项
    final OrderItem fuelItem = OrderItem(
      itemId: transaction.fuelType, // 使用 fuelType（油品 ID）而不是 fuelGrade（油品名称）
      name: transaction.fuelGrade,
      category: transaction.fuelType, // 使用 fuelType（油品 ID）而不是 fuelGrade（油品名称）
      categoryIds: [transaction.fuelType, 'fuel', vehicleType], // 使用 fuelType（油品 ID）
      price: transaction.unitPrice.toInt(), // 印尼盾单价，不需要转换
      quantity: transaction.volume.toInt(), // 升数，不需要转换
      attributes: {
        'fuel_volume': transaction.volume,
      },
    );

    final promotionRequest = PromotionRequest(
      orderId: 'FUEL-${transaction.transactionNumber}',
      userId: transaction.memberId?.toString() ?? 'anonymous',
      orderAmount: transaction.amount.toInt(), // 印尼盾总金额，不需要转换
      orderTime: transaction.createdAt,
      vehicleType: vehicleType, // 使用从交易metadata获取的车辆类型
      items: <OrderItem>[fuelItem],
    );

    debugPrint('🎁 创建燃油交易促销请求:');
    debugPrint('   订单ID: ${promotionRequest.orderId}');
    debugPrint('   车辆类型: ${promotionRequest.vehicleType}');
    debugPrint('   用户ID: ${promotionRequest.userId}');
    debugPrint('   订单金额: ${promotionRequest.orderAmount}');

    return promotionRequest;
  }

  final String? orderId;
  final String userId;
  final dynamic orderAmount; // 支持double和int
  final DateTime orderTime;
  final String? vehicleType;
  final List<OrderItem> items;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      if (orderId != null) 'orderId': orderId,
      'userId': userId,
      'orderAmount': orderAmount,
      'orderTime': orderTime.toUtc().toIso8601String(),
      if (vehicleType != null) 'vehicleType': vehicleType,
      'items': items.map((OrderItem item) => item.toJson()).toList(),
    };
  }
}

/// 订单项模型（用于促销请求）
class OrderItem {
  OrderItem({
    required this.itemId,
    required this.name,
    required this.category,
    required this.price,
    required this.quantity,
    this.categoryIds,
    this.attributes,
  });
  
  final String itemId;
  final String name;
  final String category;
  final dynamic price; // 支持double和int
  final int quantity;
  final List<String>? categoryIds;
  final Map<String, dynamic>? attributes;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'itemId': itemId,
      'name': name,
      'category': category,
      'price': price,
      'quantity': quantity,
      if (categoryIds != null) 'categoryIds': categoryIds,
      if (attributes != null) 'attributes': attributes,
    };
  }
}
