// EDC/frontend/lib/models/auth_models.dart

/// 认证相关数据模型
/// 基于BOS认证API接口文档定义

import 'dart:convert';
import 'package:equatable/equatable.dart';

/// 登录请求模型
class LoginRequest extends Equatable {
  const LoginRequest({
    required this.username,
    required this.password,
    required this.system,
    this.authType = 'local',
    this.rememberMe = false,
    this.captcha,
    this.captchaId,
  });

  /// 用户名或邮箱地址
  final String username;
  
  /// 登录密码
  final String password;
  
  /// 目标系统: BOS|EDC
  final String system;
  
  /// 认证类型: local|ldap|ad
  final String authType;
  
  /// 是否记住登录状态
  final bool rememberMe;
  
  /// 验证码
  final String? captcha;
  
  /// 验证码ID
  final String? captchaId;

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'username': username,
      'password': password,
      'system': system,
      'authType': authType,
      'rememberMe': rememberMe,
    };
    
    if (captcha != null) {
      data['captcha'] = captcha;
    }
    
    if (captchaId != null) {
      data['captchaId'] = captchaId;
    }
    
    return data;
  }

  @override
  List<Object?> get props => [
        username,
        password,
        system,
        authType,
        rememberMe,
        captcha,
        captchaId,
      ];
}

/// 站点信息模型
class SiteInfo extends Equatable {
  const SiteInfo({
    required this.siteId,
    required this.siteName,
    required this.role,
    required this.isDefault,
  });

  final String siteId; // 根据实际API，应该是String类型
  final String siteName;
  final String role;
  final bool isDefault;

  factory SiteInfo.fromJson(Map<String, dynamic> json) {
    return SiteInfo(
      siteId: json['siteId'] as String,
      siteName: json['siteName'] as String,
      role: json['role'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'siteId': siteId,
      'siteName': siteName,
      'role': role,
      'isDefault': isDefault,
    };
  }

  @override
  List<Object?> get props => [siteId, siteName, role, isDefault];
}

/// 联系信息模型
class ContactInfo extends Equatable {
  const ContactInfo({
    required this.phone,
    required this.email,
  });

  final String phone;
  final String email;

  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      phone: json['phone'] as String,
      email: json['email'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'email': email,
    };
  }

  @override
  List<Object?> get props => [phone, email];
}

/// 地址信息模型
class AddressInfo extends Equatable {
  const AddressInfo({
    required this.street,
    required this.city,
    required this.district,
    required this.province,
    required this.country,
    required this.postalCode,
    this.building = '',
    this.floor = '',
    this.unit = '',
  });

  final String street;
  final String city;
  final String district;
  final String province;
  final String country;
  final String postalCode;
  final String building;
  final String floor;
  final String unit;

  factory AddressInfo.fromJson(Map<String, dynamic> json) {
    return AddressInfo(
      street: json['street'] as String? ?? '',
      city: json['city'] as String? ?? '',
      district: json['district'] as String? ?? '',
      province: json['province'] as String? ?? '',
      country: json['country'] as String? ?? '',
      postalCode: json['postal_code'] as String? ?? '',
      building: json['building'] as String? ?? '',
      floor: json['floor'] as String? ?? '',
      unit: json['unit'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city': city,
      'district': district,
      'province': province,
      'country': country,
      'postal_code': postalCode,
      'building': building,
      'floor': floor,
      'unit': unit,
    };
  }

  @override
  List<Object?> get props => [
        street,
        city,
        district,
        province,
        country,
        postalCode,
        building,
        floor,
        unit,
      ];
}

/// 加油站详细信息模型
class StationDetail extends Equatable {
  const StationDetail({
    required this.id,
    required this.siteCode,
    required this.siteName,
    required this.businessStatus,
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  final int id;
  final String siteCode;
  final String siteName;
  final String businessStatus;
  final AddressInfo address;
  final double latitude;
  final double longitude;

  factory StationDetail.fromJson(Map<String, dynamic> json) {
    return StationDetail(
      id: json['id'] as int,
      siteCode: json['site_code'] as String,
      siteName: json['site_name'] as String,
      businessStatus: json['business_status'] as String,
      address: AddressInfo.fromJson(json['address'] as Map<String, dynamic>? ?? {}),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'site_code': siteCode,
      'site_name': siteName,
      'business_status': businessStatus,
      'address': address.toJson(),
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  List<Object?> get props => [
        id,
        siteCode,
        siteName,
        businessStatus,
        address,
        latitude,
        longitude,
      ];
}

/// 用户加油站信息模型（根据实际API响应结构）
class UserStationInfo extends Equatable {
  const UserStationInfo({
    required this.stationId,
    required this.station,
    this.hasManagePermission = false,
    this.isDefault = false,
    this.roleType,
  });

  final int stationId;
  final StationDetail station;
  final bool hasManagePermission;
  final bool isDefault;
  final String? roleType;

  factory UserStationInfo.fromJson(Map<String, dynamic> json) {
    // 处理新的API响应格式，其中station信息嵌套在station字段中
    if (json['station'] != null) {
      final Map<String, dynamic> stationData = json['station'] as Map<String, dynamic>;
      return UserStationInfo(
        stationId: json['stationId'] as int,
        station: StationDetail.fromJson(stationData),
        hasManagePermission: json['hasManagePermission'] as bool? ?? false,
        isDefault: json['isDefault'] as bool? ?? false,
        roleType: json['roleType'] as String?,
      );
    } else {
      // 向后兼容：处理旧的API响应格式，创建一个简单的StationDetail
      final stationDetail = StationDetail(
        id: json['id'] as int? ?? 0,
        siteCode: json['siteCode'] as String? ?? json['site_code'] as String? ?? '',
        siteName: json['siteName'] as String? ?? json['site_name'] as String? ?? '',
        businessStatus: json['status'] as String? ?? 'ACTIVE',
        address: AddressInfo.fromJson(json['address'] as Map<String, dynamic>? ?? {}),
        latitude: 0.0,
        longitude: 0.0,
      );

      return UserStationInfo(
        stationId: json['stationId'] as int? ?? json['id'] as int? ?? 0,
        station: stationDetail,
        hasManagePermission: json['hasManagePermission'] as bool? ?? false,
        isDefault: json['isDefault'] as bool? ?? false,
        roleType: json['roleType'] as String?,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'stationId': stationId,
      'station': station.toJson(),
      'hasManagePermission': hasManagePermission,
      'isDefault': isDefault,
      'roleType': roleType,
    };
  }

  // 为了向后兼容，添加一些便捷的getter
  int get id => station.id;
  String get siteCode => station.siteCode;
  String get siteName => station.siteName;
  AddressInfo get address => station.address;
  String get status => station.businessStatus;

  @override
  List<Object?> get props => [
        stationId,
        station,
        hasManagePermission,
        isDefault,
        roleType,
      ];
}

/// 系统访问权限模型
class SystemAccess extends Equatable {
  const SystemAccess({
    required this.system,
    required this.accessLevel,
    required this.scopeType,
    required this.scopeIds,
    required this.stationIds,
    required this.stationCount,
    required this.permissions,
  });

  final String system;        // HOS|BOS|EDC
  final String accessLevel;   // admin|manager|supervisor|operator
  final String scopeType;     // global|station|operation
  final List<int> scopeIds;   // 权限范围ID列表
  final List<int> stationIds; // 可访问站点ID列表（向后兼容）
  final int stationCount;     // 可访问站点数量
  final List<String> permissions; // 用户权限列表

  factory SystemAccess.fromJson(Map<String, dynamic> json) {
    return SystemAccess(
      system: json['system'] as String,
      accessLevel: json['accessLevel'] as String,
      scopeType: json['scopeType'] as String? ?? 'operation', // 默认值，因为API响应中可能没有
      scopeIds: json['scopeIds'] != null
          ? (json['scopeIds'] as List<dynamic>).map((e) => e as int).toList()
          : (json['stationIds'] as List<dynamic>? ?? []).map((e) => e as int).toList(), // 使用stationIds作为备用
      stationIds: (json['stationIds'] as List<dynamic>? ?? [])
          .map((e) => e as int)
          .toList(),
      stationCount: json['stationCount'] as int? ?? 0,
      permissions: json['permissions'] != null
          ? (json['permissions'] as List<dynamic>).map((e) => e as String).toList()
          : [], // 默认为空数组
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'system': system,
      'accessLevel': accessLevel,
      'scopeType': scopeType,
      'scopeIds': scopeIds,
      'stationIds': stationIds,
      'stationCount': stationCount,
      'permissions': permissions,
    };
  }

  @override
  List<Object?> get props => [
        system,
        accessLevel,
        scopeType,
        scopeIds,
        stationIds,
        stationCount,
        permissions,
      ];
}

/// 用户信息模型
class AuthUser extends Equatable {
  const AuthUser({
    required this.id,
    required this.username,
    required this.email,
    required this.fullName,
    required this.phone,
    required this.status,
    required this.roles,
    required this.permissions,
    required this.lastLoginAt,
    required this.language,
    required this.sites,
    required this.stations,
  });

  final String id;
  final String username;
  final String email;
  final String fullName;
  final String phone;
  final String status;
  final List<String> roles;
  final List<String> permissions;
  final DateTime lastLoginAt;
  final String language;
  final List<SiteInfo> sites;
  final List<UserStationInfo> stations;

  factory AuthUser.fromJson(Map<String, dynamic> json) {
    return AuthUser(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      fullName: json['fullName'] as String,
      phone: json['phone'] as String,
      status: json['status'] as String,
      roles: (json['roles'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] as String),
      language: json['language'] as String,
      sites: (json['sites'] as List<dynamic>)
          .map((e) => SiteInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      stations: (json['stations'] as List<dynamic>)
          .map((e) => UserStationInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'fullName': fullName,
      'phone': phone,
      'status': status,
      'roles': roles,
      'permissions': permissions,
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'language': language,
      'sites': sites.map((e) => e.toJson()).toList(),
      'stations': stations.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        fullName,
        phone,
        status,
        roles,
        permissions,
        lastLoginAt,
        language,
        sites,
        stations,
      ];
}

/// 登录响应数据模型
class LoginResponseData extends Equatable {
  const LoginResponseData({
    required this.accessToken,
    required this.refreshToken,
    this.tokenType = 'Bearer',
    required this.expiresIn,
    required this.user,
    required this.systemAccess,
  });

  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final AuthUser user;
  final SystemAccess systemAccess;

  factory LoginResponseData.fromJson(Map<String, dynamic> json) {
    // 从实际API响应解析基本字段
    final String accessToken = json['accessToken'] as String;
    final String refreshToken = json['refreshToken'] as String;
    final String tokenType = json['tokenType'] as String? ?? 'Bearer';
    final int expiresIn = json['expiresIn'] as int;

    // 优先使用API响应中的用户信息（新接口格式）
    AuthUser user;
    SystemAccess systemAccess;

    if (json['user'] != null) {
      // 新接口：直接从API响应中获取用户信息
      try {
        final Map<String, dynamic> userJson = json['user'] as Map<String, dynamic>;
        print('🔍 [LoginResponseData] API响应user字段存在，开始解析...');
        print('🔍 [LoginResponseData] user.stations数据: ${userJson['stations']}');
        user = AuthUser.fromJson(userJson);
        print('🔍 [LoginResponseData] 解析后用户: ${user.fullName}, stations数量: ${user.stations.length}');
        print('✅ 使用API响应中的用户信息 - fullName: ${user.fullName}, stations: ${user.stations.length}');
      } catch (e) {
        print('❌ [LoginResponseData] 解析API响应用户信息失败，回退到JWT解析: $e');
        print('🔍 [LoginResponseData] 原始user数据: ${json['user']}');
        // 回退到JWT token解析
        final Map<String, dynamic> tokenPayload = _parseJwtPayload(accessToken);
        user = _createUserFromToken(tokenPayload);
      }
    } else {
      // 向后兼容：从JWT token解析用户信息
      print('⚠️ API响应中没有user字段，使用JWT token解析');
      final Map<String, dynamic> tokenPayload = _parseJwtPayload(accessToken);
      user = _createUserFromToken(tokenPayload);
    }

    if (json['systemAccess'] != null) {
      // 新接口：直接从API响应中获取系统访问权限
      try {
        systemAccess = SystemAccess.fromJson(json['systemAccess'] as Map<String, dynamic>);
        print('✅ 使用API响应中的系统访问权限 - system: ${systemAccess.system}, stationCount: ${systemAccess.stationCount}');
      } catch (e) {
        print('❌ 解析API响应系统访问权限失败，回退到JWT解析: $e');
        // 回退到JWT token解析
        final Map<String, dynamic> tokenPayload = _parseJwtPayload(accessToken);
        systemAccess = _createSystemAccessFromToken(tokenPayload);
      }
    } else {
      // 向后兼容：从JWT token解析系统访问权限
      print('⚠️ API响应中没有systemAccess字段，使用JWT token解析');
      final Map<String, dynamic> tokenPayload = _parseJwtPayload(accessToken);
      systemAccess = _createSystemAccessFromToken(tokenPayload);
    }

    return LoginResponseData(
      accessToken: accessToken,
      refreshToken: refreshToken,
      tokenType: tokenType,
      expiresIn: expiresIn,
      user: user,
      systemAccess: systemAccess,
    );
  }

  /// 解析JWT token的payload部分
  static Map<String, dynamic> _parseJwtPayload(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        throw Exception('Invalid JWT token format');
      }

      // JWT payload是base64编码的，需要解码
      String payload = parts[1];

      // 添加padding如果需要
      switch (payload.length % 4) {
        case 0:
          break;
        case 2:
          payload += '==';
          break;
        case 3:
          payload += '=';
          break;
        default:
          throw Exception('Invalid base64 string');
      }

      // 解码base64
      final List<int> bytes = base64Url.decode(payload);
      final String decoded = utf8.decode(bytes);

      // 解析JSON
      return json.decode(decoded) as Map<String, dynamic>;
    } catch (e) {
      // 如果解析失败，返回默认值
      print('JWT解析失败: $e');
      return {};
    }
  }

  /// 从JWT token创建用户对象
  static AuthUser _createUserFromToken(Map<String, dynamic> tokenPayload) {
    return AuthUser(
      id: tokenPayload['user_id'] as String? ?? 'unknown',
      username: tokenPayload['username'] as String? ?? 'admin',
      email: tokenPayload['email'] as String? ?? '<EMAIL>',
      fullName: tokenPayload['username'] as String? ?? 'Administrator',
      phone: '',
      status: 'active',
      roles: List<String>.from(tokenPayload['roles'] as List? ?? ['Super Admin']),
      permissions: List<String>.from(tokenPayload['permissions'] as List? ?? []),
      lastLoginAt: DateTime.now(),
      language: 'en',
      sites: [],
      stations: [],
    );
  }

  /// 从JWT token创建系统访问对象
  static SystemAccess _createSystemAccessFromToken(Map<String, dynamic> tokenPayload) {
    return SystemAccess(
      system: tokenPayload['system'] as String? ?? 'EDC',
      accessLevel: tokenPayload['access_level'] as String? ?? 'manager',
      scopeType: tokenPayload['scope_type'] as String? ?? 'operation',
      scopeIds: List<int>.from(tokenPayload['scope_ids'] as List? ?? [1]),
      stationIds: List<int>.from(tokenPayload['station_ids'] as List? ?? [1]),
      stationCount: (tokenPayload['station_ids'] as List?)?.length ?? 1,
      permissions: List<String>.from(tokenPayload['permissions'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'tokenType': tokenType,
      'expiresIn': expiresIn,
      'user': user.toJson(),
      'systemAccess': systemAccess.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        tokenType,
        expiresIn,
        user,
        systemAccess,
      ];
}

/// 完整的登录响应模型
class LoginResponse extends Equatable {
  const LoginResponse({
    required this.code,
    required this.message,
    required this.data,
    required this.timestamp,
    required this.requestId,
  });

  final int code;
  final String message;
  final LoginResponseData data;
  final String timestamp;
  final String requestId;

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      code: json['code'] as int,
      message: json['message'] as String,
      data: LoginResponseData.fromJson(json['data'] as Map<String, dynamic>),
      timestamp: json['timestamp'] as String,
      requestId: json['requestId'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data.toJson(),
      'timestamp': timestamp,
      'requestId': requestId,
    };
  }

  @override
  List<Object?> get props => [code, message, data, timestamp, requestId];
}

/// 认证错误响应模型
class AuthErrorResponse extends Equatable {
  const AuthErrorResponse({
    required this.code,
    required this.message,
    this.errors,
    required this.timestamp,
    required this.requestId,
  });

  final int code;
  final String message;
  final List<AuthFieldError>? errors;
  final String timestamp;
  final String requestId;

  factory AuthErrorResponse.fromJson(Map<String, dynamic> json) {
    return AuthErrorResponse(
      code: json['code'] as int,
      message: json['message'] as String,
      errors: json['errors'] != null
          ? (json['errors'] as List<dynamic>)
              .map((e) => AuthFieldError.fromJson(e as Map<String, dynamic>))
              .toList()
          : null,
      timestamp: json['timestamp'] as String,
      requestId: json['requestId'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'errors': errors?.map((e) => e.toJson()).toList(),
      'timestamp': timestamp,
      'requestId': requestId,
    };
  }

  @override
  List<Object?> get props => [code, message, errors, timestamp, requestId];
}

/// 认证字段错误模型
class AuthFieldError extends Equatable {
  const AuthFieldError({
    required this.field,
    required this.message,
  });

  final String field;
  final String message;

  factory AuthFieldError.fromJson(Map<String, dynamic> json) {
    return AuthFieldError(
      field: json['field'] as String,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'field': field,
      'message': message,
    };
  }

  @override
  List<Object?> get props => [field, message];
}
