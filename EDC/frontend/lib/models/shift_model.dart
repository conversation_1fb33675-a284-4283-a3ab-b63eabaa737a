/// 班次数据模型
class ShiftModel {
  const ShiftModel({
    required this.id,
    required this.shiftNumber,
    required this.stationId,
    required this.startTime,
    this.endTime,
    this.status,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.metadata,
  });

  /// 从JSON创建实例
  factory ShiftModel.fromJson(Map<String, dynamic> json) {
    return ShiftModel(
      id: json['id'] as String,
      shiftNumber: json['shift_number'] as String,
      stationId: json['station_id'] as int,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'] as String)
          : null,
      status: json['status'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      deletedAt: json['deleted_at'] != null
          ? DateTime.parse(json['deleted_at'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
  final String id;
  final String shiftNumber;
  final int stationId;
  final DateTime startTime;
  final DateTime? endTime;
  final String? status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final Map<String, dynamic>? metadata;

  /// 获取班次状态
  String get statusValue {
    // 优先使用显式的status字段
    if (status != null) return status!;

    // 回退到基于时间的推断
    if (deletedAt != null) return 'deleted';
    if (endTime != null) return 'closed';
    return 'active';
  }

  /// 是否为活跃班次
  bool get isActive => statusValue == 'active';

  /// 是否已结班
  bool get isClosed => statusValue == 'closed';

  /// 获取班次持续时间
  Duration get duration {
    final DateTime endDateTime = endTime ?? DateTime.now();
    return endDateTime.difference(startTime);
  }

  /// 格式化的持续时间
  String get formattedDuration {
    final Duration d = duration;
    final int hours = d.inHours;
    final int minutes = d.inMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  /// 格式化的开始时间
  String get formattedStartTime {
    return '${startTime.day.toString().padLeft(2, '0')}/${startTime.month.toString().padLeft(2, '0')}/${startTime.year} ${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化的结束时间
  String? get formattedEndTime {
    if (endTime == null) return null;
    return '${endTime!.day.toString().padLeft(2, '0')}/${endTime!.month.toString().padLeft(2, '0')}/${endTime!.year} ${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'shift_number': shiftNumber,
      'station_id': stationId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// 复制实例并修改部分属性
  ShiftModel copyWith({
    String? id,
    String? shiftNumber,
    int? stationId,
    DateTime? startTime,
    DateTime? endTime,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ShiftModel(
      id: id ?? this.id,
      shiftNumber: shiftNumber ?? this.shiftNumber,
      stationId: stationId ?? this.stationId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'ShiftModel(id: $id, shiftNumber: $shiftNumber, stationId: $stationId, status: $statusValue)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShiftModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 开班请求模型
class StartShiftRequest {
  const StartShiftRequest({
    required this.stationId,
    required this.operatorId,
    required this.startingCash,
    this.notes,
    this.metadata,
  });

  /// 从JSON创建实例
  factory StartShiftRequest.fromJson(Map<String, dynamic> json) {
    return StartShiftRequest(
      stationId: json['station_id'] as int,
      operatorId: json['operator_id'] as String,
      startingCash: (json['starting_cash'] as num).toDouble(),
      notes: json['notes'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
  final int stationId;
  final String operatorId;
  final double startingCash;
  final String? notes;
  final Map<String, dynamic>? metadata;

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'station_id': stationId,
      'operator_id': operatorId,
      'starting_cash': startingCash,
      if (notes != null) 'notes': notes,
      if (metadata != null) 'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'StartShiftRequest(stationId: $stationId, operatorId: $operatorId, startingCash: $startingCash)';
  }
}

/// 班次列表请求参数
class ShiftListRequest {
  const ShiftListRequest({
    this.stationId,
    this.status,
    this.shiftNumber,
    this.dateFrom,
    this.dateTo,
    this.includeDeleted,
    this.page = 1,
    this.limit = 10,
    this.sortBy = 'created_at',
    this.sortDir = 'desc',
  });
  final int? stationId;
  final String? status;
  final String? shiftNumber;
  final DateTime? dateFrom;
  final DateTime? dateTo;
  final bool? includeDeleted;
  final int page;
  final int limit;
  final String sortBy;
  final String sortDir;

  /// 转换为查询参数
  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = <String, dynamic>{
      'page': page,
      'limit': limit,
      'sort_by': sortBy,
      'sort_dir': sortDir,
    };

    if (stationId != null) params['station_id'] = stationId;
    if (status != null) params['status'] = status;
    if (shiftNumber != null) params['shift_number'] = shiftNumber;
    if (dateFrom != null)
      params['date_from'] = dateFrom!.toIso8601String().split('T')[0];
    if (dateTo != null)
      params['date_to'] = dateTo!.toIso8601String().split('T')[0];
    if (includeDeleted != null) params['include_deleted'] = includeDeleted;

    return params;
  }
}

/// 班次列表响应模型
class ShiftListResponse {
  const ShiftListResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  /// 从JSON创建实例
  factory ShiftListResponse.fromJson(Map<String, dynamic> json) {
    return ShiftListResponse(
      items: (json['items'] as List<dynamic>)
          .map((item) => ShiftModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int,
      page: json['page'] as int,
      pageSize: json['page_size'] as int,
      totalPages: json['total_page'] as int,
    );
  }
  final List<ShiftModel> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;
}
