/// Station data model for BOS API integration
class Station {
  const Station({
    required this.id,
    required this.siteCode,
    required this.siteName,
    this.address,
    this.city,
    this.province,
    this.postalCode,
    this.phone,
    this.email,
    this.manager,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory Station.fromJson(Map<String, dynamic> json) {
    return Station(
      id: json['id'] as int,
      siteCode: json['site_code'] as String,
      siteName: json['site_name'] as String,
      address: json['address'] as String?,
      city: json['city'] as String?,
      province: json['province'] as String?,
      postalCode: json['postal_code'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      manager: json['manager'] as String?,
      status: json['status'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  final int id;
  final String siteCode;
  final String siteName;
  final String? address;
  final String? city;
  final String? province;
  final String? postalCode;
  final String? phone;
  final String? email;
  final String? manager;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'site_code': siteCode,
      'site_name': siteName,
      'address': address,
      'city': city,
      'province': province,
      'postal_code': postalCode,
      'phone': phone,
      'email': email,
      'manager': manager,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'Station(id: $id, siteCode: $siteCode, siteName: $siteName)';
  }
} 