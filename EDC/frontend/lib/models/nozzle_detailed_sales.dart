class NozzleDetailedSales {
  // 单价

  NozzleDetailedSales({
    required this.actualReceivedAmount,
    required this.discountAmount,
    required this.fuelGrade,
    required this.fuelType,
    required this.nozzleId,
    required this.siteId,
    required this.siteName,
    required this.totalAmount,
    required this.totalVolume,
    required this.transactionCount,
    required this.unitPrice,
  });

  factory NozzleDetailedSales.fromJson(Map<String, dynamic> json) {
    return NozzleDetailedSales(
      actualReceivedAmount:
          double.tryParse(json['actualReceivedAmount']?.toString() ?? '0.0') ??
              0.0,
      discountAmount:
          double.tryParse(json['discountAmount']?.toString() ?? '0.0') ?? 0.0,
      fuelGrade: json['fuelGrade']?.toString() ?? '',
      fuelType: json['fuelType']?.toString() ?? '',
      nozzleId: json['nozzleId']?.toString() ?? '',
      siteId: int.tryParse(json['siteId']?.toString() ?? '0') ?? 0,
      siteName: json['siteName']?.toString() ?? '',
      totalAmount:
          double.tryParse(json['totalAmount']?.toString() ?? '0.0') ?? 0.0,
      totalVolume:
          double.tryParse(json['totalVolume']?.toString() ?? '0.0') ?? 0.0,
      transactionCount:
          int.tryParse(json['transactionCount']?.toString() ?? '0') ?? 0,
      unitPrice: double.tryParse(json['unitPrice']?.toString() ?? '0.0') ?? 0.0,
    );
  }
  final double actualReceivedAmount; // 实收金额
  final double discountAmount; // 优惠金额
  final String fuelGrade; // 燃油标号
  final String fuelType; // 燃油类型
  final String nozzleId; // 油枪ID
  final int siteId; // 站点ID
  final String siteName; // 站点名称
  final double totalAmount; // 总销售金额
  final double totalVolume; // 总销售体积
  final int transactionCount; // 交易次数
  final double unitPrice;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'actualReceivedAmount': actualReceivedAmount,
      'discountAmount': discountAmount,
      'fuelGrade': fuelGrade,
      'fuelType': fuelType,
      'nozzleId': nozzleId,
      'siteId': siteId,
      'siteName': siteName,
      'totalAmount': totalAmount,
      'totalVolume': totalVolume,
      'transactionCount': transactionCount,
      'unitPrice': unitPrice,
    };
  }
}

class NozzleDetailedSalesResponse {
  NozzleDetailedSalesResponse({
    required this.data,
    required this.total,
    required this.limit,
    required this.offset,
  });

  factory NozzleDetailedSalesResponse.fromJson(Map<String, dynamic> json) {
    return NozzleDetailedSalesResponse(
      data: (json['data'] as List<dynamic>?)
              ?.map((item) =>
                  NozzleDetailedSales.fromJson(item as Map<String, dynamic>))
              .toList() ??
          <NozzleDetailedSales>[],
      total: int.tryParse(json['total']?.toString() ?? '0') ?? 0,
      limit: int.tryParse(json['limit']?.toString() ?? '10') ?? 10,
      offset: int.tryParse(json['offset']?.toString() ?? '0') ?? 0,
    );
  }
  final List<NozzleDetailedSales> data;
  final int total;
  final int limit;
  final int offset;
}
