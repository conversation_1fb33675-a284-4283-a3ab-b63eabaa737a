/// 班次员工数据模型
/// 
/// 用于展示班次中每个员工的详细加油情况，包括油品销售、支付方式等统计数据
/// 对应API接口：GET /api/v1/shifts/{id}/attendants

/// 班次员工响应模型
class ShiftAttendantResponse {
  const ShiftAttendantResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.meta,
  });

  factory ShiftAttendantResponse.fromJson(Map<String, dynamic> json) {
    return ShiftAttendantResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: ShiftAttendantData.fromJson(json['data'] as Map<String, dynamic>),
      meta: ShiftAttendantMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );
  }

  final bool success;
  final String message;
  final ShiftAttendantData data;
  final ShiftAttendantMeta meta;

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.toJson(),
      'meta': meta.toJson(),
    };
  }
}

/// 班次员工数据
class ShiftAttendantData {
  const ShiftAttendantData({
    required this.shiftInfo,
    required this.attendants,
    required this.shiftSummary,
  });

  factory ShiftAttendantData.fromJson(Map<String, dynamic> json) {
    return ShiftAttendantData(
      shiftInfo: ShiftInfo.fromJson(json['shift_info'] as Map<String, dynamic>),
      attendants: (json['attendants'] as List<dynamic>)
          .map((dynamic item) => Attendant.fromJson(item as Map<String, dynamic>))
          .toList(),
      shiftSummary: ShiftSummary.fromJson(json['shift_summary'] as Map<String, dynamic>),
    );
  }

  final ShiftInfo shiftInfo;
  final List<Attendant> attendants;
  final ShiftSummary shiftSummary;

  Map<String, dynamic> toJson() {
    return {
      'shift_info': shiftInfo.toJson(),
      'attendants': attendants.map((attendant) => attendant.toJson()).toList(),
      'shift_summary': shiftSummary.toJson(),
    };
  }
}

/// 班次基本信息
class ShiftInfo {
  const ShiftInfo({
    required this.id,
    required this.shiftNumber,
    required this.stationId,
    required this.stationName,
    required this.startTime,
    this.endTime,
    required this.status,
  });

  factory ShiftInfo.fromJson(Map<String, dynamic> json) {
    return ShiftInfo(
      id: json['id'] as String,
      shiftNumber: json['shift_number'] as String,
      stationId: json['station_id'] as int,
      stationName: json['station_name'] as String,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] != null 
          ? DateTime.parse(json['end_time'] as String) 
          : null,
      status: json['status'] as String,
    );
  }

  final String id;
  final String shiftNumber;
  final int stationId;
  final String stationName;
  final DateTime startTime;
  final DateTime? endTime;
  final String status;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'shift_number': shiftNumber,
      'station_id': stationId,
      'station_name': stationName,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'status': status,
    };
  }
}

/// 员工信息
class Attendant {
  const Attendant({
    required this.attendantInfo,
    required this.transactionCount,
    required this.salesVolumeLtr,
    required this.salesAmountIdr,
    required this.fuelSales,
    required this.paymentSummary,
    required this.dryIncome,
    required this.grandTotal,
  });

  factory Attendant.fromJson(Map<String, dynamic> json) {
    return Attendant(
      attendantInfo: AttendantInfo.fromJson(json['attendant_info'] as Map<String, dynamic>),
      transactionCount: json['transaction_count'] as int,
      salesVolumeLtr: (json['sales_volume_ltr'] as num).toDouble(),
      salesAmountIdr: (json['sales_amount_idr'] as num).toDouble(),
      fuelSales: FuelSales.fromJson(json['fuel_sales'] as Map<String, dynamic>),
      paymentSummary: PaymentSummary.fromJson(json['payment_summary'] as Map<String, dynamic>),
      dryIncome: (json['dry_income'] as num).toDouble(),
      grandTotal: (json['grand_total'] as num).toDouble(),
    );
  }

  final AttendantInfo attendantInfo;
  final int transactionCount;
  final double salesVolumeLtr;
  final double salesAmountIdr;
  final FuelSales fuelSales;
  final PaymentSummary paymentSummary;
  final double dryIncome;
  final double grandTotal;

  Map<String, dynamic> toJson() {
    return {
      'attendant_info': attendantInfo.toJson(),
      'transaction_count': transactionCount,
      'sales_volume_ltr': salesVolumeLtr,
      'sales_amount_idr': salesAmountIdr,
      'fuel_sales': fuelSales.toJson(),
      'payment_summary': paymentSummary.toJson(),
      'dry_income': dryIncome,
      'grand_total': grandTotal,
    };
  }
}

/// 员工基本信息
class AttendantInfo {
  const AttendantInfo({
    required this.attendantName,
    required this.staffCardId,
  });

  factory AttendantInfo.fromJson(Map<String, dynamic> json) {
    return AttendantInfo(
      attendantName: json['attendant_name'] as String,
      staffCardId: json['staff_card_id'] as String,
    );
  }

  final String attendantName;
  final String staffCardId;

  Map<String, dynamic> toJson() {
    return {
      'attendant_name': attendantName,
      'staff_card_id': staffCardId,
    };
  }
}

/// 油品销售数据
class FuelSales {
  const FuelSales({
    required this.byGrade,
    required this.total,
  });

  factory FuelSales.fromJson(Map<String, dynamic> json) {
    return FuelSales(
      byGrade: (json['by_grade'] as List<dynamic>)
          .map((dynamic item) => FuelGradeData.fromJson(item as Map<String, dynamic>))
          .toList(),
      total: FuelSalesTotal.fromJson(json['total'] as Map<String, dynamic>),
    );
  }

  final List<FuelGradeData> byGrade;
  final FuelSalesTotal total;

  Map<String, dynamic> toJson() {
    return {
      'by_grade': byGrade.map((grade) => grade.toJson()).toList(),
      'total': total.toJson(),
    };
  }
}

/// 按油品等级分组的销售数据
class FuelGradeData {
  const FuelGradeData({
    required this.fuelGrade,
    required this.fuelName,
    required this.fuelType,
    required this.salesVolume,
    required this.grossAmount,
    required this.discountAmount,
    required this.netAmount,
    required this.unitPrice,
    required this.transactionCount,
  });

  factory FuelGradeData.fromJson(Map<String, dynamic> json) {
    return FuelGradeData(
      fuelGrade: json['fuel_grade'] as String,
      fuelName: json['fuel_name'] as String,
      fuelType: json['fuel_type'] as String,
      salesVolume: (json['sales_volume'] as num).toDouble(),
      grossAmount: (json['gross_amount'] as num).toDouble(),
      discountAmount: (json['discount_amount'] as num).toDouble(),
      netAmount: (json['net_amount'] as num).toDouble(),
      unitPrice: (json['unit_price'] as num).toDouble(),
      transactionCount: json['transaction_count'] as int,
    );
  }

  final String fuelGrade;
  final String fuelName;
  final String fuelType;
  final double salesVolume;
  final double grossAmount;
  final double discountAmount;
  final double netAmount;
  final double unitPrice;
  final int transactionCount;

  Map<String, dynamic> toJson() {
    return {
      'fuel_grade': fuelGrade,
      'fuel_name': fuelName,
      'fuel_type': fuelType,
      'sales_volume': salesVolume,
      'gross_amount': grossAmount,
      'discount_amount': discountAmount,
      'net_amount': netAmount,
      'unit_price': unitPrice,
      'transaction_count': transactionCount,
    };
  }
}

/// 油品销售汇总
class FuelSalesTotal {
  const FuelSalesTotal({
    required this.totalVolume,
    required this.totalGrossAmount,
    required this.totalDiscountAmount,
    required this.totalNetAmount,
    required this.totalTransactions,
  });

  factory FuelSalesTotal.fromJson(Map<String, dynamic> json) {
    return FuelSalesTotal(
      totalVolume: (json['total_volume'] as num).toDouble(),
      totalGrossAmount: (json['total_gross_amount'] as num).toDouble(),
      totalDiscountAmount: (json['total_discount_amount'] as num).toDouble(),
      totalNetAmount: (json['total_net_amount'] as num).toDouble(),
      totalTransactions: json['total_transactions'] as int,
    );
  }

  final double totalVolume;
  final double totalGrossAmount;
  final double totalDiscountAmount;
  final double totalNetAmount;
  final int totalTransactions;

  Map<String, dynamic> toJson() {
    return {
      'total_volume': totalVolume,
      'total_gross_amount': totalGrossAmount,
      'total_discount_amount': totalDiscountAmount,
      'total_net_amount': totalNetAmount,
      'total_transactions': totalTransactions,
    };
  }
}

/// 支付方式汇总
class PaymentSummary {
  const PaymentSummary({
    required this.cash,
    required this.nonCashTotal,
    required this.pvc,
    required this.cimb,
    required this.bca,
    required this.mandiri,
    required this.bri,
    required this.bni,
    required this.voucher,
    required this.b2b,
    required this.tera,
    required this.byMethod,
  });

  factory PaymentSummary.fromJson(Map<String, dynamic> json) {
    return PaymentSummary(
      cash: (json['cash'] as num).toDouble(),
      nonCashTotal: (json['non_cash_total'] as num).toDouble(),
      pvc: (json['pvc'] as num).toDouble(),
      cimb: (json['cimb'] as num).toDouble(),
      bca: (json['bca'] as num).toDouble(),
      mandiri: (json['mandiri'] as num).toDouble(),
      bri: (json['bri'] as num).toDouble(),
      bni: (json['bni'] as num).toDouble(),
      voucher: (json['voucher'] as num).toDouble(),
      b2b: (json['b2b'] as num).toDouble(),
      tera: (json['tera'] as num).toDouble(),
      byMethod: (json['by_method'] as List<dynamic>)
          .map((dynamic item) => PaymentMethodData.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  final double cash;
  final double nonCashTotal;
  final double pvc;
  final double cimb;
  final double bca;
  final double mandiri;
  final double bri;
  final double bni;
  final double voucher;
  final double b2b;
  final double tera;
  final List<PaymentMethodData> byMethod;

  Map<String, dynamic> toJson() {
    return {
      'cash': cash,
      'non_cash_total': nonCashTotal,
      'pvc': pvc,
      'cimb': cimb,
      'bca': bca,
      'mandiri': mandiri,
      'bri': bri,
      'bni': bni,
      'voucher': voucher,
      'b2b': b2b,
      'tera': tera,
      'by_method': byMethod.map((method) => method.toJson()).toList(),
    };
  }
}

/// 支付方式明细数据
class PaymentMethodData {
  const PaymentMethodData({
    required this.paymentMethod,
    required this.paymentMethodName,
    required this.totalAmount,
    required this.transactionCount,
    required this.percentage,
  });

  factory PaymentMethodData.fromJson(Map<String, dynamic> json) {
    return PaymentMethodData(
      paymentMethod: json['payment_method'] as String,
      paymentMethodName: _mapPaymentMethodName(json['payment_method_name'] as String),
      totalAmount: (json['total_amount'] as num).toDouble(),
      transactionCount: json['transaction_count'] as int,
      percentage: (json['percentage'] as num).toDouble(),
    );
  }

  static String _mapPaymentMethodName(String methodName) {
    switch (methodName) {
      case '现金':
        return 'Cash';
      case '银行卡':
        return 'Bank Card';
      case '信用卡':
        return 'Credit Card';
      case '借记卡':
        return 'Debit Card';
      case '电子钱包':
        return 'E-Wallet';
      case '代金券':
        return 'Voucher';
      case '车队卡':
        return 'Fleet Card';
      default:
        return methodName;
    }
  }

  final String paymentMethod;
  final String paymentMethodName;
  final double totalAmount;
  final int transactionCount;
  final double percentage;

  Map<String, dynamic> toJson() {
    return {
      'payment_method': paymentMethod,
      'payment_method_name': paymentMethodName,
      'total_amount': totalAmount,
      'transaction_count': transactionCount,
      'percentage': percentage,
    };
  }
}

/// 班次汇总信息
class ShiftSummary {
  const ShiftSummary({
    required this.totalAttendants,
    required this.totalTransactions,
    required this.totalSalesVolume,
    required this.totalSalesAmount,
    required this.totalCash,
    required this.totalNonCash,
    required this.totalPvc,
    required this.totalCimb,
    required this.totalBca,
    required this.totalMandiri,
    required this.totalBri,
    required this.totalBni,
    required this.totalVoucher,
    required this.totalB2b,
    required this.totalTera,
    required this.grandTotal,
  });

  factory ShiftSummary.fromJson(Map<String, dynamic> json) {
    return ShiftSummary(
      totalAttendants: json['total_attendants'] as int,
      totalTransactions: json['total_transactions'] as int,
      totalSalesVolume: (json['total_sales_volume'] as num).toDouble(),
      totalSalesAmount: (json['total_sales_amount'] as num).toDouble(),
      totalCash: (json['total_cash'] as num).toDouble(),
      totalNonCash: (json['total_non_cash'] as num).toDouble(),
      totalPvc: (json['total_pvc'] as num).toDouble(),
      totalCimb: (json['total_cimb'] as num).toDouble(),
      totalBca: (json['total_bca'] as num).toDouble(),
      totalMandiri: (json['total_mandiri'] as num).toDouble(),
      totalBri: (json['total_bri'] as num).toDouble(),
      totalBni: (json['total_bni'] as num).toDouble(),
      totalVoucher: (json['total_voucher'] as num).toDouble(),
      totalB2b: (json['total_b2b'] as num).toDouble(),
      totalTera: (json['total_tera'] as num).toDouble(),
      grandTotal: (json['grand_total'] as num).toDouble(),
    );
  }

  final int totalAttendants;
  final int totalTransactions;
  final double totalSalesVolume;
  final double totalSalesAmount;
  final double totalCash;
  final double totalNonCash;
  final double totalPvc;
  final double totalCimb;
  final double totalBca;
  final double totalMandiri;
  final double totalBri;
  final double totalBni;
  final double totalVoucher;
  final double totalB2b;
  final double totalTera;
  final double grandTotal;

  Map<String, dynamic> toJson() {
    return {
      'total_attendants': totalAttendants,
      'total_transactions': totalTransactions,
      'total_sales_volume': totalSalesVolume,
      'total_sales_amount': totalSalesAmount,
      'total_cash': totalCash,
      'total_non_cash': totalNonCash,
      'total_pvc': totalPvc,
      'total_cimb': totalCimb,
      'total_bca': totalBca,
      'total_mandiri': totalMandiri,
      'total_bri': totalBri,
      'total_bni': totalBni,
      'total_voucher': totalVoucher,
      'total_b2b': totalB2b,
      'total_tera': totalTera,
      'grand_total': grandTotal,
    };
  }
}

/// 元数据信息
class ShiftAttendantMeta {
  const ShiftAttendantMeta({
    required this.generatedAt,
    required this.processingTimeMs,
    required this.dataSource,
    required this.version,
  });

  factory ShiftAttendantMeta.fromJson(Map<String, dynamic> json) {
    return ShiftAttendantMeta(
      generatedAt: DateTime.parse(json['generated_at'] as String),
      processingTimeMs: json['processing_time_ms'] as int,
      dataSource: json['data_source'] as String,
      version: json['version'] as String,
    );
  }

  final DateTime generatedAt;
  final int processingTimeMs;
  final String dataSource;
  final String version;

  Map<String, dynamic> toJson() {
    return {
      'generated_at': generatedAt.toIso8601String(),
      'processing_time_ms': processingTimeMs,
      'data_source': dataSource,
      'version': version,
    };
  }
}

/// 班次员工查询参数
class ShiftAttendantQueryParams {
  const ShiftAttendantQueryParams({
    this.attendantName,
    this.fuelGrade,
    this.paymentMethod,
  });

  final String? attendantName;
  final String? fuelGrade;
  final String? paymentMethod;

  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = <String, dynamic>{};
    
    if (attendantName != null && attendantName!.isNotEmpty) {
      params['attendant_name'] = attendantName!;
    }
    
    if (fuelGrade != null && fuelGrade!.isNotEmpty) {
      params['fuel_grade'] = fuelGrade!;
    }
    
    if (paymentMethod != null && paymentMethod!.isNotEmpty) {
      params['payment_method'] = paymentMethod!;
    }
    
    return params;
  }
} 