import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/bp_colors.dart';

/// EDC前庭支付系统文本样式定义
/// 基于移动设备优化，考虑Android EDC终端设备屏幕特性
class EDCTextStyles {
  // 主标题 - 页面标题、重要标题
  static const TextStyle mainTitle = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: BPColors.primary,
  );

  // 副标题 - 功能区域标题、卡片标题
  static const TextStyle subTitle = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    color: Color(0xFF333333),
  );

  // 正文文本 - 一般信息展示、列表项内容
  static const TextStyle bodyText = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.normal,
    color: Color(0xFF333333),
  );

  // 按钮文本 - 主要操作按钮文字
  static const TextStyle buttonText = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  // 提示文本 - 帮助说明、次要信息
  static const TextStyle hintText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: BPColors.neutral,
  );

  // 强调文本 - 价格信息、重要数据
  static const TextStyle emphasizedText = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: BPColors.accent,
  );

  // AppBar标题样式
  static const TextStyle appBarTitle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  // 卡片标题样式
  static const TextStyle cardTitle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: Color(0xFF333333),
  );

  // 错误文本样式
  static const TextStyle errorText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: BPColors.error,
  );

  // 成功文本样式
  static const TextStyle successText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: BPColors.success,
  );
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: BPColors.primarySwatch,
      primaryColor: BPColors.primary,
      scaffoldBackgroundColor: BPColors.background,
      appBarTheme: const AppBarTheme(
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 64, // 移动设备优化高度
        iconTheme: IconThemeData(color: Colors.white, size: 28),
        titleTextStyle: EDCTextStyles.appBarTitle,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarDividerColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarContrastEnforced: false,
        ),
      ),

      // 主要按钮样式
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: BPColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            vertical: 16.0,
            horizontal: 32.0,
          ),
          minimumSize: const Size(120, 48), // 确保触摸目标大小
          textStyle: EDCTextStyles.buttonText,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          elevation: 0,
        ),
      ),

      // 次要按钮样式
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: BPColors.primary,
          side: const BorderSide(color: BPColors.primary, width: 2),
          padding: const EdgeInsets.symmetric(
            vertical: 16.0,
            horizontal: 32.0,
          ),
          minimumSize: const Size(120, 48),
          textStyle: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: BPColors.primary,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
        ),
      ),

      // 文本按钮样式
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: BPColors.primary,
          padding: const EdgeInsets.symmetric(
            vertical: 12.0,
            horizontal: 24.0,
          ),
          minimumSize: const Size(80, 44),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // 卡片样式
      cardTheme: CardThemeData(
        elevation: 0,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        margin: const EdgeInsets.all(8.0),
      ),

      // 文本样式主题
      textTheme: const TextTheme(
        headlineLarge: EDCTextStyles.mainTitle,
        headlineMedium: EDCTextStyles.subTitle,
        titleLarge: EDCTextStyles.cardTitle,
        titleMedium: EDCTextStyles.bodyText,
        bodyLarge: EDCTextStyles.bodyText,
        bodyMedium: EDCTextStyles.hintText,
        labelLarge: EDCTextStyles.buttonText,
      ),

      // 输入框样式
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        labelStyle: EDCTextStyles.bodyText,
        hintStyle: EDCTextStyles.hintText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: BPColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: BPColors.error, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: BPColors.error, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16, // 增加垂直内边距适应移动设备
        ),
        errorStyle: EDCTextStyles.errorText,
      ),

      // 底部导航栏样式
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        selectedItemColor: BPColors.primary,
        unselectedItemColor: BPColors.neutral,
        backgroundColor: Colors.white,
        elevation: 8,
        selectedLabelStyle: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.normal,
        ),
      ),

      // 图标主题
      iconTheme: const IconThemeData(
        color: BPColors.neutral,
        size: 28, // 移动设备优化尺寸
      ),

      // 分割线主题
      dividerTheme: DividerThemeData(
        color: Colors.grey[200],
        thickness: 1,
        space: 1,
      ),

      // 对话框主题
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
        backgroundColor: Colors.white,
        titleTextStyle: EDCTextStyles.subTitle,
        contentTextStyle: EDCTextStyles.bodyText,
      ),

      // 加载指示器主题
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: BPColors.primary,
        linearTrackColor: Colors.grey,
        circularTrackColor: Colors.grey,
      ),

      // Snackbar主题
      snackBarTheme: SnackBarThemeData(
        backgroundColor: const Color(0xFF333333),
        contentTextStyle: EDCTextStyles.bodyText.copyWith(color: Colors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// EDC组件样式工具类
class EDCComponentStyles {
  /// 获取状态指示器样式
  static Widget buildLoadingIndicator() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
        strokeWidth: 3, // 移动设备优化宽度
      ),
    );
  }

  /// 获取空状态样式
  static Widget buildEmptyState({
    required String message,
    String? description,
    IconData? icon,
    VoidCallback? onRetry,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            icon ?? Icons.search_off,
            size: 72, // 移动设备优化尺寸
            color: Colors.grey[400],
          ),
          const SizedBox(height: 24),
          Text(
            message,
            style: EDCTextStyles.subTitle.copyWith(
              color: Colors.grey[700],
            ),
          ),
          if (description != null) ...<Widget>[
            const SizedBox(height: 12),
            Text(
              description,
              style: EDCTextStyles.hintText,
              textAlign: TextAlign.center,
            ),
          ],
          if (onRetry != null) ...<Widget>[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取页面结构样式
  static Widget buildPageStructure({
    required Widget child,
    EdgeInsets? padding,
  }) {
    return SafeArea(
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }
}
