import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/fuel_transaction.dart';
import '../services/api/api_service.dart';
import '../services/api/api_client.dart';

// 燃油交易控制器状态
class FuelTransactionState {
  const FuelTransactionState({
    required this.transactions,
    required this.isLoading,
    this.errorMessage,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPage,
    this.selectedStatus,
    this.selectedPumpId,
    required this.sortBy,
    required this.sortDir,
  });

  // 初始状态
  factory FuelTransactionState.initial() {
    return const FuelTransactionState(
      transactions: <FuelTransaction>[],
      isLoading: false,
      total: 0,
      page: 1,
      pageSize: 10,
      totalPage: 0,
      sortBy: 'created_at',
      sortDir: 'desc',
    );
  }
  final List<FuelTransaction> transactions;
  final bool isLoading;
  final String? errorMessage;
  final int total;
  final int page;
  final int pageSize;
  final int totalPage;
  final String? selectedStatus;
  final String? selectedPumpId;
  final String sortBy;
  final String sortDir;

  // 复制并修改状态
  FuelTransactionState copyWith({
    List<FuelTransaction>? transactions,
    bool? isLoading,
    String? errorMessage,
    int? total,
    int? page,
    int? pageSize,
    int? totalPage,
    String? selectedStatus,
    String? selectedPumpId,
    String? sortBy,
    String? sortDir,
  }) {
    return FuelTransactionState(
      transactions: transactions ?? this.transactions,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      total: total ?? this.total,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      totalPage: totalPage ?? this.totalPage,
      selectedStatus: selectedStatus ?? this.selectedStatus,
      selectedPumpId: selectedPumpId ?? this.selectedPumpId,
      sortBy: sortBy ?? this.sortBy,
      sortDir: sortDir ?? this.sortDir,
    );
  }
}

// 燃油交易控制器
class FuelTransactionController extends StateNotifier<FuelTransactionState> {
  FuelTransactionController(this._apiService)
      : super(FuelTransactionState.initial());
  final ApiService _apiService;

  // 加载燃油交易列表
  Future<void> loadFuelTransactions() async {
    try {
      // 设置加载状态
      debugPrint('\n🔄 控制器：开始加载燃油交易数据');
      debugPrint('当前状态：${_getStateInfo()}');

      state = state.copyWith(isLoading: true, errorMessage: null);

      // 构建查询参数
      final FuelTransactionQueryParams params = FuelTransactionQueryParams(
        status: state.selectedStatus,
        pumpId: state.selectedPumpId,
        page: state.page,
        limit: state.pageSize,
        sortBy: state.sortBy,
        sortDir: state.sortDir,
      );

      debugPrint(
          '请求参数：status=${params.status}, pumpId=${params.pumpId}, page=${params.page}, sortBy=${params.sortBy}, sortDir=${params.sortDir}');

      // 调用API获取数据
      final FuelTransactionResponse response =
          await _apiService.fuelTransactionApi.getFuelTransactions(params);

      // 更新状态
      state = state.copyWith(
        transactions: response.items,
        isLoading: false,
        total: response.total,
        page: response.page,
        pageSize: response.pageSize,
        totalPage: response.totalPage,
      );

      debugPrint('✅ 控制器：成功加载燃油交易数据，共${response.items.length}条记录');
      debugPrint('更新后状态：${_getStateInfo()}');
    } catch (e) {
      // 更新错误状态
      debugPrint('❌ 控制器：加载燃油交易失败: ${e.toString()}');

      String errorMessage;

      if (e is ApiException) {
        // 格式化API异常信息，展示更友好的提示
        if (e.statusCode == 404) {
          errorMessage = '未找到数据';
        } else if (e.statusCode == 401) {
          errorMessage = '无权限访问数据，请重新登录';
        } else if (e.statusCode == 400) {
          errorMessage = '请求参数错误: ${e.message}';
        } else if (e.statusCode == 500) {
          errorMessage = '服务器内部错误，请稍后再试';
        } else {
          errorMessage = '加载失败: ${e.message}';
        }
      } else {
        // 处理网络连接等其他错误
        errorMessage = '网络连接失败，请检查网络设置或稍后重试';
      }

      state = state.copyWith(
        isLoading: false,
        errorMessage: errorMessage,
      );

      debugPrint('错误状态：${_getStateInfo()}');
    }
  }

  // 筛选状态
  void filterByStatus(String? status) {
    debugPrint('\n🔍 控制器：按状态筛选，status=$status');
    state = state.copyWith(
      selectedStatus: status,
      page: 1, // 重置到第一页
    );
    loadFuelTransactions();
  }

  // 筛选油枪
  void filterByPumpId(String? pumpId) {
    debugPrint('\n🔍 控制器：按油枪筛选，pumpId=$pumpId');
    state = state.copyWith(
      selectedPumpId: pumpId,
      page: 1, // 重置到第一页
    );
    loadFuelTransactions();
  }

  // 排序
  void sortBy(String field) {
    debugPrint('\n🔄 控制器：排序，field=$field');

    // 如果点击当前排序字段，切换排序方向
    if (field == state.sortBy) {
      final String newSortDir = state.sortDir == 'asc' ? 'desc' : 'asc';
      debugPrint('切换排序方向：${state.sortDir} -> $newSortDir');
      state = state.copyWith(sortDir: newSortDir);
    } else {
      // 否则切换到新字段，默认降序
      debugPrint('切换排序字段：${state.sortBy} -> $field');
      state = state.copyWith(sortBy: field, sortDir: 'desc');
    }
    loadFuelTransactions();
  }

  // 切换页面
  void goToPage(int page) {
    if (page < 1 || page > state.totalPage) {
      debugPrint('\n⚠️ 控制器：页码无效，page=$page, totalPage=${state.totalPage}');
      return;
    }

    debugPrint('\n📄 控制器：翻页，page=$page');
    state = state.copyWith(page: page);
    loadFuelTransactions();
  }

  // 获取可用的状态列表
  List<String> getAvailableStatuses() {
    return _apiService.fuelTransactionApi.getAvailableStatuses();
  }

  // 获取可用的油枪列表
  List<String> getAvailablePumpIds() {
    return _apiService.fuelTransactionApi.getAvailablePumpIds();
  }

  // 获取状态描述
  String getStatusDescription(String status) {
    return _apiService.fuelTransactionApi.getStatusDescription(status);
  }

  // 获取状态信息字符串
  String _getStateInfo() {
    return 'isLoading=${state.isLoading}, '
        'errorMessage=${state.errorMessage ?? "null"}, '
        'total=${state.total}, '
        'page=${state.page}/${state.totalPage}, '
        'selectedStatus=${state.selectedStatus ?? "null"}, '
        'selectedPumpId=${state.selectedPumpId ?? "null"}, '
        'sortBy=${state.sortBy}, '
        'sortDir=${state.sortDir}';
  }
}

// Provider
final StateNotifierProvider<FuelTransactionController, FuelTransactionState>
    fuelTransactionControllerProvider =
    StateNotifierProvider<FuelTransactionController, FuelTransactionState>(
        (StateNotifierProviderRef<FuelTransactionController,
                FuelTransactionState>
            ref) {
  return FuelTransactionController(ApiService());
});
