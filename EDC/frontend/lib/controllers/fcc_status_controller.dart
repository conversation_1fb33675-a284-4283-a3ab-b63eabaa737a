import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dispenser_model.dart';
import '../services/fcc_status_polling_service.dart';
import '../services/fcc_device_service.dart';

/// FCC状态数据类
@immutable
class FccStatusState {
  const FccStatusState({
    this.isPolling = false,
    this.isConnected = false,
    this.dispensers = const <Dispenser>[],
    this.deviceStatus = const <String, dynamic>{},
    this.errorMessage,
    this.pollingInterval = const Duration(seconds: 5),
    this.lastUpdateTime,
  });
  final bool isPolling;
  final bool isConnected;
  final List<Dispenser> dispensers;
  final Map<String, dynamic> deviceStatus;
  final String? errorMessage;
  final Duration pollingInterval;
  final DateTime? lastUpdateTime;

  FccStatusState copyWith({
    bool? isPolling,
    bool? isConnected,
    List<Dispenser>? dispensers,
    Map<String, dynamic>? deviceStatus,
    String? errorMessage,
    Duration? pollingInterval,
    DateTime? lastUpdateTime,
  }) {
    return FccStatusState(
      isPolling: isPolling ?? this.isPolling,
      isConnected: isConnected ?? this.isConnected,
      dispensers: dispensers ?? this.dispensers,
      deviceStatus: deviceStatus ?? this.deviceStatus,
      errorMessage: errorMessage,
      pollingInterval: pollingInterval ?? this.pollingInterval,
      lastUpdateTime: lastUpdateTime ?? this.lastUpdateTime,
    );
  }

  /// 获取设备统计信息
  Map<String, int> get deviceStats {
    if (deviceStatus.isEmpty) return <String, int>{};
    return <String, int>{
      'total_devices': (deviceStatus['devices']?['total'] as int?) ?? 0,
      'online_devices': (deviceStatus['devices']?['online'] as int?) ?? 0,
      'offline_devices': (deviceStatus['devices']?['offline'] as int?) ?? 0,
    };
  }

  /// 获取喷嘴统计信息
  Map<String, int> get nozzleStats {
    if (deviceStatus.isEmpty) return <String, int>{};
    return <String, int>{
      'total_nozzles': (deviceStatus['nozzles']?['total'] as int?) ?? 0,
      'idle_nozzles': (deviceStatus['nozzles']?['idle'] as int?) ?? 0,
      'auth_nozzles': (deviceStatus['nozzles']?['auth'] as int?) ?? 0,
      'fuelling_nozzles': (deviceStatus['nozzles']?['fuelling'] as int?) ?? 0,
      'complete_nozzles': (deviceStatus['nozzles']?['complete'] as int?) ?? 0,
      'offline_nozzles': (deviceStatus['nozzles']?['offline'] as int?) ?? 0,
    };
  }

  /// 检查是否有错误
  bool get hasError => errorMessage != null;

  /// 检查连接状态是否健康
  bool get isHealthy => isConnected && !hasError;
}

/// FCC状态控制器
class FccStatusController extends StateNotifier<FccStatusState> {
  FccStatusController(this._pollingService) : super(const FccStatusState()) {
    _setupListeners();
  }
  final FccStatusPollingService _pollingService;

  /// 设置流监听器
  void _setupListeners() {
    // 监听设备状态
    _pollingService.dispensersStream.listen(
      (List<Dispenser> dispensers) {
        state = state.copyWith(
          dispensers: dispensers,
          lastUpdateTime: DateTime.now(),
          errorMessage: null,
        );
      },
      onError: (Object error) {
        state = state.copyWith(
          errorMessage: 'Dispensers stream error: $error',
        );
      },
    );

    // Note: deviceStatusStream is not available in the new implementation
    // Device status will be calculated from dispensers data when needed

    // 监听连接状态
    _pollingService.connectionStream.listen(
      (bool isConnected) {
        state = state.copyWith(
          isConnected: isConnected,
          errorMessage: isConnected ? null : 'FCC service disconnected',
        );
      },
      onError: (Object error) {
        state = state.copyWith(
          isConnected: false,
          errorMessage: 'Connection stream error: $error',
        );
      },
    );
  }

  /// 开始轮询
  Future<void> startPolling({Duration? interval}) async {
    try {
      debugPrint('🔄 FccStatusController: 开始轮询...');

      state = state.copyWith(
        isPolling: true,
        pollingInterval: interval ?? state.pollingInterval,
        errorMessage: null,
      );

      await _pollingService.startPolling(
        configInterval: const Duration(minutes: 5), // 配置轮询：5分钟
        statusInterval: interval ?? const Duration(seconds: 2), // 状态轮询：2秒
      );
    } catch (e) {
      debugPrint('❌ FccStatusController: 开始轮询失败 - $e');
      state = state.copyWith(
        isPolling: false,
        errorMessage: 'Failed to start polling: $e',
      );
    }
  }

  /// 停止轮询
  void stopPolling() {
    debugPrint('⏹️ FccStatusController: 停止轮询');

    _pollingService.stopPolling();
    state = state.copyWith(isPolling: false);
  }

  /// 设置轮询间隔 (需要重启轮询生效)
  void setPollingInterval(Duration interval) {
    debugPrint(
        '🔄 FccStatusController: 设置轮询间隔 ${interval.inSeconds}秒 (需要重启轮询)');

    // Note: The new implementation doesn't support dynamic interval changes
    // User needs to stop and start polling with new intervals
    state = state.copyWith(pollingInterval: interval);
  }

  /// 手动刷新
  Future<void> refresh() async {
    try {
      debugPrint('🔄 FccStatusController: 手动刷新...');
      await _pollingService.refresh();
    } catch (e) {
      debugPrint('❌ FccStatusController: 手动刷新失败 - $e');
      state = state.copyWith(
        errorMessage: 'Refresh failed: $e',
      );
    }
  }

  /// 清除错误消息
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// 获取特定设备
  Dispenser? getDispenserById(String id) { // 优化：改为 String 类型
    try {
      return state.dispensers.firstWhere((Dispenser d) => d.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取特定喷嘴
  Nozzle? getNozzleById(String nozzleId) {
    for (final Dispenser dispenser in state.dispensers) {
      for (final PumpGroup pumpGroup in dispenser.pumpGroups) {
        try {
          return pumpGroup.nozzles.firstWhere((Nozzle n) => n.id == nozzleId);
        } catch (e) {
          continue;
        }
      }
    }
    return null;
  }

  /// 获取所有喷嘴
  List<Nozzle> getAllNozzles() {
    final List<Nozzle> allNozzles = <Nozzle>[];
    for (final Dispenser dispenser in state.dispensers) {
      for (final PumpGroup pumpGroup in dispenser.pumpGroups) {
        allNozzles.addAll(pumpGroup.nozzles);
      }
    }
    return allNozzles;
  }

  /// 按状态获取喷嘴
  List<Nozzle> getNozzlesByStatus(NozzleStatus status) {
    return getAllNozzles()
        .where((Nozzle nozzle) => nozzle.status == status)
        .toList();
  }

  /// 获取健康状况摘要
  Map<String, dynamic> getHealthSummary() {
    return <String, dynamic>{
      'is_healthy': state.isHealthy,
      'is_connected': state.isConnected,
      'is_polling': state.isPolling,
      'has_error': state.hasError,
      'error_message': state.errorMessage,
      'last_update': state.lastUpdateTime?.toIso8601String(),
      'polling_interval_seconds': state.pollingInterval.inSeconds,
      'device_stats': state.deviceStats,
      'nozzle_stats': state.nozzleStats,
      'polling_service_info': <String, Object>{
        'is_active': _pollingService.isPolling,
        'current_dispensers_count': _pollingService.currentDispensers.length,
        'is_connected': _pollingService.isConnected,
      },
    };
  }

  @override
  void dispose() {
    debugPrint('🧹 FccStatusController: 清理资源');
    _pollingService.dispose();
    super.dispose();
  }
}

// === Provider 定义 ===

/// 共享的FCCDeviceService Provider (单例)
final Provider<FCCDeviceService> fccDeviceServiceProvider =
    Provider<FCCDeviceService>((ProviderRef<FCCDeviceService> ref) {
  final FCCDeviceService instance = FCCDeviceService();
  debugPrint('🏭 创建共享的FCCDeviceService实例: ${instance.hashCode}');
  return instance;
});

/// FCC轮询服务Provider
final Provider<FccStatusPollingService> fccPollingServiceProvider =
    Provider<FccStatusPollingService>(
        (ProviderRef<FccStatusPollingService> ref) {
  final FCCDeviceService fccDeviceService = ref.watch(fccDeviceServiceProvider);
  return FccStatusPollingService(fccDeviceService: fccDeviceService);
});

/// FCC状态控制器Provider
final StateNotifierProvider<FccStatusController, FccStatusState>
    fccStatusControllerProvider =
    StateNotifierProvider<FccStatusController, FccStatusState>(
        (StateNotifierProviderRef<FccStatusController, FccStatusState> ref) {
  final FccStatusPollingService pollingService =
      ref.watch(fccPollingServiceProvider);
  return FccStatusController(pollingService);
});

/// 便捷访问Provider
final Provider<bool> fccConnectionStatusProvider =
    Provider<bool>((ProviderRef<bool> ref) {
  return ref.watch(fccStatusControllerProvider).isConnected;
});

final Provider<Map<String, int>> fccDeviceStatsProvider =
    Provider<Map<String, int>>((ProviderRef<Map<String, int>> ref) {
  return ref.watch(fccStatusControllerProvider).deviceStats;
});

final Provider<Map<String, int>> fccNozzleStatsProvider =
    Provider<Map<String, int>>((ProviderRef<Map<String, int>> ref) {
  return ref.watch(fccStatusControllerProvider).nozzleStats;
});

final Provider<List<Dispenser>> fccDispensersProvider =
    Provider<List<Dispenser>>((ProviderRef<List<Dispenser>> ref) {
  return ref.watch(fccStatusControllerProvider).dispensers;
});

final Provider<bool> fccIsPollingProvider =
    Provider<bool>((ProviderRef<bool> ref) {
  return ref.watch(fccStatusControllerProvider).isPolling;
});

final Provider<String?> fccErrorMessageProvider =
    Provider<String?>((ProviderRef<String?> ref) {
  return ref.watch(fccStatusControllerProvider).errorMessage;
});
