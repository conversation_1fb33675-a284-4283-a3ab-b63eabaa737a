import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/fuel_transaction.dart';
import '../models/promotion_response.dart'; // 保留导入以支持类型
import '../services/api/api_service.dart';
import '../services/api/api_client.dart';

// 燃油交易详情控制器状态
class FuelTransactionDetailState {
  // 保留，但不再使用

  const FuelTransactionDetailState({
    this.transaction,
    required this.isLoading,
    this.errorMessage,
    this.promotionData, // 保留参数，设为可选
    this.isLoadingPromotion = false, // 保留参数，设为可选且有默认值
    this.promotionErrorMessage, // 保留参数，设为可选
  });

  // 初始状态
  factory FuelTransactionDetailState.initial() {
    return const FuelTransactionDetailState(
      transaction: null,
      isLoading: false,
      errorMessage: null,
      promotionData: null, // 初始化保留字段
      isLoadingPromotion: false, // 初始化保留字段
      promotionErrorMessage: null, // 初始化保留字段
    );
  }
  final FuelTransaction? transaction;
  final bool isLoading;
  final String? errorMessage;
  // 保留这些字段以避免const类移除字段的问题
  final PromotionResponse? promotionData; // 保留，但不再使用
  final bool isLoadingPromotion; // 保留，但不再使用
  final String? promotionErrorMessage;

  // 复制并修改状态
  FuelTransactionDetailState copyWith({
    FuelTransaction? transaction,
    bool? isLoading,
    String? errorMessage,
    PromotionResponse? promotionData, // 保留字段
    bool? isLoadingPromotion, // 保留字段
    String? promotionErrorMessage, // 保留字段
  }) {
    return FuelTransactionDetailState(
      transaction: transaction ?? this.transaction,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      promotionData: promotionData ?? this.promotionData, // 保留字段处理
      isLoadingPromotion:
          isLoadingPromotion ?? this.isLoadingPromotion, // 保留字段处理
      promotionErrorMessage: promotionErrorMessage, // 保留字段处理
    );
  }
}

// 燃油交易详情控制器
class FuelTransactionDetailController
    extends StateNotifier<FuelTransactionDetailState> {
  FuelTransactionDetailController(this._apiService)
      : super(FuelTransactionDetailState.initial());
  final ApiService _apiService;

  // 加载燃油交易详情
  Future<void> loadFuelTransactionDetail(String id) async {
    try {
      // 设置加载状态
      debugPrint('\n🔄 详情控制器：开始加载燃油交易详情，ID=$id');
      state = state.copyWith(isLoading: true, errorMessage: null);

      // 调用API获取数据
      final FuelTransaction transaction =
          await _apiService.fuelTransactionApi.getFuelTransactionById(id);

      // 更新状态
      state = state.copyWith(
        transaction: transaction,
        isLoading: false,
      );

      debugPrint('✅ 详情控制器：成功加载燃油交易详情，ID=${transaction.id}');
    } catch (e) {
      // 更新错误状态
      debugPrint('❌ 详情控制器：加载燃油交易详情失败: ${e.toString()}');

      String errorMessage;

      if (e is ApiException) {
        // 格式化API异常信息，展示更友好的提示
        if (e.statusCode == 404) {
          errorMessage = '未找到交易记录，ID可能不存在';
        } else if (e.statusCode == 401) {
          errorMessage = '无权限访问该交易记录，请重新登录';
        } else if (e.statusCode == 400) {
          errorMessage = '请求参数错误: ${e.message}';
        } else if (e.statusCode == 500) {
          errorMessage = '服务器内部错误，请稍后再试';
        } else {
          errorMessage = '加载失败: ${e.message}';
        }
      } else {
        // 处理网络连接等其他错误
        errorMessage = '网络连接失败，请检查网络设置或稍后重试';
      }

      state = state.copyWith(
        isLoading: false,
        errorMessage: errorMessage,
      );
    }
  }

  // 注释：优惠相关功能已移至PromotionController
}

// Provider
final AutoDisposeStateNotifierProvider<FuelTransactionDetailController,
        FuelTransactionDetailState> fuelTransactionDetailControllerProvider =
    StateNotifierProvider.autoDispose<FuelTransactionDetailController,
        FuelTransactionDetailState>((AutoDisposeStateNotifierProviderRef<
            FuelTransactionDetailController, FuelTransactionDetailState>
        ref) {
  return FuelTransactionDetailController(ApiService());
});
