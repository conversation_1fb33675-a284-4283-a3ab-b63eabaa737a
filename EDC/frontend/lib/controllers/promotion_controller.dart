import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/promotion_response.dart';
import '../models/fuel_transaction.dart';
import '../models/order.dart';
import '../services/api/api_service.dart';
import '../services/api/api_client.dart';

// 优惠控制器状态
class PromotionState {
  const PromotionState({
    this.promotionData,
    required this.isLoading,
    this.errorMessage,
  });

  // 初始状态
  factory PromotionState.initial() {
    return const PromotionState(
      promotionData: null,
      isLoading: false,
      errorMessage: null,
    );
  }
  final PromotionResponse? promotionData;
  final bool isLoading;
  final String? errorMessage;

  // 复制并修改状态
  PromotionState copyWith({
    PromotionResponse? promotionData,
    bool? isLoading,
    String? errorMessage,
  }) {
    return PromotionState(
      promotionData: promotionData ?? this.promotionData,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }
}

// 优惠控制器
class PromotionController extends StateNotifier<PromotionState> {
  PromotionController(this._apiService) : super(PromotionState.initial());
  final ApiService _apiService;

  // 加载燃油交易的优惠信息
  Future<void> loadFuelTransactionPromotion(FuelTransaction transaction) async {
    try {
      // 设置加载状态
      debugPrint('\n🔄 优惠控制器：开始加载燃油交易优惠信息，ID=${transaction.id}');
      state = state.copyWith(isLoading: true, errorMessage: null);

      // 调用API获取优惠数据
      final PromotionResponse promotionResponse =
          await _apiService.promotionApi.calculateFuelDiscount(transaction);

      // 更新状态
      state = state.copyWith(
        promotionData: promotionResponse,
        isLoading: false,
      );

      debugPrint('✅ 优惠控制器：成功加载燃油交易优惠信息');
    } catch (e) {
      // 更新错误状态
      debugPrint('❌ 优惠控制器：加载燃油交易优惠信息失败: ${e.toString()}');

      final String errorMessage =
          e is ApiException ? '获取优惠失败: ${e.message}' : '无法获取优惠信息，请稍后再试';

      state = state.copyWith(
        isLoading: false,
        errorMessage: errorMessage,
      );
    }
  }

  // 加载订单的优惠信息
  Future<void> loadOrderPromotion(Order order) async {
    try {
      // 设置加载状态
      debugPrint('\n🔄 优惠控制器：开始加载订单优惠信息，ID=${order.id}');
      state = state.copyWith(isLoading: true, errorMessage: null);

      // 调用API获取优惠数据
      final PromotionResponse promotionResponse =
          await _apiService.promotionApi.calculateDiscount(order);

      // 更新状态
      state = state.copyWith(
        promotionData: promotionResponse,
        isLoading: false,
      );

      debugPrint('✅ 优惠控制器：成功加载订单优惠信息');
    } catch (e) {
      // 更新错误状态
      debugPrint('❌ 优惠控制器：加载订单优惠信息失败: ${e.toString()}');

      final String errorMessage =
          e is ApiException ? '获取优惠失败: ${e.message}' : '无法获取优惠信息，请稍后再试';

      state = state.copyWith(
        isLoading: false,
        errorMessage: errorMessage,
      );
    }
  }

  // 根据交易数据加载优惠信息 (通用入口)
  Future<void> loadPromotion(
      {FuelTransaction? fuelTransaction, Order? order}) async {
    if (fuelTransaction != null) {
      await loadFuelTransactionPromotion(fuelTransaction);
    } else if (order != null) {
      await loadOrderPromotion(order);
    } else {
      state = state.copyWith(
        isLoading: false,
        errorMessage: '无法获取优惠信息：缺少交易数据',
      );
    }
  }

  // 刷新优惠信息
  Future<void> refreshPromotion(
      {FuelTransaction? fuelTransaction, Order? order}) async {
    await loadPromotion(fuelTransaction: fuelTransaction, order: order);
  }

  // 清除优惠信息
  void clearPromotion() {
    state = PromotionState.initial();
  }
}

// Provider
final AutoDisposeStateNotifierProvider<PromotionController, PromotionState>
    promotionControllerProvider =
    StateNotifierProvider.autoDispose<PromotionController, PromotionState>(
        (AutoDisposeStateNotifierProviderRef<PromotionController,
                PromotionState>
            ref) {
  return PromotionController(ApiService());
});
