import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../models/dispenser_model.dart';
import '../services/dispenser_service.dart';
import '../services/api/api_client.dart';
import '../services/fcc_device_service.dart';
import '../constants/api_constants.dart';
import '../services/nozzle_authorization_cache_service.dart';
import '../models/fuel_transaction.dart';
import 'fcc_status_controller.dart'; // 导入共享的 fccDeviceServiceProvider
import '../services/fcc_status_polling_service.dart';
import '../services/nozzle_status_validator.dart'; // 新增：导入状态验证服务

// Dispenser控制器状态
class DispenserState {
  const DispenserState({
    required this.dispensers,
    required this.selectedDispenserId,
    required this.currentPumpGroups,
    required this.nozzleStatusMap,
    required this.completedTransactions,
    required this.bosProtectedNozzles, // 新增：BOS保护的nozzle集合
    required this.isLoading,
    required this.isRefreshing,
    required this.isConnected,
    this.errorMessage,
    this.lastUpdateTime,
  });

  // 初始状态
  factory DispenserState.initial() {
    return const DispenserState(
      dispensers: <Dispenser>[],
      selectedDispenserId: '1', // 优化：默认选择第一个dispenser，改为 String
      currentPumpGroups: <PumpGroup>[],
      nozzleStatusMap: <String, Nozzle>{},
      completedTransactions: <String, List<FuelTransaction>>{},
      bosProtectedNozzles: <String, DateTime>{}, // 新增：初始化为空
      isLoading: false,
      isRefreshing: false,
      isConnected: false,
    );
  }
  final List<Dispenser> dispensers;
  final String selectedDispenserId; // 优化：改为 String 类型
  final List<PumpGroup> currentPumpGroups;
  final Map<String, Nozzle> nozzleStatusMap; // nozzleId -> Nozzle
  final Map<String, List<FuelTransaction>>
      completedTransactions; // nozzleId -> 完成的交易列表
  final Map<String, DateTime> bosProtectedNozzles; // 新增：nozzleId -> BOS保护结束时间
  final bool isLoading;
  final bool isRefreshing;
  final bool isConnected;
  final String? errorMessage;
  final DateTime? lastUpdateTime;

  // 复制并修改状态
  DispenserState copyWith({
    List<Dispenser>? dispensers,
    String? selectedDispenserId, // 优化：改为 String 类型
    List<PumpGroup>? currentPumpGroups,
    Map<String, Nozzle>? nozzleStatusMap,
    Map<String, List<FuelTransaction>>? completedTransactions,
    Map<String, DateTime>? bosProtectedNozzles, // 新增
    bool? isLoading,
    bool? isRefreshing,
    bool? isConnected,
    String? errorMessage,
    DateTime? lastUpdateTime,
  }) {
    return DispenserState(
      dispensers: dispensers ?? this.dispensers,
      selectedDispenserId: selectedDispenserId ?? this.selectedDispenserId,
      currentPumpGroups: currentPumpGroups ?? this.currentPumpGroups,
      nozzleStatusMap: nozzleStatusMap ?? this.nozzleStatusMap,
      completedTransactions:
          completedTransactions ?? this.completedTransactions,
      bosProtectedNozzles: bosProtectedNozzles ?? this.bosProtectedNozzles, // 新增
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isConnected: isConnected ?? this.isConnected,
      errorMessage: errorMessage,
      lastUpdateTime: lastUpdateTime ?? this.lastUpdateTime,
    );
  }

  // 获取当前选中的Dispenser
  Dispenser? get selectedDispenser {
    try {
      return dispensers
          .firstWhere((Dispenser d) => d.id == selectedDispenserId);
    } catch (e) {
      return null;
    }
  }

  // 获取指定Nozzle的最新状态
  Nozzle? getNozzle(String nozzleId) {
    return nozzleStatusMap[nozzleId];
  }

  // 获取可用的Nozzle数量
  int get availableNozzleCount {
    return nozzleStatusMap.values
        .where((Nozzle nozzle) => nozzle.canAuthorize)
        .length;
  }

  // 获取活跃交易数量
  int get activeTransactionCount {
    return nozzleStatusMap.values
        .where((Nozzle nozzle) =>
            nozzle.status == NozzleStatus.auth ||
            nozzle.status == NozzleStatus.fuelling)
        .length;
  }

  // 获取离线Nozzle数量
  int get offlineNozzleCount {
    return nozzleStatusMap.values
        .where((Nozzle nozzle) => nozzle.status == NozzleStatus.offline)
        .length;
  }
}

// Dispenser控制器
class DispenserController extends StateNotifier<DispenserState> {
  DispenserController(
    this._dispenserService,
    this._fccDeviceService,
    this._pollingService,
    this._nozzleStatusValidator, // 新增：状态验证服务
  ) : super(DispenserState.initial()) {
    _setupStreamListeners();
  }
  final DispenserService _dispenserService;
  final FCCDeviceService _fccDeviceService;
  final FccStatusPollingService _pollingService;
  final NozzleStatusValidator _nozzleStatusValidator; // 新增：状态验证服务

  // 流订阅
  StreamSubscription<List<Dispenser>>? _dispensersSubscription;
  StreamSubscription<bool>? _connectionSubscription;

  /// 设置流监听器
  void _setupStreamListeners() {
    // 监听设备状态更新 - 现在从轮询服务获取
    _dispensersSubscription = _pollingService.dispensersStream.listen(
      (List<Dispenser> dispensers) async {
        await _updateDispensersFromStream(dispensers);
      },
      onError: (Object error) {
        debugPrint('❌ DispenserController: Stream error: $error');
        state = state.copyWith(
          errorMessage: 'Stream error: $error',
          isConnected: false,
        );
      },
    );

    // 监听连接状态更新 - 现在从轮询服务获取
    _connectionSubscription = _pollingService.connectionStream.listen(
      (bool isConnected) {
        // debugPrint('🔌 DispenserController: Connection status changed: $isConnected');
        state = state.copyWith(
          isConnected: isConnected,
          errorMessage: isConnected ? null : 'FCC Disconnected',
        );
      },
    );
  }

  /// 从流更新设备数据
  Future<void> _updateDispensersFromStream(List<Dispenser> dispensers) async {
    if (dispensers.isEmpty) return;

    // 选择第一个dispenser（如果当前没有选择）
    String selectedId = state.selectedDispenserId; // 优化：改为 String 类型
    if (state.dispensers.isEmpty && dispensers.isNotEmpty) {
      selectedId = dispensers.first.id;
    }

    // 更新当前选中的dispenser的pump groups
    List<PumpGroup> currentPumpGroups = <PumpGroup>[];
    final Map<String, Nozzle> nozzleStatusMap = <String, Nozzle>{};

    final Dispenser selectedDispenser = dispensers.firstWhere(
      (Dispenser d) => d.id == selectedId,
      orElse: () => dispensers.first,
    );

    currentPumpGroups = selectedDispenser.pumpGroups;

    // 构建nozzle状态映射，使用智能状态验证
          for (final PumpGroup group in currentPumpGroups) {
      for (final Nozzle nozzle in group.nozzles) {
        final Nozzle? existingNozzle = state.nozzleStatusMap[nozzle.id];

        // 🔒 状态保护逻辑：如果 nozzle 有完成交易，强制保持 complete 状态
        // 不允许 FCC 轮询将其覆盖为任何其他状态（idle, auth, fuelling等）
        final bool hasCompletedTransaction = state.completedTransactions.containsKey(nozzle.id) &&
            state.completedTransactions[nozzle.id]!.isNotEmpty;

        if (hasCompletedTransaction) {
          // 强制保护 complete 状态，无论FCC返回什么状态都被忽略
          final FuelTransaction? latestTransaction = getLatestCompletedTransaction(nozzle.id);

          debugPrint('🔒 强制保护 nozzle ${nozzle.id} 的 complete 状态 (有 ${state.completedTransactions[nozzle.id]!.length} 笔待结算交易)');
          debugPrint('   FCC想要设置为: ${nozzle.status.name} -> 被强制覆盖为: complete');

          // 获取现有的nozzle数据，如果不存在则使用FCC的基础数据
          final Nozzle baseNozzle = existingNozzle ?? nozzle;

          nozzleStatusMap[nozzle.id] = baseNozzle.copyWith(
            // 强制设置为 complete 状态，无视FCC状态
            status: NozzleStatus.complete,
            // 保持现有的授权信息（如果有的话）
            currentAuth: baseNozzle.currentAuth,
            // 使用交易数据中的最终金额和升数，确保显示正确的交易信息
            currentVolume: latestTransaction?.volume ?? baseNozzle.currentVolume,
            currentAmount: latestTransaction?.amount ?? baseNozzle.currentAmount,
            // 其他属性使用FCC的最新数据
            price: nozzle.price,
            fuelGrade: nozzle.fuelGrade,
            number: nozzle.number,
            name: nozzle.name,
            lastUpdateTime: DateTime.now(),
          );
        } else {
          // 🎯 智能状态验证：当FCC状态为complete时，同步验证是否真的有pending交易
          if (nozzle.status == NozzleStatus.complete) {
            debugPrint('🔍 [同步验证] FCC报告nozzle ${nozzle.id} 为complete，立即验证BOS状态');

            // 同步验证状态，避免UI闪烁
            final NozzleStatusValidationResult result = await _validateNozzleStatusSync(nozzle.id, nozzle.status);

            // 根据验证结果设置最终状态
            nozzleStatusMap[nozzle.id] = nozzle.copyWith(
              status: result.finalStatus,
              // 如果状态变为idle，清除金额和升数
              currentVolume: result.finalStatus == NozzleStatus.idle ? 0.0 : nozzle.currentVolume,
              currentAmount: result.finalStatus == NozzleStatus.idle ? 0.0 : nozzle.currentAmount,
              lastUpdateTime: DateTime.now(),
            );

            debugPrint('✅ [同步验证] nozzle ${nozzle.id} 最终状态: ${result.finalStatus.name} (${result.reason})');
          } else {
            // 正常情况：完全使用FCC的最新数据
            nozzleStatusMap[nozzle.id] = nozzle;
          }
        }

        // // 调试：如果nozzle有交易数据，打印详细信息
        // if (nozzle.currentVolume > 0 || nozzle.currentAmount > 0) {
        //   // debugPrint('📊 Nozzle ${nozzle.id} 实时数据: status=${nozzleStatusMap[nozzle.id]!.status.name}, volume=${nozzle.currentVolume}L, amount=${nozzle.currentAmount}');
        // }
      }
    }

    state = state.copyWith(
      dispensers: dispensers,
      selectedDispenserId: selectedDispenser.id,
      currentPumpGroups: currentPumpGroups,
      nozzleStatusMap: nozzleStatusMap,
      lastUpdateTime: DateTime.now(),
      errorMessage: null,
    );
  }

  /// 处理来自FCC的实时数据更新
  Future<void> _handleDispensersUpdate(List<Dispenser> dispensers) async {
    try {
      final Dispenser selectedDispenser =
          dispensers.firstWhere((Dispenser d) => d.id == state.selectedDispenserId);
      final List<PumpGroup> currentPumpGroups = selectedDispenser.pumpGroups;

      final Map<String, Nozzle> nozzleStatusMap = <String, Nozzle>{...state.nozzleStatusMap};

      // 构建nozzle状态映射，保护complete状态不被覆盖
      for (final PumpGroup group in currentPumpGroups) {
        for (final Nozzle nozzle in group.nozzles) {
          final Nozzle? existingNozzle = state.nozzleStatusMap[nozzle.id];

          // 🔒 多重状态保护逻辑
          final bool hasCompletedTransaction = state.completedTransactions.containsKey(nozzle.id) &&
              state.completedTransactions[nozzle.id]!.isNotEmpty;
          
          // 🛡️ BOS优先级保护：检查是否在BOS保护期内
          final DateTime? bosProtectionEnd = state.bosProtectedNozzles[nozzle.id];
          final bool isBosProtected = bosProtectionEnd != null && 
              DateTime.now().isBefore(bosProtectionEnd);
          
          final bool shouldProtect = hasCompletedTransaction || isBosProtected;

          if (shouldProtect) {
            // 强化保护 complete 状态，无论FCC返回什么状态都被忽略
            final FuelTransaction? latestTransaction = getLatestCompletedTransaction(nozzle.id);
            
            final String protectionReason = hasCompletedTransaction 
                ? '存在pending交易 ${latestTransaction?.transactionNumber ?? "unknown"}'
                : 'BOS优先级保护 (${bosProtectionEnd?.difference(DateTime.now()).inSeconds ?? 0}秒剩余)';
            
            debugPrint('🔒 [多重保护] nozzle ${nozzle.id} 的 complete 状态');
            debugPrint('   FCC想要设置为: ${nozzle.status.name} -> 被强制覆盖为: complete');
            debugPrint('   保护原因: $protectionReason');
            
            // 获取现有的nozzle数据，如果不存在则使用FCC的基础数据
            final Nozzle baseNozzle = existingNozzle ?? nozzle;
            
            nozzleStatusMap[nozzle.id] = baseNozzle.copyWith(
              // 🔒 强制设置为 complete 状态，完全无视FCC状态
              status: NozzleStatus.complete,
              // 保持现有的授权信息（如果有的话）
              currentAuth: baseNozzle.currentAuth,
              // 使用交易数据中的最终金额和升数，确保显示正确的交易信息
              currentVolume: latestTransaction?.volume ?? baseNozzle.currentVolume,
              currentAmount: latestTransaction?.amount ?? baseNozzle.currentAmount,
              // 其他属性使用FCC的最新数据，但状态不变
              price: nozzle.price,
              fuelGrade: nozzle.fuelGrade,
              number: nozzle.number,
              name: nozzle.name,
              lastUpdateTime: DateTime.now(),
            );
            
            // 📊 状态保护生效时的详细日志
            final Nozzle protectedNozzle = nozzleStatusMap[nozzle.id]!;
            debugPrint('   ✅ 保护后状态: ${protectedNozzle.status.name}');
            debugPrint('   ✅ 保护后数据: volume=${protectedNozzle.currentVolume}L, amount=${protectedNozzle.currentAmount}');
            
          } else {
            // 🎯 智能状态验证：当FCC状态为complete时，同步验证是否真的有pending交易
            if (nozzle.status == NozzleStatus.complete) {
              debugPrint('🔍 [同步验证] FCC报告nozzle ${nozzle.id} 为complete，立即验证BOS状态');

              // 同步验证状态，避免UI闪烁
              final NozzleStatusValidationResult result = await _validateNozzleStatusSync(nozzle.id, nozzle.status);

              // 根据验证结果设置最终状态
              nozzleStatusMap[nozzle.id] = nozzle.copyWith(
                status: result.finalStatus,
                // 如果状态变为idle，清除金额和升数
                currentVolume: result.finalStatus == NozzleStatus.idle ? 0.0 : nozzle.currentVolume,
                currentAmount: result.finalStatus == NozzleStatus.idle ? 0.0 : nozzle.currentAmount,
                lastUpdateTime: DateTime.now(),
              );

              debugPrint('✅ [同步验证] nozzle ${nozzle.id} 最终状态: ${result.finalStatus.name} (${result.reason})');
            } else {
              // 正常情况：完全使用FCC的最新数据
              nozzleStatusMap[nozzle.id] = nozzle;
            }
            
            // 🔍 监控正常更新
            if (nozzle.currentVolume > 0 || nozzle.currentAmount > 0) {
              debugPrint('📊 [正常更新] Nozzle ${nozzle.id}: status=${nozzle.status.name}, volume=${nozzle.currentVolume}L, amount=${nozzle.currentAmount}');
            }
          }
        }
      }

      // 📈 状态更新统计
      final int protectedCount = state.completedTransactions.keys.length;
      final int totalCount = nozzleStatusMap.length;
      
      if (protectedCount > 0) {
        debugPrint('🛡️ 状态保护统计: $protectedCount/$totalCount 个nozzle受保护');
      }
      
      state = state.copyWith(
        dispensers: dispensers,
        selectedDispenserId: selectedDispenser.id,
        currentPumpGroups: currentPumpGroups,
        nozzleStatusMap: nozzleStatusMap,
        lastUpdateTime: DateTime.now(),
        errorMessage: null,
      );
      
      // 🧹 清理过期的BOS保护（在状态更新后执行）
      _cleanupExpiredBosProtections();
    } catch (e) {
      debugPrint('❌ 处理FCC数据更新失败: $e');
      state = state.copyWith(
        errorMessage: '处理FCC数据失败: $e',
        lastUpdateTime: DateTime.now(),
      );
    }
  }

  // 初始化加载
  Future<void> initialize() async {
    try {
      debugPrint('🔄 DispenserController：开始初始化');

      state = state.copyWith(isLoading: true, errorMessage: null);

      // 启动FCC轮询服务
      await _pollingService.startPolling();

      // 等待初始数据
      await Future<void>.delayed(const Duration(milliseconds: 1000));

      state = state.copyWith(
        isLoading: false,
        lastUpdateTime: DateTime.now(),
      );

      debugPrint('✅ DispenserController：初始化完成');
    } catch (e) {
      debugPrint('❌ DispenserController：初始化失败: $e');
      state = state.copyWith(
        isLoading: false,
        errorMessage: '初始化失败: $e',
      );
    }
  }

  // 选择Dispenser
  Future<void> selectDispenser(String dispenserId) async { // 优化：改为 String 类型
    if (dispenserId == state.selectedDispenserId) return;

    try {
      debugPrint('🔄 DispenserController：切换分液器: $dispenserId');

      state = state.copyWith(isLoading: true, errorMessage: null);

      await _loadPumpGroupsForDispenser(dispenserId);

      state = state.copyWith(
        selectedDispenserId: dispenserId,
        isLoading: false,
        lastUpdateTime: DateTime.now(),
      );

      debugPrint('✅ DispenserController：成功切换到分液器: $dispenserId');
    } catch (e) {
      debugPrint('❌ DispenserController：切换分液器失败: $e');
      state = state.copyWith(
        isLoading: false,
        errorMessage: '切换分液器失败: $e',
      );
    }
  }

  // 授权Nozzle
  Future<bool> authorizeNozzle(
      String nozzleId, AuthorizationRequest authRequest) async {
    try {
      debugPrint('🔄 DispenserController：授权喷枪: $nozzleId');
      debugPrint('   模式: ${authRequest.mode.name}, 值: ${authRequest.value}');
      debugPrint('   Staff ID: ${authRequest.staffId}');

      // 更新本地状态为授权中
      _updateNozzleStatusLocal(nozzleId, NozzleStatus.auth, authRequest);

      // 调用服务进行授权
      final bool success =
          await _dispenserService.authorizeNozzle(nozzleId, authRequest);

      if (success) {
        debugPrint('✅ DispenserController：喷枪授权成功: $nozzleId');
        
        // 存储授权信息到缓存中，以便后续正确显示Staff ID
        nozzleAuthorizationCache.storeAuthorization(nozzleId, authRequest);
        
        return true;
      } else {
        // 授权失败，恢复到待机状态
        _updateNozzleStatusLocal(nozzleId, NozzleStatus.idle);
        return false;
      }
    } catch (e) {
      debugPrint('❌ DispenserController：喷枪授权异常: $e');
      _updateNozzleStatusLocal(nozzleId, NozzleStatus.idle);
      return false;
    }
  }

  // 设置Nozzle预设值（不授权）
  Future<bool> presetNozzle(
      String nozzleId, AuthMode mode, double? value, String employeeId) async {
    try {
      debugPrint('🎯 DispenserController：设置喷枪预设: $nozzleId');
      debugPrint('   模式: ${mode.name}, 值: $value, 员工: $employeeId');

      // 调用FCC设备服务进行授权（简化版）
      await _fccDeviceService.authorizeNozzle(
        nozzleId,
        mode: _getPresetType(mode),
        value: value,
        employeeId: employeeId,
      );

      // 如果没有抛出异常，认为预设成功
      const bool success = true;

      if (success) {
        debugPrint('✅ DispenserController：喷枪预设成功: $nozzleId');

        // 更新本地状态 - 创建临时授权请求用于显示
        final AuthorizationRequest tempAuthRequest = AuthorizationRequest(
          nozzleId: nozzleId,
          mode: mode,
          value: value,
          staffId: employeeId, // 使用employeeId作为staffId的值
          requestTime: DateTime.now(),
        );

        // 保持当前状态，但更新授权信息
        final Nozzle? currentNozzle = state.nozzleStatusMap[nozzleId];
        if (currentNozzle != null) {
          _updateNozzleStatusLocal(
              nozzleId, currentNozzle.status, tempAuthRequest);
        }

        return true;
      } else {
        debugPrint('❌ DispenserController：喷枪预设失败: $nozzleId');
        return false;
      }
    } catch (e) {
      debugPrint('❌ DispenserController：喷枪预设异常: $e');
      return false;
    }
  }

  // 刷新Nozzle状态
  Future<void> refreshNozzleStatus() async {
    try {
      debugPrint('🔄 DispenserController：开始手动刷新...');
      state = state.copyWith(isRefreshing: true);

      // 修复: 调用轮询服务的refresh方法，重新获取FCC设备配置
      await _pollingService.refresh();

      // 等待数据更新
      await Future<void>.delayed(const Duration(milliseconds: 500));

      state = state.copyWith(
        isRefreshing: false,
        lastUpdateTime: DateTime.now(),
        errorMessage: null, // 清除错误信息
      );

      debugPrint('✅ DispenserController：手动刷新完成');
    } catch (e) {
      debugPrint('❌ DispenserController：手动刷新失败: $e');
      state = state.copyWith(
        isRefreshing: false,
        errorMessage: '刷新失败: $e',
      );
    }
  }

  // 更新本地Nozzle状态
  void _updateNozzleStatusLocal(String nozzleId, NozzleStatus status,
      [AuthorizationRequest? authRequest]) {
    final Nozzle? currentNozzle = state.nozzleStatusMap[nozzleId];
    if (currentNozzle == null) return;

    final Nozzle updatedNozzle = currentNozzle.copyWith(
      status: status,
      currentAuth: authRequest,
      lastUpdateTime: DateTime.now(),
    );

    final Map<String, Nozzle> updatedStatusMap = <String, Nozzle>{
      ...state.nozzleStatusMap
    };
    updatedStatusMap[nozzleId] = updatedNozzle;

    state = state.copyWith(
      nozzleStatusMap: updatedStatusMap,
      lastUpdateTime: DateTime.now(),
    );

    debugPrint('📝 DispenserController：本地更新喷枪状态: $nozzleId -> ${status.name}');
  }

  // 加载指定Dispenser的PumpGroups
  Future<void> _loadPumpGroupsForDispenser(String dispenserId) async { // 优化：改为 String 类型
    final List<PumpGroup> pumpGroups = await _dispenserService
        .getPumpGroupsByDispenser(dispenserId);

    // 构建nozzle状态映射
    final Map<String, Nozzle> nozzleStatusMap = <String, Nozzle>{};
    for (final PumpGroup group in pumpGroups) {
      for (final Nozzle nozzle in group.nozzles) {
        nozzleStatusMap[nozzle.id] = nozzle;
      }
    }

    state = state.copyWith(
      currentPumpGroups: pumpGroups,
      nozzleStatusMap: nozzleStatusMap,
    );
  }

  // 获取Nozzle详情
  Future<Nozzle?> getNozzleDetails(String nozzleId) async {
    try {
      return await _dispenserService.getNozzleDetails(nozzleId);
    } catch (e) {
      debugPrint('❌ DispenserController：获取喷枪详情失败: $e');
      return null;
    }
  }

  // 停止Nozzle
  Future<bool> stopNozzle(String nozzleId) async {
    try {
      debugPrint('\n🛑 DispenserController：停止喷枪: $nozzleId');

      final bool success = await _dispenserService.stopNozzle(nozzleId);

      if (success) {
        _updateNozzleStatusLocal(nozzleId, NozzleStatus.idle);
        debugPrint('✅ DispenserController：喷枪停止成功: $nozzleId');
      }

      return success;
    } catch (e) {
      debugPrint('❌ DispenserController：停止喷枪失败: $e');
      return false;
    }
  }


  /// 保存完成的交易 - 供 TransactionStateSyncService 调用
  void saveCompletedTransaction(String nozzleId, FuelTransaction transaction) {
    debugPrint(
        '💾 DispenserController：保存完成交易: nozzle $nozzleId -> ${transaction.transactionNumber}');

    final Map<String, List<FuelTransaction>> updatedTransactions =
        <String, List<FuelTransaction>>{...state.completedTransactions};

    // 如果nozzle已有交易列表，添加到列表；否则创建新列表
    if (updatedTransactions.containsKey(nozzleId)) {
      // 检查是否已存在相同的交易（避免重复）
      final List<FuelTransaction> existingTransactions =
          updatedTransactions[nozzleId]!;
      final bool isDuplicate = existingTransactions.any((FuelTransaction tx) =>
          tx.transactionNumber == transaction.transactionNumber);

      if (!isDuplicate) {
        updatedTransactions[nozzleId] = <FuelTransaction>[
          ...existingTransactions,
          transaction
        ];
      }
    } else {
      updatedTransactions[nozzleId] = <FuelTransaction>[transaction];
    }

    // 同时更新nozzle状态
    final Map<String, Nozzle> updatedNozzleMap = <String, Nozzle>{
      ...state.nozzleStatusMap
    };

    final Nozzle? currentNozzle = state.nozzleStatusMap[nozzleId];
    if (currentNozzle != null) {
      updatedNozzleMap[nozzleId] = currentNozzle.copyWith(
        status: NozzleStatus.complete,
        // 使用交易数据，如果没有数据则保持当前值
        currentVolume: transaction.volume > 0 ? transaction.volume : currentNozzle.currentVolume,
        currentAmount: transaction.amount > 0 ? transaction.amount : currentNozzle.currentAmount,
        lastUpdateTime: DateTime.now(),
      );

      debugPrint('✅ 更新nozzle $nozzleId 状态: complete (基于BOS交易: ${transaction.transactionNumber})');
    }

    // 🛡️ 设置BOS优先级保护（8秒）- 适配更快的BOS轮询
    final Map<String, DateTime> updatedBosProtection = <String, DateTime>{
      ...state.bosProtectedNozzles
    };
    final DateTime protectionEnd = DateTime.now().add(const Duration(seconds: 8));
    updatedBosProtection[nozzleId] = protectionEnd;
    
    debugPrint('🛡️ 设置BOS优先级保护: nozzle $nozzleId (8秒保护期)');

    state = state.copyWith(
      completedTransactions: updatedTransactions,
      nozzleStatusMap: updatedNozzleMap,
      bosProtectedNozzles: updatedBosProtection, // 新增保护
      lastUpdateTime: DateTime.now(),
    );
  }

  /// 获取nozzle的完成交易列表
  List<FuelTransaction> getCompletedTransactions(String nozzleId) {
    return state.completedTransactions[nozzleId] ?? <FuelTransaction>[];
  }

  /// 获取nozzle的最新一笔完成交易（为了兼容性）
  FuelTransaction? getLatestCompletedTransaction(String nozzleId) {
    final List<FuelTransaction> transactions =
        getCompletedTransactions(nozzleId);
    if (transactions.isEmpty) return null;

    // 返回最新的交易（按创建时间排序）
    transactions.sort((FuelTransaction a, FuelTransaction b) =>
        b.createdAt.compareTo(a.createdAt));
    return transactions.first;
  }

  /// 清除完成的交易
  void clearCompletedTransaction(String nozzleId) {
    debugPrint('🧹 DispenserController：清除完成交易: nozzle $nozzleId');

    final Map<String, List<FuelTransaction>> updatedTransactions =
        <String, List<FuelTransaction>>{...state.completedTransactions};
    updatedTransactions.remove(nozzleId);

    // 🛡️ 同时清除BOS优先级保护
    final Map<String, DateTime> updatedBosProtection = <String, DateTime>{
      ...state.bosProtectedNozzles
    };
    updatedBosProtection.remove(nozzleId);
    
    debugPrint('🛡️ 清除BOS优先级保护: nozzle $nozzleId');

    state = state.copyWith(
      completedTransactions: updatedTransactions,
      bosProtectedNozzles: updatedBosProtection, // 清除保护
      lastUpdateTime: DateTime.now(),
    );
  }

  /// 清理过期的BOS保护
  void _cleanupExpiredBosProtections() {
    final DateTime now = DateTime.now();
    final Map<String, DateTime> updatedBosProtection = <String, DateTime>{
      ...state.bosProtectedNozzles
    };
    
    final List<String> expiredNozzles = <String>[];
    for (final MapEntry<String, DateTime> entry in updatedBosProtection.entries) {
      if (now.isAfter(entry.value)) {
        expiredNozzles.add(entry.key);
      }
    }
    
    if (expiredNozzles.isNotEmpty) {
      for (final String nozzleId in expiredNozzles) {
        updatedBosProtection.remove(nozzleId);
      }
      
      debugPrint('🧹 清理过期BOS保护: ${expiredNozzles.join(", ")}');
      
      state = state.copyWith(
        bosProtectedNozzles: updatedBosProtection,
        lastUpdateTime: DateTime.now(),
      );
    }
  }

  // 清除错误消息
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  // 清理资源
  @override
  void dispose() {
    _dispensersSubscription?.cancel();
    _connectionSubscription?.cancel();
    _pollingService.stopPolling();
    super.dispose();
  }

  /// 转换授权模式到API格式
  String _getPresetType(AuthMode mode) {
    switch (mode) {
      case AuthMode.amount:
        return 'amount';
      case AuthMode.volume:
        return 'volume';
      case AuthMode.full:
        return 'full';
    }
  }

  /// 🎯 同步验证nozzle状态
  /// 当FCC报告complete状态时，立即验证BOS是否真的有pending交易
  /// 返回验证结果，避免UI闪烁
  Future<NozzleStatusValidationResult> _validateNozzleStatusSync(String nozzleId, NozzleStatus fccStatus) async {
    try {
      debugPrint('🔍 [同步验证] 开始验证 nozzle $nozzleId 状态');

      // 使用状态验证服务验证真实状态，使用短缓存避免阻塞
      final NozzleStatusValidationResult result =
          await _nozzleStatusValidator.validateNozzleStatus(
            nozzleId,
            fccStatus,
            useShortCache: true, // 使用短缓存，减少查询延迟
          );

      debugPrint('🎯 [同步验证] 验证结果: ${result.finalStatus.name} (${result.reason})');

      return result;

    } catch (e) {
      debugPrint('❌ [同步验证] 验证nozzle $nozzleId 状态失败: $e');
      // 验证失败时，保守地返回complete状态
      return NozzleStatusValidationResult(
        finalStatus: NozzleStatus.complete,
        hasPendingTransaction: false,
        reason: '同步验证失败，保守返回complete: $e',
      );
    }
  }

  /// 🎯 异步验证并更新nozzle状态（保留用于其他场景）
  /// 当FCC报告complete状态时，验证BOS是否真的有pending交易
  Future<void> _validateAndUpdateNozzleStatus(String nozzleId, NozzleStatus fccStatus) async {
    try {
      debugPrint('🔍 [异步验证] 开始验证 nozzle $nozzleId 状态');

      // 使用状态验证服务验证真实状态
      final NozzleStatusValidationResult result =
          await _nozzleStatusValidator.validateNozzleStatus(nozzleId, fccStatus);

      debugPrint('🎯 [异步验证] 验证结果: ${result.finalStatus.name} (${result.reason})');

      // 如果验证后的状态与当前状态不同，更新状态
      final Nozzle? currentNozzle = state.nozzleStatusMap[nozzleId];
      if (currentNozzle != null && currentNozzle.status != result.finalStatus) {
        debugPrint('📝 [异步验证] 更新nozzle $nozzleId 状态: ${currentNozzle.status.name} -> ${result.finalStatus.name}');

        final Map<String, Nozzle> updatedNozzleMap = <String, Nozzle>{
          ...state.nozzleStatusMap
        };

        updatedNozzleMap[nozzleId] = currentNozzle.copyWith(
          status: result.finalStatus,
          // 如果状态变为idle，清除金额和升数
          currentVolume: result.finalStatus == NozzleStatus.idle ? 0.0 : currentNozzle.currentVolume,
          currentAmount: result.finalStatus == NozzleStatus.idle ? 0.0 : currentNozzle.currentAmount,
          lastUpdateTime: DateTime.now(),
        );

        state = state.copyWith(
          nozzleStatusMap: updatedNozzleMap,
          lastUpdateTime: DateTime.now(),
        );

        // 如果状态变为idle，清理相关缓存
        if (result.finalStatus == NozzleStatus.idle) {
          _nozzleStatusValidator.clearNozzleCache(nozzleId);
          debugPrint('🧹 [异步验证] 清理nozzle $nozzleId 缓存');
        }
      }

    } catch (e) {
      debugPrint('❌ [异步验证] 验证nozzle $nozzleId 状态失败: $e');
    }
  }
}

// Providers
// 注意：使用 fcc_status_controller.dart 中定义的共享 fccDeviceServiceProvider

final Provider<DispenserService> dispenserServiceProvider =
    Provider<DispenserService>((ProviderRef<DispenserService> ref) {
  // 使用FCCDeviceService代替旧的ServiceRegistry
  final FCCDeviceService fccDeviceService = ref.watch(fccDeviceServiceProvider);

  // 创建一个简单的ApiClient（如果需要）
  // 目前主要使用FCCDeviceService，ApiClient作为备用
  final ApiClient apiClient = ApiClient(
    baseUrl: ApiConstants.getFccServiceUrl(ApiEnvironment.development),
  );

  return DispenserService(
    fccDeviceService: fccDeviceService,
    apiClient: apiClient,
  );
});

final StateNotifierProvider<DispenserController, DispenserState>
    dispenserControllerProvider =
    StateNotifierProvider<DispenserController, DispenserState>(
        (StateNotifierProviderRef<DispenserController, DispenserState> ref) {
  return DispenserController(
    ref.watch(dispenserServiceProvider),
    ref.watch(fccDeviceServiceProvider),
    ref.watch(fccPollingServiceProvider),
    ref.watch(nozzleStatusValidatorProvider), // 新增：状态验证服务
  );
});

// 便利的Provider用于获取特定数据
final Provider<Dispenser?> selectedDispenserProvider =
    Provider<Dispenser?>((ProviderRef<Dispenser?> ref) {
  return ref.watch(dispenserControllerProvider).selectedDispenser;
});

final Provider<List<PumpGroup>> currentPumpGroupsProvider =
    Provider<List<PumpGroup>>((ProviderRef<List<PumpGroup>> ref) {
  return ref.watch(dispenserControllerProvider).currentPumpGroups;
});

final Provider<Map<String, Nozzle>> nozzleStatusMapProvider =
    Provider<Map<String, Nozzle>>((ProviderRef<Map<String, Nozzle>> ref) {
  return ref.watch(dispenserControllerProvider).nozzleStatusMap;
});
