import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../constants/bp_colors.dart';
import '../../models/dispenser_model.dart';
import '../../services/fcc_device_service.dart';

/// 预授权详情页面
class PreauthDetailsPage extends StatefulWidget {
  const PreauthDetailsPage({super.key, required this.nozzle});
  
  final Nozzle nozzle;

  @override
  State<PreauthDetailsPage> createState() => _PreauthDetailsPageState();
}

class _PreauthDetailsPageState extends State<PreauthDetailsPage> {
  Timer? _countdownTimer;
  int _remainingSeconds = 0;
  bool _isExpired = false;

  @override
  void initState() {
    super.initState();
    _updateCountdown();
    _startCountdownTimer();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _updateCountdown() {
    final PreauthInfo? preauth = widget.nozzle.preauthInfo;
    if (preauth != null) {
      final int seconds = preauth.expiresAt.difference(DateTime.now()).inSeconds;
      setState(() {
        _remainingSeconds = seconds;
        _isExpired = seconds <= 0;
      });
    }
  }

  void _startCountdownTimer() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      _updateCountdown();
      if (_isExpired) {
        timer.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final PreauthInfo? preauth = widget.nozzle.preauthInfo;
    if (preauth == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Authorization Details'),
          backgroundColor: BPColors.primary,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text('No preauth information available.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Authorization Details'),
        backgroundColor: _isExpired ? BPColors.error : BPColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Nozzle 信息卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'Nozzle Information',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow('Nozzle', widget.nozzle.name),
                    _buildInfoRow('Fuel Type', widget.nozzle.fuelType),
                    _buildInfoRow('Price', '${widget.nozzle.price.toStringAsFixed(0)} IDR/L'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 预授权信息卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'Authorization Information',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow('Type', _getAuthTypeDisplay(preauth.type)),
                    _buildInfoRow('Preset Value', preauth.displayValue),
                    _buildInfoRow('Created At', _formatDateTime(preauth.createdAt)),
                    _buildInfoRow('Expires At', _formatDateTime(preauth.expiresAt)),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 状态显示卡片
            Card(
              color: _isExpired 
                  ? BPColors.error.withValues(alpha: 0.1)
                  : BPColors.primary.withValues(alpha: 0.1),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: <Widget>[
                    Icon(
                      _isExpired ? Icons.error : Icons.check_circle,
                      size: 48,
                      color: _isExpired ? BPColors.error : BPColors.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isExpired ? 'EXPIRED' : 'ACTIVE',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: _isExpired ? BPColors.error : BPColors.primary,
                      ),
                    ),
                    if (!_isExpired) ...<Widget>[
                      const SizedBox(height: 8),
                      Text(
                        'Expires in ${_remainingSeconds}s',
                        style: TextStyle(
                          fontSize: 16,
                          color: _remainingSeconds < 10 
                              ? BPColors.error 
                              : BPColors.primary.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const Spacer(),
            
            // 操作按钮
            Row(
              children: <Widget>[
                if (!_isExpired) ...<Widget>[
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _cancelPreauth(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: BPColors.error,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Cancel Authorization'),
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => context.pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: BPColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('Close'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getAuthTypeDisplay(String type) {
    switch (type) {
      case 'preset_amount':
        return 'Preset Amount';
      case 'preset_volume':
        return 'Preset Volume';
      case 'full_tank':
        return 'Full Tank';
      default:
        return type;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
           '${dateTime.month.toString().padLeft(2, '0')}/'
           '${dateTime.year} '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// 取消预授权
  Future<void> _cancelPreauth(BuildContext context) async {
    try {
      // 显示确认对话框
      final bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: const Text('Cancel Authorization'),
          content: Text(
            'Are you sure you want to cancel the authorization for ${widget.nozzle.name}?',
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: BPColors.error,
              ),
              child: const Text('Yes, Cancel'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // 调用 FCC 服务取消预授权
        final FCCDeviceService fccService = FCCDeviceService();
        await fccService.cancelNozzlePreauth(widget.nozzle.id);
        
        if (context.mounted) {
          // 显示成功消息
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Authorization cancelled successfully'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 2),
            ),
          );
          
          // 返回上一页
          context.pop();
        }
      }
    } catch (e) {
      debugPrint('❌ Cancel preauth failed: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel authorization: $e'),
            backgroundColor: BPColors.error,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
