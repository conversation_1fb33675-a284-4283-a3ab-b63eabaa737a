import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/bp_colors.dart';
import '../../theme/app_theme.dart';
import '../../services/timezone_service.dart';
import '../../widgets/safe_scaffold.dart';

/// Timezone Settings Page
class TimezoneSettingsPage extends ConsumerStatefulWidget {
  const TimezoneSettingsPage({super.key});

  @override
  ConsumerState<TimezoneSettingsPage> createState() =>
      _TimezoneSettingsPageState();
}

class _TimezoneSettingsPageState extends ConsumerState<TimezoneSettingsPage> {
  bool _isLoading = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<TimezoneInfo> get _filteredTimezones {
    if (_searchQuery.isEmpty) {
      return TimezoneService.availableTimezones;
    }
    return TimezoneService.availableTimezones.where((timezone) {
      return timezone.displayName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             timezone.offset.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final TimezoneService timezoneService = ref.watch(timezoneServiceProvider);
    final String currentTimezone = timezoneService.selectedTimezone;
    final String currentTime = timezoneService.formatNow();

    return SafeScaffold(
      appBar: AppBar(
        title: const Text(
          'Timezone Settings',
          style: EDCTextStyles.appBarTitle,
        ),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 64,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: BPColors.primary,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // 当前时区信息卡片
                  _buildCurrentTimezoneCard(
                      currentTime, timezoneService.currentTimezoneInfo),

                  const SizedBox(height: 24),

                  // Timezone selection title
                  const Text(
                    'Available Timezones',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: BPColors.primary,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Search box
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search timezones...',
                      hintStyle: const TextStyle(color: BPColors.neutral),
                      prefixIcon: const Icon(Icons.search, color: BPColors.primary),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear, color: BPColors.neutral),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: BPColors.primary),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: BPColors.primary, width: 2),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onChanged: (query) {
                      setState(() {
                        _searchQuery = query;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // Timezone list
                  Expanded(
                    child: _filteredTimezones.isEmpty 
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.search_off, size: 64, color: BPColors.neutral),
                                SizedBox(height: 16),
                                Text(
                                  'No timezones found',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: BPColors.neutral,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredTimezones.length,
                            itemBuilder: (BuildContext context, int index) {
                              final TimezoneInfo timezone = _filteredTimezones[index];
                              final bool isSelected = timezone.id == currentTimezone;

                              return _buildTimezoneListItem(
                                timezone,
                                isSelected,
                                () => _onTimezoneSelected(timezone.id),
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
    );
  }

  /// 构建当前时区信息卡片
  Widget _buildCurrentTimezoneCard(
      String currentTime, TimezoneInfo currentTimezoneInfo) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: <Color>[
              BPColors.primary.withOpacity(0.1),
              BPColors.primary.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
                          const Row(
                children: <Widget>[
                  Icon(
                    Icons.access_time,
                    color: BPColors.primary,
                    size: 24,
                  ),
                  SizedBox(width: 12),
                  Text(
                    'Current Timezone',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: BPColors.primary,
                    ),
                  ),
                ],
              ),
            const SizedBox(height: 16),

            // 时区名称
            Text(
              currentTimezoneInfo.displayName,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),

            // 时区偏移
            Text(
              currentTimezoneInfo.offset,
              style: const TextStyle(
                fontSize: 14,
                color: BPColors.neutral,
              ),
            ),
            const SizedBox(height: 12),

            // 当前时间
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: BPColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: BPColors.primary.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const Icon(
                    Icons.schedule,
                    color: BPColors.primary,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    currentTime,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: BPColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建时区列表项
  Widget _buildTimezoneListItem(
    TimezoneInfo timezone,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? BPColors.primary : Colors.grey.withOpacity(0.2),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color:
                isSelected ? BPColors.primary.withOpacity(0.05) : Colors.white,
          ),
          child: Row(
            children: <Widget>[
              // 时区图标
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? BPColors.primary.withOpacity(0.1)
                      : Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.public,
                  color: isSelected ? BPColors.primary : BPColors.neutral,
                  size: 20,
                ),
              ),

              const SizedBox(width: 16),

              // 时区信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      timezone.displayName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: isSelected ? BPColors.primary : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      timezone.offset,
                      style: TextStyle(
                        fontSize: 14,
                        color: isSelected
                            ? BPColors.primary.withOpacity(0.8)
                            : BPColors.neutral,
                      ),
                    ),
                  ],
                ),
              ),

              // 选择状态指示器
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: BPColors.primary,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理时区选择
  Future<void> _onTimezoneSelected(String timezoneId) async {
    if (_isLoading) return;

    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    final String currentTimezone = timezoneService.selectedTimezone;

    // If same timezone is selected, do nothing
    if (timezoneId == currentTimezone) return;

    // Find the selected timezone info
    final TimezoneInfo? selectedTimezone = TimezoneService.availableTimezones
        .where((tz) => tz.id == timezoneId)
        .firstOrNull;

    if (selectedTimezone == null) return;

    // Show confirmation dialog
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: const Row(
          children: [
            Icon(Icons.access_time, color: BPColors.primary, size: 22),
            SizedBox(width: 8),
            Text(
              'Change Timezone',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: BPColors.primary,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to change the timezone to:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: BPColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: BPColors.primary.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    selectedTimezone.displayName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: BPColors.primary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    selectedTimezone.offset,
                    style: const TextStyle(
                      fontSize: 14,
                      color: BPColors.neutral,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'This change will be applied immediately.',
              style: TextStyle(
                fontSize: 14,
                color: BPColors.neutral,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontSize: 16,
                color: BPColors.neutral,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: BPColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Confirm',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // 更新时区设置
      await timezoneService.setTimezone(timezoneId);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Timezone changed to ${selectedTimezone.displayName}'),
            backgroundColor: BPColors.success,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update timezone: $e'),
            backgroundColor: BPColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
