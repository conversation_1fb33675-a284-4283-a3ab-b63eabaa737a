import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../constants/bp_colors.dart';
import '../../constants/customer_type_constants.dart';
import '../../theme/app_theme.dart';
import '../../widgets/bp_app_bar.dart';
import '../../services/member_cache_service.dart';
import '../../models/member_model.dart';

/// 车牌号格式化器
/// 规则：字母开头，中间数字，结尾字母，最大9位，不允许空格，字母自动转大写
class PlateNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 移除空格并转换为大写
    String cleaned = newValue.text.replaceAll(' ', '').toUpperCase();

    // 限制最大长度为9位
    if (cleaned.length > 9) {
      cleaned = cleaned.substring(0, 9);
    }

    // 验证字符：只允许字母和数字
    final RegExp allowedChars = RegExp(r'^[A-Z0-9]*$');
    if (!allowedChars.hasMatch(cleaned)) {
      // 如果包含非法字符，保持原值
      return oldValue;
    }

    // 印尼车牌格式验证：字母开头，中间数字，可以字母结尾
    // 例子: B1234ABC, D123AB, H456A
    if (cleaned.isNotEmpty) {
      // 第一个字符必须是字母
      if (!RegExp(r'^[A-Z]').hasMatch(cleaned)) {
        return oldValue;
      }

      // 如果长度超过1，检查后续字符规则
      if (cleaned.length > 1) {
        // 简化验证：允许字母和数字的合理组合
        // 常见格式：1-3个字母 + 1-4个数字 + 0-3个字母
        final RegExp platePattern = RegExp(r'^[A-Z]{1,3}[0-9]{0,4}[A-Z]{0,3}$');
        if (!platePattern.hasMatch(cleaned)) {
          // 如果不符合基本格式，只在输入合理的情况下允许
          // 检查是否是在构建过程中的合理输入
          final RegExp buildingPattern =
              RegExp(r'^[A-Z]{1,3}[0-9]{0,4}[A-Z]{0,3}$');
          if (!buildingPattern.hasMatch(cleaned)) {
            return oldValue;
          }
        }
      }
    }

    // 智能处理光标位置，保持更好的用户体验
    final int originalCursorPos = newValue.selection.baseOffset;
    
    // 计算合适的光标位置：考虑文本清理造成的位置变化
    int newCursorPos;
    if (newValue.text == cleaned) {
      // 文本没有被修改，保持原位置
      newCursorPos = originalCursorPos.clamp(0, cleaned.length);
         } else {
       // 文本被清理，智能调整光标位置
       // 计算光标前被移除的字符数量（主要是空格和非法字符）
       int removedChars = 0;
       for (int i = 0; i < originalCursorPos && i < newValue.text.length; i++) {
         final String char = newValue.text[i];
         // 统计被移除的空格
         if (char == ' ') {
           removedChars++;
         }
       }
       newCursorPos = (originalCursorPos - removedChars).clamp(0, cleaned.length);
     }
    
        final TextSelection newSelection = TextSelection.collapsed(offset: newCursorPos);

    return TextEditingValue(
      text: cleaned,
      selection: newSelection,
    );
  }
}

class MemberRegistrationPage extends StatefulWidget {
  const MemberRegistrationPage({super.key});

  @override
  State<MemberRegistrationPage> createState() => _MemberRegistrationPageState();
}

class _MemberRegistrationPageState extends State<MemberRegistrationPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _plateNumberController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final FocusNode _plateNumberFocusNode = FocusNode(); // 车牌输入框焦点
  String? _selectedVehicleType = 'Car'; // 默认选择Car
  


  // 车型选项 - 简化为三类
  final List<String> _vehicleTypes = <String>[
    'Motorbike', // 摩托车
    'Car', // 汽车
    'Truck', // 卡车
  ];

  // 客户类型选项
  final List<String> _customerTypes = CustomerTypeUtils.getAllValues();
  String _selectedCustomerType = CustomerType.b2c.value; // 默认为B2C

  // 编辑模式相关
  Member? _editingMember;
  bool _isLoadingCachedData = false;
  bool _isEditMode = false; // 内部管理编辑模式状态

  @override
  void initState() {
    super.initState();

    // 检查是否有缓存数据来决定编辑模式
    _checkEditMode();

    // 监听车牌号输入变化，实时更新按钮状态
    _plateNumberController.addListener(() {
      setState(() {
        // 触发重建以更新按钮状态
      });
    });
  }

  /// 检查编辑模式并加载数据
  void _checkEditMode() {
    final bool hasCachedMember = memberCacheService.hasCachedMember;

    if (hasCachedMember) {
      // 有缓存数据，进入编辑模式
      _isEditMode = true;
      _loadCachedMemberData();
    } else {
      // 无缓存数据，进入新增模式
      _isEditMode = false;
      // 页面加载完成后自动聚焦车牌输入框
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _plateNumberFocusNode.requestFocus();
      });
    }
  }

  /// 加载缓存的会员数据用于编辑
  void _loadCachedMemberData() {
    setState(() {
      _isLoadingCachedData = true;
    });

    final Member? cachedMember = memberCacheService.cachedMember;
    if (cachedMember != null) {
      debugPrint('📝 编辑模式：加载缓存的会员数据');
      debugPrint('   会员: ${cachedMember.name}');
      debugPrint('   车牌: ${cachedMember.metadata['plateNumbers']}');
      debugPrint('   车型: ${cachedMember.metadata['vehicle']}');
      debugPrint('   客户类型: ${cachedMember.metadata['customerType']}');

      _editingMember = cachedMember;

      // 自动填入表单数据
      _fillFormWithMemberData(cachedMember);
    } else {
      debugPrint('⚠️ 编辑模式：未找到缓存的会员数据');
    }

    setState(() {
      _isLoadingCachedData = false;
    });
  }

  /// 使用会员数据填充表单
  void _fillFormWithMemberData(Member member) {
    // 填充车牌号
    final List? plateNumbers =
        member.metadata['plateNumbers'] as List<dynamic>?;
    if (plateNumbers != null && plateNumbers.isNotEmpty) {
      _plateNumberController.text = plateNumbers.first.toString();
      

    }

    // 填充车型
    final String? vehicle = member.metadata['vehicle'] as String?;
    if (vehicle != null && _vehicleTypes.contains(vehicle)) {
      _selectedVehicleType = vehicle;
    }

    // 填充客户类型
    final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(member.metadata, memberId: member.id);
    _selectedCustomerType = detectedType.value;

    // 填充姓名（如果不为空）
    if (member.name.isNotEmpty) {
      _nameController.text = member.name;
    }

    // 填充手机号（如果不为空）
    if (member.phone.isNotEmpty) {
      _phoneController.text = member.phone;
    }

    debugPrint('✅ 表单数据填充完成');
  }



  @override
  void dispose() {
    _plateNumberController.dispose();
    _phoneController.dispose();
    _nameController.dispose();
    _plateNumberFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: BPAppBar(
        title: _isEditMode ? 'Edit Customer Info' : 'Customer Info',
      ),
      body: _isLoadingCachedData
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding:
                      const EdgeInsets.fromLTRB(16, 8, 16, 60), // 进一步减少底部留白
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // 编辑模式提示
                      if (_isEditMode && _editingMember != null)
                        _buildEditModeHeader(),

                      _buildRegistrationHeader(),
                      const SizedBox(height: 12), // 进一步减少间距，因为去掉了标题
                      _buildVehicleInfoSection(), // 车辆信息放在最前面
                      const SizedBox(height: 16), // 减少间距
                      _buildContactInfoSection(), // 联系信息放在后面
                    ],
                  ),
                ),
              ),
            ),
      // 悬浮注册按钮
      bottomNavigationBar: _buildFloatingRegisterButton(),
    );
  }

  /// 构建编辑模式头部提示
  Widget _buildEditModeHeader() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: BPColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: BPColors.primary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: <Widget>[
          const Icon(
            Icons.edit,
            color: BPColors.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Editing Customer Information',
                  style: EDCTextStyles.bodyText.copyWith(
                    color: BPColors.primary,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Modify the information below and save changes',
                  style: EDCTextStyles.hintText.copyWith(
                    color: BPColors.primary.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建注册页面头部
  Widget _buildRegistrationHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 只保留 B2B/B2C 客户类型选择器
        _buildCustomerTypeSelector(),
      ],
    );
  }

  /// 构建客户类型选择器 (B2B/B2C)
  Widget _buildCustomerTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          'Customer Type',
          style: EDCTextStyles.bodyText.copyWith(
            fontWeight: FontWeight.w600,
            color: BPColors.primary,
            fontSize: 14, // 减少字体大小
          ),
        ),
        const SizedBox(height: 6), // 减少间距

        Row(
          children: _customerTypes.map((String customerType) {
            final bool isSelected = _selectedCustomerType == customerType;

            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                    right: customerType != _customerTypes.last ? 8 : 0),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedCustomerType = customerType;
                        // B2B时清空手机号
                        if (CustomerType.isB2B(customerType)) {
                          _phoneController.clear();
                        }
                      });
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding:
                          const EdgeInsets.symmetric(vertical: 8), // 减少垂直间距
                      decoration: BoxDecoration(
                        color: isSelected ? BPColors.primary : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? BPColors.primary
                              : BPColors.neutral.withValues(alpha: 0.3),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Column(
                        children: <Widget>[
                          Text(
                            customerType,
                            style: EDCTextStyles.bodyText.copyWith(
                              color: isSelected ? Colors.white : Colors.black87,
                              fontSize: 14, // 减少字体大小
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 2), // 减少间距
                          Text(
                            CustomerTypeUtils.getDisplayText(
                              CustomerType.fromString(customerType) ?? CustomerType.b2c
                            ),
                            style: EDCTextStyles.hintText.copyWith(
                              color: isSelected
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : BPColors.neutral,
                              fontSize: 11, // 减少字体大小
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建车辆信息部分 - 重点部分，放在最前面
  Widget _buildVehicleInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 分组标题
        _buildSectionTitle('Vehicle Information', isRequired: true),
        const SizedBox(height: 12), // 减少间距

        // 车牌号输入 - 必填重点
        _buildModernTextField(
          controller: _plateNumberController,
          focusNode: _plateNumberFocusNode,
          label: 'Licence Plate Number *',
          hint: 'e.g. B1234ABC, D567XY',
          icon: Icons.directions_car_outlined,
          keyboardType: TextInputType.visiblePassword, // 支持字母+数字的键盘
          inputFormatters: <TextInputFormatter>[
            PlateNumberFormatter(),
          ],
          validator: (String? value) {
            if (value == null || value.isEmpty) {
              return 'Licence plate number is required';
            }
            if (value.length < 2) {
              return 'Please enter a valid licence plate number';
            }
            // 验证车牌格式：字母开头
            if (!RegExp(r'^[A-Z]').hasMatch(value)) {
              return 'Licence plate must start with a letter';
            }
            return null;
          },
          helperText: 'Max 9 characters, letters and numbers only',
        ),
        const SizedBox(height: 12), // 减少间距

        // 车型选择 - 单选组
        _buildVehicleTypeSelector(),
      ],
    );
  }

  /// 构建车型选择器
  Widget _buildVehicleTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 子标题
        Text(
          'Vehicle Type (Optional)',
          style: EDCTextStyles.bodyText.copyWith(
            fontWeight: FontWeight.w600,
            color: BPColors.primary,
            fontSize: 14, // 减少字体大小
          ),
        ),
        const SizedBox(height: 8), // 减少间距

        // 简化的单行选择器
        Row(
          children:
              _vehicleTypes.asMap().entries.map((MapEntry<int, String> entry) {
            final int index = entry.key;
            final String vehicleType = entry.value;
            final bool isSelected = _selectedVehicleType == vehicleType;

            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                    right: index < _vehicleTypes.length - 1 ? 8 : 0),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedVehicleType = vehicleType; // 直接设置选中的车型，不允许取消选择
                      });
                    },
                    borderRadius: BorderRadius.circular(6),
                    child: Container(
                      padding:
                          const EdgeInsets.symmetric(vertical: 8), // 减少垂直间距
                      decoration: BoxDecoration(
                        color: isSelected ? BPColors.primary : Colors.white,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: isSelected
                              ? BPColors.primary
                              : BPColors.neutral.withValues(alpha: 0.3),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          vehicleType,
                          style: EDCTextStyles.bodyText.copyWith(
                            color: isSelected ? Colors.white : Colors.black87,
                            fontSize: 12, // 减少字体大小
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建联系信息部分 - 可选信息，放在后面
  Widget _buildContactInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 分组标题
        _buildSectionTitle('Contact Information', isRequired: false),
        const SizedBox(height: 12), // 减少间距

        // 根据客户类型显示不同的手机号输入逻辑
        if (CustomerType.isB2C(_selectedCustomerType)) ...<Widget>[
          // B2C客户：手机号可选
          _buildModernTextField(
            controller: _phoneController,
            label: 'Phone Number (Optional)',
            hint: 'Enter phone number',
            icon: Icons.phone_android,
            keyboardType: TextInputType.phone,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(15),
            ],
            helperText: 'For member benefits and notifications',
          ),
          const SizedBox(height: 12), // 减少间距
        ] else ...<Widget>[
          // B2B客户：不显示手机号输入，显示说明
          Container(
            padding: const EdgeInsets.all(10), // 减少内边距
            decoration: BoxDecoration(
              color: BPColors.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: BPColors.primary.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: <Widget>[
                const Icon(
                  Icons.business,
                  size: 18, // 减少图标大小
                  color: BPColors.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Business customers can manage contact information through company account settings',
                    style: EDCTextStyles.hintText.copyWith(
                      color: BPColors.primary,
                      fontSize: 12, // 减少字体大小
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12), // 减少间距
        ],

        // 姓名输入 - 两种客户类型都需要
        _buildModernTextField(
          controller: _nameController,
          label: CustomerType.isB2B(_selectedCustomerType)
              ? 'Company/Contact Name (Optional)'
              : 'Full Name (Optional)',
          hint: CustomerType.isB2B(_selectedCustomerType)
              ? 'Enter company or contact name'
              : 'Enter your full name',
          icon: CustomerType.isB2B(_selectedCustomerType)
              ? Icons.business_outlined
              : Icons.person_outline,
          textCapitalization: TextCapitalization.words,
        ),
      ],
    );
  }

  /// 构建悬浮注册按钮
  Widget _buildFloatingRegisterButton() {
    // 只要求车牌号必填
    final bool canRegister = _plateNumberController.text.isNotEmpty;

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 12), // 优化按钮区域内边距
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 48, // 恢复按钮高度以防止文字被遮挡
          child: ElevatedButton(
            onPressed: canRegister ? _registerMember : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: BPColors.primary,
              foregroundColor: Colors.white,
              disabledBackgroundColor: BPColors.neutral.withValues(alpha: 0.3),
              padding: const EdgeInsets.symmetric(vertical: 12), // 确保足够的垂直内边距
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              _isEditMode ? 'Update Customer Info' : 'Save Customer Info',
              style: EDCTextStyles.buttonText.copyWith(
                fontSize: 16, // 恢复合适的字体大小
                height: 1.2, // 设置行高确保文字不被截断
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建分组标题
  Widget _buildSectionTitle(String title, {bool isRequired = false}) {
    return Container(
      padding: const EdgeInsets.only(bottom: 6), // 减少底部间距
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isRequired ? BPColors.primary : BPColors.neutral,
            width: isRequired ? 2 : 1,
          ),
        ),
      ),
      child: Row(
        children: <Widget>[
          Text(
            title,
            style: EDCTextStyles.subTitle.copyWith(
              color: isRequired ? BPColors.primary : BPColors.neutral,
              fontSize: 16, // 减少字体大小
              fontWeight: FontWeight.w600,
            ),
          ),
          if (isRequired) ...<Widget>[
            const SizedBox(width: 6), // 减少间距
            Container(
              padding: const EdgeInsets.symmetric(
                  horizontal: 5, vertical: 1), // 减少内边距
              decoration: BoxDecoration(
                color: BPColors.primary,
                borderRadius: BorderRadius.circular(3),
              ),
              child: Text(
                'Required',
                style: EDCTextStyles.hintText.copyWith(
                  color: Colors.white,
                  fontSize: 9, // 减少字体大小
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建现代化文本输入框
  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    FocusNode? focusNode,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    TextCapitalization? textCapitalization,
    String? Function(String?)? validator,
    String? helperText,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          textCapitalization: textCapitalization ?? TextCapitalization.none,
          enabled: enabled,
          validator: validator,
          style: EDCTextStyles.bodyText.copyWith(
            fontSize: 14, // 减少字体大小
          ),
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            prefixIcon: Icon(
              icon,
              color: enabled ? BPColors.primary : BPColors.neutral,
              size: 20,
            ),
            filled: true,
            fillColor: enabled ? Colors.white : Colors.grey.shade50,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12, // 减少垂直间距
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: BPColors.neutral.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: BPColors.neutral.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: BPColors.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: BPColors.warning,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: BPColors.warning,
                width: 2,
              ),
            ),
            labelStyle: EDCTextStyles.bodyText.copyWith(
              color: enabled
                  ? BPColors.neutral
                  : BPColors.neutral.withValues(alpha: 0.6),
              fontSize: 14, // 减少字体大小
            ),
            hintStyle: EDCTextStyles.hintText.copyWith(
              fontSize: 14, // 减少字体大小
            ),
          ),
        ),

        // Helper text
        if (helperText != null)
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 12), // 减少顶部间距
            child: Text(
              helperText,
              style: EDCTextStyles.hintText.copyWith(
                fontSize: 11, // 减少字体大小
              ),
            ),
          ),
      ],
    );
  }

  // === 业务逻辑方法 ===

  void _registerMember() {
    if (_formKey.currentState!.validate()) {
      // 创建或更新会员对象并存入缓存
      final Member member =
          _isEditMode ? _updateMemberFromInput() : _createMemberFromInput();
      memberCacheService.cacheMember(member);

      // 显示成功提示并返回
      final String action = _isEditMode ? 'updated' : 'saved';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Customer information for ${member.name} $action successfully'),
          backgroundColor: BPColors.success,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );

      // 返回上一页，传递成功标识
      Navigator.of(context).pop(true);
    }
  }

  /// 从输入创建客户信息对象
  Member _createMemberFromInput() {
    final String plateNumber = _plateNumberController.text.trim().toUpperCase();
    final String phone = _phoneController.text.trim();
    final String name = _nameController.text.trim();

    // 生成临时会员ID，包含客户类型前缀
    final CustomerType customerType = CustomerType.fromString(_selectedCustomerType) ?? CustomerType.b2c;
    final String prefix = customerType.value;
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final String suffix = timestamp.length > 7 ? timestamp.substring(7) : timestamp;
    final String memberId = '${prefix}_$suffix';

    return Member(
      id: memberId,
      memberCardId: memberId,
      name: name,
      phone: phone.isEmpty ? '' : phone,
      email: '${plateNumber.toLowerCase()}@temp.member',
      level: MemberLevel.bronze, // 新会员默认为铜卡
      status: MemberStatus.active,
      balance: 0.0,
      points: 0,
      registrationDate: DateTime.now(),
      lastVisitDate: DateTime.now(),
      address: null,
      birthDate: null,
      idCard: null,
      metadata: () {
        final Map<String, dynamic> metadata = <String, dynamic>{
          'plateNumbers': <String>[plateNumber],
          'vehicle': _selectedVehicleType ?? 'Car',
          'registrationSource': 'EDC_QUICK_REGISTRATION',
          'isTemporary': true,
          'cachedAt': DateTime.now().toIso8601String(),
        };
        CustomerTypeUtils.setCustomerType(metadata, customerType);
        return metadata;
      }(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// 从输入更新现有会员信息对象
  Member _updateMemberFromInput() {
    if (_editingMember == null) {
      // 如果没有编辑的会员，则创建新的
      return _createMemberFromInput();
    }

    final String plateNumber = _plateNumberController.text.trim().toUpperCase();
    final String phone = _phoneController.text.trim();
    final String name = _nameController.text.trim();

    // 更新现有会员信息，保持原有的ID和其他基本信息
    return _editingMember!.copyWith(
      name: name,
      phone: phone.isEmpty ? '' : phone,
      email: '${plateNumber.toLowerCase()}@temp.member',
      updatedAt: DateTime.now(),
      metadata: () {
        final Map<String, dynamic> metadata = <String, dynamic>{
          ..._editingMember!.metadata, // 保持原有的metadata
          'plateNumbers': <String>[plateNumber],
          'vehicle': _selectedVehicleType ?? 'Car',
          'lastEditedAt': DateTime.now().toIso8601String(),
          'editedFromEDC': true,
        };
        final CustomerType customerType = CustomerType.fromString(_selectedCustomerType) ?? CustomerType.b2c;
        CustomerTypeUtils.setCustomerType(metadata, customerType);
        return metadata;
      }(),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isHighlight = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: EDCTextStyles.bodyText.copyWith(
              color: isHighlight ? BPColors.primary : BPColors.neutral,
              fontSize: 14,
              fontWeight: isHighlight ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: EDCTextStyles.bodyText.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: isHighlight ? BPColors.primary : null,
            ),
          ),
        ),
      ],
    );
  }
}
