import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import '../../widgets/safe_scaffold.dart';
import '../../theme/app_theme.dart';
import '../../constants/bp_colors.dart';
import '../../constants/api_constants.dart';
import '../../services/shift_service.dart';
import '../../services/api/api_service.dart';
import '../../models/shift_model.dart';
import '../../models/fuel_transaction.dart';
import '../../models/shift_attendant_model.dart' hide ShiftInfo;
import '../../models/end_shift_validation.dart' as validation;
import '../../widgets/dialogs/end_shift_dialog.dart';
import '../../services/native/sunmi_printer_service.dart';
import '../../services/auth_providers.dart';
import '../../utils/time_utils.dart';
import '../../utils/format_utils.dart';
import '../../widgets/shift_attendant_report_dialog.dart';
import '../../main.dart';

// 统计模式枚举
enum StatisticsMode { byProduct, byEmployee }

// 统计数据模型
class StatisticItem {
  StatisticItem({
    required this.name,
    required this.orders,
    required this.volume,
    required this.amount,
  });
  final String name;
  final int orders;
  final double volume;
  final double amount;
}

class ShiftHomePage extends ConsumerStatefulWidget {
  const ShiftHomePage({super.key});

  @override
  ConsumerState<ShiftHomePage> createState() => _ShiftHomePageState();
}

class _ShiftHomePageState extends ConsumerState<ShiftHomePage> {
  bool _isLoading = false;
  bool _isLoadingStats = false;
  String? _error;
  String? _statsError;
  StatisticsMode _statisticsMode = StatisticsMode.byProduct;

  // 真实班次数据
  ShiftModel? _currentShift;
  ShiftModel? _lastShift;
  ShiftAttendantResponse? _lastShiftAttendantData;
  bool _isLoadingLastShift = false;

  // 真实统计数据
  List<StatisticItem> _productStats = <StatisticItem>[];
  List<StatisticItem> _employeeStats = <StatisticItem>[];
  
  // 班次员工数据
  ShiftAttendantResponse? _shiftAttendantData;
  List<Attendant> _attendants = <Attendant>[];

  // Permission control
  bool _hasManagementAccess = false;
  bool _isCheckingPermissions = true;

  // API服务 - 使用统一的ApiService单例
  final ApiService _apiService = ApiService();
  final SunmiPrinterService _printerService = SunmiPrinterService.instance;

  // 班次状态监听
  StreamSubscription<ShiftInfo?>? _shiftStatusSubscription;

  @override
  void initState() {
    super.initState();
    _checkUserPermissions();
    _initializeServices();
    _subscribeToShiftStatus();
  }

  @override
  void dispose() {
    _shiftStatusSubscription?.cancel();
    super.dispose();
  }

  // Check user permissions
  Future<void> _checkUserPermissions() async {
    try {
      final bool hasAccess = ref.read(hasManagePermissionProvider);

      if (mounted) {
        setState(() {
          _hasManagementAccess = hasAccess;
          _isCheckingPermissions = false;
        });
      }

      debugPrint('🔐 User management permission check result: $_hasManagementAccess');
    } catch (e) {
      debugPrint('❌ Permission check failed: $e');
      if (mounted) {
        setState(() {
          _hasManagementAccess = false;
          _isCheckingPermissions = false;
        });
      }
    }
  }

  // 初始化所有服务
  Future<void> _initializeServices() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // 使用统一的ApiService单例 - 已在main.dart中用正确配置初始化
      // 无需额外配置，直接使用即可

      // 初始化ShiftService - 使用默认配置（会自动使用当前环境）
      await ShiftService().initialize();

      // 加载当前班次
      await _loadCurrentShift();

      // 如果有活跃班次，加载统计数据；否则加载上一个班次
      if (_isShiftActive) {
        await _loadShiftAttendantData();
      } else {
        await _loadLastShift();
      }
    } catch (error) {
      setState(() {
        _error = 'Failed to initialize services: $error';
      });
      debugPrint('Services initialization error: $error');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }



  /// 订阅班次状态变化
  void _subscribeToShiftStatus() {
    final ShiftService shiftService = ShiftService();
    _shiftStatusSubscription = shiftService.shiftStatusStream.listen((ShiftInfo? shift) {
      if (mounted) {
        // 更新当前班次数据
        setState(() {
          _currentShift = shiftService.currentShiftModel;
        });
        
        // 根据班次状态变化重新加载相关数据
        if (shift?.status == ShiftStatus.active && _currentShift != null) {
          // 班次变为活跃状态，加载统计数据
          _loadShiftAttendantData();
        } else if (shift?.status != ShiftStatus.active) {
          // 班次结束，清空统计数据并加载上一班次
          setState(() {
            _productStats = <StatisticItem>[];
            _employeeStats = <StatisticItem>[];
            _shiftAttendantData = null;
            _attendants = <Attendant>[];
          });
          _loadLastShift();
        }
        
        debugPrint('📊 ShiftHomePage: 班次状态更新 - ${shift?.shiftId ?? '无'} (${shift?.status.name ?? '无'})');
      }
    });
  }

  // 加载当前班次数据
  Future<void> _loadCurrentShift() async {
    try {
      await ShiftService().refreshCurrentShift();
      setState(() {
        _currentShift = ShiftService().currentShiftModel;
      });
    } catch (error) {
      debugPrint('Failed to load current shift: $error');
    }
  }

  // 加载班次员工数据
  Future<void> _loadShiftAttendantData() async {
    if (_currentShift == null) return;

    setState(() {
      _isLoadingStats = true;
      _statsError = null;
    });

    try {
      debugPrint('🔍 开始加载班次员工数据，班次ID: ${_currentShift!.id}');

      // 调用新的API获取班次员工数据
      _shiftAttendantData = await _apiService.shiftAttendantApi.getShiftAttendants(_currentShift!.id);
      _attendants = _shiftAttendantData!.data.attendants;

      debugPrint('✅ 班次员工数据加载成功，员工数量: ${_attendants.length}');

      // 从新的数据结构中生成统计数据
      _productStats = _generateProductStatistics(_attendants);
      _employeeStats = _generateEmployeeStatistics(_attendants);

      setState(() {});
    } catch (error) {
      setState(() {
        _statsError = 'Failed to load shift attendant data: $error';
      });
      debugPrint('❌ 班次员工数据加载失败: $error');
    } finally {
      setState(() {
        _isLoadingStats = false;
      });
    }
  }



  // 从员工数据生成按油品分类的统计
  List<StatisticItem> _generateProductStatistics(List<Attendant> attendants) {
    final Map<String, Map<String, dynamic>> productMap = <String, Map<String, dynamic>>{};

    for (final Attendant attendant in attendants) {
      for (final FuelGradeData fuelGrade in attendant.fuelSales.byGrade) {
        final String gradeName = fuelGrade.fuelGrade;

        if (!productMap.containsKey(gradeName)) {
          productMap[gradeName] = <String, dynamic>{
            'orders': 0,
            'volume': 0.0,
            'amount': 0.0,
          };
        }

        productMap[gradeName]!['orders'] += fuelGrade.transactionCount;
        productMap[gradeName]!['volume'] += fuelGrade.salesVolume;
        productMap[gradeName]!['amount'] += fuelGrade.netAmount;
      }
    }

    // 按销售额降序排序，显示最重要的油品在前
    final List<StatisticItem> sortedStats = productMap.entries
        .map((MapEntry<String, Map<String, dynamic>> entry) {
      return StatisticItem(
        name: entry.key,
        orders: entry.value['orders'] as int,
        volume: entry.value['volume'] as double,
        amount: entry.value['amount'] as double,
      );
    }).toList();

    sortedStats.sort((StatisticItem a, StatisticItem b) => b.amount.compareTo(a.amount));
    return sortedStats;
  }

  // 从员工数据生成按员工分类的统计
  List<StatisticItem> _generateEmployeeStatistics(List<Attendant> attendants) {
    final List<StatisticItem> employeeStats = attendants.map((Attendant attendant) {
      return StatisticItem(
        name: attendant.attendantInfo.attendantName,
        orders: attendant.transactionCount,
        volume: attendant.salesVolumeLtr,
        amount: attendant.grandTotal,
      );
    }).toList();

    // 按销售额降序排序，显示业绩最好的员工在前
    employeeStats.sort((StatisticItem a, StatisticItem b) => b.amount.compareTo(a.amount));
    return employeeStats;
  }

  // 格式化时间显示
  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'N/A';
    return TimeUtils.formatDateTime(ref, dateTime, format: 'dd/MM/yyyy HH:mm');
  }



  Widget _buildModeButton(String title, StatisticsMode mode, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _statisticsMode = mode;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? BPColors.primary : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? BPColors.primary : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : Colors.grey[700],
          ),
        ),
      ),
    );
  }

  List<StatisticItem> _getCurrentStats() {
    return _statisticsMode == StatisticsMode.byProduct
        ? _productStats
        : _employeeStats;
  }

  // 开班操作 - 显示确认对话框
  Future<void> _startShift() async {
    // 显示确认对话框
    final bool? confirmed = await _showStartShiftConfirmDialog();

    if (confirmed == true) {
      await _performStartShift();
    }
  }

  // 显示开班确认对话框
  Future<bool?> _showStartShiftConfirmDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: <Widget>[
              Icon(Icons.play_circle_outline,
                  color: BPColors.primary, size: 24),
              SizedBox(width: 8),
              Text('Start Shift'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'Are you sure you want to start a new shift?',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 16),
              Text(
                'This will begin a new shift session and enable transaction processing.',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Start Shift'),
            ),
          ],
        );
      },
    );
  }

  // 执行开班操作
  Future<void> _performStartShift() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final bool success = await ShiftService().startShift(
        operatorId: 'OP001',
        startingCash: 1000000.0,
        notes: 'Shift started from mobile app',
      );

      if (success) {
        // 刷新班次状态
        await _loadCurrentShift();

        // 开班成功后加载统计数据
        if (_isShiftActive) {
          await _loadShiftAttendantData();
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Shift started successfully'),
              backgroundColor: BPColors.success,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to start shift, please try again'),
              backgroundColor: BPColors.error,
            ),
          );
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start shift: $error'),
            backgroundColor: BPColors.error,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 结班操作
  Future<void> _endShift() async {
    // 先检查是否有未处理的交易
    setState(() {
      _isLoading = true;
    });

    try {
      // 检查未处理交易
      final bool hasPendingTransactions = await _hasPendingTransactions();
      final int pendingCount = await _getPendingTransactionCount();

      if (hasPendingTransactions) {
        setState(() {
          _isLoading = false;
        });

        // 显示未处理交易的警告对话框
        _showPendingTransactionsDialog(pendingCount);
        return;
      }

      // 调用班结预检查接口
      final validation.EndShiftValidationResponse? validationData =
          await ShiftService().validateEndShift();

      setState(() {
        _isLoading = false;
      });

      if (validationData == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to validate shift data, please try again'),
              backgroundColor: BPColors.error,
            ),
          );
        }
        return;
      }

      // 显示统一的班结确认弹窗
      _showEndShiftDialog(validationData);
    } catch (error) {
      debugPrint('End shift validation error: $error');

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to validate shift data: $error'),
            backgroundColor: BPColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  /// 显示班结确认弹窗
  void _showEndShiftDialog(validation.EndShiftValidationResponse validationData) {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            return EndShiftDialog(
              validationData: validationData,
              isLoading: _isLoading,
              onConfirm: () async {
                setDialogState(() {
                  _isLoading = true;
                });

                try {
                  await _performEndShift();
                  if (mounted) {
                    Navigator.of(context).pop();
                  }
                } catch (e) {
                  debugPrint('End shift failed: $e');
                } finally {
                  setDialogState(() {
                    _isLoading = false;
                  });
                }
              },
              onCancel: () {
                Navigator.of(context).pop();
              },
            );
          },
        );
      },
    );
  }

  /// 执行班结操作
  Future<void> _performEndShift() async {
    try {
      // 在结班前保存当前班次数据，用于打印
      final ShiftModel? shiftToEnd = _currentShift;

      final bool success = await ShiftService().endShift(endingCash: 0.0);

      if (success) {
        // 先显示成功消息，然后处理其他逻辑
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Shift ended successfully'),
              backgroundColor: BPColors.success,
            ),
          );
        }

        // 刷新班次状态
        await _loadCurrentShift();

        // 结班成功后清空统计数据并加载上一班次数据
        setState(() {
          _productStats = <StatisticItem>[];
          _employeeStats = <StatisticItem>[];
          _shiftAttendantData = null;
          _attendants = <Attendant>[];
        });

        // 在后台异步处理自动打印，不阻塞UI
        if (shiftToEnd != null) {
          // 使用 unawaited 来确保不阻塞UI响应
          _autoPrintShiftReport(shiftToEnd).catchError((Object error) {
            debugPrint('Auto print failed: $error');
            return null;
          });
        }

        // 加载上一班次数据
        await _loadLastShift();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to end shift, please try again'),
              backgroundColor: BPColors.error,
            ),
          );
        }
      }
    } catch (error) {
      debugPrint('Perform end shift error: $error');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to end shift: $error'),
            backgroundColor: BPColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
      rethrow;
    }
  }

  // 显示未处理交易的详细对话框
  void _showPendingTransactionsDialog([int? pendingCount]) {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: <Widget>[
              Icon(Icons.warning, color: BPColors.warning, size: 24),
              SizedBox(width: 8),
              Text('Cannot End Shift'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                pendingCount != null && pendingCount > 0
                    ? 'There ${pendingCount == 1 ? 'is' : 'are'} $pendingCount pending transaction${pendingCount == 1 ? '' : 's'} that must be completed first.'
                    : 'There are pending transactions that must be completed first.',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              const Text(
                'Please complete all pending transactions before ending the shift.',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
          actions: <Widget>[
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
              ),
              child: const Text('OK', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  // 获取当前班次状态
  bool get _isShiftActive => _currentShift?.isActive ?? false;

  // 检查是否有未处理的交易
  Future<bool> _hasPendingTransactions() async {
    if (_currentShift == null) return false;

    try {
      final FuelTransactionQueryParams params = FuelTransactionQueryParams(
        dateFrom: TimeUtils.safeDateFormat(_currentShift!.startTime),
        dateTo: TimeUtils.safeDateFormat(_currentShift!.endTime ?? DateTime.now()),
        stationId: _currentShift!.stationId,
        status: 'pending', // Only query pending transactions
        limit: 1, // Only need to know if there are any, not all
        page: 1,
      );

      final FuelTransactionResponse response =
          await _apiService.fuelTransactionApi.getFuelTransactions(params);
      return response.items.isNotEmpty;
    } catch (error) {
      debugPrint('Failed to check pending transactions: $error');
      return false; // 检查失败时假设没有，让用户尝试结班
    }
  }

  // 获取未处理交易数量
  Future<int> _getPendingTransactionCount() async {
    if (_currentShift == null) return 0;

    try {
      final FuelTransactionQueryParams params = FuelTransactionQueryParams(
        dateFrom: TimeUtils.safeDateFormat(_currentShift!.startTime),
        dateTo: TimeUtils.safeDateFormat(_currentShift!.endTime ?? DateTime.now()),
        stationId: _currentShift!.stationId,
        status: 'pending',
        limit: 100, // 获取更多数据以准确计数
        page: 1,
      );

      final FuelTransactionResponse response =
          await _apiService.fuelTransactionApi.getFuelTransactions(params);
      return response.items.length;
    } catch (error) {
      debugPrint('Failed to get pending transaction count: $error');
      return 0;
    }
  }

  // 自动打印班结报表
  Future<void> _autoPrintShiftReport(ShiftModel shift) async {
    try {
      debugPrint('🖨️ Starting automatic shift report printing...');

      // 使用新的班次员工API获取数据，添加超时控制
      final ShiftAttendantResponse shiftAttendantData = await _apiService.shiftAttendantApi
          .getShiftAttendants(shift.id)
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () => throw Exception('获取班次员工数据超时'),
          );

      // 调用优化后的打印逻辑，同样添加超时控制
      await _printShiftAttendantReportCompact(shiftAttendantData).timeout(
        const Duration(seconds: 60),
        onTimeout: () => throw Exception('打印班结报表超时'),
      );

      debugPrint('✅ Automatic shift report printing completed');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Shift report printed automatically'),
            backgroundColor: BPColors.success,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (error) {
      debugPrint('❌ Auto print shift report failed: $error');
      
      // 如果是超时错误，记录更详细的信息
      if (error.toString().contains('超时')) {
        debugPrint('❌ 打印操作超时，可能是网络或打印机连接问题');
      }
      
      // 不显示错误给用户，因为主要操作（结班）已经成功
      // 但可以在开发环境中显示警告
      if (mounted && kDebugMode) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Auto print failed: ${error.toString()}'),
            backgroundColor: BPColors.warning,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 紧凑格式打印班结报表
  Future<void> _printShiftAttendantReportCompact(ShiftAttendantResponse shiftResponse) async {
    final ShiftAttendantData shiftData = shiftResponse.data;

    await _printerService.enterPrinterBuffer(clearBuffer: true);

    try {
      // 58mm纸张配置：使用小字体和紧凑排版
      const String separator = '--------------------------------'; // 32字符分隔线
      const String doubleSeparator =
          '================================'; // 32字符双分隔线
      const double smallFontSize = 24.0; // 小字体
      const double mediumFontSize = 28.0; // 中等字体

      // === HEADER SECTION ===
      await _printerService.setAlignment(PrintAlignment.center);
      await _printerService.setFontSize(mediumFontSize);
      await _printerService.printText('BP-AKR FUELS RETAIL\n');
      await _printerService.printText('${shiftData.shiftInfo.stationName}\n');
      await _printerService.setFontSize(smallFontSize);
      await _printerService.printText('$doubleSeparator\n');

      // === SHIFT INFO SECTION ===
      await _printerService.setAlignment(PrintAlignment.left);
      await _printerService.setFontSize(smallFontSize);

      await _printerService.printText('SHIFT REPORT\n');
      await _printerService.printText('$separator\n');

      // 班次基本信息（紧凑格式）
      final String shiftNo = shiftData.shiftInfo.shiftNumber;
      await _printerService.printText('Shift: $shiftNo\n');

      final String stationName = shiftData.shiftInfo.stationName;
      await _printerService.printText('Station: $stationName\n');

      // Time information (single line display)
      final String startTime = TimeUtils.formatDateTime(ref, shiftData.shiftInfo.startTime, format: 'dd/MM/yy HH:mm');
      final String endTime = shiftData.shiftInfo.endTime != null
          ? TimeUtils.formatDateTime(ref, shiftData.shiftInfo.endTime!, format: 'dd/MM/yy HH:mm')
          : 'Not ended';
      await _printerService.printText('Time: $startTime - $endTime\n');

      await _printerService.printText('$separator\n');

      // === SHIFT SUMMARY SECTION ===
      await _printerService.setAlignment(PrintAlignment.center);
      await _printerService.printText('SHIFT SUMMARY\n');
      await _printerService.printText('$doubleSeparator\n');
      await _printerService.setAlignment(PrintAlignment.left);

      // 班次汇总信息
      final ShiftSummary shiftSummary = shiftData.shiftSummary;
      await _printerService.printText('Attendants: ${shiftSummary.totalAttendants}\n');
      await _printerService.printText('Transactions: ${shiftSummary.totalTransactions}\n');
      await _printerService.printText('Volume: ${FormatUtils.formatLiters(shiftSummary.totalSalesVolume)} L\n');
      await _printerService.printText('Sales: Rp ${FormatUtils.formatRupiah(shiftSummary.totalSalesAmount)}\n');
      
      // 支付方式明细
      await _printerService.printText('\nPayment Breakdown:\n');
      await _printerService.printText('Cash: Rp ${FormatUtils.formatRupiah(shiftSummary.totalCash)}\n');
      await _printerService.printText('Non-Cash: Rp ${FormatUtils.formatRupiah(shiftSummary.totalNonCash)}\n');
      
      // 具体支付方式（只显示大于0的项目）
      if (shiftSummary.totalPvc > 0) {
        await _printerService.printText('  PVC: Rp ${FormatUtils.formatRupiah(shiftSummary.totalPvc)}\n');
      }
      if (shiftSummary.totalCimb > 0) {
        await _printerService.printText('  CIMB: Rp ${FormatUtils.formatRupiah(shiftSummary.totalCimb)}\n');
      }
      if (shiftSummary.totalBca > 0) {
        await _printerService.printText('  BCA: Rp ${FormatUtils.formatRupiah(shiftSummary.totalBca)}\n');
      }
      if (shiftSummary.totalMandiri > 0) {
        await _printerService.printText('  MANDIRI: Rp ${FormatUtils.formatRupiah(shiftSummary.totalMandiri)}\n');
      }
      if (shiftSummary.totalBri > 0) {
        await _printerService.printText('  BRI: Rp ${FormatUtils.formatRupiah(shiftSummary.totalBri)}\n');
      }
      if (shiftSummary.totalBni > 0) {
        await _printerService.printText('  BNI: Rp ${FormatUtils.formatRupiah(shiftSummary.totalBni)}\n');
      }
      if (shiftSummary.totalVoucher > 0) {
        await _printerService.printText('  VOUCHER: Rp ${FormatUtils.formatRupiah(shiftSummary.totalVoucher)}\n');
      }
      if (shiftSummary.totalB2b > 0) {
        await _printerService.printText('  B2B: Rp ${FormatUtils.formatRupiah(shiftSummary.totalB2b)}\n');
      }
      if (shiftSummary.totalTera > 0) {
        await _printerService.printText('  TERA: Rp ${FormatUtils.formatRupiah(shiftSummary.totalTera)}\n');
      }

      await _printerService.printText('$separator\n');
      await _printerService.printText('GRAND TOTAL: Rp ${FormatUtils.formatRupiah(shiftSummary.grandTotal)}\n');

      await _printerService.printText('$doubleSeparator\n');

      // === ATTENDANTS SECTION ===
      await _printerService.setAlignment(PrintAlignment.center);
      await _printerService.printText('ATTENDANT DETAILS\n');
      await _printerService.printText('$doubleSeparator\n');
      await _printerService.setAlignment(PrintAlignment.left);

      for (final Attendant attendant in shiftData.attendants) {
        final String attendantName = attendant.attendantInfo.attendantName.length > 15 
            ? attendant.attendantInfo.attendantName.substring(0, 15) 
            : attendant.attendantInfo.attendantName;
        await _printerService.printText('$attendantName\n');
        await _printerService.printText('Trans: ${attendant.transactionCount}\n');
        await _printerService.printText('Volume: ${FormatUtils.formatLiters(attendant.salesVolumeLtr)} L\n');
        await _printerService.printText('Amount: Rp ${FormatUtils.formatRupiah(attendant.salesAmountIdr)}\n');
        await _printerService.printText('Total: Rp ${FormatUtils.formatRupiah(attendant.grandTotal)}\n');
        
        // 支付方式明细
        if (attendant.paymentSummary.byMethod.isNotEmpty) {
          await _printerService.printText('Payments:\n');
          for (final PaymentMethodData payment in attendant.paymentSummary.byMethod) {
            final String methodName = payment.paymentMethodName.length > 10
                ? payment.paymentMethodName.substring(0, 10)
                : payment.paymentMethodName.padRight(10);
            await _printerService.printText('  $methodName: Rp ${FormatUtils.formatRupiah(payment.totalAmount)}\n');
          }
        }
        await _printerService.printText('\n');
      }

      await _printerService.printText('$doubleSeparator\n');

      // === FOOTER SECTION ===
      await _printerService.setAlignment(PrintAlignment.center);
      await _printerService.printText('*** SHIFT END ***\n');
      await _printerService.printText('\n');

      final String printTime = TimeUtils.formatNow(ref, format: 'dd/MM/yy HH:mm');
      await _printerService.printText('Print Time: $printTime\n');
      await _printerService.printText('\n');
      await _printerService.printText('Thank you!\n');
      await _printerService.lineWrap(2);

      // Commit print
      await _printerService.exitPrinterBuffer(commit: true);
    } catch (e) {
      // Rollback on error
      await _printerService.exitPrinterBuffer(commit: false);
      rethrow;
    }
  }

  // 加载上一个班次数据
  Future<void> _loadLastShift() async {
    setState(() {
      _isLoadingLastShift = true;
    });

    try {
      // 获取上一个班次 - 使用当前班次的站点ID，如果没有当前班次则使用默认值1
      final int stationId = _currentShift?.stationId ?? 1;
      
      // 使用统一的ApiService单例
      final ShiftListRequest request = ShiftListRequest(
        stationId: stationId,
        status: 'closed',
        limit: 1,
        page: 1,
      );

      debugPrint('🔍 Loading last shift for station: $stationId');

      final ShiftListResponse shiftResponse =
          await _apiService.shiftManagementApi.getShifts(request);

      if (shiftResponse.items.isNotEmpty) {
        _lastShift = shiftResponse.items.first;
        debugPrint('✅ Found last shift: ${_lastShift!.shiftNumber} (ID: ${_lastShift!.id})');
        debugPrint('📋 Last shift details: Station ID: ${_lastShift!.stationId}, Status: ${_lastShift!.status}');
        debugPrint('📅 Last shift times: Start: ${_lastShift!.startTime}, End: ${_lastShift!.endTime}');

        // 使用新的班次员工API获取详细数据
        debugPrint('🔍 Loading shift attendant data for shift ID: ${_lastShift!.id}');
        try {
          _lastShiftAttendantData = await _apiService.shiftAttendantApi.getShiftAttendants(_lastShift!.id);
          debugPrint('✅ Loaded shift attendant data successfully');
          debugPrint('📊 Attendant data: ${_lastShiftAttendantData!.data.attendants.length} attendants');
          debugPrint('📊 Shift summary: ${_lastShiftAttendantData!.data.shiftSummary.totalSalesAmount} total sales');
        } catch (e) {
          debugPrint('❌ Failed to load shift attendant data: $e');
          _lastShiftAttendantData = null;
        }
      } else {
        debugPrint('⚠️ No previous shift found for station: $stationId');
      }

      setState(() {});
    } catch (error) {
      debugPrint('❌ Failed to load last shift: $error');
      // 添加更详细的错误信息
      if (error.toString().contains('404')) {
        debugPrint('💡 Error 404: Shift not found - checking if shift ID is correct');
        if (_lastShift != null) {
          debugPrint('📊 Shift details: ID=${_lastShift!.id}, Number=${_lastShift!.shiftNumber}, Status=${_lastShift!.status}');
        }
      }
    } finally {
      setState(() {
        _isLoadingLastShift = false;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
      appBar: AppBar(
        title: const Text(
          'Shift Management',
          style: EDCTextStyles.appBarTitle,
        ),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 64,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
              ),
            )
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: BPColors.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _error!,
                        style: const TextStyle(
                          fontSize: 16,
                          color: BPColors.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _error = null;
                          });
                          _initializeServices();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: BPColors.primary,
                        ),
                        child: const Text('Retry',
                            style: TextStyle(color: Colors.white)),
                      ),
                    ],
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(14.0),
                  child: Column(
                    children: <Widget>[
                      Expanded(
                        child: ListView(
                          children: <Widget>[
                            // Shift Status Card
                            _buildShiftStatusCard(),

                            const SizedBox(height: 16),

                            // View Previous Shift Report Button
                            _buildViewPreviousShiftButton(),

                            const SizedBox(height: 16),

                            // Statistics Card
                            _buildStatisticsCard(),
                          ],
                        ),
                      ),

                      // Action Button - Only visible to users with management permissions
                      if (_hasManagementAccess)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          child: _isLoading
                              ? const Center(
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        BPColors.primary),
                                  ),
                                )
                              : ElevatedButton.icon(
                                  onPressed:
                                      _isShiftActive ? _endShift : _startShift,
                                  icon: Icon(
                                    _isShiftActive
                                        ? Icons.stop
                                        : Icons.play_arrow,
                                    size: 20,
                                  ),
                                  label: Text(
                                    _isShiftActive ? 'End Shift' : 'Start Shift',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: _isShiftActive
                                        ? BPColors.error
                                        : BPColors.primary,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 14, horizontal: 24),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                        )
                      else if (!_isCheckingPermissions)
                        // Show no permission message
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.lock_outline,
                                  color: Colors.grey[600],
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Management access required',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
    );
  }

  // 构建班次状态卡片
  Widget _buildShiftStatusCard() {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: BPColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.schedule,
                      color: BPColors.primary, size: 22),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      const Text(
                        'Shift Status',
                        style: TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: <Widget>[
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _isShiftActive
                                  ? BPColors.primary
                                  : Colors.grey,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            _isShiftActive ? 'Shift Active' : 'No Active Shift',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: _isShiftActive
                                  ? BPColors.primary
                                  : BPColors.neutral,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Shift Details (only show when active)
            if (_isShiftActive) ...<Widget>[
              const SizedBox(height: 16),
              _buildInfoRow('Shift ID', _currentShift?.shiftNumber ?? 'N/A'),
              const SizedBox(height: 8),
              _buildInfoRow(
                  'Start Time',
                  _currentShift != null
                      ? _formatDateTime(_currentShift!.startTime)
                      : 'N/A'),
            ],
          ],
        ),
      ),
    );
  }

  // 构建统计数据卡片
  Widget _buildStatisticsCard() {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: BPColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.analytics,
                      color: BPColors.primary, size: 22),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Statistics',
                  style: TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Statistics Section - 活跃班次时显示统计，非活跃时显示上一个班次
            if (_isShiftActive) ...<Widget>[
              // Mode Selection Buttons
              Row(
                children: <Widget>[
                  Expanded(
                    child: _buildModeButton(
                      'By Product',
                      StatisticsMode.byProduct,
                      _statisticsMode == StatisticsMode.byProduct,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildModeButton(
                      'By Employee',
                      StatisticsMode.byEmployee,
                      _statisticsMode == StatisticsMode.byEmployee,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 提示信息
              if (_statisticsMode == StatisticsMode.byEmployee && _attendants.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: BPColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: BPColors.primary.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: <Widget>[
                      Icon(
                        Icons.info_outline,
                        color: BPColors.primary,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Tap on any employee to view detailed fuel sales and payment information',
                          style: TextStyle(
                            fontSize: 12,
                            color: BPColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              if (_statisticsMode == StatisticsMode.byEmployee && _attendants.isNotEmpty)
                const SizedBox(height: 16),

              // Statistics List
              if (_isLoadingStats)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(BPColors.primary),
                    ),
                  ),
                )
              else if (_statsError != null)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: BPColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: <Widget>[
                      const Icon(Icons.error_outline,
                          color: BPColors.error, size: 32),
                      const SizedBox(height: 8),
                      const Text(
                        'Failed to load statistics',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: BPColors.error,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _statsError ?? 'Unknown error',
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                                                      _statsError = null;
                        });
                        _loadShiftAttendantData();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: BPColors.primary,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                        ),
                        child: const Text('Retry',
                            style: TextStyle(color: Colors.white)),
                      ),
                    ],
                  ),
                )
              else if (_getCurrentStats().isEmpty)
                Container(
                  padding: const EdgeInsets.all(20),
                  child: const Column(
                    children: <Widget>[
                      Icon(Icons.assessment_outlined,
                          color: BPColors.neutral, size: 48),
                      SizedBox(height: 12),
                      Text(
                        'No transactions yet',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: BPColors.neutral,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Statistics will appear after transactions are completed',
                        style: TextStyle(
                          fontSize: 12,
                          color: BPColors.neutral,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _getCurrentStats().length,
                  itemBuilder: (BuildContext context, int index) {
                    final StatisticItem item = _getCurrentStats()[index];
                    final bool isTopPerformer = index == 0 && _getCurrentStats().length > 1;
                    
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      elevation: isTopPerformer ? 2 : 1,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: isTopPerformer ? Border.all(color: BPColors.primary.withOpacity(0.3)) : null,
                        ),
                        child: ListTile(
                          leading: Stack(
                            children: <Widget>[
                              CircleAvatar(
                                backgroundColor: BPColors.primary.withOpacity(isTopPerformer ? 0.2 : 0.1),
                                child: Text(
                                  item.name.isNotEmpty ? item.name.substring(0, 1).toUpperCase() : '?',
                                  style: TextStyle(
                                    color: BPColors.primary,
                                    fontWeight: FontWeight.bold,
                                    fontSize: isTopPerformer ? 16 : 14,
                                  ),
                                ),
                              ),
                              if (isTopPerformer)
                                Positioned(
                                  top: -2,
                                  right: -2,
                                  child: Container(
                                    width: 16,
                                    height: 16,
                                    decoration: const BoxDecoration(
                                      color: BPColors.warning,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.star,
                                      size: 10,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          title: Row(
                            children: <Widget>[
                              Expanded(
                                child: Text(
                                  _statisticsMode == StatisticsMode.byProduct 
                                      ? 'Grade: ${item.name}' 
                                      : item.name,
                                  style: TextStyle(
                                    fontWeight: isTopPerformer ? FontWeight.w600 : FontWeight.w500,
                                    fontSize: isTopPerformer ? 15 : 14,
                                  ),
                                ),
                              ),
                              if (isTopPerformer)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: BPColors.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Text(
                                    'TOP',
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: BPColors.primary,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              const SizedBox(height: 6),
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: _buildStatItem(
                                      icon: Icons.receipt_outlined,
                                      label: '${item.orders}',
                                      sublabel: 'orders',
                                    ),
                                  ),
                                  Expanded(
                                    child: _buildStatItem(
                                      icon: Icons.local_gas_station,
                                      label: '${FormatUtils.formatLiters(item.volume)}L',
                                      sublabel: 'volume',
                                    ),
                                  ),
                                  Expanded(
                                    child: _buildStatItem(
                                      icon: Icons.payments,
                                      label: 'Rp ${FormatUtils.formatRupiah(item.amount)}',
                                      sublabel: 'sales',
                                      isHighlight: true,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                            ],
                          ),
                          onTap: _statisticsMode == StatisticsMode.byEmployee 
                              ? () => _showEmployeeDetailDialog(index)
                              : null,
                        ),
                      ),
                    );
                  },
                ),
            ] else ...<Widget>[
              // Off Shift State - 显示简化的空状态
              _buildOffShiftEmptyState(),
            ],
          ],
        ),
      ),
    );
  }

  // 构建统计项小部件
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String sublabel,
    bool isHighlight = false,
  }) {
    return Column(
      children: <Widget>[
        Icon(
          icon,
          size: 16,
          color: isHighlight ? BPColors.primary : Colors.grey[600],
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: isHighlight ? BPColors.primary : Colors.black87,
          ),
        ),
        Text(
          sublabel,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  // 格式化金额显示 - 已弃用，使用 FormatUtils.formatRupiah 替代
  String _formatAmount(double amount) {
    return FormatUtils.formatRupiah(amount);
  }

  // 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: BPColors.neutral,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  // 构建 off shift 空状态
  Widget _buildOffShiftEmptyState() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: <Widget>[
          Icon(
            Icons.schedule_outlined,
            size: 72,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Active Shift',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start a new shift to begin tracking transactions and sales',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 构建查看前一个班次报表按钮
  Widget _buildViewPreviousShiftButton() {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: _viewPreviousShiftReport,
        icon: const Icon(Icons.history, size: 20),
        label: const Text(
          'View Previous Shift Report',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: OutlinedButton.styleFrom(
          foregroundColor: BPColors.primary,
          side: const BorderSide(color: BPColors.primary),
          padding: const EdgeInsets.symmetric(vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
  }

  // 查看前一个班次报表
  Future<void> _viewPreviousShiftReport() async {
    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading previous shift report...',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );

    try {
      // 确保当前班次数据是最新的
      if (_currentShift == null) {
        await _loadCurrentShift();
      }

      // 检查是否有有效的上一个班次数据，且站点ID匹配
      final int currentStationId = _currentShift?.stationId ?? 1;
      final bool hasValidLastShift = _lastShift != null && 
          _lastShiftAttendantData != null && 
          _lastShift!.stationId == currentStationId;

      if (hasValidLastShift) {
        Navigator.of(context).pop(); // 关闭加载对话框
        _showPreviousShiftReportDialog();
        return;
      }

      // 重新加载上一个班次数据（使用正确的站点ID）
      await _loadLastShift();
      
      Navigator.of(context).pop(); // 关闭加载对话框

      if (_lastShift != null && _lastShiftAttendantData != null) {
        _showPreviousShiftReportDialog();
      } else {
        _showNoPreviousShiftDialog();
      }
    } catch (error) {
      Navigator.of(context).pop(); // 关闭加载对话框
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load previous shift report: ${error.toString()}'),
            backgroundColor: BPColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 显示前一个班次报表对话框
  void _showPreviousShiftReportDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ShiftAttendantReportDialog(
          shiftAttendantData: _lastShiftAttendantData!,
          shiftNumber: _lastShift!.shiftNumber,
        );
      },
    );
  }

  // 显示无前一个班次数据的对话框
  void _showNoPreviousShiftDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: <Widget>[
              Icon(Icons.info_outline, color: BPColors.primary),
              SizedBox(width: 12),
              Text('No Previous Shift'),
            ],
          ),
          content: const Text(
            'No previous shift data available. Start a new shift to generate reports.',
            style: TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(
                  color: BPColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // 显示员工详细信息对话框
  void _showEmployeeDetailDialog(int index) {
    if (index >= _attendants.length) return;

    final Attendant attendant = _attendants[index];
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog.fullscreen(
          child: Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              title: Row(
                children: <Widget>[
                  CircleAvatar(
                    backgroundColor: Colors.white,
                    radius: 18,
                    child: Text(
                      attendant.attendantInfo.attendantName.isNotEmpty
                          ? attendant.attendantInfo.attendantName.substring(0, 1).toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: BPColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          attendant.attendantInfo.attendantName,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'Staff ID: ${attendant.attendantInfo.staffCardId}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              backgroundColor: BPColors.primary,
              foregroundColor: Colors.white,
              elevation: 0,
              toolbarHeight: 64,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // 快速统计卡片
                  _buildCompactOverviewCards(attendant),
                  
                  const SizedBox(height: 16),
                  
                  // 油品销售明细
                  _buildCompactFuelSalesSection(attendant),
                  
                  const SizedBox(height: 16),
                  
                  // 支付方式统计
                  _buildCompactPaymentMethodsSection(attendant),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // 构建员工总览统计部分
  Widget _buildEmployeeOverviewSection(Attendant attendant) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'Sales Overview',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: <Widget>[
              Row(
                children: <Widget>[
                  Expanded(
                    child: _buildOverviewItem(
                      icon: Icons.receipt_outlined,
                      label: 'Transactions',
                      value: '${attendant.transactionCount}',
                      color: BPColors.primary,
                    ),
                  ),
                  Expanded(
                    child: _buildOverviewItem(
                      icon: Icons.local_gas_station,
                      label: 'Volume',
                      value: '${FormatUtils.formatLiters(attendant.salesVolumeLtr)}L',
                      color: BPColors.success,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: <Widget>[
                  Expanded(
                    child: _buildOverviewItem(
                      icon: Icons.payments,
                      label: 'Total Sales',
                      value: 'Rp ${FormatUtils.formatRupiah(attendant.grandTotal)}',
                      color: BPColors.warning,
                    ),
                  ),
                  Expanded(
                    child: _buildOverviewItem(
                      icon: Icons.shopping_cart,
                      label: 'Non-Fuel',
                      value: 'Rp ${FormatUtils.formatRupiah(attendant.dryIncome)}',
                      color: BPColors.neutral,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建总览项目
  Widget _buildOverviewItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: <Widget>[
        Icon(icon, size: 24, color: color),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  // 构建油品销售部分
  Widget _buildFuelSalesSection(Attendant attendant) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'Fuel Sales by Grade',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        if (attendant.fuelSales.byGrade.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: const Center(
              child: Text(
                'No fuel sales data',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ),
          )
        else
          ...attendant.fuelSales.byGrade.map((FuelGradeData fuelGrade) {
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    fuelGrade.fuelGrade,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: _buildFuelStatItem(
                          'Volume',
                          '${FormatUtils.formatLiters(fuelGrade.salesVolume)}L',
                        ),
                      ),
                      Expanded(
                        child: _buildFuelStatItem(
                          'Transactions',
                          '${fuelGrade.transactionCount}',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: _buildFuelStatItem(
                          'Net Amount',
                          'Rp ${FormatUtils.formatRupiah(fuelGrade.netAmount)}',
                        ),
                      ),
                      Expanded(
                        child: _buildFuelStatItem(
                          'Unit Price',
                          'Rp ${FormatUtils.formatRupiah(fuelGrade.unitPrice)}',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
      ],
    );
  }

  // 构建油品统计项目
  Widget _buildFuelStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  // 构建支付方式部分
  Widget _buildPaymentMethodsSection(Attendant attendant) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'Payment Methods',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        if (attendant.paymentSummary.byMethod.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: const Center(
              child: Text(
                'No payment data',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ),
          )
        else
          ...attendant.paymentSummary.byMethod.map((PaymentMethodData method) {
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: <Widget>[
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _getPaymentMethodColor(method.paymentMethod).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getPaymentMethodIcon(method.paymentMethod),
                      color: _getPaymentMethodColor(method.paymentMethod),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          method.paymentMethodName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${method.transactionCount} transactions',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        'Rp ${FormatUtils.formatRupiah(method.totalAmount)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${method.percentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
      ],
    );
  }

  // 获取支付方式图标
  IconData _getPaymentMethodIcon(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'mandiri':
      case 'bca':
      case 'bni':
      case 'bri':
      case 'cimb':
        return Icons.credit_card;
      case 'voucher':
        return Icons.card_giftcard;
      case 'b2b':
        return Icons.business;
      case 'tera':
        return Icons.account_balance_wallet;
      default:
        return Icons.payment;
    }
  }

  // 获取支付方式颜色
  Color _getPaymentMethodColor(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return Colors.green;
      case 'mandiri':
        return Colors.blue;
      case 'bca':
        return Colors.blue[700]!;
      case 'bni':
        return Colors.orange;
      case 'bri':
        return Colors.blue[900]!;
      case 'cimb':
        return Colors.red;
      case 'voucher':
        return Colors.purple;
      case 'b2b':
        return Colors.teal;
      case 'tera':
        return Colors.indigo;
      default:
        return BPColors.primary;
    }
  }

  // 获取英文支付方式名称
  String _getEnglishPaymentMethodName(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
      case 'tunai':
        return 'CASH';
      case 'mandiri':
        return 'MANDIRI';
      case 'bca':
        return 'BCA';
      case 'bni':
        return 'BNI';
      case 'bri':
        return 'BRI';
      case 'cimb':
        return 'CIMB';
      case 'voucher':
        return 'VOUCHER';
      case 'b2b':
        return 'B2B';
      case 'tera':
        return 'TERA';
      case 'debit':
        return 'DEBIT CARD';
      case 'credit':
        return 'CREDIT CARD';
      default:
        return paymentMethod.toUpperCase();
    }
  }

  // 构建小票样式行
  Widget _buildReceiptRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: isTotal ? 15 : 14,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                color: isTotal ? Colors.black87 : Colors.black87,
                fontFamily: 'Courier',
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              value,
              style: TextStyle(
                fontSize: isTotal ? 15 : 14,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                color: isTotal ? Colors.black87 : Colors.black87,
                fontFamily: 'Courier',
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  // 构建小票样式概览卡片
  Widget _buildCompactOverviewCards(Attendant attendant) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 小票样式标题
          Center(
            child: Text(
              'PERFORMANCE SUMMARY',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
                letterSpacing: 1.2,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Center(
            child: Text(
              '================================',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontFamily: 'Courier',
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 小票样式数据行
          _buildReceiptRow('Transactions', '${attendant.transactionCount}'),
          _buildReceiptRow('Volume', '${FormatUtils.formatLiters(attendant.salesVolumeLtr)} L'),
          _buildReceiptRow('Sales Amount', 'Rp ${FormatUtils.formatRupiah(attendant.salesAmountIdr)}'),
          _buildReceiptRow('Dry Income', 'Rp ${FormatUtils.formatRupiah(attendant.dryIncome)}'),
          
          const SizedBox(height: 8),
          Center(
            child: Text(
              '--------------------------------',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontFamily: 'Courier',
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          // 总计行
          _buildReceiptRow('GRAND TOTAL', 'Rp ${FormatUtils.formatRupiah(attendant.grandTotal)}', isTotal: true),
        ],
      ),
    );
  }

  // 构建紧凑型统计卡片
  Widget _buildCompactStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    bool isHighlight = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isHighlight ? color.withOpacity(0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isHighlight ? color.withOpacity(0.3) : Colors.grey.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 6),
              Expanded(
                child:                   Text(
                    label,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isHighlight ? color : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  // 构建小票样式油品销售部分
  Widget _buildCompactFuelSalesSection(Attendant attendant) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 小票样式标题
          Center(
            child: Text(
              'FUEL SALES BY GRADE',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
                letterSpacing: 1.2,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Center(
            child: Text(
              '================================',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontFamily: 'Courier',
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 油品销售列表
          if (attendant.fuelSales.byGrade.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  'No fuel sales data',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            )
          else
            ...attendant.fuelSales.byGrade.map((FuelGradeData fuelGrade) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // 油品名称
                  Center(
                    child: Text(
                      fuelGrade.fuelGrade,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // 小票样式数据行
                  _buildReceiptRow('  Trans', '${fuelGrade.transactionCount}'),
                  _buildReceiptRow('  Volume', '${FormatUtils.formatLiters(fuelGrade.salesVolume)} L'),
                  _buildReceiptRow('  Amount', 'Rp ${FormatUtils.formatRupiah(fuelGrade.netAmount)}'),
                  _buildReceiptRow('  Price', 'Rp ${FormatUtils.formatRupiah(fuelGrade.unitPrice)}'),
                  
                  const SizedBox(height: 12),
                  Center(
                    child: Text(
                      '- - - - - - - - - - - - - - - -',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                        fontFamily: 'Courier',
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                ],
              );
            }).toList(),
        ],
      ),
    );
  }

  // 构建紧凑型油品统计项
  Widget _buildCompactFuelStat(String label, String value, IconData icon) {
    return Row(
      children: <Widget>[
        Icon(icon, size: 12, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建小票样式支付方式部分
  Widget _buildCompactPaymentMethodsSection(Attendant attendant) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 小票样式标题
          Center(
            child: Text(
              'PAYMENT METHODS',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
                letterSpacing: 1.2,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Center(
            child: Text(
              '================================',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontFamily: 'Courier',
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 支付方式列表
          if (attendant.paymentSummary.byMethod.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  'No payment data',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            )
          else
            ...attendant.paymentSummary.byMethod.map((PaymentMethodData method) {
              // 获取英文支付方式名称
              final String englishMethodName = _getEnglishPaymentMethodName(method.paymentMethod);
              
              return Column(
                children: <Widget>[
                  _buildReceiptRow(
                    englishMethodName,
                    'Rp ${FormatUtils.formatRupiah(method.totalAmount)}',
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Text(
                          '${method.transactionCount} trans',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                            fontFamily: 'Courier',
                          ),
                        ),
                        Text(
                          '${method.percentage.toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                            fontFamily: 'Courier',
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              );
            }).toList(),
        ],
      ),
    );
  }
}
