import 'package:flutter/material.dart';
import '../../constants/bp_colors.dart';
import '../../theme/app_theme.dart';
import '../../widgets/safe_scaffold.dart';
import '../../widgets/edc_card.dart';
import '../../models/shift_model.dart';
import '../../services/api/api_service.dart';
// import 'shift_report_page.dart'; // 已删除

class ShiftHistoryPage extends StatefulWidget {
  const ShiftHistoryPage({super.key});

  @override
  State<ShiftHistoryPage> createState() => _ShiftHistoryPageState();
}

class _ShiftHistoryPageState extends State<ShiftHistoryPage> {
  bool _isLoading = false;
  List<ShiftModel> _shifts = <ShiftModel>[];
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMore = false;

  // API服务 - 使用统一的ApiService单例
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    _loadShifts();
  }

  Future<void> _loadShifts({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _shifts.clear();
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      debugPrint('📋 ShiftHistoryPage: 开始加载班次历史');
      debugPrint('  - 刷新模式: $refresh');
      debugPrint('  - 当前页码: $_currentPage');
      debugPrint('  - 已有记录数: ${_shifts.length}');

      // 使用统一的ApiService单例 - 已在main.dart中用正确配置初始化
      final ShiftListRequest request = ShiftListRequest(
        stationId: 1, // 固定站点ID
        page: _currentPage,
        limit: 10,
        sortBy: 'created_at',
        sortDir: 'desc', // 最新的在前
      );

      debugPrint('  - 请求参数: ${request.toString()}');

      final ShiftListResponse response =
          await _apiService.shiftManagementApi.getShifts(request);

      // 详细记录响应数据
      _logShiftListData(response, refresh);

      setState(() {
        if (refresh) {
          _shifts = response.items;
        } else {
          _shifts.addAll(response.items);
        }
        _totalPages = response.totalPages;
        _hasMore = _currentPage < _totalPages;
        _isLoading = false;
      });

      debugPrint('✅ ShiftHistoryPage: 班次历史加载成功');
    } catch (e, stackTrace) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
      debugPrint('❌ ShiftHistoryPage: 加载班次历史失败');
      debugPrint('  - 错误类型: ${e.runtimeType}');
      debugPrint('  - 错误消息: $e');
      debugPrint('  - 堆栈跟踪: $stackTrace');
    }
  }

  /// 记录班次列表数据的完整性日志
  void _logShiftListData(ShiftListResponse response, bool refresh) {
    try {
      debugPrint('📊 班次列表数据完整性检查:');
      debugPrint('  - 总记录数: ${response.total}');
      debugPrint('  - 当前页: ${response.page}');
      debugPrint('  - 每页数量: ${response.pageSize}');
      debugPrint('  - 总页数: ${response.totalPages}');
      debugPrint('  - 本次返回: ${response.items.length} 条记录');

      if (response.items.isEmpty) {
        debugPrint('  ⚠️ 返回的班次列表为空');
      } else {
        debugPrint('  📋 班次记录详情:');
        for (int i = 0; i < response.items.length; i++) {
          try {
            final ShiftModel shift = response.items[i];
            debugPrint('    [$i] ID: ${shift.id}, 班次号: ${shift.shiftNumber}');
            debugPrint(
                '        状态: ${shift.status ?? 'null'}, 活跃: ${shift.isActive}, 已关闭: ${shift.isClosed}');
            debugPrint('        开始: ${shift.startTime}');
            debugPrint('        结束: ${shift.endTime ?? 'NULL'}');
            debugPrint('        持续: ${shift.duration}小时');

            // 检查关键字段是否为空
            if (shift.shiftNumber.isEmpty) {
              debugPrint('        ⚠️ 班次号为空');
            }
            if (shift.status?.isEmpty ?? true) {
              debugPrint('        ⚠️ 状态为空');
            }
          } catch (e) {
            debugPrint('    ❌ 班次记录[$i]解析失败: $e');
          }
        }
      }

      debugPrint('✅ 班次列表数据检查完成');
    } catch (e, stackTrace) {
      debugPrint('❌ 记录班次列表数据时出错: $e');
      debugPrint('❌ 堆栈跟踪: $stackTrace');
    }
  }

  Future<void> _loadMore() async {
    if (_hasMore && !_isLoading) {
      _currentPage++;
      await _loadShifts();
    }
  }

  void _viewShiftReport(ShiftModel shift) {
    // 班次报告页面已删除，可以导航到其他页面或显示消息
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Shift report feature is under development'),
        backgroundColor: BPColors.warning,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
      appBar: AppBar(
        title: Row(
          children: <Widget>[
            Image.asset(
              'assets/images/bp_logo.png',
              height: 36,
              fit: BoxFit.contain,
              errorBuilder:
                  (BuildContext context, Object error, StackTrace? stackTrace) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'BP',
                    style: TextStyle(
                      color: BPColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(width: 12),
            const Text(
              'Shift History',
              style: EDCTextStyles.appBarTitle,
            ),
          ],
        ),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 64,
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _loadShifts(refresh: true),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading && _shifts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
            ),
            SizedBox(height: 16),
            Text(
              'Loading shift history...',
              style: EDCTextStyles.bodyText,
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null && _shifts.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.error_outline,
              size: 64,
              color: BPColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load shift history',
              style: EDCTextStyles.subTitle.copyWith(
                color: BPColors.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: EDCTextStyles.bodyText,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _loadShifts(refresh: true),
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_shifts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.history,
              size: 64,
              color: BPColors.neutral,
            ),
            const SizedBox(height: 16),
            Text(
              'No shift history found',
              style: EDCTextStyles.subTitle.copyWith(
                color: BPColors.neutral,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Shift records will appear here after shifts are completed',
              style: EDCTextStyles.bodyText,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadShifts(refresh: true),
      child: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: _shifts.length + (_hasMore ? 1 : 0),
        itemBuilder: (BuildContext context, int index) {
          if (index == _shifts.length) {
            // 加载更多指示器
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(
                child: _isLoading
                    ? const CircularProgressIndicator(
                        valueColor:
                            AlwaysStoppedAnimation<Color>(BPColors.primary),
                      )
                    : ElevatedButton(
                        onPressed: _loadMore,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: BPColors.primary,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Load More'),
                      ),
              ),
            );
          }

          final ShiftModel shift = _shifts[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildShiftCard(shift),
          );
        },
      ),
    );
  }

  Widget _buildShiftCard(ShiftModel shift) {
    final Color statusColor = shift.isActive
        ? BPColors.success
        : shift.isClosed
            ? BPColors.primary
            : BPColors.neutral;

    return EDCCard(
      child: InkWell(
        onTap: () => _viewShiftReport(shift),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      shift.isActive
                          ? Icons.play_circle
                          : shift.isClosed
                              ? Icons.check_circle
                              : Icons.pause_circle,
                      color: statusColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          shift.shiftNumber,
                          style: EDCTextStyles.subTitle,
                        ),
                        Text(
                          'Status: ${shift.status?.toUpperCase() ?? 'UNKNOWN'}',
                          style: EDCTextStyles.hintText.copyWith(
                            color: statusColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: BPColors.neutral,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: <Widget>[
                  Expanded(
                    child: _buildInfoItem(
                      'Start Time',
                      shift.formattedStartTime,
                      Icons.schedule,
                    ),
                  ),
                  if (shift.endTime != null)
                    Expanded(
                      child: _buildInfoItem(
                        'End Time',
                        shift.formattedEndTime!,
                        Icons.schedule_send,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              _buildInfoItem(
                'Duration',
                shift.formattedDuration,
                Icons.timer,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: <Widget>[
        Icon(
          icon,
          size: 16,
          color: BPColors.neutral,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                label,
                style: EDCTextStyles.hintText,
              ),
              Text(
                value,
                style: EDCTextStyles.bodyText.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
