import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:math';

enum ErrorType {
  presetFailure,
  memberIdentificationFailure,
  fuelingFailure,
  paymentFailure,
  networkIssue,
  systemError
}

class ErrorHandlingPage extends StatefulWidget {
  const ErrorHandlingPage({
    super.key,
    required this.errorType,
    required this.errorMessage,
    this.onRetry,
    this.onCancel,
    this.onAlternativeSolution,
  });
  final ErrorType errorType;
  final String errorMessage;
  final Function? onRetry;
  final Function? onCancel;
  final Function? onAlternativeSolution;

  /// 生成随机错误类型
  static ErrorType getRandomErrorType() {
    final Random random = Random();
    const List<ErrorType> values = ErrorType.values;
    return values[random.nextInt(values.length)];
  }

  /// 根据错误类型生成演示用错误信息
  static String getDemoErrorMessage(ErrorType errorType) {
    switch (errorType) {
      case ErrorType.presetFailure:
        return '无法设置加油枪3号，设备返回错误代码：E-1045';
      case ErrorType.memberIdentificationFailure:
        return '会员卡无法识别，请检查卡片是否有效或尝试其他识别方式';
      case ErrorType.fuelingFailure:
        return '加油过程中出现异常中断，加油机返回错误：油枪未挂好';
      case ErrorType.paymentFailure:
        return '支付处理失败，银行返回错误：交易超时(Timeout-30s)';
      case ErrorType.networkIssue:
        return '网络连接中断，无法连接到服务器，请检查网络设置';
      case ErrorType.systemError:
        return '系统内部错误，错误代码：0xF0A1，请联系技术支持';
    }
  }

  @override
  State<ErrorHandlingPage> createState() => _ErrorHandlingPageState();
}

class _ErrorHandlingPageState extends State<ErrorHandlingPage> {
  bool _isLoading = false;
  bool _isExpanded = false;

  // 根据错误类型获取图标
  IconData _getErrorIcon() {
    switch (widget.errorType) {
      case ErrorType.presetFailure:
        return Icons.local_gas_station;
      case ErrorType.memberIdentificationFailure:
        return Icons.person_off;
      case ErrorType.fuelingFailure:
        return Icons.warning_amber;
      case ErrorType.paymentFailure:
        return Icons.payment;
      case ErrorType.networkIssue:
        return Icons.wifi_off;
      case ErrorType.systemError:
        return Icons.error;
    }
  }

  // 根据错误类型获取标题
  String _getErrorTitle() {
    switch (widget.errorType) {
      case ErrorType.presetFailure:
        return '预设失败';
      case ErrorType.memberIdentificationFailure:
        return '会员识别失败';
      case ErrorType.fuelingFailure:
        return '加油异常';
      case ErrorType.paymentFailure:
        return '支付异常';
      case ErrorType.networkIssue:
        return '网络连接问题';
      case ErrorType.systemError:
        return '系统错误';
    }
  }

  // 根据错误类型获取建议操作
  String _getRecommendedAction() {
    switch (widget.errorType) {
      case ErrorType.presetFailure:
        return '请检查加油机状态或尝试选择其他加油枪';
      case ErrorType.memberIdentificationFailure:
        return '请尝试其他会员识别方式或确认会员信息';
      case ErrorType.fuelingFailure:
        return '请联系加油站工作人员检查加油机';
      case ErrorType.paymentFailure:
        return '请尝试其他支付方式或稍后再试';
      case ErrorType.networkIssue:
        return '请检查网络连接或使用离线模式';
      case ErrorType.systemError:
        return '请联系技术支持或重启应用';
    }
  }

  // 构建错误图标和标题
  Widget _buildErrorHeader() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Icon(
          _getErrorIcon(),
          size: 80,
          color: Colors.red[700],
        ),
        const SizedBox(height: 24),
        Text(
          _getErrorTitle(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.red[700],
          ),
        ),
      ],
    );
  }

  // 构建错误详情
  Widget _buildErrorDetails() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          const Text(
            '错误详情:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.errorMessage,
            style: const TextStyle(
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '建议操作:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getRecommendedAction(),
            style: const TextStyle(
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // 构建操作按钮
  Widget _buildActionButtons() {
    final List<Widget> buttons = <Widget>[];

    // 添加重试按钮（如果有回调）
    if (widget.onRetry != null) {
      buttons.add(
        Expanded(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.refresh),
            label: const Text('重试'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: Colors.blue[700],
            ),
            onPressed: _isLoading ? null : _handleRetry,
          ),
        ),
      );
    }

    // 添加取消按钮（所有错误类型都可以取消）
    if (buttons.isNotEmpty) {
      buttons.add(const SizedBox(width: 16));
    }

    buttons.add(
      Expanded(
        child: OutlinedButton.icon(
          icon: const Icon(Icons.cancel),
          label: const Text('取消'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          onPressed: _isLoading ? null : _handleCancel,
        ),
      ),
    );

    final List<Widget> columnChildren = <Widget>[Row(children: buttons)];

    // 添加替代解决方案按钮（如果有回调）
    if (widget.onAlternativeSolution != null) {
      columnChildren.add(const SizedBox(height: 16));
      columnChildren.add(
        ElevatedButton.icon(
          icon: const Icon(Icons.alt_route),
          label: Text(_getAlternativeSolutionText()),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: Colors.green[700],
            minimumSize: const Size(double.infinity, 50),
          ),
          onPressed: _isLoading ? null : _handleAlternativeSolution,
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: columnChildren,
    );
  }

  // 处理重试按钮
  Future<void> _handleRetry() async {
    if (widget.onRetry == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onRetry!();
    } catch (e) {
      // 处理错误
      debugPrint('重试过程中发生错误: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 处理取消按钮
  void _handleCancel() {
    if (widget.onCancel != null) {
      widget.onCancel!();
    } else {
      context.pop();
    }
  }

  // 处理替代解决方案按钮
  void _handleAlternativeSolution() {
    if (widget.onAlternativeSolution != null) {
      widget.onAlternativeSolution!();
    }
  }

  // 获取替代解决方案的文本
  String _getAlternativeSolutionText() {
    switch (widget.errorType) {
      case ErrorType.presetFailure:
        return '选择其他加油枪';
      case ErrorType.memberIdentificationFailure:
        return '使用其他识别方式';
      case ErrorType.fuelingFailure:
        return '手动输入加油数据';
      case ErrorType.paymentFailure:
        return '使用其他支付方式';
      case ErrorType.networkIssue:
        return '切换到离线模式';
      case ErrorType.systemError:
        return '返回主页';
    }
  }

  // 构建错误日志信息
  Widget _buildErrorLog() {
    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: ExpansionTile(
        initiallyExpanded: _isExpanded,
        onExpansionChanged: (bool value) {
          // 使用安全的方式更新状态
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _isExpanded = value;
              });
            }
          });
        },
        title: const Text(
          '错误日志（技术信息）',
          style: TextStyle(
            fontSize: 14,
          ),
        ),
        children: <Widget>[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  '错误类型: ${widget.errorType}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '错误信息: ${widget.errorMessage}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '时间戳: ${DateTime.now()}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  '设备信息: EDC-A200 / Android 10',
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 使用Builder来确保context正确
    return Builder(builder: (BuildContext context) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('错误处理'),
          backgroundColor: Colors.red[700],
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        // 避免使用Stack，简化布局结构
        body: SafeArea(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      _buildErrorHeader(),
                      _buildErrorDetails(),
                      const SizedBox(height: 24),
                      _buildActionButtons(),
                      const SizedBox(height: 24),
                      _buildErrorLog(),
                    ],
                  ),
                ),
        ),
      );
    });
  }
}

// 错误提示对话框
class ErrorDialog extends StatelessWidget {
  const ErrorDialog({
    super.key,
    required this.errorType,
    required this.errorMessage,
    this.onRetry,
    this.onCancel,
    this.onAlternativeSolution,
  });
  final ErrorType errorType;
  final String errorMessage;
  final Function? onRetry;
  final Function? onCancel;
  final Function? onAlternativeSolution;

  @override
  Widget build(BuildContext context) {
    // 根据错误类型获取图标
    IconData getErrorIcon() {
      switch (errorType) {
        case ErrorType.presetFailure:
          return Icons.local_gas_station;
        case ErrorType.memberIdentificationFailure:
          return Icons.person_off;
        case ErrorType.fuelingFailure:
          return Icons.warning_amber;
        case ErrorType.paymentFailure:
          return Icons.payment;
        case ErrorType.networkIssue:
          return Icons.wifi_off;
        case ErrorType.systemError:
          return Icons.error;
      }
    }

    // 根据错误类型获取标题
    String getErrorTitle() {
      switch (errorType) {
        case ErrorType.presetFailure:
          return '预设失败';
        case ErrorType.memberIdentificationFailure:
          return '会员识别失败';
        case ErrorType.fuelingFailure:
          return '加油异常';
        case ErrorType.paymentFailure:
          return '支付异常';
        case ErrorType.networkIssue:
          return '网络连接问题';
        case ErrorType.systemError:
          return '系统错误';
      }
    }

    return AlertDialog(
      title: Row(
        children: <Widget>[
          Icon(
            getErrorIcon(),
            color: Colors.red[700],
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              getErrorTitle(),
              style: TextStyle(
                color: Colors.red[700],
              ),
            ),
          ),
        ],
      ),
      content: Text(errorMessage),
      actions: <Widget>[
        if (onCancel != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onCancel!();
            },
            child: const Text('取消'),
          ),
        if (onAlternativeSolution != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onAlternativeSolution!();
            },
            child: const Text('替代方案'),
          ),
        if (onRetry != null)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRetry!();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[700],
            ),
            child: const Text('重试'),
          ),
      ],
    );
  }
}

// 错误处理服务
class ErrorHandlingService {
  // 显示错误页面
  static void showErrorPage(
    BuildContext context, {
    required ErrorType errorType,
    required String errorMessage,
    Function? onRetry,
    Function? onCancel,
    Function? onAlternativeSolution,
  }) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (BuildContext context) => ErrorHandlingPage(
          errorType: errorType,
          errorMessage: errorMessage,
          onRetry: onRetry,
          onCancel: onCancel,
          onAlternativeSolution: onAlternativeSolution,
        ),
      ),
    );
  }

  // 显示错误对话框
  static void showErrorDialog(
    BuildContext context, {
    required ErrorType errorType,
    required String errorMessage,
    Function? onRetry,
    Function? onCancel,
    Function? onAlternativeSolution,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) => ErrorDialog(
        errorType: errorType,
        errorMessage: errorMessage,
        onRetry: onRetry,
        onCancel: onCancel,
        onAlternativeSolution: onAlternativeSolution,
      ),
    );
  }

  // 记录错误日志
  static void logError(ErrorType errorType, String errorMessage) {
    // 实际应用中，这里应该实现日志记录逻辑
    debugPrint(
        'ERROR [${DateTime.now()}] Type: $errorType, Message: $errorMessage');
  }
}
