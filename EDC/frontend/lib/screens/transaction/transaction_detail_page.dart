import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// 支付方式枚举
enum PaymentMethod { cash, bankCard, eWallet, memberAccount }

class TransactionDetailPage extends StatelessWidget {
  const TransactionDetailPage({super.key, required this.transactionId});
  final String transactionId;

  @override
  Widget build(BuildContext context) {
    // 模拟获取交易详情数据
    final Map<String, dynamic>? transaction =
        _getTransactionData(transactionId);

    return Scaffold(
      appBar: AppBar(
        title: const Text('交易详情'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: '打印小票',
            onPressed: () =>
                context.push('/printing/preview', extra: transactionId),
          ),
        ],
      ),
      body: transaction == null
          ? _buildErrorView(context)
          : _buildTransactionDetail(context, transaction),
    );
  }

  Widget _buildErrorView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            '未找到交易数据',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            '交易ID: $transactionId',
            style: const TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.go('/transaction'),
            child: const Text('返回交易管理'),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionDetail(
      BuildContext context, Map<String, dynamic> transaction) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 交易状态卡片
          _buildStatusCard(context, transaction),
          const SizedBox(height: 24),

          // 交易基本信息部分
          _buildSectionTitle(context, '交易信息'),
          _buildInfoCard(context, <Widget>[
            _buildInfoRow('交易编号', transaction['id']),
            _buildInfoRow('交易时间', transaction['time']),
            _buildInfoRow('交易类型', transaction['type']),
            _buildInfoRow('交易员工', transaction['employee']),
            _buildInfoRow('油枪号', transaction['nozzle']),
          ]),
          const SizedBox(height: 24),

          // 商品信息部分
          _buildSectionTitle(context, '商品信息'),
          _buildProductCard(context, transaction['product']),
          const SizedBox(height: 24),

          // 支付信息部分
          _buildSectionTitle(context, '支付信息'),
          _buildPaymentCard(context, transaction['payment']),
          const SizedBox(height: 24),

          // 会员信息部分
          if (transaction['member'] != null) ...<Widget>[
            _buildSectionTitle(context, '会员信息'),
            _buildMemberCard(context, transaction['member']),
            const SizedBox(height: 24),
          ],

          // 操作按钮组
          _buildActionButtons(context, transaction),
        ],
      ),
    );
  }

  Widget _buildStatusCard(
      BuildContext context, Map<String, dynamic> transaction) {
    final Map<String, MaterialColor> statusColors = <String, MaterialColor>{
      '已完成': Colors.green,
      '处理中': Colors.blue,
      '已取消': Colors.red,
      '已退款': Colors.orange,
    };

    final Color statusColor =
        statusColors[transaction['status']] ?? Colors.grey;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getStatusIcon(transaction['status']),
                color: statusColor,
                size: 32,
              ),
            ),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  transaction['status'],
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '总金额: ${transaction['payment']['totalAmount']} IDR',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildInfoCard(BuildContext context, List<Widget> children) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: children,
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(BuildContext context, Map<String, dynamic> product) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            Row(
              children: <Widget>[
                Icon(
                  Icons.local_gas_station,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  product['name'],
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                _buildProductDetail('单价', 'Rp${product['unitPrice']}/L'),
                _buildProductDetail('数量', '${product['quantity']} L'),
                _buildProductDetail('金额', 'Rp${product['amount']}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductDetail(String label, String value) {
    return Column(
      children: <Widget>[
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentCard(BuildContext context, Map<String, dynamic> payment) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            _buildInfoRow('支付方式', payment['method']),
            if (payment['cardNumber'] != null)
              _buildInfoRow('卡号', payment['cardNumber']),
            if (payment['transactionRef'] != null)
              _buildInfoRow('交易参考号', payment['transactionRef']),
            const Divider(),
            _buildInfoRow('商品金额', 'Rp${payment['productAmount']}'),
            if (payment['discount'] != null &&
                double.parse(payment['discount']) > 0)
              _buildInfoRow('折扣优惠', '-Rp${payment['discount']}'),
            if (payment['tax'] != null)
              _buildInfoRow('税额', 'Rp${payment['tax']}'),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text(
                  '总计',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  'Rp${payment['totalAmount']}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberCard(BuildContext context, Map<String, dynamic> member) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            Row(
              children: <Widget>[
                CircleAvatar(
                  backgroundColor:
                      Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  child: Text(
                    (member['name'] != null && member['name'].isNotEmpty) 
                        ? member['name'].substring(0, 1).toUpperCase()
                        : '?',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      member['name'],
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      member['id'],
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                OutlinedButton(
                  onPressed: () {
                    debugPrint(
                        '🎯 TransactionDetailPage: 点击查看会员按钮，跳转到 member_registration_page');
                    context.push('/member/register');
                  },
                  child: const Text('查看'),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow('会员等级', member['level']),
            _buildInfoRow('本次积分', '+${member['pointsEarned']}'),
            _buildInfoRow('总积分', member['totalPoints']),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
      BuildContext context, Map<String, dynamic> transaction) {
    // 从交易数据中获取应付金额
    final String? totalAmountString = transaction['payment']?['totalAmount'];
    final double totalAmount =
        double.tryParse(totalAmountString ?? '0.0') ?? 0.0;
    // 检查交易状态，只有在特定状态下才显示支付按钮 (例如 '待支付')
    // 这里为了演示，我们暂时假设所有 '未完成' 状态都需要支付
    final bool canPay = transaction['status'] != '已完成' &&
        transaction['status'] != '已取消' &&
        transaction['status'] != '已退款';

    return Column(
      // 使用 Column 容纳多行按钮
      children: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () =>
                    context.push('/printing/preview', extra: transaction['id']),
                icon: const Icon(Icons.print),
                label: const Text('打印小票'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // 添加到收藏夹或标记
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('已添加到收藏夹')),
                  );
                },
                icon: const Icon(Icons.star_outline),
                label: const Text('收藏'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        // 只有在可以支付的状态下才显示支付按钮
        if (canPay) ...<Widget>[
          const SizedBox(height: 16), // 添加一些间距
          SizedBox(
            // 让按钮宽度充满
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                // 导航到支付页面，并传递金额
                // 我们将整个 transaction 数据传递过去，支付页面可以选择需要的信息
                context.push('/payment', extra: transaction);
              },
              icon: const Icon(Icons.payment),
              label: const Text('确认交易并支付'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 14),
                textStyle: const TextStyle(fontSize: 16),
                backgroundColor: Theme.of(context).colorScheme.primary, // 主题色
                foregroundColor: Colors.white, // 白色文字
              ),
            ),
          ),
        ],
      ],
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case '已完成':
        return Icons.check_circle;
      case '处理中':
        return Icons.pending;
      case '已取消':
        return Icons.cancel;
      case '已退款':
        return Icons.replay;
      default:
        return Icons.receipt;
    }
  }

  // 模拟获取交易数据
  Map<String, dynamic>? _getTransactionData(String transactionId) {
    // 在实际应用中，这里应该从API或本地数据库获取数据

    // 测试用例：如果ID是TX12345，返回null表示未找到数据
    if (transactionId == 'TX12345') {
      return null;
    }

    // 返回模拟数据
    return <String, dynamic>{
      'id': transactionId,
      'time': '2024-05-01 15:30:45',
      'type': '燃油交易',
      'status': '已完成',
      'employee': '张师傅',
      'nozzle': '3号枪',
      'product': <String, String>{
        'name': '92# 汽油',
        'unitPrice': '7.25',
        'quantity': '36.5',
        'amount': '264.63',
      },
      'payment': <String, String>{
        'method': '银行卡',
        'cardNumber': '****6789',
        'transactionRef': 'PAY234567890',
        'productAmount': '264.63',
        'discount': '10.00',
        'tax': '0.00',
        'totalAmount': '254.63',
      },
      'member': <String, String>{
        'id': 'M10086',
        'name': '李四',
        'level': '金卡会员',
        'pointsEarned': '25',
        'totalPoints': '1,280',
      },
    };
  }
}
