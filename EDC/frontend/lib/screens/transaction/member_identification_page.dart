import 'package:flutter/material.dart';

enum IdentificationMethod { qrCode, phoneNumber, licensePlate }

class MemberIdentificationPage extends StatefulWidget {
  const MemberIdentificationPage({super.key});

  @override
  State<MemberIdentificationPage> createState() =>
      _MemberIdentificationPageState();
}

class _MemberIdentificationPageState extends State<MemberIdentificationPage>
    with SingleTickerProviderStateMixin {
  // 当前选择的识别方式
  IdentificationMethod _currentMethod = IdentificationMethod.qrCode;

  // Tab控制器
  late final TabController _tabController;

  // 文本输入控制器
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _licensePlateController = TextEditingController();

  // 是否显示注册表单
  bool _showRegistrationForm = false;

  // 注册表单控制器
  final TextEditingController _regPhoneController = TextEditingController();
  final TextEditingController _regLicensePlateController =
      TextEditingController();

  // 车辆类型选项
  final List<String> _vehicleTypes = <String>['轿车', 'SUV', '货车', '摩托车', '其他'];
  String? _selectedVehicleType;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _currentMethod = IdentificationMethod.values[_tabController.index];
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _phoneController.dispose();
    _licensePlateController.dispose();
    _regPhoneController.dispose();
    _regLicensePlateController.dispose();
    super.dispose();
  }

  // 构建扫码识别页
  Widget _buildQRScanTab() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Container(
          margin: const EdgeInsets.all(16),
          width: double.infinity,
          height: 300,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.blue, width: 2),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Icon(
                Icons.qr_code_scanner,
                size: 80,
                color: Colors.blue[700],
              ),
              const SizedBox(height: 16),
              const Text(
                '对准会员卡二维码',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '自动扫描识别会员信息',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          icon: const Icon(Icons.camera_alt),
          label: const Text('打开相机扫描'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            backgroundColor: Colors.blue[700],
          ),
          onPressed: () {
            // 这里实际应该调用相机扫描功能
          },
        ),
        const SizedBox(height: 16),
        TextButton(
          onPressed: () {
            setState(() {
              _showRegistrationForm = true;
            });
          },
          child: const Text('非会员？快速注册'),
        ),
      ],
    );
  }

  // 构建手机号识别页
  Widget _buildPhoneTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: <Widget>[
          const SizedBox(height: 16),
          TextField(
            controller: _phoneController,
            decoration: InputDecoration(
              labelText: '会员手机号',
              hintText: '请输入会员注册的手机号',
              prefixIcon: const Icon(Icons.phone_android),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // 这里实际应该执行手机号查询逻辑
              if (_phoneController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入手机号')),
                );
                return;
              }

              // 模拟无匹配会员的情况
              setState(() {
                _showRegistrationForm = true;
                _regPhoneController.text = _phoneController.text;
              });
            },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              minimumSize: const Size(double.infinity, 50),
              backgroundColor: Colors.blue[700],
            ),
            child: const Text('查询会员信息'),
          ),
          const SizedBox(height: 16),
          const Text(
            '最近查询记录',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          // 模拟的最近查询记录
          _buildRecentSearchItem(
              '138****1234', DateTime.now().subtract(const Duration(hours: 2))),
          _buildRecentSearchItem(
              '159****5678', DateTime.now().subtract(const Duration(days: 1))),
          _buildRecentSearchItem(
              '177****9012', DateTime.now().subtract(const Duration(days: 2))),
        ],
      ),
    );
  }

  // 构建车牌号识别页
  Widget _buildLicensePlateTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: <Widget>[
          const SizedBox(height: 16),
          TextField(
            controller: _licensePlateController,
            decoration: InputDecoration(
              labelText: '车牌号',
              hintText: '请输入车牌号',
              prefixIcon: const Icon(Icons.directions_car),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            textCapitalization: TextCapitalization.characters,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // 这里实际应该执行车牌号查询逻辑
              if (_licensePlateController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入车牌号')),
                );
                return;
              }

              // 模拟无匹配会员的情况
              setState(() {
                _showRegistrationForm = true;
                _regLicensePlateController.text = _licensePlateController.text;
              });
            },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              minimumSize: const Size(double.infinity, 50),
              backgroundColor: Colors.blue[700],
            ),
            child: const Text('查询会员信息'),
          ),
          const SizedBox(height: 16),
          const Text(
            '最近查询记录',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          // 模拟的最近查询记录
          _buildRecentSearchItem(
              'B 12345 XYZ', DateTime.now().subtract(const Duration(hours: 5))),
          _buildRecentSearchItem(
              'B 67890 ABC', DateTime.now().subtract(const Duration(days: 1))),
          _buildRecentSearchItem(
              'B 54321 DEF', DateTime.now().subtract(const Duration(days: 3))),
        ],
      ),
    );
  }

  // 构建最近查询记录项
  Widget _buildRecentSearchItem(String value, DateTime time) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(value),
        subtitle: Text('查询时间: ${_formatDateTime(time)}'),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          if (_currentMethod == IdentificationMethod.phoneNumber) {
            _phoneController.text = value;
          } else if (_currentMethod == IdentificationMethod.licensePlate) {
            _licensePlateController.text = value;
          }
        },
      ),
    );
  }

  // 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 构建非会员注册表单
  Widget _buildRegistrationForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              const Text(
                '快速注册新会员',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _showRegistrationForm = false;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _regPhoneController,
            decoration: InputDecoration(
              labelText: '手机号（必填）',
              hintText: '请输入手机号',
              prefixIcon: const Icon(Icons.phone_android),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _regLicensePlateController,
            decoration: InputDecoration(
              labelText: '车牌号（选填）',
              hintText: '请输入车牌号',
              prefixIcon: const Icon(Icons.directions_car),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            textCapitalization: TextCapitalization.characters,
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: '车辆类型（选填）',
              prefixIcon: const Icon(Icons.local_shipping),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            value: _selectedVehicleType,
            hint: const Text('请选择车辆类型'),
            items: _vehicleTypes
                .map((String type) => DropdownMenuItem(
                      value: type,
                      child: Text(type),
                    ))
                .toList(),
            onChanged: (String? value) {
              setState(() {
                _selectedVehicleType = value;
              });
            },
          ),
          const SizedBox(height: 16),
          CheckboxListTile(
            title: const Text('我已阅读并同意《会员服务协议》'),
            value: true,
            onChanged: (bool? value) {},
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // 这里实际应该执行会员注册逻辑
              if (_regPhoneController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请输入手机号')),
                );
                return;
              }

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('会员注册成功！'),
                  backgroundColor: Colors.green,
                ),
              );

              setState(() {
                _showRegistrationForm = false;
              });

              // 注册成功后跳转到会员信息页面
              Navigator.of(context).pop(true);
            },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              minimumSize: const Size(double.infinity, 50),
              backgroundColor: Colors.blue[700],
            ),
            child: const Text('完成注册'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('会员识别'),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const <Widget>[
            Tab(text: '扫码识别', icon: Icon(Icons.qr_code)),
            Tab(text: '手机号识别', icon: Icon(Icons.phone_android)),
            Tab(text: '车牌号识别', icon: Icon(Icons.directions_car)),
          ],
        ),
      ),
      body: Stack(
        children: <Widget>[
          TabBarView(
            controller: _tabController,
            children: <Widget>[
              _buildQRScanTab(),
              _buildPhoneTab(),
              _buildLicensePlateTab(),
            ],
          ),
          if (_showRegistrationForm)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: _buildRegistrationForm(),
            ),
        ],
      ),
    );
  }
}
