import 'package:edc_app/services/api/order_api.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../models/order.dart';
import '../../services/api/api_service.dart';
import '../../services/native/sunmi_printer_service.dart';
import '../../services/auto_print_service.dart';
import '../../constants/bp_colors.dart';
import '../../theme/app_theme.dart';
import '../../utils/time_utils.dart';

class ReceiptPreviewPage extends ConsumerStatefulWidget {
  // 修改为接收订单ID

  const ReceiptPreviewPage({super.key, required this.orderId});
  final String orderId;

  @override
  ConsumerState<ReceiptPreviewPage> createState() => _ReceiptPreviewPageState();
}

class _ReceiptPreviewPageState extends ConsumerState<ReceiptPreviewPage> {
  bool _isLoading = true;
  bool _isPrinting = false;
  Order? _order;
  String? _errorMessage;

  final SunmiPrinterService _printerService = SunmiPrinterService.instance;
  final NumberFormat _currencyFormat = NumberFormat('#,##0.00', 'id_ID');

  @override
  void initState() {
    super.initState();
    _loadOrderData();
  }

  Future<void> _loadOrderData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 使用真实的OrderApi获取订单数据
      final OrderApi orderApi = ApiService().orderApi;
      final Order order = await orderApi.getOrderDetail(widget.orderId);

      if (mounted) {
        setState(() {
          _order = order;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load order data: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _printReceipt() async {
    if (_order == null || _isPrinting) return;

    try {
      setState(() => _isPrinting = true);

      // 检查打印机状态
      final PrinterStatus status = await _printerService.getPrinterStatus();
      if (status != PrinterStatus.normal) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Printer status not normal: ${status.name}'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // 使用统一的打印方法，设置为重打模式
      await AutoPrintService.printOrderReceipt(
        _order!,
        isReprint: true, // 从打印预览页面打印设置为重打模式
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Receipt printed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Print failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isPrinting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Receipt Preview'),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        actions: <Widget>[
          if (_order != null)
            IconButton(
              icon: _isPrinting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.print),
              onPressed: _isPrinting ? null : _printReceipt,
              tooltip: 'Print Receipt',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            CircularProgressIndicator(color: BPColors.primary),
            SizedBox(height: 16),
            Text('Loading order data...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadOrderData,
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (_order == null) {
      return const Center(
        child: Text('Order data not available'),
      );
    }

    return _buildReceiptPreview();
  }

  Widget _buildReceiptPreview() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          // Header
          Text(
            'BP-AKR FUELS RETAIL',
            style: EDCTextStyles.mainTitle.copyWith(
              fontWeight: FontWeight.bold,
              color: BPColors.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'PSPBU ${_order!.stationId}',
            style: EDCTextStyles.subTitle,
          ),
          const Text(
            'Thank you for your visit',
            style: EDCTextStyles.bodyText,
          ),
          const SizedBox(height: 16),
          const Divider(thickness: 1),

          // Transaction Info
          _buildInfoRow('Order No', _order!.orderId),
          _buildInfoRow('Date', TimeUtils.formatDateTime(ref, _order!.createTime, format: 'dd/MM/yyyy HH:mm:ss')),
          if (_order!.customerName?.isNotEmpty == true)
            _buildInfoRow('Customer', _order!.customerName!),
          if (_order!.customerPhone?.isNotEmpty == true)
            _buildInfoRow('Phone', _order!.customerPhone!),
          if (_order!.vehicleType?.isNotEmpty == true)
            _buildInfoRow('Vehicle Type', _order!.vehicleType!),
          if (_order!.licensePlate?.isNotEmpty == true)
            _buildInfoRow('License Plate', _order!.licensePlate!),
          _buildInfoRow('Cashier', _order!.operatorId),

          const SizedBox(height: 16),
          const Divider(thickness: 1),

          // Items Header
          _buildItemsHeader(),
          const Divider(thickness: 1),

          // Items
          ..._buildReceiptItems(),

          const Divider(thickness: 2, color: BPColors.primary),
          const SizedBox(height: 12),

          // Subtotal Section
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              children: <Widget>[
                if (_order!.discountAmount > 0) ...<Widget>[
                  _buildTotalRow('Subtotal',
                      'Rp ${_currencyFormat.format(_order!.amount)}'),
                  const SizedBox(height: 4),
                  _buildTotalRow(
                    'Discount',
                    '- Rp ${_currencyFormat.format(_order!.discountAmount)}',
                    isDiscount: true,
                  ),
                  const Divider(thickness: 1, color: Colors.grey),
                  const SizedBox(height: 8),
                ],
                _buildTotalRow(
                  'TOTAL',
                  'Rp ${_currencyFormat.format(_order!.finalAmount)}',
                  isBold: true,
                  isLarge: true,
                ),
                const SizedBox(height: 8),
                const Divider(thickness: 1, color: Colors.grey),
                const SizedBox(height: 4),
                _buildTotalRow(
                    'Paid', 'Rp ${_currencyFormat.format(_order!.paidAmount)}'),
              ],
            ),
          ),

          const SizedBox(height: 16),
          _buildInfoRow('Payment Method',
              AutoPrintService.getPaymentMethodText(_order!.paymentMethod)),
          _buildInfoRow(
              'Status', AutoPrintService.getStatusText(_order!.status)),

          const SizedBox(height: 24),
          const Divider(thickness: 1),

          // Footer
          const Text(
            'Please keep this receipt as\nvalid proof of payment',
            style: EDCTextStyles.hintText,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            '*** THANK YOU ***',
            style: EDCTextStyles.subTitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Text(
            'Drive safely and have a great day',
            style: EDCTextStyles.hintText,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(label, style: EDCTextStyles.bodyText),
          Text(value, style: EDCTextStyles.bodyText),
        ],
      ),
    );
  }

  Widget _buildItemsHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
      decoration: BoxDecoration(
        color: BPColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: BPColors.primary.withOpacity(0.3)),
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 5,
            child: Text(
              'ITEM',
              style: EDCTextStyles.bodyText.copyWith(
                fontWeight: FontWeight.bold,
                color: BPColors.primary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'QTY',
              style: EDCTextStyles.bodyText.copyWith(
                fontWeight: FontWeight.bold,
                color: BPColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              'AMT',
              style: EDCTextStyles.bodyText.copyWith(
                fontWeight: FontWeight.bold,
                color: BPColors.primary,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildReceiptItems() {
    return _order!.items.map((Map<String, dynamic> item) {
      final String productName = item['product_name'] as String? ?? 'Product';
      final double quantity = (item['quantity'] as num?)?.toDouble() ?? 0.0;
      final double unitPrice = (item['unit_price'] as num?)?.toDouble() ?? 0.0;
      final double totalPrice =
          (item['total_price'] as num?)?.toDouble() ?? 0.0;
      final String pumpId = item['pump_id'] as String? ?? _order!.pumpId;
      final String nozzleId = item['nozzle_id'] as String? ?? '';

      // Truncate product name if too long for table display
      final String displayName = productName.length > 20
          ? '${productName.substring(0, 17)}...'
          : productName;

      return Container(
        margin: const EdgeInsets.symmetric(vertical: 2.0),
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(color: Colors.grey[200]!, width: 1),
          ),
        ),
        child: Column(
          children: <Widget>[
            // Single row with all information side-by-side
            Row(
              children: <Widget>[
                // Product Name
                Expanded(
                  flex: 5,
                  child: Text(
                    displayName,
                    style: EDCTextStyles.bodyText.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                // Quantity
                Expanded(
                  flex: 3,
                  child: Text(
                    '${quantity.toStringAsFixed(2)} L',
                    style: EDCTextStyles.bodyText,
                    textAlign: TextAlign.center,
                  ),
                ),
                // Total Amount
                Expanded(
                  flex: 4,
                  child: Text(
                    _currencyFormat.format(totalPrice),
                    style: EDCTextStyles.bodyText.copyWith(
                      fontWeight: FontWeight.bold,
                      color: BPColors.primary,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
            // Unit price info (as subtitle row)
            const SizedBox(height: 4),
            Row(
              children: <Widget>[
                Expanded(
                  flex: 5,
                  child: Text(
                    '@${_currencyFormat.format(unitPrice)}',
                    style: EDCTextStyles.hintText.copyWith(
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(flex: 7, child: Container()), // Spacer
              ],
            ),
            // Pump and Nozzle info (as separate subtitle rows)
            if (pumpId.isNotEmpty || nozzleId.isNotEmpty) ...<Widget>[
              const SizedBox(height: 4),
              Row(
                children: <Widget>[
                  if (pumpId.isNotEmpty)
                    Text(
                      'Pump: $pumpId',
                      style: EDCTextStyles.hintText.copyWith(
                        fontSize: 14,
                      ),
                    ),
                  if (pumpId.isNotEmpty && nozzleId.isNotEmpty)
                    Text(
                      '  •  ',
                      style: EDCTextStyles.hintText.copyWith(
                        fontSize: 14,
                      ),
                    ),
                  if (nozzleId.isNotEmpty)
                    Text(
                      'Nozzle: $nozzleId',
                      style: EDCTextStyles.hintText.copyWith(
                        fontSize: 14,
                      ),
                    ),
                ],
              ),
            ],
          ],
        ),
      );
    }).toList();
  }

  Widget _buildTotalRow(String label, String value,
      {bool isBold = false, bool isLarge = false, bool isDiscount = false}) {
    final TextStyle textStyle = EDCTextStyles.bodyText.copyWith(
      fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
      fontSize: isLarge ? 20 : 18,
      color: isLarge ? BPColors.primary : (isDiscount ? Colors.red[600] : null),
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(label, style: textStyle),
          Text(value, style: textStyle),
        ],
      ),
    );
  }
}
