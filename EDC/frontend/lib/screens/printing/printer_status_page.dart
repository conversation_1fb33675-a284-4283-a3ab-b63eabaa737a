import 'package:flutter/material.dart';
import 'dart:math' as math;

class PrinterStatusPage extends StatefulWidget {
  const PrinterStatusPage({super.key});

  @override
  State<PrinterStatusPage> createState() => _PrinterStatusPageState();
}

class _PrinterStatusPageState extends State<PrinterStatusPage> {
  // 预设数据
  final Map<String, dynamic> _printerInfo = <String, dynamic>{
    'name': 'BP-Printer-A123',
    'type': 'Bluetooth',
    'address': '00:11:22:33:44:55',
    'model': 'EPSON TM-T82',
    'isConnected': true,
    'batteryLevel': 85,
    'hasPaper': true,
    'isCoverOpen': false,
    'isHeadOverheated': false,
    'firmwareVersion': '2.3.1',
    'lastConnected': '2023-06-15 08:30',
    'printedCount': 238,
    'errorCount': 3,
    'status': 'Online'
  };

  // 常见问题列表
  final List<Map<String, dynamic>> _commonIssues = <Map<String, dynamic>>[
    <String, dynamic>{
      'title': '打印机缺纸',
      'description': '请检查打印机是否已装入打印纸，并正确关闭打印机盖子。',
      'icon': Icons.description_outlined,
      'steps': <String>[
        '打开打印机上盖',
        '检查打印纸是否已用完',
        '如需要，装入新的热敏打印纸卷',
        '确保纸张正确放置，纸张前端露出一小段',
        '关闭打印机上盖，确保完全卡紧'
      ]
    },
    <String, dynamic>{
      'title': '打印机无法连接',
      'description': '请检查打印机是否已开机，并在蓝牙设置中确认已配对。',
      'icon': Icons.bluetooth_disabled,
      'steps': <String>[
        '确保打印机电源已开启（电源指示灯应亮起）',
        '检查打印机是否有足够的电量',
        '确认手机蓝牙功能已开启',
        '在系统设置中删除已有的打印机配对，然后重新配对',
        '将打印机重启（关闭后再开启）',
        '将EDC设备与打印机放置在较近距离后重试'
      ]
    },
    <String, dynamic>{
      'title': '打印质量差',
      'description': '可能是打印浓度设置过低或打印头需要清洁。',
      'icon': Icons.blur_linear,
      'steps': <String>[
        '在打印设置中增加打印浓度',
        '检查使用的打印纸质量是否符合要求',
        '关闭打印机并等待打印头冷却',
        '使用专用清洁笔或沾有少量酒精的棉签轻轻清洁打印头',
        '确保打印头完全干燥后再开启打印机'
      ]
    },
    <String, dynamic>{
      'title': '打印机自动断开连接',
      'description': '可能是电池电量低、信号干扰或省电模式导致的问题。',
      'icon': Icons.power_off,
      'steps': <String>[
        '连接充电器为打印机充电',
        '检查打印机与EDC设备之间的距离',
        '排除周围可能的信号干扰源',
        '在打印机设置中延长自动关机时间',
        '将打印机固件更新至最新版本'
      ]
    },
  ];

  bool _isRefreshing = false;
  bool _isRunningDiagnostic = false;

  @override
  Widget build(BuildContext context) {
    final bool isConnected = _printerInfo['isConnected'] as bool;
    final bool hasPaper = _printerInfo['hasPaper'] as bool;
    final bool isCoverOpen = _printerInfo['isCoverOpen'] as bool;
    final bool isHeadOverheated = _printerInfo['isHeadOverheated'] as bool;
    final int batteryLevel = _printerInfo['batteryLevel'] as int;

    return Scaffold(
      appBar: AppBar(
        title: const Text('打印机状态'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshStatus,
            tooltip: '刷新状态',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // 打印机基本信息卡片
            Card(
              elevation: 3,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              _printerInfo['name'] as String,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _printerInfo['model'] as String,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: isConnected
                                ? Colors.green.withOpacity(0.2)
                                : Colors.red.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: <Widget>[
                              Icon(
                                isConnected ? Icons.check_circle : Icons.error,
                                color: isConnected ? Colors.green : Colors.red,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                isConnected ? '已连接' : '未连接',
                                style: TextStyle(
                                  color:
                                      isConnected ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const Divider(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: <Widget>[
                        _buildStatusIndicator(
                          '电池',
                          batteryLevel > 20
                              ? Icons.battery_charging_full
                              : Icons.battery_alert,
                          batteryLevel > 20 ? Colors.green : Colors.orange,
                          '$batteryLevel%',
                        ),
                        _buildStatusIndicator(
                          '纸张',
                          hasPaper ? Icons.check_circle : Icons.cancel,
                          hasPaper ? Colors.green : Colors.red,
                          hasPaper ? '正常' : '缺纸',
                        ),
                        _buildStatusIndicator(
                          '打印机盖',
                          isCoverOpen ? Icons.lock_open : Icons.lock,
                          isCoverOpen ? Colors.red : Colors.green,
                          isCoverOpen ? '已打开' : '已关闭',
                        ),
                        _buildStatusIndicator(
                          '打印头',
                          isHeadOverheated
                              ? Icons.whatshot
                              : Icons.check_circle,
                          isHeadOverheated ? Colors.red : Colors.green,
                          isHeadOverheated ? '过热' : '正常',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: isConnected ? _runDiagnostic : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 44),
                      ),
                      icon: _isRunningDiagnostic
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Icon(Icons.healing),
                      label: Text(_isRunningDiagnostic ? '诊断中...' : '运行诊断'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // 打印机详细信息
            Text(
              '详细信息',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Card(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: <Widget>[
                    _buildInfoRow('连接类型', _printerInfo['type'] as String),
                    _buildInfoRow('设备地址', _printerInfo['address'] as String),
                    _buildInfoRow(
                        '固件版本', _printerInfo['firmwareVersion'] as String),
                    _buildInfoRow(
                        '上次连接', _printerInfo['lastConnected'] as String),
                    _buildInfoRow('已打印数', '${_printerInfo['printedCount']} 张'),
                    _buildInfoRow('错误次数', '${_printerInfo['errorCount']} 次'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // 常见问题与解决方案
            Text(
              '常见问题排查',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _commonIssues.length,
              itemBuilder: (BuildContext context, int index) {
                final Map<String, dynamic> issue = _commonIssues[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 6),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                  child: ExpansionTile(
                    leading: Icon(
                      issue['icon'] as IconData,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    title: Text(
                      issue['title'] as String,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                      issue['description'] as String,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            const Text(
                              '解决步骤:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...List.generate(
                              (issue['steps'] as List).length,
                              (int i) => Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Text('${i + 1}. ',
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold)),
                                    Expanded(
                                        child:
                                            Text(issue['steps'][i] as String)),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(
      String label, IconData icon, Color color, String value) {
    return Column(
      children: <Widget>[
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _refreshStatus() {
    // 实际应用中，这里应该调用API获取最新状态
    setState(() {
      _isRefreshing = true;
    });

    // 模拟刷新延迟
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        // 模拟随机更新数据
        _printerInfo['batteryLevel'] = math.max(
            0,
            math.min(
                100,
                (_printerInfo['batteryLevel'] as int) +
                    (math.Random().nextInt(5) - 2)));
        _isRefreshing = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('打印机状态已更新'),
          duration: Duration(seconds: 2),
        ),
      );
    });
  }

  void _runDiagnostic() {
    // 实际应用中，这里应该调用打印机诊断API
    setState(() {
      _isRunningDiagnostic = true;
    });

    // 模拟诊断延迟
    Future.delayed(const Duration(seconds: 3), () {
      setState(() {
        _isRunningDiagnostic = false;
      });

      showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: const Text('诊断结果'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text('打印机自检完成。未发现硬件问题。'),
              SizedBox(height: 8),
              Text('• 打印头温度: 正常'),
              Text('• 通信接口: 正常'),
              Text('• 纸张传感器: 正常'),
              Text('• 切刀状态: 正常'),
            ],
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('关闭'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // 这里可以导航到打印测试页面
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('打印测试页'),
            ),
          ],
        ),
      );
    });
  }
}
