import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../widgets/safe_scaffold.dart';
import 'package:edc_app/services/native/sunmi_printer_service.dart';
import 'package:logging/logging.dart';

class PrintingHomePage extends StatefulWidget {
  const PrintingHomePage({super.key});

  @override
  State<PrintingHomePage> createState() => _PrintingHomePageState();
}

class _PrintingHomePageState extends State<PrintingHomePage> {
  final Logger _logger = Logger('PrintingHomePage');
  final SunmiPrinterService _printerService = SunmiPrinterService.instance;
  bool _isPrinting = false;

  Future<void> _handleTestPrint() async {
    if (_isPrinting) return;

    setState(() {
      _isPrinting = true;
    });

    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('正在准备测试打印...'), duration: Duration(seconds: 2)),
    );

    try {
      _logger.info('Checking printer connection...');
      final bool isConnected = await _printerService.isPrinterConnected();
      if (!mounted) return;
      if (!isConnected) {
        _logger.warning('Printer is not connected.');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('打印机未连接，请检查设备。'), backgroundColor: Colors.orange),
        );
        setState(() => _isPrinting = false);
        return;
      }
      _logger.info('Printer connected. Checking status...');

      final PrinterStatus status = await _printerService.getPrinterStatus();
      if (!mounted) return;
      if (status != PrinterStatus.normal) {
        _logger.warning('Printer not ready. Status: $status');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('打印机状态异常: ${status.name} (${status.code})，无法打印。'),
              backgroundColor: Colors.orange),
        );
        setState(() => _isPrinting = false);
        return;
      }
      _logger
          .info('Printer status normal. Attempting to print demo receipt...');

      await _printerService.printDemoReceipt();
      if (!mounted) return;

      _logger.info('Test print command sequence completed successfully.');
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('测试小票已发送至打印机。'), backgroundColor: Colors.green),
      );
    } on PrinterException catch (e) {
      if (!mounted) return;
      _logger.severe(
          'PrinterException during test print: ${e.code} - ${e.message}', e);
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('打印错误: ${e.message ?? e.code}'),
            backgroundColor: Colors.red),
      );
    } catch (e, stackTrace) {
      if (!mounted) return;
      _logger.severe('Unexpected error during test print', e, stackTrace);
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发生未知错误: $e'), backgroundColor: Colors.red),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isPrinting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        context.go('/');
      },
      child: SafeScaffold(
        appBar: AppBar(
          title: const Text('打印功能'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go('/'),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildPrinterStatusCard(context),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  '打印功能',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: <Widget>[
                  _buildFeatureCard(
                    context,
                    '小票重打',
                    Icons.receipt_long,
                    Colors.blue,
                    () => context.push('/printing/receipt_reprint'),
                  ),
                  _buildFeatureCard(
                    context,
                    '打印设置',
                    Icons.settings,
                    Colors.orange,
                    () => context.push('/printing/settings'),
                  ),
                  _buildFeatureCard(
                    context,
                    '打印状态',
                    Icons.info_outline,
                    Colors.green,
                    () => context.push('/printing/status'),
                  ),
                  _buildFeatureCard(
                    context,
                    '特殊打印',
                    Icons.print,
                    Colors.purple,
                    () => _showSpecialPrintOptions(context),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  '最近打印任务',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              _buildRecentPrintTasks(context),
              if (_isPrinting)
                const CircularProgressIndicator()
              else
                ElevatedButton.icon(
                  icon: const Icon(Icons.print_outlined),
                  label: const Text('测试打印'),
                  style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 30, vertical: 15),
                      textStyle: const TextStyle(fontSize: 16)),
                  onPressed: _isPrinting ? null : _handleTestPrint,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrinterStatusCard(BuildContext context) {
    const bool isConnected = true;
    const String printerName = 'BP-Printer-A123';
    const int batteryLevel = 85;
    const bool hasPaper = true;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  '打印机状态',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Icon(
                  isConnected ? Icons.check_circle : Icons.error,
                  color: isConnected ? Colors.green : Colors.red,
                )
              ],
            ),
            const Divider(),
            Row(
              children: <Widget>[
                Icon(Icons.print, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                const Text(printerName, style: TextStyle(fontSize: 16)),
              ],
            ),
            const SizedBox(height: 8),
            const Row(
              children: <Widget>[
                Icon(Icons.battery_charging_full, color: Colors.green),
                SizedBox(width: 8),
                Text('电量: $batteryLevel%', style: TextStyle(fontSize: 14)),
                SizedBox(width: 16),
                Icon(Icons.description,
                    color: hasPaper ? Colors.green : Colors.red),
                SizedBox(width: 8),
                Text(hasPaper ? '纸张正常' : '缺纸',
                    style: TextStyle(
                      fontSize: 14,
                      color: hasPaper ? Colors.black : Colors.red,
                    )),
              ],
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () => context.push('/printing/status'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 36),
              ),
              child: const Text('查看详情'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Icon(icon, size: 48, color: color),
              const SizedBox(height: 12),
              Text(
                title,
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentPrintTasks(BuildContext context) {
    final List<Map<String, dynamic>> recentTasks = <Map<String, dynamic>>[
      <String, dynamic>{
        'id': 'PT-1234',
        'type': '交易小票',
        'status': '已完成',
        'time': '2023-06-15 14:30',
        'info': '交易号: TX-123456'
      },
      <String, dynamic>{
        'id': 'PT-1235',
        'type': '日结单',
        'status': '已完成',
        'time': '2023-06-15 18:45',
        'info': '操作员: John Doe'
      },
      <String, dynamic>{
        'id': 'PT-1236',
        'type': '交易小票',
        'status': '失败',
        'time': '2023-06-15 19:20',
        'info': '交易号: TX-123457'
      },
    ];

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: recentTasks.length,
      separatorBuilder: (BuildContext context, int index) => const Divider(),
      itemBuilder: (BuildContext context, int index) {
        final Map<String, dynamic> task = recentTasks[index];
        return ListTile(
          leading: Icon(
            task['type'] == '交易小票' ? Icons.receipt : Icons.summarize,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: Text(task['type']),
          subtitle: Text('${task['time']} · ${task['info']}'),
          trailing: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: task['status'] == '已完成'
                  ? Colors.green.withOpacity(0.2)
                  : Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              task['status'],
              style: TextStyle(
                color: task['status'] == '已完成' ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          onTap: () {
            // 查看打印任务详情
          },
        );
      },
    );
  }

  void _showSpecialPrintOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                '特殊打印选项',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: const Text('交易撤销凭证'),
              onTap: () {
                Navigator.pop(context);
                context.push('/printing/preview', extra: 'TX20240409001');
              },
            ),
            ListTile(
              leading: const Icon(Icons.card_giftcard, color: Colors.orange),
              title: const Text('礼品核销凭证'),
              onTap: () {
                Navigator.pop(context);
                context.push('/printing/preview', extra: 'GC20240409001');
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_add, color: Colors.blue),
              title: const Text('会员注册凭证'),
              onTap: () {
                Navigator.pop(context);
                context.push('/printing/preview', extra: 'MB20240409001');
              },
            ),
            ListTile(
              leading: const Icon(Icons.summarize, color: Colors.green),
              title: const Text('日结单打印'),
              onTap: () {
                Navigator.pop(context);
                context.push('/printing/preview', extra: 'DS20240409001');
              },
            ),
          ],
        ),
      ),
    );
  }
}
