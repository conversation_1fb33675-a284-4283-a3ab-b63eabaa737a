import 'package:flutter/material.dart';

class ReceiptReprintPage extends StatefulWidget {
  const ReceiptReprintPage({super.key});

  @override
  State<ReceiptReprintPage> createState() => _ReceiptReprintPageState();
}

class _ReceiptReprintPageState extends State<ReceiptReprintPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // 查询条件控制器
  final TextEditingController _transactionIdController =
      TextEditingController();
  final TextEditingController _memberIdController = TextEditingController();
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();

  // 预设数据
  final List<Map<String, dynamic>> _searchResults = <Map<String, dynamic>>[];
  bool _isSearching = false;
  bool _showResults = false;

  @override
  void initState() {
    super.initState();
    // 初始化日期范围为今天
    final DateTime now = DateTime.now();
    final String dateStr =
        "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";
    _startDateController.text = dateStr;
    _endDateController.text = dateStr;
  }

  @override
  void dispose() {
    _transactionIdController.dispose();
    _memberIdController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Receipt Reprint'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: <Widget>[
          // 查询表单部分
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // 标题
                  Text(
                    '查询条件',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),

                  // 交易号
                  TextFormField(
                    controller: _transactionIdController,
                    decoration: const InputDecoration(
                      labelText: '交易号',
                      hintText: '输入交易号查询',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 会员信息
                  TextFormField(
                    controller: _memberIdController,
                    decoration: const InputDecoration(
                      labelText: '会员手机号/ID',
                      hintText: '输入会员手机号或ID',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 日期范围
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: TextFormField(
                          controller: _startDateController,
                          decoration: const InputDecoration(
                            labelText: '开始日期',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          readOnly: true,
                          onTap: () =>
                              _selectDate(context, _startDateController),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: _endDateController,
                          decoration: const InputDecoration(
                            labelText: '结束日期',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          readOnly: true,
                          onTap: () => _selectDate(context, _endDateController),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // 查询按钮
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isSearching ? null : _performSearch,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: _isSearching
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Text('查询'),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 查询结果
          if (_showResults) Expanded(child: _buildSearchResults()),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return const Center(
        child: Text('没有找到符合条件的交易记录'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                '查询结果 (${_searchResults.length})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _showResults = false;
                  });
                },
                icon: const Icon(Icons.close),
                label: const Text('关闭'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _searchResults.length,
            itemBuilder: (BuildContext context, int index) {
              final Map<String, dynamic> transaction = _searchResults[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Text(
                            '交易号: ${transaction['id']}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            transaction['date'],
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: <Widget>[
                          const Icon(Icons.local_gas_station, size: 16),
                          const SizedBox(width: 4),
                          Text(transaction['product']),
                          const SizedBox(width: 12),
                          Text('${transaction['quantity']} L'),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Text('金额: ${transaction['amount']} IDR'),
                          Text(
                            '支付方式: ${transaction['paymentMethod']}',
                            style: TextStyle(
                              color: Colors.grey[700],
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (transaction['memberInfo'] != null)
                        Text(
                          '会员: ${transaction['memberInfo']}',
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontSize: 13,
                          ),
                        ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: <Widget>[
                          OutlinedButton(
                            onPressed: () =>
                                _showTransactionDetails(transaction),
                            style: OutlinedButton.styleFrom(
                              foregroundColor:
                                  Theme.of(context).colorScheme.primary,
                              side: BorderSide(
                                  color: Theme.of(context).colorScheme.primary),
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                            ),
                            child: const Text('查看详情'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed: () => _showReprintDialog(transaction),
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              foregroundColor: Colors.white,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                            ),
                            icon: const Icon(Icons.print, size: 16),
                            label: const Text('重打'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(
      BuildContext context, TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      controller.text =
          "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
    }
  }

  void _performSearch() {
    // 在实际应用中，这里应该调用API进行查询
    setState(() {
      _isSearching = true;
    });

    // 模拟网络延迟
    Future.delayed(const Duration(seconds: 1), () {
      // 使用预设数据
      setState(() {
        _searchResults.clear();
        _searchResults.addAll(<Map<String, dynamic>>[
          <String, dynamic>{
            'id': 'TX-123456',
            'date': '2023-06-15 14:30',
            'product': '汽油 92#',
            'quantity': '25.35',
            'amount': '250,000',
            'paymentMethod': '现金',
            'memberInfo': 'John (0812****5678)',
          },
          <String, dynamic>{
            'id': 'TX-123457',
            'date': '2023-06-15 15:45',
            'product': '汽油 95#',
            'quantity': '30.20',
            'amount': '320,000',
            'paymentMethod': '信用卡',
            'memberInfo': 'Mary (0813****1234)',
          },
          <String, dynamic>{
            'id': 'TX-123458',
            'date': '2023-06-15 16:30',
            'product': '柴油',
            'quantity': '40.75',
            'amount': '425,000',
            'paymentMethod': '会员卡',
            'memberInfo': 'Robert (0817****8901)',
          },
        ]);
        _isSearching = false;
        _showResults = true;
      });
    });
  }

  void _showTransactionDetails(Map<String, dynamic> transaction) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('交易详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _detailRow('交易号', transaction['id']),
            _detailRow('日期时间', transaction['date']),
            _detailRow('油品', transaction['product']),
            _detailRow('数量', '${transaction['quantity']} L'),
            _detailRow('金额', '${transaction['amount']} IDR'),
            _detailRow('支付方式', transaction['paymentMethod']),
            if (transaction['memberInfo'] != null)
              _detailRow('会员信息', transaction['memberInfo']),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _detailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showReprintDialog(Map<String, dynamic> transaction) {
    bool showDuplicate = true;
    int copies = 1;

    showDialog(
      context: context,
      builder: (BuildContext context) => StatefulBuilder(
        builder: (BuildContext context, setState) {
          return AlertDialog(
            title: const Text('小票重打确认'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text('您确定要重打交易 ${transaction['id']} 的小票吗？'),
                const SizedBox(height: 16),

                // 打印选项
                const Text(
                  '打印选项',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                // 是否显示副本标记
                Row(
                  children: <Widget>[
                    Checkbox(
                      value: showDuplicate,
                      onChanged: (bool? value) {
                        setState(() {
                          showDuplicate = value ?? true;
                        });
                      },
                    ),
                    const Text('显示"副本"标记'),
                  ],
                ),

                // 打印份数
                Row(
                  children: <Widget>[
                    const Text('打印份数:'),
                    const SizedBox(width: 16),
                    InkWell(
                      onTap: () {
                        if (copies > 1) {
                          setState(() {
                            copies--;
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(Icons.remove, size: 16),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 12),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        copies.toString(),
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        if (copies < 5) {
                          setState(() {
                            copies++;
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(Icons.add, size: 16),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _executeReprint(transaction, showDuplicate, copies);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('确认打印'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _executeReprint(
      Map<String, dynamic> transaction, bool showDuplicate, int copies) {
    // 实际应用中，这里应该调用打印服务
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            '正在打印 ${transaction['id']} 的小票 ($copies 份)${showDuplicate ? '，含副本标记' : ''}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
