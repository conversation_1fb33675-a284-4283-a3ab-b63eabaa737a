import 'package:flutter/material.dart';
import '../../constants/bp_colors.dart';
import '../../theme/app_theme.dart';
import '../../widgets/safe_scaffold.dart';
import '../../services/native/sunmi_printer_service.dart';

class PrinterSettingsPage extends StatefulWidget {
  const PrinterSettingsPage({super.key});

  @override
  State<PrinterSettingsPage> createState() => _PrinterSettingsPageState();
}

class _PrinterSettingsPageState extends State<PrinterSettingsPage> {
  // 商米内置打印机状态
  bool _isConnected = false;
  bool _isLoading = true;
  PrinterStatus _printerStatus = PrinterStatus.unknown;

  // 打印机信息（固定为商米内置打印机）
  final Map<String, dynamic> _printerInfo = <String, dynamic>{
    'name': 'Sunmi Internal Printer',
    'type': 'Built-in',
    'model': 'P2-Lite/P2A-11',
    'isDefault': true,
  };

  // 打印设置参数
  int _printDensity = 12;
  final bool _autoCut = false; // 商米打印机通常不支持自动切纸
  bool _autoFeed = true;
  int _fontSize = 1; // 0: 小, 1: 中, 2: 大
  final String _paperWidth = '58mm'; // 商米内置打印机默认58mm

  @override
  void initState() {
    super.initState();
    _initPrinter();
  }

  Future<void> _initPrinter() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 检查商米打印机连接状态
      final bool isConnected =
          await SunmiPrinterService.instance.isPrinterConnected();
      final PrinterStatus status =
          await SunmiPrinterService.instance.getPrinterStatus();

      setState(() {
        _isConnected = isConnected;
        _printerStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isConnected = false;
        _printerStatus = PrinterStatus.serviceDisconnected;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('打印机连接检查失败: ${e.toString()}'),
            backgroundColor: BPColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
      appBar: AppBar(
        title: const Row(
          children: <Widget>[
            Icon(
              Icons.print,
              color: Colors.white,
              size: 28,
            ),
            SizedBox(width: 12),
            Text(
              'Printer Settings',
              style: EDCTextStyles.appBarTitle,
            ),
          ],
        ),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: EDCComponentStyles.buildPageStructure(
        child: _isLoading
            ? EDCComponentStyles.buildLoadingIndicator()
            : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    // 页面标题
                    const Text(
                      'Sunmi Internal Printer',
                      style: EDCTextStyles.mainTitle,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Configure your built-in thermal printer settings',
                      style: EDCTextStyles.hintText,
                    ),
                    const SizedBox(height: 32),

                    // 打印机状态卡片
                    _buildPrinterStatusCard(),
                    const SizedBox(height: 24),

                    // 打印参数设置
                    _buildPrintSettingsSection(),
                    const SizedBox(height: 32),

                    // 操作按钮
                    _buildActionButtons(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildPrinterStatusCard() {
    final bool isNormal = _printerStatus == PrinterStatus.normal;
    final Color statusColor = isNormal ? BPColors.success : BPColors.error;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text(
                  'Connection Status',
                  style: EDCTextStyles.cardTitle,
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Icon(
                        _isConnected ? Icons.check_circle : Icons.error_outline,
                        color: statusColor,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getStatusText(),
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // 打印机信息
            _buildInfoRow('Printer Model', _printerInfo['model'] as String),
            _buildInfoRow('Connection Type', _printerInfo['type'] as String),
            _buildInfoRow('Paper Width', _paperWidth),
            _buildInfoRow('Current Status', _getDetailedStatusText()),

            const SizedBox(height: 20),

            // 刷新按钮
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : _initPrinter,
                style: OutlinedButton.styleFrom(
                  foregroundColor: BPColors.primary,
                  side: const BorderSide(color: BPColors.primary),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                icon: _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(BPColors.primary),
                        ),
                      )
                    : const Icon(Icons.refresh),
                label: Text(_isLoading ? 'Checking...' : 'Refresh Status'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrintSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'Print Parameters',
          style: EDCTextStyles.subTitle,
        ),
        const SizedBox(height: 16),

        // 打印浓度
        Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: Colors.grey.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text(
                  'Print Density',
                  style: EDCTextStyles.cardTitle,
                ),
                const SizedBox(height: 8),
                const Text(
                  'Adjust darkness of printed text and images',
                  style: EDCTextStyles.hintText,
                ),
                const SizedBox(height: 16),
                Slider(
                  value: _printDensity.toDouble(),
                  min: 1,
                  max: 20,
                  divisions: 19,
                  label: _printDensity.toString(),
                  activeColor: BPColors.primary,
                  onChanged: (double value) {
                    setState(() {
                      _printDensity = value.toInt();
                    });
                  },
                ),
                const Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text('Light', style: EDCTextStyles.hintText),
                    Text('Standard', style: EDCTextStyles.hintText),
                    Text('Dark', style: EDCTextStyles.hintText),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // 字体大小
        Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: Colors.grey.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text(
                  'Font Size',
                  style: EDCTextStyles.cardTitle,
                ),
                const SizedBox(height: 8),
                const Text(
                  'Default text size for receipts',
                  style: EDCTextStyles.hintText,
                ),
                const SizedBox(height: 16),
                SegmentedButton<int>(
                  segments: const <ButtonSegment<int>>[
                    ButtonSegment<int>(
                      value: 0,
                      label: Text('Small'),
                    ),
                    ButtonSegment<int>(
                      value: 1,
                      label: Text('Medium'),
                    ),
                    ButtonSegment<int>(
                      value: 2,
                      label: Text('Large'),
                    ),
                  ],
                  selected: <int>{_fontSize},
                  onSelectionChanged: (Set<int> newSelection) {
                    setState(() {
                      _fontSize = newSelection.first;
                    });
                  },
                  style: SegmentedButton.styleFrom(
                    selectedBackgroundColor: BPColors.primary,
                    selectedForegroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // 自动走纸设置
        Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: Colors.grey.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: <Widget>[
                SwitchListTile(
                  title: const Text(
                    'Auto Feed Paper',
                    style: EDCTextStyles.bodyText,
                  ),
                  subtitle: const Text(
                    'Automatically feed paper lines after printing',
                    style: EDCTextStyles.hintText,
                  ),
                  value: _autoFeed,
                  activeColor: BPColors.primary,
                  onChanged: (bool value) {
                    setState(() {
                      _autoFeed = value;
                    });
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: <Widget>[
        // 测试打印按钮
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: (_isConnected && _printerStatus == PrinterStatus.normal)
                ? _printTestPage
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: BPColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.print, size: 24),
            label: const Text(
              'Print Test Receipt',
              style: EDCTextStyles.buttonText,
            ),
          ),
        ),
        const SizedBox(height: 16),

        // 打印机自检按钮
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _isConnected ? _printerSelfCheck : null,
            style: OutlinedButton.styleFrom(
              foregroundColor: BPColors.primary,
              side: const BorderSide(color: BPColors.primary),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.settings_applications, size: 24),
            label: const Text(
              'Printer Self Check',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: BPColors.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: EDCTextStyles.hintText,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: EDCTextStyles.bodyText.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText() {
    if (!_isConnected) return 'Disconnected';

    switch (_printerStatus) {
      case PrinterStatus.normal:
        return 'Ready';
      case PrinterStatus.preparing:
        return 'Preparing';
      case PrinterStatus.outOfPaper:
        return 'Out of Paper';
      case PrinterStatus.overheated:
        return 'Overheated';
      case PrinterStatus.coverOpen:
        return 'Cover Open';
      default:
        return 'Error';
    }
  }

  String _getDetailedStatusText() {
    switch (_printerStatus) {
      case PrinterStatus.normal:
        return 'Normal - Ready to print';
      case PrinterStatus.preparing:
        return 'Preparing printer...';
      case PrinterStatus.abnormalCommunication:
        return 'Communication error';
      case PrinterStatus.outOfPaper:
        return 'Please load paper';
      case PrinterStatus.overheated:
        return 'Printer overheated, please wait';
      case PrinterStatus.coverOpen:
        return 'Please close printer cover';
      case PrinterStatus.cutterError:
        return 'Cutter mechanism error';
      case PrinterStatus.printerNotDetected:
        return 'Printer hardware not detected';
      case PrinterStatus.serviceDisconnected:
        return 'Printer service not connected';
      default:
        return 'Unknown status';
    }
  }

  Future<void> _printTestPage() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 使用商米打印机服务打印测试页面
      await SunmiPrinterService.instance.printDemoReceipt();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test receipt sent to printer successfully!'),
            backgroundColor: BPColors.success,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Print test failed: ${e.toString()}'),
            backgroundColor: BPColors.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _printerSelfCheck() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 刷新打印机状态作为自检替代方案
      await _initPrinter();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Printer status check completed!'),
            backgroundColor: BPColors.success,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Status check failed: ${e.toString()}'),
            backgroundColor: BPColors.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
