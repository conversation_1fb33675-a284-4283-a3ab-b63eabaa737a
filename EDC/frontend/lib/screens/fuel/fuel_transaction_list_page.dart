import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../controllers/fuel_transaction_controller.dart';
import '../../models/fuel_transaction.dart';
import '../../widgets/app_loading_indicator.dart';
import '../../widgets/safe_scaffold.dart';
import 'fuel_transaction_detail_page.dart';

class FuelTransactionListPage extends ConsumerStatefulWidget {
  const FuelTransactionListPage({super.key});

  @override
  ConsumerState<FuelTransactionListPage> createState() =>
      _FuelTransactionListPageState();
}

class _FuelTransactionListPageState
    extends ConsumerState<FuelTransactionListPage> {
  @override
  void initState() {
    super.initState();
    // 页面加载后自动获取数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(fuelTransactionControllerProvider.notifier)
          .loadFuelTransactions();
    });
  }

  // 获取状态对应的颜色
  Color _getStatusColor(String status) {
    switch (status) {
      case 'processed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // 获取状态对应的英文文本
  String _getStatusText(String status) {
    switch (status) {
      case 'processed':
        return 'Completed';
      case 'pending':
        return 'Pending';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  // 构建交易列表
  Widget _buildTransactionList() {
    final FuelTransactionState state =
        ref.watch(fuelTransactionControllerProvider);
    final FuelTransactionController controller =
        ref.read(fuelTransactionControllerProvider.notifier);

    if (state.isLoading) {
      return const Center(child: AppLoadingIndicator());
    }

    if (state.errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              'Failed to load',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(state.errorMessage!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: controller.loadFuelTransactions,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.search_off_rounded,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              'No transactions found',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // 格式化金额
    final NumberFormat currencyFormatter = NumberFormat.currency(
      locale: 'en_US',
      symbol: 'Rp',
      decimalDigits: 2,
    );

    // 时间格式
    final DateFormat timeFormatter = DateFormat('h:mm a');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 未结算交易标题
        const Padding(
          padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Row(
            children: <Widget>[
              Icon(
                Icons.schedule,
                size: 18,
                color: Colors.amber,
              ),
              SizedBox(width: 8),
              Text(
                'Unsettled Transactions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),

        // 交易列表
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.only(bottom: 16),
            itemCount: state.transactions.length,
            itemBuilder: (BuildContext context, int index) {
              final FuelTransaction transaction = state.transactions[index];

              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                elevation: 0,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(
                    color: Colors.grey.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (BuildContext context) =>
                            FuelTransactionDetailPage(
                          transactionId: transaction.id,
                          initialTransaction: transaction,
                        ),
                      ),
                    );
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.all(0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        // 泵号和时间栏
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.amber.withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Text(
                                'Pump ${transaction.pumpId}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              Row(
                                children: <Widget>[
                                  Text(
                                    timeFormatter.format(transaction.createdAt),
                                    style: const TextStyle(
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  const Icon(
                                    Icons.chevron_right,
                                    size: 20,
                                    color: Colors.grey,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // 燃油类型和价格信息
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              // 左侧燃油类型
                              Text(
                                '${transaction.fuelType} ${transaction.fuelGrade}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),

                              // 右侧价格
                              Text(
                                currencyFormatter.format(transaction.amount),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // 升数和单价信息，右下角显示状态标签
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              // 左侧容量和单价信息
                              Row(
                                children: <Widget>[
                                  // 容量信息
                                  Icon(
                                    Icons.water_drop_outlined,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${transaction.volume.toStringAsFixed(1)} L',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[700],
                                    ),
                                  ),

                                  const SizedBox(width: 16),

                                  // 单价信息
                                  Icon(
                                    Icons.currency_yen,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${transaction.unitPrice.toStringAsFixed(2)}/L',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                ],
                              ),

                              // 右侧状态标签
                              _buildStatusChip(transaction.status),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // 如果有分页，展示分页控件
        if (state.totalPage > 1)
          Container(
            height: 48,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withOpacity(0.95),
              border: Border(
                  top: BorderSide(
                      color: Colors.grey.withOpacity(0.15), width: 1)),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                IconButton(
                  icon: const Icon(Icons.navigate_before),
                  padding: const EdgeInsets.all(8.0),
                  constraints: const BoxConstraints(),
                  iconSize: 24,
                  color: state.page > 1 ? Colors.green : Colors.grey[400],
                  onPressed: state.page > 1
                      ? () => controller.goToPage(state.page - 1)
                      : null,
                ),
                const SizedBox(width: 16),
                Text(
                  '${state.page}/${state.totalPage}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                IconButton(
                  icon: const Icon(Icons.navigate_next),
                  padding: const EdgeInsets.all(8.0),
                  constraints: const BoxConstraints(),
                  iconSize: 24,
                  color: state.page < state.totalPage
                      ? Colors.green
                      : Colors.grey[400],
                  onPressed: state.page < state.totalPage
                      ? () => controller.goToPage(state.page + 1)
                      : null,
                ),
              ],
            ),
          ),
      ],
    );
  }

  // 构建状态标签
  Widget _buildStatusChip(String status) {
    final Color chipColor = _getStatusColor(status);
    final String statusText = _getStatusText(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: chipColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: chipColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            statusText,
            style: TextStyle(
              color: chipColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        context.go('/');
      },
      child: SafeScaffold(
        appBar: AppBar(
          title: Row(
            children: <Widget>[
              // 使用assets中的BP logo
              Image.asset(
                'assets/images/bp_logo.png',
                height: 32,
                fit: BoxFit.contain,
              ),
              const SizedBox(width: 12),
              const Text(
                'Transactions',
                style: TextStyle(
                  color: Color(0xFF2E8B57), // BP绿色
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          backgroundColor: Colors.white, // 白色背景
          foregroundColor: const Color(0xFF2E8B57), // 文字和图标颜色为BP绿色
          elevation: 0,
          // 移除右上角的New按钮
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            // 筛选控件
            Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey.withOpacity(0.2),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: _buildStatusDropdown(),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildPumpDropdown(),
                  ),
                ],
              ),
            ),
            // 内容区域
            Expanded(
              child: _buildTransactionList(),
            ),
            // 底部添加新交易按钮
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton.icon(
                onPressed: () {
                  // TODO 导航到交易预设页面  extra
                  context.push('/transaction/preset', extra: 'pts-002');
                },
                icon: const Icon(Icons.add),
                label: const Text('New Transaction'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建状态下拉框
  Widget _buildStatusDropdown() {
    final FuelTransactionController controller =
        ref.read(fuelTransactionControllerProvider.notifier);
    final FuelTransactionState state =
        ref.watch(fuelTransactionControllerProvider);
    final List<String> statuses = controller.getAvailableStatuses();

    return _buildFilterDropdown(
      hint: 'Filter by status',
      icon: Icons.flag_rounded,
      value: state.selectedStatus,
      items: <DropdownMenuItem<String?>>[
        const DropdownMenuItem<String?>(
          value: null,
          child: Text('All statuses'),
        ),
        ...statuses.map((String status) {
          return DropdownMenuItem<String>(
            value: status,
            child: Row(
              children: <Widget>[
                Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    color: _getStatusColor(status),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 6),
                Text(_getStatusText(status)),
              ],
            ),
          );
        }),
      ],
      onChanged: controller.filterByStatus,
    );
  }

  // 构建油枪下拉框
  Widget _buildPumpDropdown() {
    final FuelTransactionController controller =
        ref.read(fuelTransactionControllerProvider.notifier);
    final FuelTransactionState state =
        ref.watch(fuelTransactionControllerProvider);
    final List<String> pumpIds = controller.getAvailablePumpIds();

    return _buildFilterDropdown(
      hint: 'Filter by pump',
      icon: Icons.local_gas_station_rounded,
      value: state.selectedPumpId,
      items: <DropdownMenuItem<String?>>[
        const DropdownMenuItem<String?>(
          value: null,
          child: Text('All pumps'),
        ),
        ...pumpIds.map((String pumpId) => DropdownMenuItem<String>(
              value: pumpId,
              child: Text('Pump $pumpId'),
            )),
      ],
      onChanged: controller.filterByPumpId,
    );
  }

  // 构建美化的下拉菜单
  Widget _buildFilterDropdown<T>({
    required String hint,
    required IconData icon,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    // 保留原有下拉菜单样式
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<T>(
          isExpanded: true,
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          hint: Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Icon(
                icon,
                size: 16,
                color: Colors.green,
              ),
              const SizedBox(width: 6),
              Text(
                hint,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 13,
                ),
              ),
            ],
          ),
          value: value,
          icon: const Icon(
            Icons.keyboard_arrow_down_rounded,
            size: 18,
            color: Colors.green,
          ),
          elevation: 2,
          borderRadius: BorderRadius.circular(8.0),
          style: TextStyle(
            color: Colors.grey[800],
            fontSize: 13,
          ),
          onChanged: onChanged,
          items: items,
        ),
      ),
    );
  }
}
