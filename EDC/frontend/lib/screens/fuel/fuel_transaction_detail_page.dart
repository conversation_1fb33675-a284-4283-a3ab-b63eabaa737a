import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../controllers/fuel_transaction_detail_controller.dart';
import '../../models/fuel_transaction.dart';
import '../../models/payment_transaction_data.dart';
import '../../widgets/app_loading_indicator.dart';
import '../../widgets/safe_scaffold.dart';

class FuelTransactionDetailPage extends ConsumerStatefulWidget {
  const FuelTransactionDetailPage({
    super.key,
    required this.transactionId,
    this.initialTransaction,
  });
  final String transactionId;

  // 可选参数，用于预先展示数据，减少加载等待感
  final FuelTransaction? initialTransaction;

  @override
  ConsumerState<FuelTransactionDetailPage> createState() =>
      _FuelTransactionDetailPageState();
}

class _FuelTransactionDetailPageState
    extends ConsumerState<FuelTransactionDetailPage> {
  @override
  void initState() {
    super.initState();
    // 页面加载后自动获取数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(fuelTransactionDetailControllerProvider.notifier)
          .loadFuelTransactionDetail(widget.transactionId);
    });
  }

  @override
  Widget build(BuildContext context) {
    // 从控制器获取状态
    final FuelTransactionDetailState state =
        ref.watch(fuelTransactionDetailControllerProvider);

    // 初次加载时如果有初始数据则使用初始数据，否则等待API返回数据
    final FuelTransaction? transaction =
        state.transaction ?? widget.initialTransaction;

    // 格式化器
    final NumberFormat currencyFormatter = NumberFormat.currency(
      locale: 'en_US',
      symbol: 'Rp',
      decimalDigits: 2,
    );
    final DateFormat dateFormatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    final ThemeData theme = Theme.of(context);

    return SafeScaffold(
      appBar: AppBar(
        title: const Text('Transaction Details'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF2E8B57), // BP绿色
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: <Widget>[
          // 添加刷新按钮
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh data',
            onPressed: () {
              ref
                  .read(fuelTransactionDetailControllerProvider.notifier)
                  .loadFuelTransactionDetail(widget.transactionId);
            },
          ),
          // 添加分享按钮
          IconButton(
            icon: const Icon(Icons.share),
            tooltip: 'Share details',
            onPressed: () {
              // 实现分享功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Sharing feature coming soon')),
              );
            },
          ),
        ],
      ),
      body: Stack(
        children: <Widget>[
          // 错误提示
          if (state.errorMessage != null)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 60,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.errorMessage!,
                    textAlign: TextAlign.center,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      ref
                          .read(
                              fuelTransactionDetailControllerProvider.notifier)
                          .loadFuelTransactionDetail(widget.transactionId);
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E8B57),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

          // 加载指示器
          if (state.isLoading)
            const AppLoadingIndicator(
                loadingText: 'Loading transaction details...'),

          // 交易详情内容
          if (transaction != null && !state.isLoading)
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // 状态卡片，突出显示交易状态
                  _buildStatusCard(transaction),

                  // 详情内容
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        // 交易头部信息
                        _buildSectionHeader(
                          title: 'Basic Information',
                          icon: Icons.receipt_long_rounded,
                        ),
                        const SizedBox(height: 12),
                        _buildInfoCard(
                          children: <Widget>[
                            _buildInfoRow('Transaction ID',
                                transaction.transactionNumber),
                            _buildInfoRow('Transaction Time',
                                dateFormatter.format(transaction.createdAt)),
                            if (transaction.processedAt != null)
                              _buildInfoRow(
                                  'Process Time',
                                  dateFormatter
                                      .format(transaction.processedAt!)),
                            if (transaction.cancelledAt != null)
                              _buildInfoRow(
                                  'Cancel Time',
                                  dateFormatter
                                      .format(transaction.cancelledAt!)),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // 金额信息 - 使用更突出的设计
                        _buildAmountSection(transaction, currencyFormatter),
                        const SizedBox(height: 20),

                        // 燃油信息
                        _buildSectionHeader(
                          title: 'Fuel Information',
                          icon: Icons.local_gas_station_rounded,
                        ),
                        const SizedBox(height: 12),
                        _buildInfoCard(
                          children: <Widget>[
                            _buildInfoRow(
                                'Station ID', transaction.stationId.toString()),
                            _buildInfoRow('Pump ID', transaction.pumpId),
                            _buildInfoRow('Nozzle ID', transaction.nozzleId),
                            _buildInfoRow('Fuel Type', transaction.fuelType),
                            _buildInfoRow('Fuel Grade', transaction.fuelGrade),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // 会员信息
                        if (transaction.memberCardId != null ||
                            transaction.memberId != null)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              _buildSectionHeader(
                                title: 'Member Information',
                                icon: Icons.person_rounded,
                              ),
                              const SizedBox(height: 12),
                              _buildInfoCard(
                                children: <Widget>[
                                  if (transaction.memberCardId != null)
                                    _buildInfoRow(
                                        'Card No.', transaction.memberCardId!),
                                  if (transaction.memberId != null)
                                    _buildInfoRow('Member ID',
                                        transaction.memberId.toString()),
                                ],
                              ),
                              const SizedBox(height: 20),
                            ],
                          ),

                        // 其他信息
                        _buildSectionHeader(
                          title: 'Other Information',
                          icon: Icons.info_outline_rounded,
                        ),
                        const SizedBox(height: 12),
                        _buildInfoCard(
                          children: <Widget>[
                            if (transaction.employeeId != null)
                              _buildInfoRow('Employee ID',
                                  transaction.employeeId.toString()),
                            if (transaction.fccTransactionId != null)
                              _buildInfoRow('FCC Trans. ID',
                                  transaction.fccTransactionId!),
                            if (transaction.posTerminalId != null)
                              _buildInfoRow('POS Terminal ID',
                                  transaction.posTerminalId!),
                            _buildInfoRow('Last Updated',
                                dateFormatter.format(transaction.updatedAt)),
                            if (transaction.metadata.isNotEmpty)
                              _buildInfoRow(
                                  'Metadata', transaction.metadata.toString()),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
      bottomNavigationBar:
          transaction != null ? _buildBottomActions(transaction) : null,
    );
  }

  // 构建状态卡片
  Widget _buildStatusCard(FuelTransaction transaction) {
    // 获取状态颜色
    Color statusColor;
    IconData statusIcon;
    String statusDescription;

    switch (transaction.status) {
      case 'processed':
        statusColor = const Color(0xFF2E8B57); // BP绿色
        statusIcon = Icons.check_circle;
        statusDescription = 'Transaction has been processed successfully';
        break;
      case 'pending':
        statusColor = Colors.amber;
        statusIcon = Icons.pending;
        statusDescription =
            'Transaction is being processed, please check back later';
        break;
      case 'cancelled':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusDescription = 'This transaction has been cancelled';
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
        statusDescription = 'Unknown status, please contact administrator';
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 20.0),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: statusColor.withOpacity(0.3),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: <Widget>[
          Icon(
            statusIcon,
            color: statusColor,
            size: 24.0,
          ),
          const SizedBox(width: 12.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  _getStatusText(transaction.status),
                  style: TextStyle(
                    fontSize: 18.0,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
                const SizedBox(height: 4.0),
                Text(
                  statusDescription,
                  style: TextStyle(
                    fontSize: 14.0,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建金额部分（更加突出）
  Widget _buildAmountSection(
      FuelTransaction transaction, NumberFormat formatter) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: const Color(0xFF2E8B57).withOpacity(0.1), // BP绿色背景
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: const Color(0xFF2E8B57).withOpacity(0.3), // BP绿色边框
          width: 1.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          const Text(
            'Transaction Amount',
            style: TextStyle(
              fontSize: 16.0,
              color: Color(0xFF2E8B57),
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            formatter.format(transaction.amount),
            style: const TextStyle(
              fontSize: 28.0,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E8B57), // BP绿色
            ),
          ),
          const SizedBox(height: 16.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: <Widget>[
              _buildAmountInfoItem(
                label: 'Unit Price',
                value: formatter.format(transaction.unitPrice),
              ),
              Container(
                height: 30.0,
                width: 1.0,
                color: const Color(0xFF2E8B57).withOpacity(0.2), // BP绿色分隔线
              ),
              _buildAmountInfoItem(
                label: 'Volume',
                value: '${transaction.volume} L',
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建金额子项
  Widget _buildAmountInfoItem({required String label, required String value}) {
    return Column(
      children: <Widget>[
        Text(
          label,
          style: TextStyle(
            fontSize: 14.0,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4.0),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16.0,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // 构建底部按钮
  Widget _buildBottomActions(FuelTransaction transaction) {
    // 根据交易状态显示不同按钮
    final List<Widget> actionButtons = <Widget>[];

    if (transaction.status == 'processed') {
      actionButtons.add(
        Expanded(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.print),
            label: const Text('Print Receipt'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E8B57), // BP绿色
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12.0),
            ),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Printing feature coming soon')),
              );
            },
          ),
        ),
      );
    } else if (transaction.status == 'pending') {
      actionButtons.add(
        Expanded(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.check_circle),
            label: const Text('Confirm Transaction'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E8B57), // BP绿色
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12.0),
            ),
            onPressed: () async {
              // 获取员工信息
              final SharedPreferences prefs =
                  await SharedPreferences.getInstance();
              final String employeeId =
                  prefs.getString('user_id') ?? 'unknown';  // 改为 user_id
              final String employeeNo =
                  prefs.getString('employee_no') ?? 'unknown';

              // 使用新的工厂方法创建PaymentTransactionData
              final PaymentTransactionData paymentData =
                  PaymentTransactionData.fromFuelTransaction(
                transaction: transaction,
                employeeId: employeeId,
                employeeNo: employeeNo,
              );

              // 导航到支付页面
              if (context.mounted) {
                context.push('/payment', extra: paymentData);
              }
            },
          ),
        ),
      );

      // 移除取消按钮，因为管控不允许取消交易
    }

    // 如果没有按钮，则返回null（不显示底部导航栏）
    if (actionButtons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5.0,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: actionButtons,
      ),
    );
  }

  // 构建信息卡片
  Widget _buildInfoCard({required List<Widget> children}) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: children,
        ),
      ),
    );
  }

  // 构建信息行
  Widget _buildInfoRow(String label, String value, {TextStyle? valueStyle}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: valueStyle ??
                  TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建章节标题
  Widget _buildSectionHeader({
    required String title,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        children: <Widget>[
          Icon(
            icon,
            size: 20,
            color: const Color(0xFF2E8B57), // BP绿色
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E8B57), // BP绿色
            ),
          ),
        ],
      ),
    );
  }

  // 获取状态文本
  String _getStatusText(String status) {
    switch (status) {
      case 'processed':
        return 'Completed';
      case 'pending':
        return 'Processing';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }
}
