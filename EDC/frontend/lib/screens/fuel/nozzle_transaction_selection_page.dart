import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../widgets/safe_scaffold.dart';

// BP Design Guide Text Styles
class EDCTextStyles {
  static const TextStyle mainTitle = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: Color(0xFF00A650),
  );

  static const TextStyle subTitle = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    color: Color(0xFF333333),
  );

  static const TextStyle bodyText = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.normal,
    color: Color(0xFF333333),
  );

  static const TextStyle buttonText = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  static const TextStyle hintText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: Color(0xFF666666),
  );

  static const TextStyle emphasizedText = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Color(0xFFFFD903),
  );
}

class BPStandardColors {
  static const Color bpGreen = Color(0xFF00A650);
  static const Color bpYellow = Color(0xFFFFD903);
  static const Color assistGreen = Color(0xFF80C242);
  static const Color warningRed = Color(0xFFCC0000);
  static const Color neutralGray = Color(0xFF666666);
  static const Color darkGray = Color(0xFF333333);
  static const Color background = Color(0xFFFFFFFF);
}

class NozzleTransactionSelectionPage extends StatefulWidget {
  const NozzleTransactionSelectionPage({
    super.key,
    required this.pageData,
  });
  final Map<String, dynamic> pageData;

  @override
  State<NozzleTransactionSelectionPage> createState() =>
      _NozzleTransactionSelectionPageState();
}

class _NozzleTransactionSelectionPageState
    extends State<NozzleTransactionSelectionPage> {
  late Map<String, dynamic> nozzleData;
  late List<Map<String, dynamic>> transactions;

  @override
  void initState() {
    super.initState();
    nozzleData = widget.pageData['nozzle'] as Map<String, dynamic>;
    transactions = (widget.pageData['transactions'] as List<dynamic>)
        .cast<Map<String, dynamic>>();

    debugPrint('NozzleTransactionSelectionPage initialization');
    debugPrint('Nozzle: ${nozzleData['name']}');
    debugPrint('Transaction count: ${transactions.length}');
  }

  @override
  Widget build(BuildContext context) {
    final NumberFormat currencyFormatter = NumberFormat.currency(
      locale: 'en_US',
      symbol: 'Rp',
      decimalDigits: 0,
    );

    final DateFormat timeFormatter = DateFormat('HH:mm');

    return SafeScaffold(
      appBar: AppBar(
        title: Row(
          children: <Widget>[
            Image.asset(
              'assets/images/bp_logo.png',
              height: 36,
              fit: BoxFit.contain,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Select Transaction',
                  style: EDCTextStyles.mainTitle.copyWith(fontSize: 22),
                ),
                Text(
                  '${nozzleData['name']} - ${nozzleData['fuelType']}',
                  style: EDCTextStyles.hintText.copyWith(
                    color: BPStandardColors.bpGreen,
                  ),
                ),
              ],
            ),
          ],
        ),
        backgroundColor: BPStandardColors.background,
        foregroundColor: BPStandardColors.bpGreen,
        elevation: 0,
        toolbarHeight: 64,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          iconSize: 28,
          onPressed: () => context.pop(),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: BPStandardColors.bpGreen.withOpacity(0.05),
              border: Border(
                bottom: BorderSide(
                  color: BPStandardColors.neutralGray.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Icon(
                      Icons.local_gas_station,
                      color: BPStandardColors.bpGreen,
                      size: 28,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Pending Transactions',
                      style: EDCTextStyles.subTitle,
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Text(
                  'Select a transaction to proceed with payment',
                  style: EDCTextStyles.hintText,
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: transactions.length,
              itemBuilder: (BuildContext context, int index) {
                final Map<String, dynamic> transaction = transactions[index];
                final double amount = transaction['amount'] as double;
                final double volume = transaction['volume'] as double;
                final DateTime createdAt =
                    DateTime.parse(transaction['createdAt'] as String);

                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Card(
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: BPStandardColors.neutralGray.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    color: BPStandardColors.background,
                    child: InkWell(
                      onTap: () => _selectTransaction(transaction),
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                Row(
                                  children: <Widget>[
                                    const Icon(
                                      Icons.access_time,
                                      size: 28,
                                      color: BPStandardColors.neutralGray,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      timeFormatter.format(createdAt),
                                      style: EDCTextStyles.bodyText,
                                    ),
                                  ],
                                ),
                                Text(
                                  currencyFormatter.format(amount),
                                  style: EDCTextStyles.emphasizedText.copyWith(
                                    color: BPStandardColors.bpGreen,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: <Widget>[
                                const Icon(
                                  Icons.water_drop_outlined,
                                  size: 28,
                                  color: BPStandardColors.neutralGray,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${volume.toStringAsFixed(2)} L',
                                  style: EDCTextStyles.bodyText,
                                ),
                                const SizedBox(width: 24),
                                const Icon(
                                  Icons.local_gas_station_outlined,
                                  size: 28,
                                  color: BPStandardColors.neutralGray,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${transaction['fuelType']} ${transaction['fuelGrade']}',
                                  style: EDCTextStyles.bodyText,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                Expanded(
                                  child: Text(
                                    'ID: ${transaction['id']}',
                                    style: EDCTextStyles.hintText.copyWith(
                                      fontSize: 14,
                                      fontFamily: 'monospace',
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                Row(
                                  children: <Widget>[
                                    Text(
                                      'Tap to select',
                                      style: EDCTextStyles.hintText.copyWith(
                                        color: BPStandardColors.bpGreen,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    const Icon(
                                      Icons.arrow_forward_ios,
                                      size: 20,
                                      color: BPStandardColors.bpGreen,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: BPStandardColors.background,
              border: Border(
                top: BorderSide(
                  color: BPStandardColors.neutralGray.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: BPStandardColors.neutralGray.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      const Icon(
                        Icons.info_outline,
                        size: 28,
                        color: BPStandardColors.neutralGray,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Total ${transactions.length} pending transactions',
                        style: EDCTextStyles.bodyText.copyWith(
                          color: BPStandardColors.neutralGray,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                OutlinedButton(
                  onPressed: () => context.pop(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: BPStandardColors.bpGreen,
                    side: const BorderSide(
                        color: BPStandardColors.bpGreen, width: 2),
                    padding: const EdgeInsets.symmetric(
                      vertical: 16.0,
                      horizontal: 32.0,
                    ),
                    minimumSize: const Size(120, 48),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                  ),
                  child: Text(
                    'Back to Nozzle View',
                    style: EDCTextStyles.buttonText.copyWith(
                      color: BPStandardColors.bpGreen,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _selectTransaction(Map<String, dynamic> transaction) {
    debugPrint('User selected transaction: ${transaction['id']}');
    debugPrint('Amount: Rp ${transaction['amount']}');
    debugPrint('Volume: ${transaction['volume']}L');

    final Map<String, Map<String, dynamic>> transactionData =
        <String, Map<String, dynamic>>{
      'nozzle': nozzleData,
      'transaction': transaction,
      'settlement': <String, dynamic>{
        'needsPayment': true,
        'totalAmount': transaction['amount'],
        'currency': 'IDR',
        'transactionId': transaction['bosId'],
      }
    };

    debugPrint('Navigating to settlement page');

    context.push('/payment', extra: transactionData);
  }
}
