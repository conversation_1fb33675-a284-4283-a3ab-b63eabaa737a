import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';

import '../../constants/bp_colors.dart';
import '../../theme/app_theme.dart';
import '../../models/dispenser_model.dart';

import '../../widgets/bp_app_bar.dart';
import '../../widgets/staff_verification_dialog.dart';
import '../../controllers/fcc_status_controller.dart';
import '../../services/shift_service.dart';
import '../../services/fcc_device_service.dart';
import '../../controllers/dispenser_controller.dart';
import '../../models/staff_card.dart';

/// Nozzle授权页面
/// 优化的1步流程：授权设置 -> 直接弹出员工验证弹窗
class NozzleAuthorizationPage extends ConsumerStatefulWidget {
  // 改为必需参数，从主页传入

  const NozzleAuthorizationPage({
    super.key,
    required this.selectedNozzle,
  });
  final Nozzle selectedNozzle;

  @override
  ConsumerState<NozzleAuthorizationPage> createState() =>
      _NozzleAuthorizationPageState();
}

class _NozzleAuthorizationPageState
    extends ConsumerState<NozzleAuthorizationPage> {
  // ✅ 简化：移除步骤管理，只保留授权设置
  late Nozzle _selectedNozzle;
  AuthMode _selectedMode = AuthMode.amount;
  double _currentValue = 0.0;

  // ✅ 新增：价格信息状态
  bool _isLoadingPrice = false;
  String? _priceErrorMessage;

  // ✅ 生命周期管理标志
  bool _isDisposed = false;

  // 预设值配置 - 印尼货币标准
  final List<double> _amountPresets = <double>[
    1000,
    5000,
    10000,
    20000,
    50000,
    100000
  ];
  final List<double> _volumePresets = <double>[1, 2, 5, 10, 20, 50]; // 整数升数

  @override
  void initState() {
    super.initState();
    _selectedNozzle = widget.selectedNozzle; // 直接使用传入的Nozzle

    // ✅ 新增：进入授权页面时异步调用 FCC 设备重置和价格获取
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isDisposed && mounted) {
        _initializeFccData();
      }
    });
  }

  @override
  void dispose() {
    // ✅ 设置销毁标志
    _isDisposed = true;
    super.dispose();
  }

  /// 初始化FCC数据：重置设备状态并获取实时价格
  Future<void> _initializeFccData() async {
    if (_isDisposed || !mounted) return;

    // 并行执行重置和价格获取
    await Future.wait(<Future<void>>[
      _loadFccNozzlePrice(),
      // _resetFccNozzle(),
    ]);
  }

  

  /// 从FCC缓存数据获取实时nozzle价格信息
  Future<void> _loadFccNozzlePrice() async {
    if (_isDisposed || !mounted) return;

    setState(() {
      _isLoadingPrice = true;
      _priceErrorMessage = null;
    });

    try {
      debugPrint('🔍 从FCC缓存获取Nozzle价格信息 (Nozzle ID: ${_selectedNozzle.id})');

      // 从FCC缓存的dispensers中查找对应的nozzle
      final FccStatusController fccStatusController =
          ref.read(fccStatusControllerProvider.notifier);
      final Nozzle? cachedNozzle =
          fccStatusController.getNozzleById(_selectedNozzle.id);

      if (_isDisposed || !mounted) return;

      setState(() {
        _isLoadingPrice = false;

        if (cachedNozzle == null) {
          _priceErrorMessage = 'Nozzle not found in cache';
          debugPrint('⚠️ 未在缓存中找到对应的Nozzle');
        } else if (cachedNozzle.price <= 0) {
          debugPrint('⚠️ Nozzle价格配置异常: ${cachedNozzle.price}');
          _priceErrorMessage =
              'Price configuration error - please check with administrator';
          debugPrint('🔧 价格配置异常，需要管理员检查');
        } else {
          debugPrint('✅ 成功从缓存获取价格: ${cachedNozzle.price}');
          // 更新本地nozzle数据，使用缓存中的最新价格
          _selectedNozzle = _selectedNozzle.copyWith(price: cachedNozzle.price);
        }
      });
    } catch (e) {
      debugPrint('❌ 获取缓存Nozzle价格失败: $e');
      if (_isDisposed || !mounted) return;

      setState(() {
        _isLoadingPrice = false;
        _priceErrorMessage =
            'Price fetch failed - please check network connection';
        debugPrint('🔧 获取价格失败，网络异常');
      });
    }
  }

  /// 异步重置 FCC 设备状态
  /// 确保交易完成后设备处于干净的状态
  Future<void> _resetFccNozzle() async {
    try {
      final FCCDeviceService fccDeviceService =
          ref.read(fccDeviceServiceProvider);
      final bool success = await fccDeviceService.resetNozzle(
        _selectedNozzle.id,
        employeeId: 'EDC-CASH-RESET-${DateTime.now().millisecondsSinceEpoch}',
      );

      if (success) {
        debugPrint('✅ FCC 设备重置成功 (Nozzle Authorization)');
      } else {
        debugPrint('⚠️ FCC 设备重置失 (Nozzle Authorization)');
      }
    } catch (e) {
      debugPrint('❌ FCC 设备重置异常 (Nozzle Authorization): $e');
      debugPrint('   异常类型: ${e.runtimeType}');
    }
  }

  /// 获取实际使用的价格
  double get _actualPrice {
    return _selectedNozzle.price;
  }

  /// 检查价格是否有效
  bool get _isPriceValid {
    return _actualPrice > 0;
  }

  /// 获取价格错误信息
  String? get _priceErrorInfo {
    if (_isLoadingPrice) {
      return 'Loading price...';
    }
    if (_priceErrorMessage != null) {
      return _priceErrorMessage;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const BPAppBar(
        title: 'Authorization',
      ),
      body: Column(
        children: <Widget>[
          Expanded(child: _buildAuthorizationSettings()),
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// 构建授权设置内容
  Widget _buildAuthorizationSettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 8), // 精确控制顶部间距
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 选中的Nozzle信息
          _buildSelectedNozzleInfo(),

          // ✅ 修复：显示价格异常警告（基于缓存价格）
          if (_priceErrorInfo != null)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (_isLoadingPrice ? BPColors.warning : BPColors.warning)
                    .withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: (_isLoadingPrice ? BPColors.warning : BPColors.warning)
                      .withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: <Widget>[
                  if (_isLoadingPrice)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: BPColors.warning,
                      ),
                    )
                  else
                    const Icon(
                      Icons.info,
                      color: BPColors.warning,
                      size: 20,
                    ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _priceErrorInfo!,
                      style: EDCTextStyles.bodyText.copyWith(
                        color: Colors.orange.shade600, // 改用更清晰的橙色
                        fontSize: 15, // 增大字体
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 20), // 8px网格：20px = 8*2.5

          // 授权模式选择
          _buildModeSelector(),
          const SizedBox(height: 20),

          // 预设值快捷选择
          _buildPresetButtons(),
          const SizedBox(height: 20),

          // 当前值和预估显示
          _buildValueDisplay(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// 构建选中的Nozzle信息 - 简洁版本（无图标）
  Widget _buildSelectedNozzleInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: BPColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: BPColors.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: <Widget>[
          // 主要信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // 第一行：Pump + Nozzle + 油品信息
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Text(
                        '${_selectedNozzle.deviceName} • ${_selectedNozzle.name} • ${_selectedNozzle.fuelGrade}',
                        style: EDCTextStyles.bodyText.copyWith(
                          color: BPColors.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),

                // 第二行：单价信息
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: <Widget>[
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50, // 改用更清晰的背景色
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: Colors.orange.shade200,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        // ✅ 修复：简化价格显示，处理长文本
                        _isLoadingPrice
                            ? 'Loading...'
                            : !_isPriceValid
                                ? 'Price Error'
                                : 'Rp ${_formatRupiah(_actualPrice)}/L',
                        style: EDCTextStyles.bodyText.copyWith(
                          color: _isLoadingPrice
                              ? Colors.orange.shade600 // 改用更清晰的橙色
                              : !_isPriceValid
                                  ? BPColors.error
                                  : Colors.orange.shade700, // 改用更深的橙色
                          fontSize: 13, // 稍微增大字体
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis, // ✅ 修复：添加文本溢出处理
                        maxLines: 1, // ✅ 修复：限制为单行
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模式选择器 - 精细设计版本
  Widget _buildModeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 标题
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Authorization Mode',
            style: EDCTextStyles.bodyText.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              fontSize: 15, // 稍小的标题字体
              letterSpacing: 0.2, // 字间距
            ),
          ),
        ),
        const SizedBox(height: 8), // 标题与内容间距

        // 模式选择按钮组
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(6), // 统一6px圆角
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Row(
              children: AuthMode.values
                  .asMap()
                  .entries
                  .map((MapEntry<int, AuthMode> entry) {
                final int index = entry.key;
                final AuthMode mode = entry.value;
                final bool isSelected = _selectedMode == mode;
                final bool isLast = index == AuthMode.values.length - 1;

                return Expanded(
                  child: _ModernModeButton(
                    mode: mode,
                    isSelected: isSelected,
                    isLast: isLast,
                    onTap: () => setState(() {
                      _selectedMode = mode;
                      _currentValue = 0.0;
                    }),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建预设值按钮 - 2*2网格布局版本
  Widget _buildPresetButtons() {
    if (_selectedMode == AuthMode.full) return const SizedBox.shrink();

    final List<double> presets =
        _selectedMode == AuthMode.amount ? _amountPresets : _volumePresets;

    debugPrint('presets: $presets');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 标题
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Quick Add',
            style: EDCTextStyles.bodyText.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              fontSize: 15,
              letterSpacing: 0.2,
            ),
          ),
        ),
        const SizedBox(height: 8),

        // 4*2网格布局
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          height: 112, // 调整高度以适应3行按钮 (3 * 32px + 2 * 8px spacing)
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3, // 3列改为3×3布局
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 2.2, // 调整宽高比以适应更紧凑的按钮
            ),
            itemCount: presets.length,
            itemBuilder: (BuildContext context, int index) {
              final double value = presets[index];
              return _ModernPresetButton(
                value: value,
                isAmount: _selectedMode == AuthMode.amount,
                formatRupiah: _formatRupiah,
                onTap: () => _addPresetValue(value),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建当前值显示 - 简化版本（单价已移至Nozzle信息）
  Widget _buildValueDisplay() {
    if (_selectedMode == AuthMode.full) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: BPColors.primary.withOpacity(0.08), // 改用主色调
          border: Border.all(
            color: BPColors.primary.withOpacity(0.3),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: <Widget>[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: BPColors.primary.withOpacity(0.15),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(
                Icons.local_gas_station,
                color: BPColors.primary, // 改用主色调
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Full Tank Authorization',
              style: EDCTextStyles.bodyText.copyWith(
                color: BPColors.primary, // 改用主色调
                fontWeight: FontWeight.w600,
                fontSize: 17, // 稍微增大字体
                height: 1.3,
              ),
            ),
          ],
        ),
      );
    }

    // ✅ 修复：使用默认价格后，价格总是有效的，简化计算逻辑
    double estimatedValue = 0.0;

    if (_currentValue > 0 && _actualPrice > 0) {
      estimatedValue = _selectedMode == AuthMode.amount
          ? _currentValue / _actualPrice
          : _currentValue * _actualPrice;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: <Widget>[
          // 当前值 - 主要信息（可点击输入）
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _selectedMode != AuthMode.full
                  ? _showSimpleInputDialog
                  : null,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Row(
                            children: <Widget>[
                              Text(
                                'Current Value',
                                style: EDCTextStyles.bodyText.copyWith(
                                  color: Colors.grey.shade600,
                                  fontSize: 13, // 小标签字体
                                  fontWeight: FontWeight.w500,
                                  letterSpacing: 0.3,
                                ),
                              ),
                              if (_selectedMode != AuthMode.full) ...<Widget>[
                                const SizedBox(width: 8),
                                Icon(
                                  Icons.edit,
                                  size: 16,
                                  color: Colors.grey.shade500,
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _selectedMode == AuthMode.amount
                                ? 'Rp ${_formatRupiah(_currentValue)}'
                                : '${_currentValue.toStringAsFixed(3)}L', // 显示3位小数
                            style: EDCTextStyles.bodyText.copyWith(
                              color: BPColors.primary,
                              fontWeight: FontWeight.bold,
                              fontSize: 22, // 突出的数值字体
                              height: 1.2,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 清空按钮
                    if (_currentValue > 0)
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              _currentValue = 0.0;
                            });
                          },
                          borderRadius: BorderRadius.circular(6),
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Icon(
                              Icons.clear,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          // 分割线
          Container(
            height: 1,
            color: Colors.grey.shade200,
          ),

          // 预估值信息
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Estimated ${_selectedMode == AuthMode.amount ? 'Volume' : 'Amount'}',
                  style: EDCTextStyles.bodyText.copyWith(
                    color: Colors.grey.shade600,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                // ✅ 修复：简化预估值显示逻辑，使用默认价格后总是有效
                _isLoadingPrice
                    ? Text(
                        'Calculating...',
                        style: EDCTextStyles.bodyText.copyWith(
                          color: Colors.orange.shade600, // 改用更清晰的橙色
                          fontSize: 16, // 增大字体
                          fontWeight: FontWeight.w600,
                        ),
                      )
                    : Text(
                        estimatedValue > 0
                            ? (_selectedMode == AuthMode.amount
                                ? '${estimatedValue.toStringAsFixed(3)}L' // 显示3位小数
                                : 'Rp ${_formatRupiah(estimatedValue)}')
                            : (_selectedMode == AuthMode.amount
                                ? '0.000L'
                                : 'Rp 0'), // 显示3位小数
                        style: EDCTextStyles.bodyText.copyWith(
                          color: Colors.grey.shade700,
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 显示简单的输入对话框 - 使用最基础的机制避免框架异常
  Future<void> _showSimpleInputDialog() async {
    if (_isDisposed || !mounted) return;

    String inputValue = '';

    final String? result = await showDialog<String>(
      context: context,
      builder: (BuildContext dialogContext) => _SimpleInputDialog(
        mode: _selectedMode,
        onValueChanged: (String value) {
          inputValue = value;
        },
      ),
    );

    if (_isDisposed || !mounted) return;

    if (result == 'confirmed' && inputValue.isNotEmpty) {
      final double? value = double.tryParse(inputValue);
      if (value != null && value > 0) {
        setState(() {
          if (_selectedMode == AuthMode.amount) {
            // 金额模式：确保是整数（印尼盾不使用小数）
            _currentValue = value.round().toDouble();
          } else {
            // 体积模式：允许小数，但限制到3位小数
            _currentValue = double.parse(value.toStringAsFixed(3));
          }
        });
      } else {
        // 显示错误提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please enter a valid ${_selectedMode == AuthMode.amount ? 'amount' : 'volume'}',
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  /// 构建动作按钮
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: BPColors.neutral.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: <Widget>[
            Expanded(
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: const BorderSide(color: BPColors.primary),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Cancel',
                  style: EDCTextStyles.buttonText.copyWith(
                    color: BPColors.primary,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: _canProceed() ? _completeAuthorization : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: BPColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Authorize',
                  style: EDCTextStyles.buttonText,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // === 辅助方法 ===

  /// 格式化印尼盾金额，添加千位分隔符
  String _formatRupiah(double amount) {
    // ✅ 修复：处理异常值
    if (amount.isNaN || amount.isInfinite) {
      return 'N/A';
    }

    if (amount < 0) {
      return '-${_formatRupiah(-amount)}';
    }

    if (amount == 0) {
      return '0';
    }

    final int intAmount = amount.toInt();
    final String str = intAmount.toString();
    if (str.length <= 3) return str;

    String result = '';
    int counter = 0;
    for (int i = str.length - 1; i >= 0; i--) {
      if (counter == 3) {
        result = '.$result';
        counter = 0;
      }
      result = str[i] + result;
      counter++;
    }
    return result;
  }

  void _addPresetValue(double value) {
    debugPrint('📝 预设值添加:');
    debugPrint('   添加值: $value (${_selectedMode.name} 模式)');
    debugPrint('   当前值: $_currentValue');

    setState(() {
      _currentValue += value;
    });

    debugPrint('   新的值: $_currentValue');

    // ✅ 新增：验证数值有效性
    if (_currentValue < 0) {
      debugPrint('⚠️ 检测到负值，重置为0');
      setState(() {
        _currentValue = 0.0;
      });
    }

    // ✅ 新增：检查数值上限（防止异常大的数值）
    final double maxValue = _selectedMode == AuthMode.amount
        ? 10000000.0
        : 1000.0; // 1千万印尼盾 或 1000升
    if (_currentValue > maxValue) {
      debugPrint('⚠️ 数值过大，限制为最大值: $maxValue');
      setState(() {
        _currentValue = maxValue;
      });
    }
  }

  bool _canProceed() {
    // ✅ 修复：移除价格检查，允许价格异常时继续
    return _selectedMode == AuthMode.full || _currentValue > 0;
  }

  /// 显示错误消息
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: BPColors.error,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _completeAuthorization() async {
    if (_isDisposed || !mounted) return;

    // 检查班次状态
    final ShiftService shiftService = ShiftService();
    if (!shiftService.hasActiveShift) {
      _showErrorMessage(
          'Cannot authorize nozzle: No active shift. Please start a shift first.');
      debugPrint('❌ 授权被拒绝: 没有活跃班次');
      return;
    }

          // Launch enhanced staff verification dialog with BOS integration
      try {
        debugPrint('🎯 Starting authorization flow with BOS verification');
        debugPrint('   Nozzle: ${_selectedNozzle.id} (${_selectedNozzle.name})');
        debugPrint('   Mode: ${_selectedMode.name}');
        debugPrint('   Value: $_currentValue');
        debugPrint('   Price: ${_selectedNozzle.price} (Valid: $_isPriceValid)');

        bool verified = false;
        String? staffId;
        VerifiedStaffInfo? verifiedStaffInfo;

        // Create authorization request object
        final AuthorizationRequest authRequest = AuthorizationRequest(
          nozzleId: _selectedNozzle.id,
          mode: _selectedMode,
          value: _selectedMode == AuthMode.full ? null : _currentValue,
          staffId: 'TEMP_STAFF', // Temporary staff ID, will be updated after verification
          requestTime: DateTime.now(),
        );

        debugPrint('📱 Launching enhanced staff verification dialog...');

        // Safety check: ensure context is still valid
        if (!mounted) return;

        // Use enhanced verification dialog with BOS integration
        verified = await StaffVerificationDialog.show(
          context,
          title: 'Staff Verification',
          message: 'Please tap your staff card to authorize fuel dispensing',
          authRequest: authRequest,
          nozzle: _selectedNozzle,
          onVerified: (VerifiedStaffInfo staffInfo) {
            verifiedStaffInfo = staffInfo;
            staffId = staffInfo.staffIdForFcc;
            debugPrint('✅ Staff verification successful:');
            debugPrint('   Staff ID: ${staffInfo.staffIdForFcc}');
            debugPrint('   Card Number: ${staffInfo.staffCard.cardNumber}');
            debugPrint('   Department: ${staffInfo.department}');
            debugPrint('   NFC Card: ${staffInfo.nfcCardNumber}');
            debugPrint('   Can authorize fuel: ${staffInfo.canAuthorizeFuel}');
          },
        );

              if (!mounted) return; // Safety check again

        if (verified && staffId != null && verifiedStaffInfo != null) {
          final AuthorizationRequest finalAuthRequest = AuthorizationRequest(
            nozzleId: _selectedNozzle.id,
            mode: _selectedMode,
            value: _selectedMode == AuthMode.full ? null : _currentValue,
            staffId: staffId!,
            requestTime: DateTime.now(),
          );

          debugPrint('🚀 Starting FCC authorization with verified staff...');
          debugPrint('   Final AuthRequest: ${finalAuthRequest.toString()}');
          debugPrint('   Verified Staff Card: ${verifiedStaffInfo!.staffCard.cardNumber}');

          // Show loading state
          final OverlayEntry? loadingOverlay = _showLoadingOverlay('Authorizing nozzle...');

          try {
            // Call dispenser controller for authorization
            final bool success = await ref
                .read(dispenserControllerProvider.notifier)
                .authorizeNozzle(_selectedNozzle.id, finalAuthRequest);

            // Remove loading state
            loadingOverlay?.remove();

            if (!mounted) return;

            if (success) {
              debugPrint('✅ FCC authorization successful');
              
              // Show success message with card number and staff ID
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Nozzle ${_selectedNozzle.name} authorized successfully',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Card: ${verifiedStaffInfo!.staffCard.cardNumber}',
                        style: const TextStyle(fontSize: 14),
                      ),
                      Text(
                        'Staff ID (FCC): ${verifiedStaffInfo!.staffCard.staffIdForFcc}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                  backgroundColor: BPColors.success,
                  duration: const Duration(seconds: 5), // 延长显示时间
                  behavior: SnackBarBehavior.floating,
                ),
              );

              // Close page only on success
              Navigator.of(context).pop(finalAuthRequest);
            } else {
              debugPrint('❌ FCC authorization failed');
              _showErrorMessage('Failed to authorize nozzle. Please try again.');
            }
          } catch (e) {
            // Remove loading state
            loadingOverlay?.remove();
            
            debugPrint('❌ FCC authorization exception: $e');
            if (mounted) {
              _showErrorMessage('Authorization failed: ${e.toString()}');
            }
          }
        } else {
          debugPrint('❌ Staff verification failed or was cancelled');
          if (verified && staffId == null) {
            _showErrorMessage('Staff verification incomplete. Please try again.');
          }
        }
    } catch (e) {
      debugPrint('❌ Authorization error: $e');
      debugPrint('   Error type: ${e.runtimeType}');

      // Show error dialog or handle error appropriately
      if (mounted) {
        _showErrorMessage('Authorization failed: ${e.toString()}');
      }
    }
  }

  /// 显示加载遮罩
  OverlayEntry? _showLoadingOverlay(String message) {
    if (!mounted) return null;

    final OverlayEntry overlayEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black.withOpacity(0.5),
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
                ),
                const SizedBox(height: 16),
                Text(
                  message,
                  style: EDCTextStyles.bodyText,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(overlayEntry);
    return overlayEntry;
  }
}

// === 辅助组件 ===

class _ModernModeButton extends StatelessWidget {
  const _ModernModeButton({
    required this.mode,
    required this.isSelected,
    required this.isLast,
    required this.onTap,
  });
  final AuthMode mode;
  final bool isSelected;
  final bool isLast;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final Map<AuthMode, String> modeNames = <AuthMode, String>{
      AuthMode.amount: 'Amount',
      AuthMode.volume: 'Volume',
      AuthMode.full: 'Full Tank',
    };

    return InkWell(
      onTap: onTap,
      child: Container(
        height: 44, // 精确的触摸目标高度
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? BPColors.primary : Colors.transparent,
          border: Border(
            right: BorderSide(
              color: Colors.grey.shade300,
              width: isLast ? 0 : 1,
            ),
          ),
        ),
        child: Center(
          child: Text(
            modeNames[mode]!,
            style: EDCTextStyles.bodyText.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 14, // 精确字体大小
              letterSpacing: 0.2,
              color: isSelected ? Colors.white : BPColors.primary,
              height: 1.2,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

class _ModernPresetButton extends StatelessWidget {
  const _ModernPresetButton({
    required this.value,
    required this.isAmount,
    required this.formatRupiah,
    required this.onTap,
  });
  final double value;
  final bool isAmount;
  final String Function(double) formatRupiah;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          height: 20, // 减少高度以适应2.5的宽高比
          padding: const EdgeInsets.symmetric(
              horizontal: 6, vertical: 4), // 进一步减少padding
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: BPColors.primary.withOpacity(0.4),
              width: 1,
            ),
          ),
          child: Center(
            child: Text(
              isAmount
                  ? '+${formatRupiah(value)}' // 金额：使用印尼格式（千位分隔符）
                  : '+${value.toInt()}L', // 体积：只显示整数
              style: EDCTextStyles.bodyText.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16, // 恢复正常字体大小
                color: BPColors.primary,
                height: 1.2,
                letterSpacing: 0.1,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// 简单的输入对话框 - 独立的StatefulWidget避免生命周期问题
class _SimpleInputDialog extends StatefulWidget {
  const _SimpleInputDialog({
    required this.mode,
    required this.onValueChanged,
  });
  final AuthMode mode;
  final void Function(String) onValueChanged;

  @override
  State<_SimpleInputDialog> createState() => _SimpleInputDialogState();
}

class _SimpleInputDialogState extends State<_SimpleInputDialog> {
  late TextEditingController _controller;
  String _preview = '';

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _controller.addListener(_updatePreview);
  }

  @override
  void dispose() {
    _controller.removeListener(_updatePreview);
    _controller.dispose();
    super.dispose();
  }

  void _updatePreview() {
    if (!mounted) return;

    final String text = _controller.text;
    widget.onValueChanged(text);

    if (text.isEmpty) {
      setState(() {
        _preview = '';
      });
      return;
    }

    final double? value = double.tryParse(text);
    if (value != null && value > 0) {
      setState(() {
        if (widget.mode == AuthMode.amount) {
          // 金额模式：显示格式化的印尼盾
          final NumberFormat formatter = NumberFormat('#,##0', 'id_ID');
          final String formatted = formatter.format(value.round());
          _preview = '💰 Rp $formatted';
        } else {
          // 体积模式：显示体积（3位小数）
          _preview = '⛽ ${value.toStringAsFixed(3)} L';
        }
      });
    } else {
      setState(() {
        _preview = '';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.mode == AuthMode.amount
            ? 'Enter Amount (Rupiah)'
            : 'Enter Volume (Liters)',
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 提示文本
          if (widget.mode == AuthMode.amount)
            const Padding(
              padding: EdgeInsets.only(bottom: 8),
              child: Text(
                'Enter amount in Rupiah (without decimals)',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
              ),
            ),

          // 输入框
          TextField(
            controller: _controller,
            autofocus: true,
            keyboardType: widget.mode == AuthMode.amount
                ? TextInputType.number
                : const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: widget.mode == AuthMode.volume
                ? <TextInputFormatter>[_DecimalTextInputFormatter()]
                : <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              prefixText: widget.mode == AuthMode.amount ? 'Rp ' : null,
              suffixText: widget.mode == AuthMode.volume ? ' L' : null,
              hintText:
                  widget.mode == AuthMode.amount ? 'e.g. 50000' : 'e.g. 10.500',
              border: const OutlineInputBorder(),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 16,
              ),
            ),
          ),

          // 预览
          if (_preview.isNotEmpty) ...<Widget>[
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: BPColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: BPColors.primary.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Text(
                _preview,
                style: const TextStyle(
                  color: BPColors.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                ),
              ),
            ),
          ],
        ],
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'Cancel',
            style: TextStyle(
              color: Colors.grey,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop('confirmed'),
          child: const Text(
            'OK',
            style: TextStyle(
              color: BPColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

/// 自定义小数输入格式化器 - 简化版本
class _DecimalTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 只允许数字和小数点
    final String newText = newValue.text;
    if (!RegExp(r'^[0-9.]*$').hasMatch(newText)) {
      return oldValue;
    }

    // 检查小数点数量
    final int dotCount = newText.split('.').length - 1;
    if (dotCount > 1) {
      return oldValue;
    }

    // 检查小数位数（最多3位）
    if (newText.contains('.')) {
      final List<String> parts = newText.split('.');
      if (parts.length == 2 && parts[1].length > 3) {
        return oldValue;
      }
    }

    // 验证数值范围
    final double? doubleValue = double.tryParse(newText);
    if (doubleValue != null && doubleValue > 9999999) {
      return oldValue;
    }

    return newValue;
  }
}
