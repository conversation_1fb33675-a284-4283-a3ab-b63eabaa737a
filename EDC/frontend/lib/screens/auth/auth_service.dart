// EDC/frontend/lib/screens/auth/auth_service.dart

/// 认证服务 - 兼容性包装器
/// 保持原有接口不变，内部使用新的认证系统

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/new_auth_service.dart';
import '../../services/api/employee_api.dart';
import '../../models/auth_models.dart' as auth_models;

/// 旧的认证服务，现在作为新认证服务的包装器
class AuthService extends ChangeNotifier {
  AuthService({required this.newAuthService}) {
    // 监听新认证服务的状态变化
    newAuthService.addListener(_onNewAuthStateChanged);
    _updateStateFromNewService();
  }

  final NewAuthService newAuthService;
  
  // 内部状态
  Employee? _currentEmployee;
  bool _isLoggedIn = false;
  bool _initialized = false;

  // 公共接口 - 保持与原有代码兼容
  Employee? get currentEmployee => _currentEmployee;
  bool get isLoggedInSync => _isLoggedIn;
  bool get initialized => _initialized;

  /// 监听新认证服务状态变化
  void _onNewAuthStateChanged() {
    _updateStateFromNewService();
  }

  /// 从新认证服务更新状态
  void _updateStateFromNewService() {
    final auth_models.AuthUser? user = newAuthService.currentUser;
    final bool wasLoggedIn = _isLoggedIn;

    _isLoggedIn = newAuthService.isLoggedIn;
    _initialized = newAuthService.isInitialized;
    _currentEmployee = _convertUserToEmployee(user);

    // 只有状态真正改变时才通知监听者
    if (wasLoggedIn != _isLoggedIn || _currentEmployee != null) {
      notifyListeners();
    }
  }

  /// 将AuthUser转换为Employee（兼容性）
  Employee? _convertUserToEmployee(auth_models.AuthUser? user) {
    if (user == null) return null;
    
    return Employee(
      id: user.id,
      employeeNo: user.username,
      name: user.fullName,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roles: user.roles,
      permissions: user.permissions,
      lastLoginAt: user.lastLoginAt,
      stationIds: user.stations.map((s) => s.id).toList(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// 登录方法 - 委托给新认证服务
  Future<bool> login(String username, String password) async {
    try {
      final bool success = await newAuthService.login(username, password);
      return success;
    } catch (e) {
      debugPrint('❌ AuthService.login failed: $e');
      rethrow;
    }
  }

  /// 登出方法 - 委托给新认证服务
  Future<void> logout() async {
    try {
      await newAuthService.logout();
    } catch (e) {
      debugPrint('❌ AuthService.logout failed: $e');
      rethrow;
    }
  }

  /// 刷新员工详情（兼容性方法）
  Future<void> refreshEmployeeDetail() async {
    // 新认证服务不需要单独刷新，状态会自动同步
    debugPrint('🔄 AuthService.refreshEmployeeDetail called (no-op in new system)');
  }

  /// 获取当前员工ID（兼容性方法）
  Future<String?> getCurrentEmployeeId() async {
    return _currentEmployee?.id;
  }

  /// 获取当前员工编号（兼容性方法）
  Future<String?> getCurrentEmployeeNo() async {
    return _currentEmployee?.employeeNo;
  }

  /// 获取当前站点ID列表（兼容性方法）
  Future<List<int>?> getCurrentStationIds() async {
    return _currentEmployee?.stationIds;
  }

  /// 检查管理权限（兼容性方法）
  Future<bool> hasManagementAccess() async {
    final auth_models.SystemAccess? systemAccess = newAuthService.systemAccess;
    if (systemAccess != null) {
      return systemAccess.accessLevel == 'admin' ||
             systemAccess.accessLevel == 'manager';
    }
    return false;
  }

  /// 设置登录状态（兼容性方法）
  void setLoggedInState(bool loggedIn) {
    // 这个方法在新系统中不需要，状态由新认证服务管理
    debugPrint('🔄 AuthService.setLoggedInState called (no-op in new system)');
  }

  /// 更新员工信息（兼容性方法）
  void updateEmployeeInfo(Employee employee) {
    // 这个方法在新系统中不需要，状态由新认证服务管理
    debugPrint('🔄 AuthService.updateEmployeeInfo called (no-op in new system)');
  }

  /// 清除员工信息（兼容性方法）
  void clearEmployeeInfo() {
    // 这个方法在新系统中不需要，状态由新认证服务管理
    debugPrint('🔄 AuthService.clearEmployeeInfo called (no-op in new system)');
  }

  /// 清除登录状态（兼容性方法）
  void clearLoginState() {
    // 委托给新认证服务
    newAuthService.logout();
  }

  @override
  void dispose() {
    newAuthService.removeListener(_onNewAuthStateChanged);
    super.dispose();
  }
}

/// AuthService Provider - 使用新认证服务作为依赖
final Provider<AuthService> authServiceProvider = Provider<AuthService>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return AuthService(newAuthService: newAuthService);
});
