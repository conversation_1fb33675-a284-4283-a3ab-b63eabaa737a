import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../widgets/safe_scaffold.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// M1卡操作类型
enum M1Operation {
  read,
  write,
  initWallet,
  getBalance,
  increaseValue,
  decreaseValue,
  restore,
}

// M1卡模块状态
class M1CardState {
  M1CardState({
    this.isProcessing = false,
    this.errorMessage,
    this.blockData = const <String, String>{},
    this.balance = 0,
    this.isAuthenticated = false,
  });
  final bool isProcessing;
  final String? errorMessage;
  final Map<String, String> blockData;
  final int balance;
  final bool isAuthenticated;

  M1CardState copyWith({
    bool? isProcessing,
    String? errorMessage,
    Map<String, String>? blockData,
    int? balance,
    bool? isAuthenticated,
  }) {
    return M1CardState(
      isProcessing: isProcessing ?? this.isProcessing,
      errorMessage: errorMessage,
      blockData: blockData ?? this.blockData,
      balance: balance ?? this.balance,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

// M1卡控制器
class M1CardController extends StateNotifier<M1CardState> {
  M1CardController() : super(M1CardState()) {
    _channel = const MethodChannel('com.example.edc_app/card');
  }

  late final MethodChannel _channel;

  // 认证M1卡
  Future<bool> authenticate({
    required int sector,
    required int keyType,
    required String key,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, errorMessage: null);

      final result = await _channel.invokeMethod('m1Auth', <String, Object>{
        'sector': sector,
        'keyType': keyType,
        'key': key,
      });

      final bool success = result['success'] ?? false;
      if (!success) {
        final String message = result['message'] ?? '认证失败';
        state = state.copyWith(
          isProcessing: false,
          errorMessage: message,
          isAuthenticated: false,
        );
        return false;
      }

      state = state.copyWith(
        isProcessing: false,
        errorMessage: null,
        isAuthenticated: true,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: '认证异常: $e',
        isAuthenticated: false,
      );
      return false;
    }
  }

  // 读取M1卡数据
  Future<bool> readBlock({
    required int sector,
    required int block,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, errorMessage: null);

      final result = await _channel.invokeMethod('m1ReadBlock', <String, int>{
        'sector': sector,
        'block': block,
      });

      final bool success = result['success'] ?? false;
      if (!success) {
        final String message = result['message'] ?? '读取失败';
        state = state.copyWith(
          isProcessing: false,
          errorMessage: message,
        );
        return false;
      }

      final Map<String, dynamic> data =
          Map<String, dynamic>.from(result['data'] ?? <dynamic, dynamic>{});
      final Map<String, String> blockData = state.blockData.map(MapEntry.new);

      data.forEach((String key, value) {
        if (value is String) {
          blockData[key] = value;
        }
      });

      state = state.copyWith(
        isProcessing: false,
        errorMessage: null,
        blockData: blockData,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: '读取异常: $e',
      );
      return false;
    }
  }

  // 写入M1卡数据
  Future<bool> writeBlock({
    required int sector,
    required int block,
    required String data,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, errorMessage: null);

      final result =
          await _channel.invokeMethod('m1WriteBlock', <String, Object>{
        'sector': sector,
        'block': block,
        'data': data,
      });

      final bool success = result['success'] ?? false;
      if (!success) {
        final String message = result['message'] ?? '写入失败';
        state = state.copyWith(
          isProcessing: false,
          errorMessage: message,
        );
        return false;
      }

      state = state.copyWith(
        isProcessing: false,
        errorMessage: null,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: '写入异常: $e',
      );
      return false;
    }
  }

  // 初始化钱包
  Future<bool> initWallet({
    required int sector,
    required int block,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, errorMessage: null);

      final result = await _channel.invokeMethod('m1InitWallet', <String, int>{
        'sector': sector,
        'block': block,
      });

      final bool success = result['success'] ?? false;
      if (!success) {
        final String message = result['message'] ?? '初始化钱包失败';
        state = state.copyWith(
          isProcessing: false,
          errorMessage: message,
        );
        return false;
      }

      state = state.copyWith(
        isProcessing: false,
        errorMessage: null,
        balance: 0,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: '初始化钱包异常: $e',
      );
      return false;
    }
  }

  // 获取钱包余额
  Future<bool> getBalance({
    required int sector,
    required int block,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, errorMessage: null);

      final result = await _channel.invokeMethod('m1GetBalance', <String, int>{
        'sector': sector,
        'block': block,
      });

      final bool success = result['success'] ?? false;
      if (!success) {
        final String message = result['message'] ?? '获取余额失败';
        state = state.copyWith(
          isProcessing: false,
          errorMessage: message,
        );
        return false;
      }

      final int balance = result['balance'] ?? 0;
      state = state.copyWith(
        isProcessing: false,
        errorMessage: null,
        balance: balance,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: '获取余额异常: $e',
      );
      return false;
    }
  }

  // 增加钱包金额
  Future<bool> increaseValue({
    required int sector,
    required int block,
    required int amount,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, errorMessage: null);

      final result =
          await _channel.invokeMethod('m1IncreaseValue', <String, int>{
        'sector': sector,
        'block': block,
        'amount': amount,
      });

      final bool success = result['success'] ?? false;
      if (!success) {
        final String message = result['message'] ?? '增加金额失败';
        state = state.copyWith(
          isProcessing: false,
          errorMessage: message,
        );
        return false;
      }

      final int balance = result['balance'] ?? 0;
      state = state.copyWith(
        isProcessing: false,
        errorMessage: null,
        balance: balance,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: '增加金额异常: $e',
      );
      return false;
    }
  }

  // 减少钱包金额
  Future<bool> decreaseValue({
    required int sector,
    required int block,
    required int amount,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, errorMessage: null);

      final result =
          await _channel.invokeMethod('m1DecreaseValue', <String, int>{
        'sector': sector,
        'block': block,
        'amount': amount,
      });

      final bool success = result['success'] ?? false;
      if (!success) {
        final String message = result['message'] ?? '减少金额失败';
        state = state.copyWith(
          isProcessing: false,
          errorMessage: message,
        );
        return false;
      }

      final int balance = result['balance'] ?? 0;
      state = state.copyWith(
        isProcessing: false,
        errorMessage: null,
        balance: balance,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: '减少金额异常: $e',
      );
      return false;
    }
  }

  // 恢复操作
  Future<bool> restore({
    required int sector,
    required int block,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, errorMessage: null);

      final result = await _channel.invokeMethod('m1Restore', <String, int>{
        'sector': sector,
        'block': block,
      });

      final bool success = result['success'] ?? false;
      if (!success) {
        final String message = result['message'] ?? '恢复操作失败';
        state = state.copyWith(
          isProcessing: false,
          errorMessage: message,
        );
        return false;
      }

      state = state.copyWith(
        isProcessing: false,
        errorMessage: null,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: '恢复操作异常: $e',
      );
      return false;
    }
  }
}

// 创建Provider
final StateNotifierProvider<M1CardController, M1CardState>
    m1CardControllerProvider =
    StateNotifierProvider<M1CardController, M1CardState>(
        (StateNotifierProviderRef<M1CardController, M1CardState> ref) {
  return M1CardController();
});

class M1Page extends ConsumerStatefulWidget {
  const M1Page({super.key});

  @override
  ConsumerState<M1Page> createState() => _M1PageState();
}

class _M1PageState extends ConsumerState<M1Page> {
  final TextEditingController _sectorController =
      TextEditingController(text: '0');
  final TextEditingController _blockController =
      TextEditingController(text: '0');
  final TextEditingController _keyAController =
      TextEditingController(text: 'FFFFFFFFFFFF');
  final TextEditingController _keyBController = TextEditingController();
  final TextEditingController _block0Controller = TextEditingController();
  final TextEditingController _block1Controller = TextEditingController();
  final TextEditingController _block2Controller = TextEditingController();
  final TextEditingController _amountController =
      TextEditingController(text: '10');

  // 用于钱包操作的控制器
  final TextEditingController _walletSectorController =
      TextEditingController(text: '1');
  final TextEditingController _walletBlockController =
      TextEditingController(text: '0');
  final TextEditingController _walletKeyAController =
      TextEditingController(text: 'FFFFFFFFFFFF');
  final TextEditingController _walletKeyBController = TextEditingController();

  // 键盘焦点
  FocusNode? _currentFocus;

  @override
  void dispose() {
    _sectorController.dispose();
    _blockController.dispose();
    _keyAController.dispose();
    _keyBController.dispose();
    _block0Controller.dispose();
    _block1Controller.dispose();
    _block2Controller.dispose();
    _amountController.dispose();
    _walletSectorController.dispose();
    _walletBlockController.dispose();
    _walletKeyAController.dispose();
    _walletKeyBController.dispose();
    super.dispose();
  }

  // 隐藏键盘
  void _hideKeyboard() {
    if (_currentFocus != null) {
      _currentFocus!.unfocus();
      _currentFocus = null;
    }
  }

  // 读取所有扇区数据
  Future<void> _readAllSector() async {
    _hideKeyboard();

    final int sector = int.tryParse(_sectorController.text) ?? 0;
    int keyType = 0; // 默认使用KeyA
    String key = _keyAController.text;

    if (_keyAController.text.isEmpty && _keyBController.text.isNotEmpty) {
      keyType = 1;
      key = _keyBController.text;
    } else if (_keyAController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入密钥')),
      );
      return;
    }

    final M1CardController controller =
        ref.read(m1CardControllerProvider.notifier);

    // 先进行认证
    final bool authSuccess = await controller.authenticate(
      sector: sector,
      keyType: keyType,
      key: key,
    );

    if (!authSuccess) {
      // 认证失败的提示已在controller中处理
      return;
    }

    // 读取数据
    for (int i = 0; i < 3; i++) {
      final bool success = await controller.readBlock(
        sector: sector,
        block: i,
      );

      if (success) {
        final M1CardState state = ref.read(m1CardControllerProvider);
        final Map<String, String> blockData = state.blockData;

        if (i == 0) {
          _block0Controller.text = blockData['block0'] ?? '';
        } else if (i == 1) {
          _block1Controller.text = blockData['block1'] ?? '';
        } else if (i == 2) {
          _block2Controller.text = blockData['block2'] ?? '';
        }
      }
    }
  }

  // 写入所有扇区数据
  Future<void> _writeAllSector() async {
    _hideKeyboard();

    final int sector = int.tryParse(_sectorController.text) ?? 0;
    int keyType = 0; // 默认使用KeyA
    String key = _keyAController.text;

    if (_keyAController.text.isEmpty && _keyBController.text.isNotEmpty) {
      keyType = 1;
      key = _keyBController.text;
    } else if (_keyAController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入密钥')),
      );
      return;
    }

    final M1CardController controller =
        ref.read(m1CardControllerProvider.notifier);

    // 先进行认证
    final bool authSuccess = await controller.authenticate(
      sector: sector,
      keyType: keyType,
      key: key,
    );

    if (!authSuccess) {
      // 认证失败的提示已在controller中处理
      return;
    }

    // 写入数据
    if (_block0Controller.text.isNotEmpty) {
      final bool success = await controller.writeBlock(
        sector: sector,
        block: 0,
        data: _block0Controller.text,
      );

      if (success) {
        _block0Controller.clear();
      }
    }

    if (_block1Controller.text.isNotEmpty) {
      final bool success = await controller.writeBlock(
        sector: sector,
        block: 1,
        data: _block1Controller.text,
      );

      if (success) {
        _block1Controller.clear();
      }
    }

    if (_block2Controller.text.isNotEmpty) {
      final bool success = await controller.writeBlock(
        sector: sector,
        block: 2,
        data: _block2Controller.text,
      );

      if (success) {
        _block2Controller.clear();
      }
    }
  }

  // 初始化钱包
  Future<void> _initWallet() async {
    _hideKeyboard();

    final int sector = int.tryParse(_walletSectorController.text) ?? 1;
    final int block = int.tryParse(_walletBlockController.text) ?? 0;
    int keyType = 0; // 默认使用KeyA
    String key = _walletKeyAController.text;

    if (_walletKeyAController.text.isEmpty &&
        _walletKeyBController.text.isNotEmpty) {
      keyType = 1;
      key = _walletKeyBController.text;
    } else if (_walletKeyAController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入密钥')),
      );
      return;
    }

    final M1CardController controller =
        ref.read(m1CardControllerProvider.notifier);

    // 先进行认证
    final bool authSuccess = await controller.authenticate(
      sector: sector,
      keyType: keyType,
      key: key,
    );

    if (!authSuccess) {
      // 认证失败的提示已在controller中处理
      return;
    }

    // 初始化钱包
    await controller.initWallet(
      sector: sector,
      block: block,
    );
  }

  // 获取余额
  Future<void> _getBalance() async {
    _hideKeyboard();

    final int sector = int.tryParse(_walletSectorController.text) ?? 1;
    final int block = int.tryParse(_walletBlockController.text) ?? 0;
    int keyType = 0; // 默认使用KeyA
    String key = _walletKeyAController.text;

    if (_walletKeyAController.text.isEmpty &&
        _walletKeyBController.text.isNotEmpty) {
      keyType = 1;
      key = _walletKeyBController.text;
    } else if (_walletKeyAController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入密钥')),
      );
      return;
    }

    final M1CardController controller =
        ref.read(m1CardControllerProvider.notifier);

    // 先进行认证
    final bool authSuccess = await controller.authenticate(
      sector: sector,
      keyType: keyType,
      key: key,
    );

    if (!authSuccess) {
      // 认证失败的提示已在controller中处理
      return;
    }

    // 获取余额
    await controller.getBalance(
      sector: sector,
      block: block,
    );
  }

  // 增加金额
  Future<void> _increaseValue() async {
    _hideKeyboard();

    final int sector = int.tryParse(_walletSectorController.text) ?? 1;
    final int block = int.tryParse(_walletBlockController.text) ?? 0;
    final int amount = int.tryParse(_amountController.text) ?? 0;
    int keyType = 0; // 默认使用KeyA
    String key = _walletKeyAController.text;

    if (_walletKeyAController.text.isEmpty &&
        _walletKeyBController.text.isNotEmpty) {
      keyType = 1;
      key = _walletKeyBController.text;
    } else if (_walletKeyAController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入密钥')),
      );
      return;
    }

    final M1CardController controller =
        ref.read(m1CardControllerProvider.notifier);

    // 先进行认证
    final bool authSuccess = await controller.authenticate(
      sector: sector,
      keyType: keyType,
      key: key,
    );

    if (!authSuccess) {
      // 认证失败的提示已在controller中处理
      return;
    }

    // 增加金额
    await controller.increaseValue(
      sector: sector,
      block: block,
      amount: amount,
    );
  }

  // 减少金额
  Future<void> _decreaseValue() async {
    _hideKeyboard();

    final int sector = int.tryParse(_walletSectorController.text) ?? 1;
    final int block = int.tryParse(_walletBlockController.text) ?? 0;
    final int amount = int.tryParse(_amountController.text) ?? 0;
    int keyType = 0; // 默认使用KeyA
    String key = _walletKeyAController.text;

    if (_walletKeyAController.text.isEmpty &&
        _walletKeyBController.text.isNotEmpty) {
      keyType = 1;
      key = _walletKeyBController.text;
    } else if (_walletKeyAController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入密钥')),
      );
      return;
    }

    final M1CardController controller =
        ref.read(m1CardControllerProvider.notifier);

    // 先进行认证
    final bool authSuccess = await controller.authenticate(
      sector: sector,
      keyType: keyType,
      key: key,
    );

    if (!authSuccess) {
      // 认证失败的提示已在controller中处理
      return;
    }

    // 减少金额
    await controller.decreaseValue(
      sector: sector,
      block: block,
      amount: amount,
    );
  }

  @override
  Widget build(BuildContext context) {
    final M1CardState m1CardState = ref.watch(m1CardControllerProvider);

    return SafeScaffold(
      appBar: AppBar(
        title: const Text('M1卡操作'),
        centerTitle: true,
      ),
      body: m1CardState.isProcessing
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  if (m1CardState.errorMessage != null)
                    Card(
                      color: Colors.red.shade50,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: <Widget>[
                            const Icon(Icons.error, color: Colors.red),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                m1CardState.errorMessage!,
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // 基本操作
                  _buildOperationCard(),

                  const SizedBox(height: 16),

                  // 钱包操作
                  _buildWalletOperationCard(m1CardState),
                ],
              ),
            ),
    );
  }

  // 基本操作卡片
  Widget _buildOperationCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              '基本操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // 扇区和密钥输入
            Row(
              children: <Widget>[
                Expanded(
                  child: TextField(
                    controller: _sectorController,
                    decoration: const InputDecoration(
                      labelText: '扇区',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onTap: () =>
                        _currentFocus = FocusScope.of(context).focusedChild,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _keyAController,
                    decoration: const InputDecoration(
                      labelText: 'KeyA (16进制)',
                      border: OutlineInputBorder(),
                    ),
                    onTap: () =>
                        _currentFocus = FocusScope.of(context).focusedChild,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _keyBController,
                    decoration: const InputDecoration(
                      labelText: 'KeyB (16进制)',
                      border: OutlineInputBorder(),
                    ),
                    onTap: () =>
                        _currentFocus = FocusScope.of(context).focusedChild,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 数据块输入
            TextField(
              controller: _block0Controller,
              decoration: const InputDecoration(
                labelText: '块0数据 (16进制)',
                border: OutlineInputBorder(),
              ),
              maxLines: 1,
              onTap: () => _currentFocus = FocusScope.of(context).focusedChild,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _block1Controller,
              decoration: const InputDecoration(
                labelText: '块1数据 (16进制)',
                border: OutlineInputBorder(),
              ),
              maxLines: 1,
              onTap: () => _currentFocus = FocusScope.of(context).focusedChild,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _block2Controller,
              decoration: const InputDecoration(
                labelText: '块2数据 (16进制)',
                border: OutlineInputBorder(),
              ),
              maxLines: 1,
              onTap: () => _currentFocus = FocusScope.of(context).focusedChild,
            ),
            const SizedBox(height: 16),

            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                ElevatedButton.icon(
                  onPressed: _readAllSector,
                  icon: const Icon(Icons.download),
                  label: const Text('读取'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _writeAllSector,
                  icon: const Icon(Icons.upload),
                  label: const Text('写入'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 钱包操作卡片
  Widget _buildWalletOperationCard(M1CardState state) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              '钱包操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // 钱包余额显示
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(Icons.account_balance_wallet, color: Colors.green),
                  const SizedBox(width: 8),
                  Text(
                    '当前余额：${state.balance}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // 扇区、块和密钥输入
            Row(
              children: <Widget>[
                Expanded(
                  child: TextField(
                    controller: _walletSectorController,
                    decoration: const InputDecoration(
                      labelText: '扇区',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onTap: () =>
                        _currentFocus = FocusScope.of(context).focusedChild,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _walletBlockController,
                    decoration: const InputDecoration(
                      labelText: '块',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onTap: () =>
                        _currentFocus = FocusScope.of(context).focusedChild,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: <Widget>[
                Expanded(
                  child: TextField(
                    controller: _walletKeyAController,
                    decoration: const InputDecoration(
                      labelText: 'KeyA (16进制)',
                      border: OutlineInputBorder(),
                    ),
                    onTap: () =>
                        _currentFocus = FocusScope.of(context).focusedChild,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _walletKeyBController,
                    decoration: const InputDecoration(
                      labelText: 'KeyB (16进制)',
                      border: OutlineInputBorder(),
                    ),
                    onTap: () =>
                        _currentFocus = FocusScope.of(context).focusedChild,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 金额输入
            TextField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: '金额',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              onTap: () => _currentFocus = FocusScope.of(context).focusedChild,
            ),
            const SizedBox(height: 16),

            // 操作按钮
            Wrap(
              spacing: 8,
              runSpacing: 8,
              alignment: WrapAlignment.spaceEvenly,
              children: <Widget>[
                ElevatedButton.icon(
                  onPressed: _initWallet,
                  icon: const Icon(Icons.restart_alt),
                  label: const Text('初始化'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _getBalance,
                  icon: const Icon(Icons.account_balance),
                  label: const Text('查询余额'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _increaseValue,
                  icon: const Icon(Icons.add),
                  label: const Text('增加'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _decreaseValue,
                  icon: const Icon(Icons.remove),
                  label: const Text('减少'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
