import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:developer'; // For logging
import 'package:dio/dio.dart'; // <<< Add this import for DioException
// 添加路由导航
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/order.dart';
import '../../services/api/api_service.dart';
import '../../services/api/api_client.dart'; // Import ApiException
import '../../services/auto_print_service.dart'; // 添加自动打印服务
import '../../widgets/order_status_badge.dart';
import '../../widgets/safe_scaffold.dart'; // ADD import
import '../../constants/bp_colors.dart'; // 添加BP颜色
import '../../theme/app_theme.dart'; // 添加EDC主题
import '../../utils/time_utils.dart'; // 添加时间工具

// 印尼盾格式化
final NumberFormat _currencyFormatter = NumberFormat.currency(
  locale: 'id_ID',
  symbol: 'Rp ',
  decimalDigits: 0,
);

/// Order Detail Page
/// Display complete order information and operation options
class OrderDetailPage extends ConsumerStatefulWidget {
  const OrderDetailPage({super.key, required this.orderId});
  // 修改：接收 int 类型的订单 ID
  final String orderId;

  @override
  ConsumerState<OrderDetailPage> createState() => _OrderDetailPageState();
}

class _OrderDetailPageState extends ConsumerState<OrderDetailPage> {
  final ApiService _apiService = ApiService();
  bool _isLoading = true;
  Order? _orderDetail;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchOrderDetail();
  }

  Future<void> _fetchOrderDetail() async {
    if (!_isLoading) {
      setState(() {
        _isLoading = true;
        _error = null;
      });
    }

    try {
      final Order order =
          await _apiService.orderApi.getOrderDetail(widget.orderId);
      if (mounted) {
        setState(() {
          _orderDetail = order;
          _isLoading = false;
          _error = null;
        });
      }
    } catch (e) {
      log('Error fetching order detail: $e', error: e);
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (e is ApiException) {
            _error =
                'Failed to load order: ${e.message} (Code: ${e.statusCode})';
          } else if (e is DioException) {
            _error = 'Network error: ${e.message ?? e.toString()}';
          } else {
            _error = 'Unknown error occurred: ${e.toString()}';
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
      appBar: AppBar(
        title: const Text(
          'Receipt Detail',
          style: EDCTextStyles.appBarTitle,
        ),
        backgroundColor: BPColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: 64,
        actions: <Widget>[
          // Print button
          if (_orderDetail != null && !_isLoading)
            IconButton(
              icon: const Icon(Icons.print, size: 24),
              tooltip: 'Print Receipt',
              onPressed: _printOrderReceipt,
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// 直接打印订单小票
  Future<void> _printOrderReceipt() async {
    if (_orderDetail == null) {
      _showSnackBar('Order data is not available for printing', BPColors.error);
      return;
    }

    // 显示打印中状态
    _showSnackBar('Printing receipt...', BPColors.primary, showProgress: true);

    try {
      // 使用AutoPrintService直接打印订单小票，设置为重打模式
      await AutoPrintService.printOrderReceipt(
        _orderDetail!,
        isReprint: true, // 从订单详情页面打印设置为重打模式
      );

      if (mounted) {
        _showSnackBar('Receipt printed successfully', BPColors.success);
      }
    } catch (e) {
      log('Error printing receipt: $e', error: e);
      if (mounted) {
        _showSnackBar('Print failed: ${e.toString()}', BPColors.error);
      }
    }
  }

  void _showSnackBar(String message, Color backgroundColor,
      {bool showProgress = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: <Widget>[
            if (showProgress) ...<Widget>[
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
            ],
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: showProgress ? 2 : 3),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
        ),
      );
    } else if (_error != null) {
      return _buildErrorState();
    } else if (_orderDetail != null) {
      return _buildOrderDetailContent(_orderDetail!);
    } else {
      return _buildEmptyState();
    }
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.error_outline,
              size: 64,
              color: BPColors.error,
            ),
            const SizedBox(height: 16),
            const Text(
              'Connection Error',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: BPColors.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: const TextStyle(
                fontSize: 14,
                color: BPColors.neutral,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _fetchOrderDetail,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: BPColors.neutral.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'No Order Found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: BPColors.neutral,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Unable to load order information',
            style: TextStyle(
              fontSize: 14,
              color: BPColors.neutral,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetailContent(Order order) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header Card - Order Info
            _buildHeaderCard(order),
            const SizedBox(height: 12),

            // Amount Summary Card
            _buildAmountSummaryCard(order),
            const SizedBox(height: 12),

            // Order Items Card
            _buildOrderItemsCard(order),
            const SizedBox(height: 12),

            // Payment Information Card
            _buildPaymentInfoCard(order),
            const SizedBox(height: 12),

            // Additional Information Cards
            _buildAdditionalInfoCard(order),
            const SizedBox(height: 12),

            // Promotion Information Card (if any)
            if (order.promotions.isNotEmpty) _buildPromotionInfoCard(order),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(Order order) {
    final String formattedTime = TimeUtils.formatDateTime(ref, order.createTime, isUtc: true,
        format: 'yyyy-MM-dd HH:mm:ss');

    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header with icon
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: BPColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.receipt_long,
                      color: BPColors.primary, size: 22),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Receipt Information',
                    style: TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),
                OrderStatusBadge(status: order.status),
              ],
            ),

            const SizedBox(height: 16),

            // Order details
            _buildInfoRow('Order Serial No', order.orderId),
            const SizedBox(height: 8),
            _buildInfoRow('Date & Time', formattedTime),
            const SizedBox(height: 8),
            _buildInfoRow('Station Name', order.stationName ?? 'Unknown Station'),
            const SizedBox(height: 8),
            _buildInfoRow('Staff Name', order.staffName ?? order.operatorId),
            if (order.customerName != null &&
                order.customerName!.isNotEmpty) ...<Widget>[
              const SizedBox(height: 8),
              _buildInfoRow('Customer', order.customerName!),
            ],
            if (order.customerPhone != null &&
                order.customerPhone!.isNotEmpty) ...<Widget>[
              const SizedBox(height: 8),
              _buildInfoRow('Phone', order.customerPhone!),
            ],
            if (order.vehicleType != null &&
                order.vehicleType!.isNotEmpty) ...<Widget>[
              const SizedBox(height: 8),
              _buildInfoRow('Vehicle Type', order.vehicleType!),
            ],
            if (order.licensePlate != null &&
                order.licensePlate!.isNotEmpty) ...<Widget>[
              const SizedBox(height: 8),
              _buildInfoRow('License Plate', order.licensePlate!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSummaryCard(Order order) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: BPColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.monetization_on,
                      color: BPColors.primary, size: 22),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Amount Summary',
                  style: TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Amount breakdown
            _buildAmountRow('Order Amount', order.amount),
            if (order.discountAmount > 0) ...<Widget>[
              const SizedBox(height: 8),
              _buildAmountRow('Discount', -order.discountAmount,
                  isDiscount: true),
            ],
            const SizedBox(height: 12),
            Container(
              height: 1,
              color: Colors.grey.withOpacity(0.3),
            ),
            const SizedBox(height: 12),
            _buildAmountRow('Final Amount', order.finalAmount, isTotal: true),
            const SizedBox(height: 8),
            _buildAmountRow('Paid Amount', order.paidAmount, isPaid: true),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItemsCard(Order order) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: BPColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.shopping_cart,
                      color: BPColors.primary, size: 22),
                ),
                const SizedBox(width: 12),
                Text(
                  'Order Items (${order.items.length})',
                  style: const TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Items list
            if (order.items.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'No items in this order',
                    style: TextStyle(
                      fontSize: 14,
                      color: BPColors.neutral,
                    ),
                  ),
                ),
              )
            else
              ...order.items
                  .asMap()
                  .entries
                  .map((MapEntry<int, Map<String, dynamic>> entry) {
                final int index = entry.key;
                final Map<String, dynamic> item = entry.value;
                return _buildOrderItem(item, index > 0);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(Map<String, dynamic> item, bool showDivider) {
    final String productName =
        item['product_name'] as String? ?? 'Unknown Product';
    final double quantity = (item['quantity'] as num?)?.toDouble() ?? 0.0;
    final double unitPrice = (item['unit_price'] as num?)?.toDouble() ?? 0.0;
    final double totalPrice = (item['total_price'] as num?)?.toDouble() ?? 0.0;
    final String? pumpId = item['pump_id'] as String?;
    final String? nozzleId = item['nozzle_id'] as String?;

    return Column(
      children: <Widget>[
        if (showDivider) ...<Widget>[
          const SizedBox(height: 12),
          Container(height: 1, color: Colors.grey.withOpacity(0.2)),
          const SizedBox(height: 12),
        ],
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Product icon
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: BPColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(Icons.local_gas_station,
                  color: BPColors.primary, size: 16),
            ),
            const SizedBox(width: 12),

            // Product details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    productName,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${quantity.toStringAsFixed(3)} L × ${_currencyFormatter.format(unitPrice)}/L',
                    style: const TextStyle(
                      fontSize: 13,
                      color: BPColors.neutral,
                    ),
                  ),
                  if (pumpId != null) ...<Widget>[
                    const SizedBox(height: 2),
                    Text(
                      'Pump: $pumpId${nozzleId != null && nozzleId != '-1' ? ' - Nozzle: $nozzleId' : ''}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: BPColors.neutral,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Amount
            Text(
              _currencyFormatter.format(totalPrice),
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: BPColors.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaymentInfoCard(Order order) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: BPColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.payment,
                      color: BPColors.primary, size: 22),
                ),
                const SizedBox(width: 12),
                Text(
                  'Payment Information (${order.payments.length})',
                  style: const TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Payment methods
            if (order.payments.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'No payment information',
                    style: TextStyle(
                      fontSize: 14,
                      color: BPColors.neutral,
                    ),
                  ),
                ),
              )
            else
              ...order.payments
                  .asMap()
                  .entries
                  .map((MapEntry<int, Map<String, dynamic>> entry) {
                final int index = entry.key;
                final Map<String, dynamic> payment = entry.value;
                return _buildPaymentMethod(payment, index > 0);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethod(Map<String, dynamic> payment, bool showDivider) {
    final String method =
        payment['payment_method_name'] as String? ?? 
        payment['payment_method'] as String? ?? 'Unknown Method';
    final double amount = (payment['amount'] as num?)?.toDouble() ?? 0.0;
    final String status = payment['status'] as String? ?? 'Unknown';
    final String? transactionId = payment['transaction_id'] as String?;

    return Column(
      children: <Widget>[
        if (showDivider) ...<Widget>[
          const SizedBox(height: 12),
          Container(height: 1, color: Colors.grey.withOpacity(0.2)),
          const SizedBox(height: 12),
        ],
        Row(
          children: <Widget>[
            // Payment method icon
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: _getPaymentMethodColor(method).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(_getPaymentMethodIcon(method),
                  color: _getPaymentMethodColor(method), size: 16),
            ),
            const SizedBox(width: 12),

            // Payment details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    method,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Status: $status',
                    style: TextStyle(
                      fontSize: 13,
                      color: _getStatusColor(status),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (transactionId != null) ...<Widget>[
                    const SizedBox(height: 2),
                    Text(
                      'ID: $transactionId',
                      style: const TextStyle(
                        fontSize: 12,
                        color: BPColors.neutral,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Amount
            Text(
              _currencyFormatter.format(amount),
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: BPColors.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdditionalInfoCard(Order order) {
    final String updateTime = TimeUtils.formatDateTime(ref, order.updatedAt, isUtc: true,
        format: 'yyyy-MM-dd HH:mm:ss');
    final String? completedTime = order.completedAt != null
        ? TimeUtils.formatDateTime(ref, order.completedAt!, isUtc: true,
            format: 'yyyy-MM-dd HH:mm:ss')
        : null;
    final String? cancelledTime = order.cancelledAt != null
        ? TimeUtils.formatDateTime(ref, order.cancelledAt!, isUtc: true,
            format: 'yyyy-MM-dd HH:mm:ss')
        : null;

    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: BPColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.info_outline,
                      color: BPColors.primary, size: 22),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Additional Information',
                  style: TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Time records
            _buildInfoRow('Last Updated', updateTime),
            if (completedTime != null) ...<Widget>[
              const SizedBox(height: 8),
              _buildInfoRow('Completed At', completedTime),
            ],
            if (cancelledTime != null) ...<Widget>[
              const SizedBox(height: 8),
              _buildInfoRow('Cancelled At', cancelledTime),
            ],
            if (order.customerId != null) ...<Widget>[
              const SizedBox(height: 8),
              _buildInfoRow('Customer ID', order.customerId.toString()),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPromotionInfoCard(Order order) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Header
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.local_offer,
                      color: Colors.orange, size: 22),
                ),
                const SizedBox(width: 12),
                Text(
                  'Promotions Applied (${order.promotions.length})',
                  style: const TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Promotions list
            ...order.promotions
                .asMap()
                .entries
                .map((MapEntry<int, Map<String, dynamic>> entry) {
              final int index = entry.key;
              final Map<String, dynamic> promotion = entry.value;
              return _buildPromotionItem(promotion, index > 0);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildPromotionItem(Map<String, dynamic> promotion, bool showDivider) {
    final String name =
        promotion['promotion_name'] as String? ?? 'Unknown Promotion';
    final String type =
        promotion['promotion_type'] as String? ?? 'Unknown Type';
    final double discount =
        (promotion['discount_amount'] as num?)?.toDouble() ?? 0.0;

    return Column(
      children: <Widget>[
        if (showDivider) ...<Widget>[
          const SizedBox(height: 12),
          Container(height: 1, color: Colors.grey.withOpacity(0.2)),
          const SizedBox(height: 12),
        ],
        Row(
          children: <Widget>[
            // Promotion icon
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(Icons.discount, color: Colors.orange, size: 16),
            ),
            const SizedBox(width: 12),

            // Promotion details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Type: $type',
                    style: const TextStyle(
                      fontSize: 13,
                      color: BPColors.neutral,
                    ),
                  ),
                ],
              ),
            ),

            // Discount amount
            Text(
              '-${_currencyFormatter.format(discount)}',
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: BPColors.neutral,
            fontWeight: FontWeight.w500,
          ),
        ),
        Flexible(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildAmountRow(String label, double amount,
      {bool isDiscount = false, bool isTotal = false, bool isPaid = false}) {
    Color textColor = Colors.black87;
    FontWeight fontWeight = FontWeight.w500;

    if (isDiscount) {
      textColor = Colors.orange;
    } else if (isTotal) {
      textColor = BPColors.primary;
      fontWeight = FontWeight.w700;
    } else if (isPaid) {
      textColor = BPColors.primary;
      fontWeight = FontWeight.w600;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            color: isTotal ? BPColors.primary : BPColors.neutral,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
        Text(
          isDiscount
              ? '-${_currencyFormatter.format(amount)}'
              : _currencyFormatter.format(amount),
          style: TextStyle(
            fontSize: isTotal ? 18 : 15,
            color: textColor,
            fontWeight: fontWeight,
          ),
        ),
      ],
    );
  }

  IconData _getPaymentMethodIcon(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return Icons.payments;
      case 'credit card':
      case 'debit card':
        return Icons.credit_card;
      case 'digital wallet':
      case 'mobile payment':
        return Icons.phone_android;
      case 'bank transfer':
        return Icons.account_balance;
      default:
        return Icons.payment;
    }
  }

  Color _getPaymentMethodColor(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return BPColors.primary;
      case 'credit card':
      case 'debit card':
        return Colors.blue;
      case 'digital wallet':
      case 'mobile payment':
        return Colors.purple;
      case 'bank transfer':
        return Colors.indigo;
      default:
        return BPColors.primary;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return BPColors.primary;
      case 'pending':
      case 'processing':
        return BPColors.warning;
      case 'failed':
      case 'cancelled':
        return BPColors.error;
      default:
        return BPColors.neutral;
    }
  }
}
