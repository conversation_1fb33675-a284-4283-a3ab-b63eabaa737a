import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart'; // Add import for intl
import '../../models/order.dart'; // Add import for Order model

import '../../widgets/safe_scaffold.dart'; // ADD import
import '../../constants/bp_colors.dart'; // Add BP colors
import '../../theme/app_theme.dart'; // Add EDC theme
import '../../utils/time_utils.dart'; // Add time utils

import '../../services/api/api_service.dart'; // Import ApiService
import '../../services/api/api_client.dart'; // Import ApiException
import '../../services/api/order_api.dart'; // Import PaginatedOrders
import 'dart:developer'; // For logging

// 印尼盾格式化
final NumberFormat _currencyFormatter = NumberFormat.currency(
  locale: 'id_ID',
  symbol: 'Rp ',
  decimalDigits: 0,
);

class OrderHomePage extends ConsumerStatefulWidget {
  const OrderHomePage({super.key});

  @override
  ConsumerState<OrderHomePage> createState() => _OrderHomePageState();
}

class _OrderHomePageState extends ConsumerState<OrderHomePage> {
  // --- Add ApiService instance ---
  final ApiService _apiService = ApiService(); // Get ApiService instance

  // --- Start: Added state variables from OrderListPage ---
  // 是否正在加载数据
  bool _isLoading = false;
  // 是否已加载所有数据
  bool _hasReachedMax = false;
  // 订单列表（使用 Order 模型）
  final List<Order> _orders = <Order>[]; // Initialize empty list
  // 控制器用于实现下拉刷新和上拉加载更多
  final ScrollController _scrollController = ScrollController();
  // --- End: Added state variables ---

  // --- Start: Added query condition state ---
  OrderQueryCondition _queryCondition = OrderQueryCondition(
    startDate: DateTime.now().subtract(const Duration(days: 7)),
    endDate: DateTime.now(),
    page: 1, // Initial page
    pageSize: 10, // NEW: Changed page size to 10
  );
  // --- End: Added query condition state ---

  // --- Start: Filter dialog state variables ---
  String? _filterReceiptId;
  TimeOfDay? _filterStartTime;
  TimeOfDay? _filterEndTime;
  // --- End: Filter dialog state variables ---

  // --- Start: Added initState and dispose ---
  @override
  void initState() {
    super.initState();
    // 监听滚动事件，实现上拉加载更多
    _scrollController.addListener(_onScroll);
    // 初始加载数据
    _loadOrders(refresh: true);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll); // Correctly remove listener
    _scrollController.dispose();
    super.dispose();
  }
  // --- End: Added initState and dispose ---

  // --- Start: Added methods from OrderListPage ---
  // 滚动监听，用于实现加载更多
  void _onScroll() {
    if (_isLoading || _hasReachedMax) return;
    // 检查是否滚动到底部附近
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      log('Reached end of list, loading more...');
      _loadOrders();
    }
  }

  // 加载订单数据（从 API）
  Future<void> _loadOrders({bool refresh = false}) async {
    if (_isLoading) return;

    // If refreshing, reset page number in condition
    if (refresh) {
      _queryCondition = _queryCondition.copyWith(page: 1);
    }

    // Set loading state
    setState(() {
      _isLoading = true;
      // Clear list only when refreshing
      if (refresh) {
        _orders.clear();
        _hasReachedMax = false;
      }
    });

    log('[OrderHomePage] Loading page ${_queryCondition.page}...');

    try {
      // --- Call API using ApiService ---
      final PaginatedOrders result = await _apiService.orderApi.getOrders(
        condition: _queryCondition,
      );

      // --- Update state with API results ---
      setState(() {
        _orders.addAll(result.orders);
        _hasReachedMax = !result.hasMore;
        // Update the condition with the next page number if there are more items
        if (result.hasMore) {
          _queryCondition =
              _queryCondition.copyWith(page: _queryCondition.page + 1);
        }
        _isLoading = false;
      });
      log('[OrderHomePage] Loaded ${result.orders.length} orders. HasMore: ${result.hasMore}. Next page: ${_queryCondition.page}');
    } on ApiException catch (e) {
      log('[OrderHomePage] ApiException: ${e.message}', error: e);
      setState(() {
        _isLoading = false;
      });
      // Show error message to the user
      if (mounted) {
        // Check if the widget is still in the tree
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load orders: ${e.message}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      log('[OrderHomePage] Unexpected error: $e', error: e);
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unexpected error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 刷新订单列表
  Future<void> _refreshOrders() async {
    // Reset page number for the refresh
    await _loadOrders(refresh: true);
  }

  // 构建空数据视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.receipt_long,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 12),
          const Text(
            'No receipts found',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: BPColors.neutral,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'Try adjusting your search criteria',
            style: TextStyle(
              fontSize: 12,
              color: BPColors.neutral,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            icon: const Icon(Icons.refresh, size: 18),
            onPressed: _refreshOrders,
            label: const Text('Refresh'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              foregroundColor: Colors.white,
              backgroundColor: BPColors.primary,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建加载指示器
  Widget _buildLoadingIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: _hasReachedMax
            ? const Text(
                'No More Receipts',
                style: TextStyle(
                  color: BPColors.neutral,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              )
            : const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
              ),
      ),
    );
  }

  // 导航到订单详情页
  void _navigateToOrderDetail(Order order) {
    // 使用 GoRouter 进行导航，传递订单 ID
    context.push('/order/detail', extra: order.id);
  }
  // --- End: Added methods ---

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        context.go('/');
      },
      child: SafeScaffold(
        appBar: AppBar(
          title: const Text(
            'Receipts',
            style: EDCTextStyles.appBarTitle,
          ),
          backgroundColor: BPColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          toolbarHeight: 64,

          automaticallyImplyLeading: false, // 取消返回按钮
        ),
        body: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // Query Condition Bar (卡片化)
              _buildQueryConditionBar(),
              const SizedBox(height: 8),

              // Receipt List Card
              Expanded(
                child: Card(
                  elevation: 0,
                  margin: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                    side: BorderSide(
                        color: Colors.grey.withOpacity(0.2), width: 1),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        // 标题行
                        Row(
                          children: <Widget>[
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: BPColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(Icons.receipt_long,
                                  color: BPColors.primary, size: 22),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Receipt List',
                              style: TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // 列表内容
                        Expanded(
                          child: RefreshIndicator(
                            color: BPColors.primary,
                            onRefresh: _refreshOrders,
                            child: _isLoading && _orders.isEmpty
                                ? const Center(
                                    child: CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          BPColors.primary),
                                    ),
                                  )
                                : _orders.isEmpty
                                    ? _buildEmptyView()
                                    : ListView.builder(
                                        controller: _scrollController,
                                        itemCount: _orders.length + 1,
                                        padding: EdgeInsets.zero,
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          if (index >= _orders.length) {
                                            return _buildLoadingIndicator();
                                          }
                                          final Order order = _orders[index];
                                          return _buildReceiptListItem(order);
                                        },
                                      ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }



  // 显示筛选弹窗
  void _showFilterDialog() {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: 400,
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    // 标题栏
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        color: BPColors.primary,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Row(
                        children: <Widget>[
                          const Icon(Icons.filter_list,
                              color: Colors.white, size: 22),
                          const SizedBox(width: 10),
                          const Text(
                            'Filter Options',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: const Icon(Icons.close, color: Colors.white),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                        ],
                      ),
                    ),

                    // 筛选内容
                    Flexible(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            // Date Range
                            _buildFilterSection(
                              title: 'Date Range',
                              icon: Icons.date_range,
                              child: Column(
                                children: <Widget>[
                                  _buildDateField(
                                    label: 'Start Date',
                                    value: _queryCondition.startDate,
                                    onTap: () =>
                                        _selectDate(true, setDialogState),
                                  ),
                                  const SizedBox(height: 8),
                                  _buildDateField(
                                    label: 'End Date',
                                    value: _queryCondition.endDate,
                                    onTap: () =>
                                        _selectDate(false, setDialogState),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Time Range
                            _buildFilterSection(
                              title: 'Time Range',
                              icon: Icons.access_time,
                              child: Row(
                                children: <Widget>[
                                  Expanded(
                                    child: _buildTimeField(
                                      label: 'Start Time',
                                      value: _filterStartTime,
                                      onTap: () =>
                                          _selectTime(true, setDialogState),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: _buildTimeField(
                                      label: 'End Time',
                                      value: _filterEndTime,
                                      onTap: () =>
                                          _selectTime(false, setDialogState),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Receipt ID
                            _buildFilterSection(
                              title: 'Receipt ID',
                              icon: Icons.receipt_long,
                              child: TextField(
                                controller: TextEditingController(
                                    text: _filterReceiptId),
                                onChanged: (String value) {
                                  setDialogState(() {
                                    _filterReceiptId =
                                        value.isEmpty ? null : value;
                                  });
                                },
                                style: const TextStyle(fontSize: 14),
                                decoration: InputDecoration(
                                  hintText: 'Enter receipt ID',
                                  hintStyle: const TextStyle(fontSize: 14),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 10),
                                  isDense: true,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 按钮栏
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                _resetFilters();
                                Navigator.of(context).pop();
                              },
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(color: BPColors.primary),
                                foregroundColor: BPColors.primary,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const Text('Reset',
                                  style: TextStyle(fontSize: 14)),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                _applyFilters();
                                Navigator.of(context).pop();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: BPColors.primary,
                                foregroundColor: Colors.white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: const Text('Apply Filter',
                                  style: TextStyle(fontSize: 14)),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // 构建筛选区域
  Widget _buildFilterSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: <Widget>[
            Icon(icon, color: BPColors.primary, size: 18),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  // 构建日期字段
  Widget _buildDateField({
    required String label,
    required DateTime? value,
    required VoidCallback onTap,
  }) {
    final DateFormat dateFormat = DateFormat('MMM dd, yyyy');
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Text(
                value != null ? dateFormat.format(value) : label,
                style: TextStyle(
                  color: value != null ? Colors.black87 : Colors.grey.shade600,
                  fontSize: 13,
                ),
              ),
            ),
            const Icon(Icons.calendar_today, color: BPColors.primary, size: 18),
          ],
        ),
      ),
    );
  }

  // 构建时间字段
  Widget _buildTimeField({
    required String label,
    required TimeOfDay? value,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Text(
                value != null ? value.format(context) : label,
                style: TextStyle(
                  color: value != null ? Colors.black87 : Colors.grey.shade600,
                  fontSize: 13,
                ),
              ),
            ),
            const Icon(Icons.access_time, color: BPColors.primary, size: 18),
          ],
        ),
      ),
    );
  }



  // 选择日期
  Future<void> _selectDate(bool isStartDate, StateSetter setDialogState) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate
          ? (_queryCondition.startDate ?? DateTime.now())
          : (_queryCondition.endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: BPColors.primary,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setDialogState(() {
        if (isStartDate) {
          _queryCondition = _queryCondition.copyWith(
            startDate: picked,
            page: 1,
          );
        } else {
          _queryCondition = _queryCondition.copyWith(
            endDate:
                DateTime(picked.year, picked.month, picked.day, 23, 59, 59),
            page: 1,
          );
        }
      });
    }
  }

  // 选择时间
  Future<void> _selectTime(bool isStartTime, StateSetter setDialogState) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime
          ? (_filterStartTime ?? TimeOfDay.now())
          : (_filterEndTime ?? TimeOfDay.now()),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: BPColors.primary,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setDialogState(() {
        if (isStartTime) {
          _filterStartTime = picked;
        } else {
          _filterEndTime = picked;
        }
      });
    }
  }

  // 重置筛选条件
  void _resetFilters() {
    setState(() {
      _filterReceiptId = null;
      _filterStartTime = null;
      _filterEndTime = null;

      // 重置查询条件为默认值
      _queryCondition = OrderQueryCondition(
        startDate: DateTime.now().subtract(const Duration(days: 7)),
        endDate: DateTime.now(),
        page: 1,
        pageSize: 10,
      );
    });
    _refreshOrders();
  }

  // 应用筛选条件
  void _applyFilters() {
    setState(() {
      // 构建新的查询条件
      _queryCondition = _queryCondition.copyWith(
        orderId: _filterReceiptId,
        page: 1, // 重置到第一页
      );

      // 如果有时间筛选，可以结合日期进行处理
    });
    _refreshOrders();
  }

  // 获取当前筛选条件的描述
  String _getFilterDescription() {
    final List<String> filters = <String>[];

    // 日期范围
    if (_queryCondition.startDate != null || _queryCondition.endDate != null) {
      final DateFormat dateFormat = DateFormat('MMM dd');
      String dateRange = '';
      if (_queryCondition.startDate != null &&
          _queryCondition.endDate != null) {
        dateRange =
            '${dateFormat.format(_queryCondition.startDate!)} - ${dateFormat.format(_queryCondition.endDate!)}';
      } else if (_queryCondition.startDate != null) {
        dateRange = 'From ${dateFormat.format(_queryCondition.startDate!)}';
      } else if (_queryCondition.endDate != null) {
        dateRange = 'Until ${dateFormat.format(_queryCondition.endDate!)}';
      }
      if (dateRange.isNotEmpty) filters.add(dateRange);
    }

    // 时间范围
    if (_filterStartTime != null || _filterEndTime != null) {
      String timeRange = '';
      if (_filterStartTime != null && _filterEndTime != null) {
        timeRange =
            '${_filterStartTime!.format(context)} - ${_filterEndTime!.format(context)}';
      } else if (_filterStartTime != null) {
        timeRange = 'From ${_filterStartTime!.format(context)}';
      } else if (_filterEndTime != null) {
        timeRange = 'Until ${_filterEndTime!.format(context)}';
      }
      if (timeRange.isNotEmpty) filters.add(timeRange);
    }

    // 收据ID
    if (_filterReceiptId != null && _filterReceiptId!.isNotEmpty) {
      filters.add('ID: $_filterReceiptId');
    }



    return filters.isEmpty ? 'No filters applied' : filters.join(' • ');
  }

  // 检查是否有活动的筛选条件
  bool _hasActiveFilters() {
    return _filterReceiptId != null && _filterReceiptId!.isNotEmpty ||
        _filterStartTime != null ||
        _filterEndTime != null ||
        (_queryCondition.startDate != null &&
            _queryCondition.startDate!
                    .difference(
                        DateTime.now().subtract(const Duration(days: 7)))
                    .abs() >
                const Duration(hours: 1)) ||
        (_queryCondition.endDate != null &&
            _queryCondition.endDate!.difference(DateTime.now()).abs() >
                const Duration(hours: 1));
  }



  // 构建查询条件栏 (卡片化) - 改为弹窗筛选
  Widget _buildQueryConditionBar() {
    final bool hasFilters = _hasActiveFilters();
    final String filterDescription = _getFilterDescription();

    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(
          color: hasFilters
              ? BPColors.primary.withOpacity(0.3)
              : Colors.grey.withOpacity(0.2),
          width: hasFilters ? 1.5 : 1,
        ),
      ),
      child: InkWell(
        onTap: _showFilterDialog,
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Icon(
                    Icons.filter_list,
                    color: hasFilters ? BPColors.primary : BPColors.neutral,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      hasFilters ? 'Active Filters' : 'Filter Options',
                      style: TextStyle(
                        fontSize: 13,
                        color: hasFilters ? BPColors.primary : BPColors.neutral,
                        fontWeight:
                            hasFilters ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                  ),
                  if (hasFilters) ...<Widget>[
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: BPColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        'ON',
                        style: TextStyle(
                          fontSize: 10,
                          color: BPColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                  ],
                  const Icon(Icons.tune, color: BPColors.neutral, size: 20),
                ],
              ),
              if (hasFilters) ...<Widget>[
                const SizedBox(height: 6),
                Text(
                  filterDescription,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.normal,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // 构建收据列表项 (卡片化)
  Widget _buildReceiptListItem(Order order) {
    // 使用时区服务格式化时间
    final String formattedTime = TimeUtils.formatDateTime(ref, order.createTime, isUtc: true,
        format: 'MMM dd, HH:mm');

    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(bottom: 6),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
      ),
      child: InkWell(
        onTap: () => _navigateToOrderDetail(order),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Stack(
            children: <Widget>[
              // 主要内容
              Row(
                children: <Widget>[
                  // 左侧图标
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: BPColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(Icons.receipt_long,
                        color: BPColors.primary, size: 18),
                  ),
                  const SizedBox(width: 10),

                  // 中间内容
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        // 订单号 - 字体更小
                        Text(
                          order.orderId,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 3),
                        // 时间 - 使用时区服务
                        Text(
                          formattedTime,
                          style: const TextStyle(
                              fontSize: 11, color: BPColors.neutral),
                        ),
                        const SizedBox(height: 2),
                        // 金额 - 使用印尼盾格式，调大字体
                        Text(
                          _currencyFormatter.format(order.amount),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: BPColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 右侧箭头
                  const Icon(Icons.arrow_forward_ios,
                      color: BPColors.neutral, size: 12),
                ],
              ),

              // 状态徽章 - 放在下方角落
              Positioned(
                bottom: 0,
                right: 20,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getStatusColor(order.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _getStatusText(order.status),
                    style: TextStyle(
                      fontSize: 9,
                      fontWeight: FontWeight.w500,
                      color: _getStatusColor(order.status),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 获取状态颜色
  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.completed:
        return BPColors.success;
      case OrderStatus.processing:
        return BPColors.primary;
      case OrderStatus.cancelled:
        return BPColors.error;
      case OrderStatus.created:
        return BPColors.warning;
      default:
        return BPColors.neutral;
    }
  }

  // 获取状态文本
  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.created:
        return 'Created';
      default:
        return 'Unknown';
    }
  }
}
