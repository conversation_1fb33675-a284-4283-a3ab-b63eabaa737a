import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/order.dart';
import '../../widgets/safe_scaffold.dart';

/// 订单搜索页面
/// 提供多条件组合查询功能
class OrderSearchPage extends StatefulWidget {
  const OrderSearchPage({
    super.key,
    required this.initialCondition,
  });
  final OrderQueryCondition initialCondition;

  @override
  State<OrderSearchPage> createState() => _OrderSearchPageState();
}

class _OrderSearchPageState extends State<OrderSearchPage> {
  late OrderQueryCondition _queryCondition;

  // 文本输入控制器
  final TextEditingController _orderIdController = TextEditingController();
  // Removed controllers for unsupported filters
  // final TextEditingController _memberPhoneController = TextEditingController();
  // final TextEditingController _operatorIdController = TextEditingController();

  // 支付方式选项 (Keep for now, assuming API supports these strings)
  final List<String> _paymentMethods = <String>[
    'Total',
    'Cash',
    'QRIS',
    'Credit',
    'Member Card'
  ];

  // 支付方式映射函数 - 将中文支付方式映射为英文
  String _mapPaymentMethod(String paymentMethod) {
    switch (paymentMethod) {
      case '现金':
        return 'Cash';
      case '银行卡':
        return 'Bank Card';
      case '信用卡':
        return 'Credit';
      case '会员卡':
        return 'Member Card';
      case '全部':
        return 'Total';
      default:
        return paymentMethod;
    }
  }

  // 反向映射函数 - 将英文支付方式映射为中文显示
  String _reverseMapPaymentMethod(String paymentMethod) {
    switch (paymentMethod) {
      case 'Cash':
        return 'Cash';
      case 'Bank Card':
        return '银行卡';
      case 'Credit':
        return '信用卡';
      case 'Member Card':
        return '会员卡';
      case 'Total':
        return '全部';
      default:
        return paymentMethod;
    }
  }

  @override
  void initState() {
    super.initState();
    _queryCondition = widget.initialCondition;

    // 初始化文本控制器
    _orderIdController.text = _queryCondition.orderId ?? '';
    // Removed initialization for unsupported filters
    // _memberPhoneController.text = _queryCondition.memberPhone ?? '';
    // _operatorIdController.text = _queryCondition.operatorId ?? '';
  }

  @override
  void dispose() {
    _orderIdController.dispose();
    // Removed disposal for unsupported filters
    // _memberPhoneController.dispose();
    // _operatorIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeScaffold(
      appBar: AppBar(
        title: const Text('收据搜索'),
        actions: <Widget>[
          TextButton(
            onPressed: _resetFilters,
            child: const Text('重置', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: <Widget>[
          _buildSectionTitle('日期范围'),
          _buildDateRangeSelector(),
          const SizedBox(height: 24),

          _buildSectionTitle('订单信息'),
          const SizedBox(height: 8),
          _buildTextInput(
            controller: _orderIdController,
            labelText: '订单编号',
            hintText: '输入完整或部分订单号',
            prefixIcon: Icons.receipt_long,
          ),
          const SizedBox(height: 16),
          // Removed unsupported filter inputs
          // _buildTextInput(
          //   controller: _memberPhoneController,
          //   ...
          // ),
          // const SizedBox(height: 16),
          // _buildTextInput(
          //   controller: _operatorIdController,
          //   ...
          // ),
          // const SizedBox(height: 24),

          _buildSectionTitle('订单状态'),
          const SizedBox(height: 8),
          _buildStatusSelector(), // This method needs update
          const SizedBox(height: 24),

          _buildSectionTitle('支付方式'),
          const SizedBox(height: 8),
          _buildPaymentMethodSelector(),
          const SizedBox(height: 32),

          ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('应用筛选'),
          ),
        ],
      ),
    );
  }

  // 构建区域标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  // 构建日期范围选择器
  Widget _buildDateRangeSelector() {
    final DateFormat dateFormat = DateFormat('yyyy-MM-dd');
    final String startDateStr = _queryCondition.startDate != null
        ? dateFormat.format(_queryCondition.startDate!)
        : '开始日期';
    final String endDateStr = _queryCondition.endDate != null
        ? dateFormat.format(_queryCondition.endDate!)
        : '结束日期';

    return Column(
      children: <Widget>[
        const SizedBox(height: 8),
        // 快速日期选择
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: <Widget>[
              _buildDateChip('今日', isSelected: _isToday()),
              _buildDateChip('昨日', isSelected: _isYesterday()),
              _buildDateChip('最近7天', isSelected: _isLast7Days()),
              _buildDateChip('最近30天', isSelected: _isLast30Days()),
              _buildDateChip('本月', isSelected: _isCurrentMonth()),
              _buildDateChip('上月', isSelected: _isLastMonth()),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // 自定义日期范围
        Row(
          children: <Widget>[
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(isStartDate: true),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: <Widget>[
                      const Icon(Icons.calendar_today,
                          size: 18, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        startDateStr,
                        style: TextStyle(
                          color: _queryCondition.startDate != null
                              ? Colors.black
                              : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Text('至'),
            ),
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(isStartDate: false),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: <Widget>[
                      const Icon(Icons.calendar_today,
                          size: 18, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        endDateStr,
                        style: TextStyle(
                          color: _queryCondition.endDate != null
                              ? Colors.black
                              : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 构建日期选择芯片
  Widget _buildDateChip(String label, {required bool isSelected}) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: ChoiceChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (bool selected) {
          if (selected) {
            setState(() {
              // 根据选择设置日期范围
              if (label == '今日') {
                _setTodayRange();
              } else if (label == '昨日') {
                _setYesterdayRange();
              } else if (label == '最近7天') {
                _setLast7DaysRange();
              } else if (label == '最近30天') {
                _setLast30DaysRange();
              } else if (label == '本月') {
                _setCurrentMonthRange();
              } else if (label == '上月') {
                _setLastMonthRange();
              }
            });
          }
        },
        selectedColor: Colors.blue.shade100,
      ),
    );
  }

  // 构建文本输入框
  Widget _buildTextInput({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData prefixIcon,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        border: const OutlineInputBorder(),
        prefixIcon: Icon(prefixIcon),
        suffixIcon: controller.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  controller.clear();
                },
              )
            : null,
      ),
      keyboardType: keyboardType,
    );
  }

  // 构建订单状态选择器 - Updated to match API filterable statuses
  Widget _buildStatusSelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: <Widget>[
          RadioListTile<OrderStatus?>(
            title: const Text('全部状态'),
            value: null,
            groupValue: _queryCondition.status,
            onChanged: (OrderStatus? value) => setState(() {
              _queryCondition = _queryCondition.copyWith(status: value);
            }),
            controlAffinity: ListTileControlAffinity.trailing, // Adjust layout
            dense: true, // Make it more compact
          ),
          const Divider(height: 1),
          // Option for 'new' status (mapped from created enum)
          RadioListTile<OrderStatus>(
            title: const Text('新创建'), // UI text for 'new' status (created)
            value:
                OrderStatus.created, // Enum value mapped to 'new' by API layer
            groupValue: _queryCondition.status,
            onChanged: (OrderStatus? value) => setState(() {
              _queryCondition = _queryCondition.copyWith(status: value);
            }),
            controlAffinity: ListTileControlAffinity.trailing,
            dense: true,
          ),
          const Divider(height: 1),
          RadioListTile<OrderStatus>(
            title: const Text('处理中'),
            value: OrderStatus.processing,
            groupValue: _queryCondition.status,
            onChanged: (OrderStatus? value) => setState(() {
              _queryCondition = _queryCondition.copyWith(status: value);
            }),
            controlAffinity: ListTileControlAffinity.trailing,
            dense: true,
          ),
          const Divider(height: 1),
          RadioListTile<OrderStatus>(
            title: const Text('已完成'),
            value: OrderStatus.completed,
            groupValue: _queryCondition.status,
            onChanged: (OrderStatus? value) => setState(() {
              _queryCondition = _queryCondition.copyWith(status: value);
            }),
            controlAffinity: ListTileControlAffinity.trailing,
            dense: true,
          ),
          const Divider(height: 1),
          RadioListTile<OrderStatus>(
            title: const Text('已取消'),
            value: OrderStatus.cancelled,
            groupValue: _queryCondition.status,
            onChanged: (OrderStatus? value) => setState(() {
              _queryCondition = _queryCondition.copyWith(status: value);
            }),
            controlAffinity: ListTileControlAffinity.trailing,
            dense: true,
          ),
          // Removed 'cancelling' and 'failed' as they are not filterable by API
        ],
      ),
    );
  }

  // 构建支付方式选择器
  Widget _buildPaymentMethodSelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: _paymentMethods.map((String method) {
          final bool isSelected = method == 'Total'
              ? _queryCondition.paymentMethod == null
              : _queryCondition.paymentMethod == method;

          return Column(
            children: <Widget>[
              RadioListTile<String?>(
                title: Text(method),
                value: method == 'Total' ? null : method,
                groupValue: _queryCondition.paymentMethod,
                onChanged: (String? value) {
                  setState(() {
                    _queryCondition =
                        _queryCondition.copyWith(paymentMethod: value);
                  });
                },
              ),
              if (method != _paymentMethods.last) const Divider(height: 1),
            ],
          );
        }).toList(),
      ),
    );
  }

  // 检查是否为今日日期范围
  bool _isToday() {
    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);

    return _queryCondition.startDate != null &&
        _queryCondition.endDate != null &&
        _isSameDay(_queryCondition.startDate!, today) &&
        _isSameDay(_queryCondition.endDate!, today);
  }

  // 检查是否为昨日日期范围
  bool _isYesterday() {
    final DateTime now = DateTime.now();
    final DateTime yesterday = DateTime(now.year, now.month, now.day - 1);

    return _queryCondition.startDate != null &&
        _queryCondition.endDate != null &&
        _isSameDay(_queryCondition.startDate!, yesterday) &&
        _isSameDay(_queryCondition.endDate!, yesterday);
  }

  // 检查是否为最近7天日期范围
  bool _isLast7Days() {
    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);
    final DateTime sevenDaysAgo = DateTime(now.year, now.month, now.day - 6);

    return _queryCondition.startDate != null &&
        _queryCondition.endDate != null &&
        _isSameDay(_queryCondition.startDate!, sevenDaysAgo) &&
        _isSameDay(_queryCondition.endDate!, today);
  }

  // 检查是否为最近30天日期范围
  bool _isLast30Days() {
    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);
    final DateTime thirtyDaysAgo = DateTime(now.year, now.month, now.day - 29);

    return _queryCondition.startDate != null &&
        _queryCondition.endDate != null &&
        _isSameDay(_queryCondition.startDate!, thirtyDaysAgo) &&
        _isSameDay(_queryCondition.endDate!, today);
  }

  // 检查是否为本月日期范围
  bool _isCurrentMonth() {
    final DateTime now = DateTime.now();
    final DateTime firstDayOfMonth = DateTime(now.year, now.month, 1);
    final DateTime lastDayOfMonth = DateTime(now.year, now.month + 1, 0);

    return _queryCondition.startDate != null &&
        _queryCondition.endDate != null &&
        _isSameDay(_queryCondition.startDate!, firstDayOfMonth) &&
        _isSameDay(_queryCondition.endDate!, lastDayOfMonth);
  }

  // 检查是否为上月日期范围
  bool _isLastMonth() {
    final DateTime now = DateTime.now();
    final DateTime firstDayOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final DateTime lastDayOfLastMonth = DateTime(now.year, now.month, 0);

    return _queryCondition.startDate != null &&
        _queryCondition.endDate != null &&
        _isSameDay(_queryCondition.startDate!, firstDayOfLastMonth) &&
        _isSameDay(_queryCondition.endDate!, lastDayOfLastMonth);
  }

  // 判断两个日期是否为同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  // 设置今日日期范围
  void _setTodayRange() {
    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);

    _queryCondition = _queryCondition.copyWith(
      startDate: today,
      endDate: today,
    );
  }

  // 设置昨日日期范围
  void _setYesterdayRange() {
    final DateTime now = DateTime.now();
    final DateTime yesterday = DateTime(now.year, now.month, now.day - 1);

    _queryCondition = _queryCondition.copyWith(
      startDate: yesterday,
      endDate: yesterday,
    );
  }

  // 设置最近7天日期范围
  void _setLast7DaysRange() {
    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);
    final DateTime sevenDaysAgo = DateTime(now.year, now.month, now.day - 6);

    _queryCondition = _queryCondition.copyWith(
      startDate: sevenDaysAgo,
      endDate: today,
    );
  }

  // 设置最近30天日期范围
  void _setLast30DaysRange() {
    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);
    final DateTime thirtyDaysAgo = DateTime(now.year, now.month, now.day - 29);

    _queryCondition = _queryCondition.copyWith(
      startDate: thirtyDaysAgo,
      endDate: today,
    );
  }

  // 设置本月日期范围
  void _setCurrentMonthRange() {
    final DateTime now = DateTime.now();
    final DateTime firstDayOfMonth = DateTime(now.year, now.month, 1);
    final DateTime lastDayOfMonth = DateTime(now.year, now.month + 1, 0);

    _queryCondition = _queryCondition.copyWith(
      startDate: firstDayOfMonth,
      endDate: lastDayOfMonth,
    );
  }

  // 设置上月日期范围
  void _setLastMonthRange() {
    final DateTime now = DateTime.now();
    final DateTime firstDayOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final DateTime lastDayOfLastMonth = DateTime(now.year, now.month, 0);

    _queryCondition = _queryCondition.copyWith(
      startDate: firstDayOfLastMonth,
      endDate: lastDayOfLastMonth,
    );
  }

  // 选择日期
  void _selectDate({required bool isStartDate}) async {
    final DateTime initialDate = isStartDate
        ? _queryCondition.startDate ?? DateTime.now()
        : _queryCondition.endDate ?? DateTime.now();

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _queryCondition = _queryCondition.copyWith(startDate: picked);
          // 如果开始日期大于结束日期，则同时更新结束日期
          if (_queryCondition.endDate != null &&
              picked.isAfter(_queryCondition.endDate!)) {
            _queryCondition = _queryCondition.copyWith(endDate: picked);
          }
        } else {
          _queryCondition = _queryCondition.copyWith(endDate: picked);
          // 如果结束日期小于开始日期，则同时更新开始日期
          if (_queryCondition.startDate != null &&
              picked.isBefore(_queryCondition.startDate!)) {
            _queryCondition = _queryCondition.copyWith(startDate: picked);
          }
        }
      });
    }
  }

  // 重置所有筛选条件 - Updated
  void _resetFilters() {
    setState(() {
      _queryCondition = OrderQueryCondition(
        // Reset date to default (e.g., null or last 7 days)
        startDate: null,
        endDate: null,
        // Keep page/pageSize defaults
        page: 1,
        pageSize: _queryCondition.pageSize, // Keep existing page size
      );
      _orderIdController.clear();
      // Removed clearing for unsupported filters
      // _memberPhoneController.clear();
      // _operatorIdController.clear();
    });
  }

  // 应用筛选条件 - Updated
  void _applyFilters() {
    _queryCondition = _queryCondition.copyWith(
      orderId: _orderIdController.text.isEmpty ? null : _orderIdController.text,
      // Removed setting for unsupported filters
      // memberPhone: _memberPhoneController.text.isEmpty ? null : _memberPhoneController.text,
      // operatorId: _operatorIdController.text.isEmpty ? null : _operatorIdController.text,
      page: 1, // Always reset to page 1 when applying filters
    );
    Navigator.pop(context, _queryCondition);
  }
}
