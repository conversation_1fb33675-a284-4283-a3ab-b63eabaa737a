import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../auth/auth_service.dart';
import '../../services/api/employee_api.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/safe_scaffold.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 下拉刷新时，触发AuthService重新获取详情
  Future<void> _handleRefresh() async {
    // 通过 ref 读取 AuthService
    final AuthService authService = ref.read(authServiceProvider);
    // 获取当前员工ID
    final String? currentEmployeeId = authService.currentEmployee?.id;

    if (currentEmployeeId != null) {
      // 调用AuthService的公共方法重新获取详情
      await authService.refreshEmployeeDetail();

      // 刷新后再次读取最新的员工信息用于判断
      final Employee? updatedEmployee =
          ref.read(authServiceProvider).currentEmployee;

      // 显示刷新成功提示 (判断条件可以简化或移除)
      if (mounted && (updatedEmployee != null)) {
        // 只要员工信息不为空就提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('员工信息已更新'),
            duration: Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } else {
      // 如果没有员工信息，提示错误
      setState(() {
        _errorMessage = '无法刷新，请重新登录';
      });
    }
  }

  // 显示退出确认对话框
  Future<bool> _showExitConfirmationDialog() async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) => AlertDialog(
            title: const Text('确认退出'),
            content: const Text('您确定要退出应用吗？'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text(
                  '退出',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  // 处理登出
  Future<void> _handleLogout() async {
    final bool confirmed = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) => AlertDialog(
            title: const Text('确认登出'),
            content: const Text('您确定要退出登录吗？'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text(
                  '登出',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
        ) ??
        false;

    if (confirmed && mounted) {
      // 通过 ref 读取 AuthService 并调用 logout
      await ref.read(authServiceProvider).logout();
      // 移除手动跳转，依赖 GoRouter 的重定向
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        if (didPop) return;

        final bool shouldPop = await _showExitConfirmationDialog();
        if (shouldPop && mounted) {
          SystemNavigator.pop();
        }
      },
      child: SafeScaffold(
        appBar: AppBar(
          title: const Text('BP 加油站 EDC'),
          centerTitle: true,
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.logout),
              tooltip: '退出登录',
              onPressed: _handleLogout,
            ),
          ],
        ),
        body: RefreshIndicator(
          onRefresh: _handleRefresh,
          child: CustomScrollView(
            slivers: <Widget>[
              // 直接使用 ref.watch 监听员工信息变化
              SliverToBoxAdapter(
                child: Consumer(
                  // 使用 Consumer 来访问 ref
                  builder:
                      (BuildContext context, WidgetRef ref, Widget? child) {
                    // 监听 authServiceProvider 的变化
                    final Employee? employee =
                        ref.watch(authServiceProvider).currentEmployee;
                    return _buildEmployeeCard(employee);
                  },
                ),
              ),

              // 功能菜单网格
              SliverPadding(
                padding: const EdgeInsets.all(16.0),
                sliver: SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    mainAxisSpacing: 16.0,
                    crossAxisSpacing: 16.0,
                    childAspectRatio: 1.0,
                  ),
                  delegate: SliverChildListDelegate(<Widget>[
                    _buildMenuItem(
                      context,
                      title: '交易记录',
                      icon: Icons.receipt_long,
                      color: Colors.blue,
                      onTap: () => context.go('/transaction'),
                    ),
                    _buildMenuItem(
                      context,
                      title: '收据管理',
                      icon: Icons.receipt_long,
                      color: Colors.orange,
                      onTap: () => context.go('/order'),
                    ),
                    _buildMenuItem(
                      context,
                      title: '支付管理',
                      icon: Icons.payment,
                      color: Colors.green,
                      onTap: () => context.go('/payment'),
                    ),
                    _buildMenuItem(
                      context,
                      title: '会员服务',
                      icon: Icons.person,
                      color: Colors.purple,
                      onTap: () => context.go('/member'),
                    ),
                    _buildMenuItem(
                      context,
                      title: '营销活动',
                      icon: Icons.card_giftcard,
                      color: Colors.red,
                      onTap: () => context.go('/marketing'),
                    ),
                    _buildMenuItem(
                      context,
                      title: '小票打印',
                      icon: Icons.print,
                      color: Colors.teal,
                      onTap: () => context.go('/printing'),
                    ),
                    _buildMenuItem(
                      context,
                      title: '管控列表',
                      icon: Icons.local_gas_station,
                      color: Colors.amber,
                      onTap: () => context.go('/fuel-transactions'),
                    ),
                    _buildMenuItem(
                      context,
                      title: '卡模块',
                      icon: Icons.credit_card,
                      color: Colors.indigo,
                      onTap: () => context.go('/card'),
                    ),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建员工信息卡片 (精简版)
  Widget _buildEmployeeCard(Employee? employee) {
    // 如果没有员工信息（包括初始化失败的情况）
    if (employee == null) {
      return Padding(
        padding: const EdgeInsets.symmetric(
            horizontal: 16.0, vertical: 8.0), // Match card margin
        child: Card(
          color: Colors.orange.shade50,
          elevation: 2, // Match card elevation
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0), // Match card shape
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const Icon(Icons.warning_amber_rounded,
                    color: Colors.orange, size: 40),
                const SizedBox(height: 8),
                Text(
                  _errorMessage ?? 'Loading employee information...',
                  style: TextStyle(color: Colors.orange.shade800),
                  textAlign: TextAlign.center,
                ),
                // 如果是错误信息，提供刷新按钮
                if (_errorMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: ElevatedButton.icon(
                      onPressed: _handleRefresh,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
    }

    // 正常显示员工信息
    return Card(
      margin: const EdgeInsets.symmetric(
          horizontal: 16.0, vertical: 8.0), // Reduced vertical margin
      elevation: 2, // Reduced elevation
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          // Main layout is now a Row
          children: <Widget>[
            // Icon on the left
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.person,
                size: 36,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 16), // Spacer
            // Name and Employee Number on the right
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize
                    .min, // Prevent Column from taking extra vertical space
                children: <Widget>[
                  Text(
                    employee.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis, // Handle long names
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Employee ID: ${employee.employeeNo}',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow:
                        TextOverflow.ellipsis, // Handle long employee numbers
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建信息项
  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: <Widget>[
            Icon(icon, size: 16, color: Colors.grey),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.0),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 36,
                color: color,
              ),
            ),
            const SizedBox(height: 12.0),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
