import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../widgets/safe_scaffold.dart';
import '../../models/payment_transaction_data.dart';

class PaymentHomePage extends StatelessWidget {
  const PaymentHomePage({super.key, this.paymentData});
  final PaymentTransactionData? paymentData;

  @override
  Widget build(BuildContext context) {
    // BP green color constant
    const Color bpGreenColor = Color(0xFF2E8B57);
    final bool isPaymentFlow = paymentData != null;

    // 如果是支付流程，直接导航到支付方式选择页面
    if (isPaymentFlow) {
      // 使用微任务延迟导航，避免在build过程中导航
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // 使用replace而不是push，这样就不会有返回循环的问题
        context.replace('/payment/methods', extra: paymentData);
      });
    }

    return PopScope(
      canPop: !isPaymentFlow,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        if (isPaymentFlow) {
          context.pop();
        } else {
          context.go('/');
        }
      },
      child: SafeScaffold(
        appBar: AppBar(
          title: Row(
            children: <Widget>[
              // BP logo
              Image.asset(
                'assets/images/bp_logo.png',
                height: 32,
                fit: BoxFit.contain,
              ),
              const SizedBox(width: 12),
              Text(
                isPaymentFlow ? 'Payment Methods' : 'Payment Management',
                style: const TextStyle(
                  color: bpGreenColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          backgroundColor: Colors.white,
          foregroundColor: bpGreenColor,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              if (isPaymentFlow) {
                context.pop();
              } else {
                context.go('/');
              }
            },
          ),
        ),
        body: _buildDefaultFeatures(context),
      ),
    );
  }

  Widget _buildPaymentSelection(BuildContext context) {
    // BP green color constant
    const Color bpGreenColor = Color(0xFF2E8B57);

    print('💳 [DEBUG] Payment page received data:');
    print('📦 Payment Data: $paymentData');

    final double totalAmount = paymentData?.totalAmount ?? 0.0;

    print('💰 Total amount: $totalAmount');
    final ThemeData theme = Theme.of(context);
    final TextTheme textTheme = theme.textTheme;

    final NumberFormat currencyFormatter =
        NumberFormat.currency(locale: 'id_ID', symbol: 'Rp', decimalDigits: 0);

    // 默认选中的车辆类型
    final ValueNotifier<String> selectedVehicleType =
        ValueNotifier<String>('Car');

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Card(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                  width: 1,
                ),
              ),
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    vertical: 24.0, horizontal: 16.0),
                child: Column(
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        const Icon(Icons.receipt_long,
                            color: bpGreenColor, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Amount Due',
                          style: textTheme.titleMedium?.copyWith(
                            color: bpGreenColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      currencyFormatter.format(totalAmount),
                      style: textTheme.displaySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: bpGreenColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 车牌号输入卡片
            Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                  width: 1,
                ),
              ),
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      children: <Widget>[
                        const Icon(Icons.directions_car,
                            color: bpGreenColor, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Vehicle Information',
                          style: textTheme.titleMedium?.copyWith(
                            color: bpGreenColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // 车牌号输入框
                    TextField(
                      // 车牌号输入格式控制器
                      inputFormatters: <TextInputFormatter>[
                        FilteringTextInputFormatter.allow(
                            RegExp(r'[A-Za-z0-9]')),
                        LengthLimitingTextInputFormatter(8),
                      ],
                      textCapitalization: TextCapitalization.characters,
                      decoration: InputDecoration(
                        labelText: 'License Plate Number',
                        hintText: 'Enter vehicle license plate',
                        prefixIcon:
                            const Icon(Icons.credit_card, color: bpGreenColor),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: bpGreenColor, width: 2),
                        ),
                      ),
                      style: textTheme.bodyLarge,
                      onChanged: (String value) {
                        // 车牌号变更处理
                        // 将新的车牌号存储到适当的地方，例如可以添加到paymentData中
                      },
                    ),

                    const SizedBox(height: 16),

                    // 车辆类型选择
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'Vehicle Type',
                          style: textTheme.titleSmall?.copyWith(
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),

                        // 单选Tag组
                        ValueListenableBuilder<String>(
                          valueListenable: selectedVehicleType,
                          builder: (BuildContext context, String selectedType,
                              Widget? child) {
                            return SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: <Widget>[
                                  _buildVehicleTypeTag(
                                      context, 'Car', selectedType,
                                      (String type) {
                                    selectedVehicleType.value = type;
                                  }),
                                  _buildVehicleTypeTag(
                                      context, 'Motorcycle', selectedType,
                                      (String type) {
                                    selectedVehicleType.value = type;
                                  }),
                                  _buildVehicleTypeTag(
                                      context, 'Truck', selectedType,
                                      (String type) {
                                    selectedVehicleType.value = type;
                                  }),
                                  _buildVehicleTypeTag(
                                      context, 'Van', selectedType,
                                      (String type) {
                                    selectedVehicleType.value = type;
                                  }),
                                  _buildVehicleTypeTag(
                                      context, 'Other', selectedType,
                                      (String type) {
                                    selectedVehicleType.value = type;
                                  }),
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            _buildPaymentOptionButton(
              context: context,
              label: 'Cash Payment',
              icon: Icons.money,
              color: bpGreenColor,
              onPressed: () {
                context.push('/payment/cash', extra: paymentData);
              },
            ),
            const SizedBox(height: 16),

            _buildPaymentOptionButton(
              context: context,
              label: 'Card Payment',
              icon: Icons.credit_card,
              color: Colors.grey,
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('银行卡支付功能正在开发中...'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            Text(
              'Please select your payment method',
              textAlign: TextAlign.center,
              style: textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  // 车辆类型标签组件
  Widget _buildVehicleTypeTag(BuildContext context, String type,
      String selectedType, Function(String) onSelected) {
    // BP绿色常量
    const Color bpGreenColor = Color(0xFF2E8B57);

    final bool isSelected = type == selectedType;

    return GestureDetector(
      onTap: () => onSelected(type),
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? bpGreenColor : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? bpGreenColor : Colors.grey.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Text(
          type,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[800],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentOptionButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      icon: Icon(icon, size: 24),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 18),
        textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        backgroundColor: color,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      onPressed: onPressed,
    );
  }

  Widget _buildDefaultFeatures(BuildContext context) {
    // BP green color constant
    const Color bpGreenColor = Color(0xFF2E8B57);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            'Payment Features',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: bpGreenColor,
                ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: <Widget>[
                _buildFeatureCard(
                  context,
                  icon: Icons.payments,
                  title: 'Cash Payment',
                  subtitle: 'Process cash transactions',
                  color: bpGreenColor,
                  onTap: () => context.push('/payment/cash'),
                ),
                _buildFeatureCard(
                  context,
                  icon: Icons.person,
                  title: 'Member Payment',
                  subtitle: 'Feature coming soon',
                  color: Colors.grey,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('会员支付功能正在开发中...'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  },
                ),
                _buildFeatureCard(
                  context,
                  icon: Icons.history,
                  title: 'Transaction History',
                  subtitle: 'View past transactions',
                  color: bpGreenColor,
                  onTap: () => context.push('/payment/history'),
                ),
                _buildFeatureCard(
                  context,
                  icon: Icons.refresh,
                  title: 'Refund Request',
                  subtitle: 'Process refunds and returns',
                  color: bpGreenColor,
                  onTap: () => context.push('/payment/refund'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      color: Colors.white,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 36,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                subtitle,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
