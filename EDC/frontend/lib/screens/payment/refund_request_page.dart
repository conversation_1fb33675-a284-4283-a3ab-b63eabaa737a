import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class RefundRequestPage extends StatefulWidget {
  const RefundRequestPage({super.key});

  @override
  State<RefundRequestPage> createState() => _RefundRequestPageState();
}

class _RefundRequestPageState extends State<RefundRequestPage>
    with SingleTickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;

  // 交易号搜索控制器
  final TextEditingController _transactionIdController =
      TextEditingController();

  // 是否加载中
  bool _isLoading = false;

  // 选中的交易记录
  Map<String, dynamic>? _selectedTransaction;

  // 撤销原因控制器
  final TextEditingController _reasonController = TextEditingController();

  // 预设交易历史数据
  final List<Map<String, dynamic>> _eligibleTransactions =
      <Map<String, dynamic>>[
    <String, dynamic>{
      'id': 'TX********001',
      'nozzleId': '01',
      'fuelType': 'Pertamax',
      'volume': 10.45,
      'amount': 130625.0,
      'paymentMethod': '现金',
      'status': 'completed',
      'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
      'receiptNo': 'R0001-********',
    },
    <String, dynamic>{
      'id': 'TX********002',
      'nozzleId': '02',
      'fuelType': 'Solar',
      'volume': 15.75,
      'amount': 157500.0,
      'paymentMethod': '银行卡',
      'bankType': 'BCA',
      'cardPrefix': '400000',
      'status': 'completed',
      'timestamp':
          DateTime.now().subtract(const Duration(hours: 1, minutes: 45)),
      'receiptNo': 'R0002-********',
    },
    <String, dynamic>{
      'id': 'TX********003',
      'nozzleId': '03',
      'fuelType': 'Pertamax Turbo',
      'volume': 8.2,
      'amount': 123000.0,
      'paymentMethod': '会员账户',
      'memberId': '345678',
      'memberName': 'Ahmad Wijaya',
      'status': 'completed',
      'timestamp':
          DateTime.now().subtract(const Duration(hours: 1, minutes: 20)),
      'receiptNo': 'R0003-********',
    },
    <String, dynamic>{
      'id': 'TX********005',
      'nozzleId': '04',
      'fuelType': 'Solar',
      'volume': 20.0,
      'amount': 200000.0,
      'paymentMethod': '银行卡',
      'bankType': 'Mandiri',
      'cardPrefix': '422222',
      'status': 'completed',
      'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
      'receiptNo': 'R0005-********',
    },
    <String, dynamic>{
      'id': 'TX********006',
      'nozzleId': '02',
      'fuelType': 'Pertamax',
      'volume': 12.5,
      'amount': 156250.0,
      'paymentMethod': '会员账户',
      'memberId': '234567',
      'memberName': 'Siti Nurhayati',
      'status': 'completed',
      'timestamp': DateTime.now().subtract(const Duration(minutes: 15)),
      'receiptNo': 'R0006-********',
    },
  ];

  // 预设撤销申请数据
  final List<Map<String, dynamic>> _refundRequests = <Map<String, dynamic>>[
    <String, dynamic>{
      'id': 'RF********001',
      'transactionId': 'TX********004',
      'amount': 68750.0,
      'reason': '客户要求退款 - 金额错误',
      'status': 'pending',
      'requestTime': DateTime.now().subtract(const Duration(hours: 1)),
      'approver': '-',
      'approveTime': null,
    },
    <String, dynamic>{
      'id': 'RF20230414001',
      'transactionId': 'TX20230414010',
      'amount': 112500.0,
      'reason': '油枪故障',
      'status': 'approved',
      'requestTime': DateTime.now().subtract(const Duration(days: 1, hours: 5)),
      'approver': 'Supervisor 001',
      'approveTime': DateTime.now().subtract(const Duration(days: 1, hours: 4)),
    },
    <String, dynamic>{
      'id': 'RF20230414002',
      'transactionId': 'TX20230414015',
      'amount': 93750.0,
      'reason': '客户不满意服务质量',
      'status': 'rejected',
      'requestTime': DateTime.now().subtract(const Duration(days: 1, hours: 3)),
      'approver': 'Supervisor 002',
      'approveTime': DateTime.now().subtract(const Duration(days: 1, hours: 2)),
      'rejectReason': '不符合退款政策',
    },
  ];

  // 搜索结果
  List<Map<String, dynamic>> _searchResults = <Map<String, dynamic>>[];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _transactionIdController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  // 搜索交易
  void _searchTransaction() {
    final String query = _transactionIdController.text.trim();

    if (query.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入交易号'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _selectedTransaction = null;
    });

    // 模拟网络请求延迟
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _searchResults = _eligibleTransactions.where((Map<String, dynamic> tx) {
          return tx['id']
              .toString()
              .toLowerCase()
              .contains(query.toLowerCase());
        }).toList();

        _isLoading = false;
      });
    });
  }

  // 选择交易
  void _selectTransaction(Map<String, dynamic> transaction) {
    setState(() {
      _selectedTransaction = transaction;
    });
  }

  // 提交退款申请
  void _submitRefundRequest() {
    if (_selectedTransaction == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先选择需要撤销的交易'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_reasonController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入撤销原因'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // 模拟提交过程
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _isLoading = false;

        // 添加到撤销申请列表
        _refundRequests.insert(0, <String, dynamic>{
          'id':
              'RF${DateFormat('yyyyMMdd').format(DateTime.now())}${(_refundRequests.length + 1).toString().padLeft(3, '0')}',
          'transactionId': _selectedTransaction!['id'],
          'amount': _selectedTransaction!['amount'],
          'reason': _reasonController.text,
          'status': 'pending',
          'requestTime': DateTime.now(),
          'approver': '-',
          'approveTime': null,
        });

        // 重置表单
        _selectedTransaction = null;
        _reasonController.clear();
        _transactionIdController.clear();
        _searchResults = <Map<String, dynamic>>[];
      });

      // 显示成功提示
      _showSuccessDialog();
    });
  }

  // 格式化货币
  String _formatCurrency(double amount) {
    return amount.toStringAsFixed(0);
  }

  // 格式化日期时间
  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '-';
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('交易撤销'),
        backgroundColor: Colors.blue[800],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: const <Widget>[
            Tab(text: '撤销申请'),
            Tab(text: '申请记录'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: <Widget>[
          _buildRefundRequestTab(),
          _buildRefundStatusTab(),
        ],
      ),
    );
  }

  Widget _buildRefundRequestTab() {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _buildTransactionSearchSection(),
                const SizedBox(height: 24),
                if (_searchResults.isNotEmpty) ...<Widget>[
                  _buildSearchResultsSection(),
                  const SizedBox(height: 24),
                ],
                if (_selectedTransaction != null) ...<Widget>[
                  _buildSelectedTransactionSection(),
                  const SizedBox(height: 24),
                  _buildRefundReasonSection(),
                  const SizedBox(height: 24),
                  _buildSubmitButton(),
                  const SizedBox(height: 16),
                  _buildRefundPolicySection(),
                ],
              ],
            ),
          );
  }

  Widget _buildTransactionSearchSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          '第1步: 查找需要撤销的交易',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: <Widget>[
            Expanded(
              child: TextField(
                controller: _transactionIdController,
                decoration: const InputDecoration(
                  labelText: '交易号',
                  hintText: '输入要撤销的交易号',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                ),
                onSubmitted: (_) => _searchTransaction(),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _searchTransaction,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.blue,
              ),
              child: const Text('查询'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        const Text(
          '提示: 只有当天完成的交易才能申请撤销',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResultsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          '搜索结果: ${_searchResults.length}条交易',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _searchResults.length,
          separatorBuilder: (_, __) => const Divider(),
          itemBuilder: (BuildContext context, int index) {
            final Map<String, dynamic> transaction = _searchResults[index];
            final bool isSelected = _selectedTransaction != null &&
                _selectedTransaction!['id'] == transaction['id'];

            return ListTile(
              selected: isSelected,
              selectedTileColor: Colors.blue[50],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: isSelected
                    ? BorderSide(color: Colors.blue[300]!, width: 2)
                    : BorderSide.none,
              ),
              leading: CircleAvatar(
                backgroundColor: Colors.blue[100],
                child: const Icon(Icons.receipt, color: Colors.blue),
              ),
              title: Text(transaction['id'] as String),
              subtitle: Text(
                '${transaction['fuelType']} - ${_formatDateTime(transaction['timestamp'] as DateTime)}',
              ),
              trailing: Text(
                'IDR ${_formatCurrency(transaction['amount'] as double)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              onTap: () => _selectTransaction(transaction),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSelectedTransactionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          '第2步: 确认交易详情',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: Colors.blue[300]!, width: 1),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Icon(Icons.info_outline, color: Colors.blue[700]),
                    const SizedBox(width: 8),
                    const Text(
                      '交易详情',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const Divider(height: 24),
                _buildInfoRow('交易编号:', _selectedTransaction!['id'] as String),
                _buildInfoRow(
                    '交易时间:',
                    _formatDateTime(
                        _selectedTransaction!['timestamp'] as DateTime)),
                _buildInfoRow(
                    '油品类型:', _selectedTransaction!['fuelType'] as String),
                _buildInfoRow('加油量:', '${_selectedTransaction!['volume']} L'),
                _buildInfoRow(
                    '支付方式:', _selectedTransaction!['paymentMethod'] as String),
                _buildInfoRow('支付金额:',
                    'IDR ${_formatCurrency(_selectedTransaction!['amount'] as double)}'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: <Widget>[
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRefundReasonSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          '第3步: 输入撤销原因',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _reasonController,
          decoration: const InputDecoration(
            labelText: '撤销原因',
            hintText: '请详细描述撤销原因',
            border: OutlineInputBorder(),
            alignLabelWithHint: true,
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _submitRefundRequest,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          '提交撤销申请',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildRefundPolicySection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '撤销政策',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.amber,
            ),
          ),
          SizedBox(height: 8),
          Text('1. 所有撤销申请需要主管审批'),
          Text('2. 只有当天的交易可以申请撤销'),
          Text('3. 撤销申请一旦提交无法撤回'),
          Text('4. 原支付方式将用于退款处理'),
        ],
      ),
    );
  }

  Widget _buildRefundStatusTab() {
    return _refundRequests.isEmpty
        ? _buildEmptyRefundRequestsState()
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _refundRequests.length,
            itemBuilder: (BuildContext context, int index) {
              final Map<String, dynamic> request = _refundRequests[index];
              return _buildRefundRequestCard(request);
            },
          );
  }

  Widget _buildEmptyRefundRequestsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.history,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '没有撤销申请记录',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '您可以在撤销申请标签页提交新的申请',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRefundRequestCard(Map<String, dynamic> request) {
    final _StatusInfo statusInfo = _getStatusInfo(request['status'] as String);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  request['id'] as String,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusInfo.color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    statusInfo.text,
                    style: TextStyle(
                      color: statusInfo.color,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '交易号: ${request['transactionId']}',
              style: const TextStyle(
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '申请时间: ${_formatDateTime(request['requestTime'] as DateTime)}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const Divider(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text(
                  '申请金额:',
                  style: TextStyle(fontSize: 14),
                ),
                Text(
                  'IDR ${_formatCurrency(request['amount'] as double)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '撤销原因:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 4),
            Text(
              request['reason'] as String,
              style: const TextStyle(
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text(
                  '审批人:',
                  style: TextStyle(fontSize: 14),
                ),
                Text(
                  request['approver'] as String,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            if (request['status'] == 'approved' ||
                request['status'] == 'rejected') ...<Widget>[
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  const Text(
                    '审批时间:',
                    style: TextStyle(fontSize: 14),
                  ),
                  Text(
                    _formatDateTime(request['approveTime'] as DateTime?),
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ],
            if (request['status'] == 'rejected' &&
                request.containsKey('rejectReason')) ...<Widget>[
              const SizedBox(height: 12),
              const Text(
                '拒绝原因:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 4),
              Text(
                request['rejectReason'] as String,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.red,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  _StatusInfo _getStatusInfo(String status) {
    switch (status) {
      case 'pending':
        return _StatusInfo(text: '待审批', color: Colors.orange);
      case 'approved':
        return _StatusInfo(text: '已批准', color: Colors.green);
      case 'rejected':
        return _StatusInfo(text: '已拒绝', color: Colors.red);
      default:
        return _StatusInfo(text: '未知状态', color: Colors.grey);
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Row(
          children: <Widget>[
            Icon(Icons.check_circle, color: Colors.green[600]),
            const SizedBox(width: 8),
            const Text('申请提交成功'),
          ],
        ),
        content: const Text('您的撤销申请已成功提交，请等待主管审批。您可以在"申请记录"标签页查看审批状态。'),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // 切换到申请记录标签
              _tabController.animateTo(1);
            },
            child: const Text('查看申请记录'),
          ),
        ],
      ),
    );
  }
}

class _StatusInfo {
  _StatusInfo({required this.text, required this.color});
  final String text;
  final Color color;
}
