import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TransactionHistoryPage extends ConsumerStatefulWidget {
  const TransactionHistoryPage({super.key});

  @override
  ConsumerState<TransactionHistoryPage> createState() =>
      _TransactionHistoryPageState();
}

class _TransactionHistoryPageState
    extends ConsumerState<TransactionHistoryPage> {
  // 当前选中的日期
  DateTime _selectedDate = DateTime.now();

  // 交易号搜索控制器
  final TextEditingController _transactionIdController =
      TextEditingController();

  // 支付方式过滤
  String? _selectedPaymentMethod;

  // 是否加载中
  bool _isLoading = false;

  // 预设支付方式列表
  final List<String> _paymentMethods = <String>[
    '全部',
    '现金',
    '银行卡',
    '会员账户',
    '电子钱包',
  ];

  // 预设交易历史数据
  final List<Map<String, dynamic>> _allTransactions = <Map<String, dynamic>>[
    <String, dynamic>{
      'id': 'TX********001',
      'nozzleId': '01',
      'fuelType': 'Pertamax',
      'volume': 10.45,
      'amount': 130625.0,
      'paymentMethod': '现金',
      'status': 'completed',
      'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
      'receiptNo': 'R0001-********',
    },
    <String, dynamic>{
      'id': 'TX********002',
      'nozzleId': '02',
      'fuelType': 'Solar',
      'volume': 15.75,
      'amount': 157500.0,
      'paymentMethod': '银行卡',
      'bankType': 'BCA',
      'cardPrefix': '400000',
      'status': 'completed',
      'timestamp':
          DateTime.now().subtract(const Duration(hours: 1, minutes: 45)),
      'receiptNo': 'R0002-********',
    },
    <String, dynamic>{
      'id': 'TX********003',
      'nozzleId': '03',
      'fuelType': 'Pertamax Turbo',
      'volume': 8.2,
      'amount': 123000.0,
      'paymentMethod': '会员账户',
      'memberId': '345678',
      'memberName': 'Ahmad Wijaya',
      'status': 'completed',
      'timestamp':
          DateTime.now().subtract(const Duration(hours: 1, minutes: 20)),
      'receiptNo': 'R0003-********',
    },
    <String, dynamic>{
      'id': 'TX********004',
      'nozzleId': '01',
      'fuelType': 'Pertamax',
      'volume': 5.5,
      'amount': 68750.0,
      'paymentMethod': '现金',
      'status': 'refund_requested',
      'timestamp': DateTime.now().subtract(const Duration(hours: 1)),
      'receiptNo': 'R0004-********',
      'refundReason': '客户要求退款 - 金额错误',
    },
    <String, dynamic>{
      'id': 'TX********005',
      'nozzleId': '04',
      'fuelType': 'Solar',
      'volume': 20.0,
      'amount': 200000.0,
      'paymentMethod': '银行卡',
      'bankType': 'Mandiri',
      'cardPrefix': '422222',
      'status': 'completed',
      'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
      'receiptNo': 'R0005-********',
    },
    <String, dynamic>{
      'id': 'TX********006',
      'nozzleId': '02',
      'fuelType': 'Pertamax',
      'volume': 12.5,
      'amount': 156250.0,
      'paymentMethod': '会员账户',
      'memberId': '234567',
      'memberName': 'Siti Nurhayati',
      'status': 'completed',
      'timestamp': DateTime.now().subtract(const Duration(minutes: 15)),
      'receiptNo': 'R0006-********',
    },
  ];

  // 过滤后的交易数据
  List<Map<String, dynamic>> _filteredTransactions = <Map<String, dynamic>>[];

  @override
  void initState() {
    super.initState();
    // 初始化过滤交易列表
    _applyFilters();
  }

  @override
  void dispose() {
    _transactionIdController.dispose();
    super.dispose();
  }

  // 应用过滤条件
  void _applyFilters() {
    setState(() {
      _isLoading = true;
    });

    // 模拟网络请求延迟
    Future.delayed(const Duration(milliseconds: 800), () {
      setState(() {
        _filteredTransactions =
            _allTransactions.where((Map<String, dynamic> tx) {
          // 检查日期是否匹配
          final DateTime txDate = tx['timestamp'] as DateTime;
          final bool isSameDate = txDate.year == _selectedDate.year &&
              txDate.month == _selectedDate.month &&
              txDate.day == _selectedDate.day;

          // 检查交易号是否匹配
          final String transactionIdFilter =
              _transactionIdController.text.trim();
          final bool transactionIdMatches = transactionIdFilter.isEmpty ||
              tx['id']
                  .toString()
                  .toLowerCase()
                  .contains(transactionIdFilter.toLowerCase());

          // 检查支付方式是否匹配
          final bool paymentMethodMatches = _selectedPaymentMethod == null ||
              _selectedPaymentMethod == '全部' ||
              tx['paymentMethod'] == _selectedPaymentMethod;

          return isSameDate && transactionIdMatches && paymentMethodMatches;
        }).toList();

        // 按时间倒序排列
        _filteredTransactions
            .sort((Map<String, dynamic> a, Map<String, dynamic> b) {
          final DateTime aTime = a['timestamp'] as DateTime;
          final DateTime bTime = b['timestamp'] as DateTime;
          return bTime.compareTo(aTime);
        });

        _isLoading = false;
      });
    });
  }

  // 日期选择器
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 90)),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _applyFilters();
    }
  }

  // 打开交易详情
  void _openTransactionDetails(Map<String, dynamic> transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) =>
          _buildTransactionDetailsSheet(transaction),
    );
  }

  // 格式化货币
  String _formatCurrency(double amount) {
    return amount.toStringAsFixed(0);
  }

  // 格式化日期时间
  String _formatDateTime(DateTime dateTime, {bool showTime = true}) {
    if (showTime) {
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    } else {
      return DateFormat('yyyy-MM-dd').format(dateTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('交易历史'),
        backgroundColor: Colors.blue[800],
      ),
      body: Column(
        children: <Widget>[
          _buildFilterSection(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredTransactions.isEmpty
                    ? _buildEmptyState()
                    : _buildTransactionList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Expanded(
                child: _buildDateSelectField(),
              ),
              const SizedBox(width: 8),
              _buildFilterButton(),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: <Widget>[
              Expanded(
                child: _buildSearchField(),
              ),
              const SizedBox(width: 8),
              _buildPaymentMethodDropdown(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelectField() {
    return InkWell(
      onTap: () => _selectDate(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: <Widget>[
            const Icon(Icons.calendar_today, size: 18),
            const SizedBox(width: 8),
            Text(
              _formatDateTime(_selectedDate, showTime: false),
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterButton() {
    return ElevatedButton.icon(
      onPressed: _applyFilters,
      icon: const Icon(Icons.filter_list),
      label: const Text('筛选'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: _transactionIdController,
      decoration: const InputDecoration(
        hintText: '输入交易号查询',
        prefixIcon: Icon(Icons.search),
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(vertical: 0),
      ),
      onSubmitted: (_) => _applyFilters(),
    );
  }

  Widget _buildPaymentMethodDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedPaymentMethod ?? '全部',
          items: _paymentMethods.map((String method) {
            return DropdownMenuItem<String>(
              value: method,
              child: Text(method),
            );
          }).toList(),
          onChanged: (String? value) {
            setState(() {
              _selectedPaymentMethod = value;
            });
            _applyFilters();
          },
          hint: const Text('支付方式'),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.receipt_long,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '没有找到交易记录',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '尝试调整筛选条件或选择其他日期',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionList() {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredTransactions.length,
      separatorBuilder: (_, __) => const SizedBox(height: 12),
      itemBuilder: (BuildContext context, int index) {
        final Map<String, dynamic> transaction = _filteredTransactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  Widget _buildTransactionCard(Map<String, dynamic> transaction) {
    final Color statusColor = _getStatusColor(transaction['status'] as String);
    final String statusText = _getStatusText(transaction['status'] as String);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _openTransactionDetails(transaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Expanded(
                    child: Text(
                      transaction['id'] as String,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      statusText,
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        '支付方式: ${transaction['paymentMethod'] as String}',
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${transaction['fuelType'] as String} / ${transaction['volume']} L',
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        'IDR ${_formatCurrency(transaction['amount'] as double)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDateTime(transaction['timestamp'] as DateTime,
                            showTime: true),
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    '枪号: ${transaction['nozzleId'] as String}',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                  Row(
                    children: <Widget>[
                      Icon(
                        Icons.receipt_outlined,
                        size: 14,
                        color: Colors.blue[700],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        transaction['receiptNo'] as String,
                        style: TextStyle(
                          color: Colors.blue[700],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionDetailsSheet(Map<String, dynamic> transaction) {
    return Container(
      padding: const EdgeInsets.all(24),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              const Text(
                '交易详情',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  _buildDetailSection(
                    title: '基本信息',
                    children: <Widget>[
                      _buildDetailRow('交易编号', transaction['id'] as String),
                      _buildDetailRow(
                          '交易时间',
                          _formatDateTime(
                              transaction['timestamp'] as DateTime)),
                      _buildDetailRow(
                          '小票号码', transaction['receiptNo'] as String),
                      _buildDetailRow('状态',
                          _getStatusText(transaction['status'] as String)),
                      if (transaction['status'] == 'refund_requested' &&
                          transaction.containsKey('refundReason'))
                        _buildDetailRow(
                            '撤销原因', transaction['refundReason'] as String),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildDetailSection(
                    title: '油品信息',
                    children: <Widget>[
                      _buildDetailRow('枪号', transaction['nozzleId'] as String),
                      _buildDetailRow(
                          '油品类型', transaction['fuelType'] as String),
                      _buildDetailRow('加油量', '${transaction['volume']} L'),
                      _buildDetailRow('金额',
                          'IDR ${_formatCurrency(transaction['amount'] as double)}'),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildDetailSection(
                    title: '支付信息',
                    children: <Widget>[
                      _buildDetailRow(
                          '支付方式', transaction['paymentMethod'] as String),
                      if (transaction['paymentMethod'] == '银行卡') ...<Widget>[
                        _buildDetailRow(
                            '银行', transaction['bankType'] as String),
                        _buildDetailRow(
                            '卡号前缀', transaction['cardPrefix'] as String),
                      ],
                      if (transaction['paymentMethod'] == '会员账户') ...<Widget>[
                        _buildDetailRow(
                            '会员ID', transaction['memberId'] as String),
                        _buildDetailRow(
                            '会员姓名', transaction['memberName'] as String),
                      ],
                    ],
                  ),
                  const SizedBox(height: 24),
                  if (transaction['status'] == 'completed')
                    _buildActionButtons(transaction),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 90,
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.grey,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Map<String, dynamic> transaction) {
    return Row(
      children: <Widget>[
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              // 显示打印确认对话框
              Navigator.pop(context); // 关闭详情页
              _showPrintConfirmDialog(transaction);
            },
            icon: const Icon(Icons.print),
            label: const Text('重打小票'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              // 显示撤销确认对话框
              Navigator.pop(context); // 关闭详情页
              _showRefundConfirmDialog(transaction);
            },
            icon: const Icon(Icons.replay),
            label: const Text('申请撤销'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  void _showPrintConfirmDialog(Map<String, dynamic> transaction) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('打印小票确认'),
        content: Text('确定要重新打印交易 ${transaction['id']} 的小票吗？'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // 显示打印成功提示
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('小票打印请求已发送'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('确认打印'),
          ),
        ],
      ),
    );
  }

  void _showRefundConfirmDialog(Map<String, dynamic> transaction) {
    final TextEditingController reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('申请交易撤销'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text('交易编号: ${transaction['id']}'),
            Text('金额: IDR ${_formatCurrency(transaction['amount'] as double)}'),
            const SizedBox(height: 16),
            const Text('请输入撤销原因:'),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                hintText: '例如: 客户要求退款',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            const Text(
              '注意: 所有交易撤销需要主管审批通过后才能处理',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('请输入撤销原因'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pop(context);
              // 显示申请成功提示
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('撤销申请已提交，等待主管审批'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('提交申请'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'refund_requested':
        return Colors.orange;
      case 'refunded':
        return Colors.red;
      case 'pending':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'refund_requested':
        return '撤销申请中';
      case 'refunded':
        return '已撤销';
      case 'pending':
        return '处理中';
      default:
        return '未知状态';
    }
  }
}
