import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../widgets/safe_scaffold.dart';
import '../../theme/app_theme.dart';
import '../../constants/bp_colors.dart';

class MarketingHomePage extends StatefulWidget {
  const MarketingHomePage({super.key});

  @override
  State<MarketingHomePage> createState() => _MarketingHomePageState();
}

class _MarketingHomePageState extends State<MarketingHomePage> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        // 直接返回主页
        context.go('/');
      },
      child: SafeScaffold(
        appBar: AppBar(
          title: const Text(
            'Promotion',
            style: EDCTextStyles.appBarTitle,
          ),
          backgroundColor: BPColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          toolbarHeight: 64,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Text(
                'Promotion Functions',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              // 营销功能网格
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  childAspectRatio: 1.1,
                  children: <Widget>[
                    _buildFeatureCard(
                      context,
                      icon: Icons.card_giftcard,
                      title: 'Promotions',
                      subtitle: 'Feature coming soon',
                      color: Colors.grey,
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('促销功能正在开发中...'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      },
                    ),
                    _buildFeatureCard(
                      context,
                      icon: Icons.confirmation_number,
                      title: 'Coupon Verification',
                      subtitle: 'Feature coming soon',
                      color: Colors.grey,
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('优惠券验证功能正在开发中...'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      },
                    ),
                    _buildFeatureCard(
                      context,
                      icon: Icons.redeem,
                      title: 'Gift Redemption',
                      subtitle: 'Feature coming soon',
                      color: Colors.grey,
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('礼品兑换功能正在开发中...'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      },
                    ),
                    _buildFeatureCard(
                      context,
                      icon: Icons.history,
                      title: 'Activity History',
                      subtitle: 'Feature coming soon',
                      color: Colors.grey,
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('活动历史功能正在开发中...'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 28,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 6),
              Text(
                subtitle,
                style: const TextStyle(
                  color: BPColors.neutral,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
