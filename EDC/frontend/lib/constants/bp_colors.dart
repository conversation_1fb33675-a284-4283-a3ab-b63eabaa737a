import 'package:flutter/material.dart';

/// BP 品牌标准色彩体系
/// 基于 BP Logo 标准色彩规范设计
class BPColors {
  // 纯白背景

  // 私有构造函数，防止实例化
  BPColors._();
  // BP Logo 标准色彩 - 从提供的Logo图片中提取
  static const Color primary = Color(0xFF00A650); // BP标准绿色 - 主色调
  static const Color secondary = Color(0xFF80C242); // BP辅助绿色 - 次要元素
  static const Color accent = Color(0xFF007AFF); // BP黄色 - 强调色和警告，太多弱了看不清，所以改成蓝色
  static const Color current = Color(0xFF2E8B57); // 当前系统绿色 - 保持兼容

  // 功能色彩
  static const Color success = Color(0xFF80C242); // 成功状态
  static const Color warning = Color(0xFFFFD903); // 警告提示
  static const Color error = Color(0xFFCC0000); // 错误状态
  static const Color neutral = Color(0xFF666666); // 中性文本
  static const Color background = Color(0xFFFFFFFF);

  /// 获取BP主色的MaterialColor样本
  static MaterialColor get primarySwatch => _createSwatch(primary);

  /// 基于给定颜色创建Material样本
  static MaterialColor _createSwatch(Color color) {
    final HSLColor hsl = HSLColor.fromColor(color);
    return MaterialColor(color.value, <int, Color>{
      50: hsl.withLightness((hsl.lightness + 0.4).clamp(0.0, 1.0)).toColor(),
      100: hsl.withLightness((hsl.lightness + 0.3).clamp(0.0, 1.0)).toColor(),
      200: hsl.withLightness((hsl.lightness + 0.2).clamp(0.0, 1.0)).toColor(),
      300: hsl.withLightness((hsl.lightness + 0.1).clamp(0.0, 1.0)).toColor(),
      400: hsl.withLightness((hsl.lightness + 0.05).clamp(0.0, 1.0)).toColor(),
      500: color, // 基础色
      600: hsl.withLightness((hsl.lightness - 0.1).clamp(0.0, 1.0)).toColor(),
      700: hsl.withLightness((hsl.lightness - 0.2).clamp(0.0, 1.0)).toColor(),
      800: hsl.withLightness((hsl.lightness - 0.3).clamp(0.0, 1.0)).toColor(),
      900: hsl.withLightness((hsl.lightness - 0.4).clamp(0.0, 1.0)).toColor(),
    });
  }

  /// 获取次要色的MaterialColor样本
  static MaterialColor get secondarySwatch => _createSwatch(secondary);

  /// 获取强调色的MaterialColor样本
  static MaterialColor get accentSwatch => _createSwatch(accent);

  /// 根据背景颜色获取合适的文本颜色
  static Color getTextColorForBackground(Color backgroundColor) {
    // 计算背景颜色的亮度
    final double luminance = backgroundColor.computeLuminance();
    // 如果背景较暗，使用白色文字；如果背景较亮，使用深色文字
    return luminance > 0.5 ? const Color(0xFF212121) : Colors.white;
  }

  /// 获取颜色的透明度变体
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity.clamp(0.0, 1.0));
  }

  /// BP绿色渐变色（用于特殊UI效果）
  static const LinearGradient primaryGradient = LinearGradient(
    colors: <Color>[primary, secondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 获取状态颜色
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'active':
        return success;
      case 'warning':
      case 'pending':
        return warning;
      case 'error':
      case 'failed':
      case 'cancelled':
        return error;
      default:
        return neutral;
    }
  }
}
