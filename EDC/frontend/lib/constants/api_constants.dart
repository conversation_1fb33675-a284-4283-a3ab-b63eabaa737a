// EDC/frontend/lib/constants/api_constants.dart

import '../services/api/service_registry.dart';
import '../services/api/api_service.dart';
import '../services/shift_service.dart';

// 定义后端服务类型
enum BackendService {
  base, // 基础服务 (Base Service)
  fcc, // FCC 服务 (FCC Service)
}

// 定义API环境的枚举类型
// 使用枚举可以提高代码的可读性和类型安全
enum ApiEnvironment {
  local,
  development,
  staging, // 添加测试环境
  production, // 添加生产环境
}

// API相关的常量和配置
class ApiConstants {
  // 用于在SharedPreferences中存储环境选择的键
  static const String environmentPreferenceKey = 'api_environment_preference';

  // ======================== 多服务配置 ========================

  // 各服务在不同环境下的 BaseURL 配置
  static const Map<ApiEnvironment, Map<BackendService, String>> serviceUrls =
      <ApiEnvironment, Map<BackendService, String>>{
    ApiEnvironment.local: <BackendService, String>{
      BackendService.base: 'http://172.18.7.81:8080', // Base 服务本地地址 (BOS)
      BackendService.fcc: 'http://172.18.7.82:8081', // FCC 服务本地地址
    },
    ApiEnvironment.development: <BackendService, String>{
      BackendService.base: 'http://172.18.7.81:8080', // Base 服务开发地址 (BOS)
      BackendService.fcc: 'http://172.18.7.82:8081', // FCC 服务本地地址
    },
    ApiEnvironment.staging: <BackendService, String>{
      BackendService.base: 'http://172.18.7.81:8080', // Base 服务测试地址 (BOS)
      BackendService.fcc: 'http://172.18.7.82:8081', // FCC 服务测试地址
    },
    ApiEnvironment.production: <BackendService, String>{
      BackendService.base: 'http://172.18.7.81:8080', // Base 服务生产地址 (BOS)
      BackendService.fcc: 'http://172.18.7.82:8081', // FCC 服务生产地址
    },
  };

  // ======================== 兼容性支持 ========================

  // 兼容原有的单一服务配置（如果项目还有使用单一baseUrl的地方）
  static const String localBaseUrl = 'https://sruampsmujry.sealosgzg.site';
  static const String developmentBaseUrl = 'http://192.168.85.66:8080';
  static const String stagingBaseUrl = 'https://api-staging.bp-edc.com';
  static const String productionBaseUrl = 'https://api.bp-edc.com';

  // ======================== 服务URL获取方法 ========================

  /// 获取指定服务在指定环境下的 BaseURL
  static String getServiceUrl(BackendService service, ApiEnvironment env) {
    final Map<BackendService, String>? serviceUrlMap = serviceUrls[env];
    if (serviceUrlMap == null) {
      throw ArgumentError('不支持的环境: $env');
    }

    final String? url = serviceUrlMap[service];
    if (url == null) {
      throw ArgumentError('环境 $env 中未配置服务 $service 的URL');
    }

    return url;
  }

  /// 获取所有服务在指定环境下的URL配置
  static Map<BackendService, String> getAllServiceUrls(ApiEnvironment env) {
    final Map<BackendService, String>? serviceUrlMap = serviceUrls[env];
    if (serviceUrlMap == null) {
      throw ArgumentError('不支持的环境: $env');
    }
    return Map.from(serviceUrlMap);
  }

  /// 检查指定服务在指定环境下是否已配置
  static bool isServiceConfigured(BackendService service, ApiEnvironment env) {
    return serviceUrls[env]?.containsKey(service) ?? false;
  }

  // ======================== 兼容性方法 ========================

  /// 兼容原有的 getBaseUrl 方法（返回 base 服务的URL作为默认）
  @Deprecated('请使用 getServiceUrl 方法指定具体服务')
  static String getBaseUrl(ApiEnvironment env) {
    // 默认返回 base 服务的URL以保持兼容性
    try {
      return getServiceUrl(BackendService.base, env);
    } catch (e) {
      // 如果服务URL未配置，回退到原有的单一URL配置
      switch (env) {
        case ApiEnvironment.local:
          return localBaseUrl;
        case ApiEnvironment.development:
          return developmentBaseUrl;
        case ApiEnvironment.staging:
          return stagingBaseUrl;
        case ApiEnvironment.production:
          return productionBaseUrl;
      }
    }
  }

  // ======================== 工具方法 ========================

  /// 获取环境名称
  static String getEnvironmentName(ApiEnvironment env) {
    switch (env) {
      case ApiEnvironment.local:
        return 'Local';
      case ApiEnvironment.development:
        return 'Development';
      case ApiEnvironment.staging:
        return 'Staging';
      case ApiEnvironment.production:
        return 'Production';
    }
  }

  /// 获取服务名称
  static String getServiceName(BackendService service) {
    switch (service) {
      case BackendService.base:
        return 'Base Service';
      case BackendService.fcc:
        return 'FCC Service';
    }
  }

  // ======================== 自定义URL支持 ========================

  // 自定义服务URL配置
  static final Map<BackendService, String> _customServiceUrls =
      <BackendService, String>{};

  /// 设置自定义服务URL
  static void setCustomServiceUrl(BackendService service, String url) {
    _customServiceUrls[service] = url;
  }

  /// 获取自定义服务URL
  static String? getCustomServiceUrl(BackendService service) {
    return _customServiceUrls[service];
  }

  /// 清除自定义服务URL
  static void clearCustomServiceUrl(BackendService service) {
    _customServiceUrls.remove(service);
  }

  /// 清除所有自定义服务URL
  static void clearAllCustomServiceUrls() {
    _customServiceUrls.clear();
  }

  /// 获取最终使用的服务URL（优先使用自定义URL）
  static String getFinalServiceUrl(BackendService service, ApiEnvironment env) {
    // 1. 优先使用自定义URL
    final String? customUrl = getCustomServiceUrl(service);
    if (customUrl != null) {
      return customUrl;
    }

    // 2. 使用配置中的URL
    return getServiceUrl(service, env);
  }

  // === Convenience Methods for FCC Service ===

  /// Get FCC Service URL for a specific environment
  /// Uses the existing multi-service configuration system
  static String getFccServiceUrl(ApiEnvironment env) {
    return getServiceUrl(BackendService.fcc, env);
  }

  /// Get FCC WebSocket URL for real-time updates
  static String getFccWebSocketUrl(ApiEnvironment env) {
    final String baseUrl = getFccServiceUrl(env);
    return '${baseUrl.replaceFirst('http', 'ws')}/ws';
  }

  /// Get all configured service URLs for an environment
  static Map<String, String> getAllServiceUrlsForEnv(ApiEnvironment env) {
    final Map<BackendService, String> urls = getAllServiceUrls(env);
    return urls.map(
        (BackendService service, String url) => MapEntry(service.name, url));
  }

  // ======================== 全局配置更新方法 ========================

  /// 全局更新所有API服务的配置
  /// 这个方法会同时更新ServiceRegistry、旧版ApiService和其他独立的API服务
  static Future<void> updateAllApiConfigurations(ApiEnvironment env) async {
    try {
      // 1. 重新配置ServiceRegistry
      final serviceRegistry = ServiceRegistry();
      if (serviceRegistry.isInitialized) {
        await serviceRegistry.reconfigureAllServices();
        print('✅ ServiceRegistry配置已更新');
      }

      // 2. 重新配置旧版ApiService
      final apiService = ApiService();
      final String newBaseUrl = getFinalServiceUrl(BackendService.base, env);
      apiService.reinit(baseUrl: newBaseUrl);
      print('✅ 旧版ApiService配置已更新: $newBaseUrl');

      // 3. ShiftManagementApi已集成到ApiService中，无需单独配置
      print('✅ ShiftManagementApi配置已通过ApiService更新');

      // 4. 重新配置ShiftService
      try {
        // ShiftService现在使用ApiService，会自动使用最新配置
        final shiftService = ShiftService();
        if (shiftService.isInitialized) {
          shiftService.switchEnvironment(env);
          print('✅ ShiftService配置已更新');
        } else {
          print('ℹ️ ShiftService未初始化，将在首次使用时使用新配置');
        }
      } catch (e) {
        print('⚠️ ShiftService配置更新失败: $e');
      }

      // 5. 通知FCC服务需要重新配置
      // 注意：由于FCC服务使用Provider管理，我们需要通过其他方式通知重新配置
      // 这里我们打印一个警告，提醒需要重启应用或手动重新初始化FCC服务
      print('⚠️ FCC服务配置已更新，但需要重新创建FCCDeviceService实例才能生效');
      print('💡 建议：重启应用或在相关页面重新初始化FCC服务');

      print('✅ 所有API服务配置更新完成');
    } catch (e) {
      print('❌ API服务配置更新失败: $e');
      rethrow;
    }
  }
}
