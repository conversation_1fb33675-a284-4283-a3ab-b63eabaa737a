// EDC/frontend/lib/constants/customer_type_constants.dart

/// 客户类型相关的常量和枚举定义
/// 用于统一管理客户类型的字段名、值和检测逻辑

/// 客户类型枚举
enum CustomerType {
  b2b('B2B'),
  b2c('B2C');

  const CustomerType(this.value);
  
  /// 客户类型的字符串值
  final String value;

  /// 从字符串值创建客户类型枚举
  static CustomerType? fromString(String? value) {
    if (value == null || value.isEmpty) return null;
    
    final String upperValue = value.toUpperCase();
    for (final CustomerType type in CustomerType.values) {
      if (type.value == upperValue) {
        return type;
      }
    }
    return null;
  }

  /// 检查字符串是否为B2B类型
  static bool isB2B(String? value) {
    return fromString(value) == CustomerType.b2b;
  }

  /// 检查字符串是否为B2C类型
  static bool isB2C(String? value) {
    return fromString(value) == CustomerType.b2c;
  }

  @override
  String toString() => value;
}

/// 客户类型字段名常量
class CustomerTypeFields {
  /// 标准字段名（唯一使用）
  static const String customerType = 'customerType';
}

/// 客户类型工具类
class CustomerTypeUtils {
  /// 从metadata中检测客户类型
  ///
  /// 只检查标准字段名 customerType
  /// 如果字段不存在或值无效，默认返回B2C
  static CustomerType detectCustomerType(Map<String, dynamic> metadata, {String? memberId}) {
    final String? customerTypeValue = metadata[CustomerTypeFields.customerType]?.toString();
    if (customerTypeValue != null) {
      final CustomerType? type = CustomerType.fromString(customerTypeValue);
      if (type != null) {
        return type;
      }
    }

    // 默认为B2C
    return CustomerType.b2c;
  }

  /// 检查是否为B2B客户
  static bool isB2BCustomer(Map<String, dynamic> metadata, {String? memberId}) {
    return detectCustomerType(metadata, memberId: memberId) == CustomerType.b2b;
  }

  /// 检查是否为B2C客户
  static bool isB2CCustomer(Map<String, dynamic> metadata, {String? memberId}) {
    return detectCustomerType(metadata, memberId: memberId) == CustomerType.b2c;
  }

  /// 设置客户类型到metadata中（使用标准字段名）
  static void setCustomerType(Map<String, dynamic> metadata, CustomerType customerType) {
    metadata[CustomerTypeFields.customerType] = customerType.value;
  }

  /// 获取客户类型的显示文本
  static String getDisplayText(CustomerType customerType) {
    switch (customerType) {
      case CustomerType.b2b:
        return 'Business Customer';
      case CustomerType.b2c:
        return 'Individual Customer';
    }
  }

  /// 获取所有客户类型的字符串值列表
  static List<String> getAllValues() {
    return CustomerType.values.map((type) => type.value).toList();
  }
}
