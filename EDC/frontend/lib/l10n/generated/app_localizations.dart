import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'BP EDC System'**
  String get appName;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No Data'**
  String get noData;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @warning.
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @previous.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Transaction tab label in navigation
  ///
  /// In en, this message translates to:
  /// **'Transactions'**
  String get transactionTab;

  /// No description provided for @ordersTab.
  ///
  /// In en, this message translates to:
  /// **'Receipts'**
  String get ordersTab;

  /// No description provided for @membersTab.
  ///
  /// In en, this message translates to:
  /// **'Members'**
  String get membersTab;

  /// No description provided for @marketingTab.
  ///
  /// In en, this message translates to:
  /// **'Marketing'**
  String get marketingTab;

  /// No description provided for @settingsTab.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsTab;

  /// No description provided for @shiftsTab.
  ///
  /// In en, this message translates to:
  /// **'Shifts'**
  String get shiftsTab;

  /// No description provided for @analyticsTab.
  ///
  /// In en, this message translates to:
  /// **'Analytics'**
  String get analyticsTab;

  /// No description provided for @devicesTab.
  ///
  /// In en, this message translates to:
  /// **'Devices'**
  String get devicesTab;

  /// No description provided for @staffTab.
  ///
  /// In en, this message translates to:
  /// **'Staff'**
  String get staffTab;

  /// No description provided for @systemTab.
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get systemTab;

  /// No description provided for @transaction.
  ///
  /// In en, this message translates to:
  /// **'Transaction'**
  String get transaction;

  /// No description provided for @transactions.
  ///
  /// In en, this message translates to:
  /// **'Transactions'**
  String get transactions;

  /// No description provided for @transactionId.
  ///
  /// In en, this message translates to:
  /// **'Transaction ID'**
  String get transactionId;

  /// No description provided for @transactionDate.
  ///
  /// In en, this message translates to:
  /// **'Transaction Date'**
  String get transactionDate;

  /// No description provided for @transactionTime.
  ///
  /// In en, this message translates to:
  /// **'Transaction Time'**
  String get transactionTime;

  /// No description provided for @transactionAmount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get transactionAmount;

  /// No description provided for @transactionStatus.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get transactionStatus;

  /// No description provided for @transactionDetails.
  ///
  /// In en, this message translates to:
  /// **'Transaction Details'**
  String get transactionDetails;

  /// No description provided for @transactionHistory.
  ///
  /// In en, this message translates to:
  /// **'Transaction History'**
  String get transactionHistory;

  /// No description provided for @fuelTransaction.
  ///
  /// In en, this message translates to:
  /// **'Fuel Transaction'**
  String get fuelTransaction;

  /// No description provided for @paymentTransaction.
  ///
  /// In en, this message translates to:
  /// **'Payment Transaction'**
  String get paymentTransaction;

  /// No description provided for @order.
  ///
  /// In en, this message translates to:
  /// **'Receipt'**
  String get order;

  /// No description provided for @orders.
  ///
  /// In en, this message translates to:
  /// **'Receipts'**
  String get orders;

  /// No description provided for @orderId.
  ///
  /// In en, this message translates to:
  /// **'Receipt ID'**
  String get orderId;

  /// No description provided for @orderDate.
  ///
  /// In en, this message translates to:
  /// **'Receipt Date'**
  String get orderDate;

  /// No description provided for @orderTime.
  ///
  /// In en, this message translates to:
  /// **'Receipt Time'**
  String get orderTime;

  /// No description provided for @orderAmount.
  ///
  /// In en, this message translates to:
  /// **'Receipt Amount'**
  String get orderAmount;

  /// No description provided for @orderStatus.
  ///
  /// In en, this message translates to:
  /// **'Receipt Status'**
  String get orderStatus;

  /// No description provided for @orderDetails.
  ///
  /// In en, this message translates to:
  /// **'Receipt Details'**
  String get orderDetails;

  /// No description provided for @createOrder.
  ///
  /// In en, this message translates to:
  /// **'Create Receipt'**
  String get createOrder;

  /// No description provided for @cancelOrder.
  ///
  /// In en, this message translates to:
  /// **'Cancel Receipt'**
  String get cancelOrder;

  /// No description provided for @orderSearch.
  ///
  /// In en, this message translates to:
  /// **'Receipt Search'**
  String get orderSearch;

  /// No description provided for @orderList.
  ///
  /// In en, this message translates to:
  /// **'Receipt List'**
  String get orderList;

  /// No description provided for @member.
  ///
  /// In en, this message translates to:
  /// **'Member'**
  String get member;

  /// No description provided for @members.
  ///
  /// In en, this message translates to:
  /// **'Members'**
  String get members;

  /// No description provided for @memberId.
  ///
  /// In en, this message translates to:
  /// **'Member ID'**
  String get memberId;

  /// No description provided for @memberName.
  ///
  /// In en, this message translates to:
  /// **'Member Name'**
  String get memberName;

  /// No description provided for @memberPhone.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get memberPhone;

  /// No description provided for @memberEmail.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get memberEmail;

  /// No description provided for @memberLevel.
  ///
  /// In en, this message translates to:
  /// **'Member Level'**
  String get memberLevel;

  /// No description provided for @memberPoints.
  ///
  /// In en, this message translates to:
  /// **'Points'**
  String get memberPoints;

  /// No description provided for @memberStatus.
  ///
  /// In en, this message translates to:
  /// **'Member Status'**
  String get memberStatus;

  /// No description provided for @memberRegistration.
  ///
  /// In en, this message translates to:
  /// **'Member Registration'**
  String get memberRegistration;

  /// No description provided for @memberManagement.
  ///
  /// In en, this message translates to:
  /// **'Member Management'**
  String get memberManagement;

  /// No description provided for @memberInfo.
  ///
  /// In en, this message translates to:
  /// **'Member Information'**
  String get memberInfo;

  /// No description provided for @memberQuery.
  ///
  /// In en, this message translates to:
  /// **'Member Query'**
  String get memberQuery;

  /// No description provided for @pointsManagement.
  ///
  /// In en, this message translates to:
  /// **'Points Management'**
  String get pointsManagement;

  /// No description provided for @payment.
  ///
  /// In en, this message translates to:
  /// **'Payment'**
  String get payment;

  /// No description provided for @payments.
  ///
  /// In en, this message translates to:
  /// **'Payments'**
  String get payments;

  /// No description provided for @paymentMethod.
  ///
  /// In en, this message translates to:
  /// **'Payment Method'**
  String get paymentMethod;

  /// No description provided for @paymentAmount.
  ///
  /// In en, this message translates to:
  /// **'Payment Amount'**
  String get paymentAmount;

  /// No description provided for @paymentStatus.
  ///
  /// In en, this message translates to:
  /// **'Payment Status'**
  String get paymentStatus;

  /// No description provided for @cashPayment.
  ///
  /// In en, this message translates to:
  /// **'Cash Payment'**
  String get cashPayment;

  /// No description provided for @cardPayment.
  ///
  /// In en, this message translates to:
  /// **'Card Payment'**
  String get cardPayment;

  /// No description provided for @bankCardPayment.
  ///
  /// In en, this message translates to:
  /// **'Bank Card Payment'**
  String get bankCardPayment;

  /// No description provided for @memberPayment.
  ///
  /// In en, this message translates to:
  /// **'Member Payment'**
  String get memberPayment;

  /// No description provided for @paymentSuccess.
  ///
  /// In en, this message translates to:
  /// **'Payment Successful'**
  String get paymentSuccess;

  /// No description provided for @paymentFailed.
  ///
  /// In en, this message translates to:
  /// **'Payment Failed'**
  String get paymentFailed;

  /// No description provided for @refund.
  ///
  /// In en, this message translates to:
  /// **'Refund'**
  String get refund;

  /// No description provided for @refundRequest.
  ///
  /// In en, this message translates to:
  /// **'Refund Request'**
  String get refundRequest;

  /// No description provided for @marketing.
  ///
  /// In en, this message translates to:
  /// **'Marketing'**
  String get marketing;

  /// No description provided for @promotion.
  ///
  /// In en, this message translates to:
  /// **'Promotion'**
  String get promotion;

  /// No description provided for @promotions.
  ///
  /// In en, this message translates to:
  /// **'Promotions'**
  String get promotions;

  /// No description provided for @coupon.
  ///
  /// In en, this message translates to:
  /// **'Coupon'**
  String get coupon;

  /// No description provided for @coupons.
  ///
  /// In en, this message translates to:
  /// **'Coupons'**
  String get coupons;

  /// No description provided for @discount.
  ///
  /// In en, this message translates to:
  /// **'Discount'**
  String get discount;

  /// No description provided for @gift.
  ///
  /// In en, this message translates to:
  /// **'Gift'**
  String get gift;

  /// No description provided for @giftRedemption.
  ///
  /// In en, this message translates to:
  /// **'Gift Redemption'**
  String get giftRedemption;

  /// No description provided for @promotionDetails.
  ///
  /// In en, this message translates to:
  /// **'Promotion Details'**
  String get promotionDetails;

  /// No description provided for @promotionHistory.
  ///
  /// In en, this message translates to:
  /// **'Promotion History'**
  String get promotionHistory;

  /// No description provided for @couponVerification.
  ///
  /// In en, this message translates to:
  /// **'Coupon Verification'**
  String get couponVerification;

  /// No description provided for @promotionCalculation.
  ///
  /// In en, this message translates to:
  /// **'Promotion Calculation'**
  String get promotionCalculation;

  /// No description provided for @printing.
  ///
  /// In en, this message translates to:
  /// **'Printing'**
  String get printing;

  /// No description provided for @printer.
  ///
  /// In en, this message translates to:
  /// **'Printer'**
  String get printer;

  /// No description provided for @printerStatus.
  ///
  /// In en, this message translates to:
  /// **'Printer Status'**
  String get printerStatus;

  /// No description provided for @printerSettings.
  ///
  /// In en, this message translates to:
  /// **'Printer Settings'**
  String get printerSettings;

  /// No description provided for @receipt.
  ///
  /// In en, this message translates to:
  /// **'Receipt'**
  String get receipt;

  /// No description provided for @receiptPreview.
  ///
  /// In en, this message translates to:
  /// **'Receipt Preview'**
  String get receiptPreview;

  /// No description provided for @receiptReprint.
  ///
  /// In en, this message translates to:
  /// **'Receipt Reprint'**
  String get receiptReprint;

  /// No description provided for @printHistory.
  ///
  /// In en, this message translates to:
  /// **'Print History'**
  String get printHistory;

  /// No description provided for @printTask.
  ///
  /// In en, this message translates to:
  /// **'Print Task'**
  String get printTask;

  /// No description provided for @statusActive.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get statusActive;

  /// No description provided for @statusInactive.
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get statusInactive;

  /// No description provided for @statusPending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get statusPending;

  /// No description provided for @statusCompleted.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get statusCompleted;

  /// No description provided for @statusCancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get statusCancelled;

  /// No description provided for @statusFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get statusFailed;

  /// No description provided for @statusProcessing.
  ///
  /// In en, this message translates to:
  /// **'Processing'**
  String get statusProcessing;

  /// No description provided for @errorNetworkConnection.
  ///
  /// In en, this message translates to:
  /// **'Network connection error'**
  String get errorNetworkConnection;

  /// No description provided for @errorInvalidInput.
  ///
  /// In en, this message translates to:
  /// **'Invalid input'**
  String get errorInvalidInput;

  /// No description provided for @errorUnauthorized.
  ///
  /// In en, this message translates to:
  /// **'Unauthorized access'**
  String get errorUnauthorized;

  /// No description provided for @errorServerError.
  ///
  /// In en, this message translates to:
  /// **'Server error'**
  String get errorServerError;

  /// No description provided for @errorUnknown.
  ///
  /// In en, this message translates to:
  /// **'Unknown error'**
  String get errorUnknown;

  /// No description provided for @msgOperationSuccess.
  ///
  /// In en, this message translates to:
  /// **'Operation successful'**
  String get msgOperationSuccess;

  /// No description provided for @msgOperationFailed.
  ///
  /// In en, this message translates to:
  /// **'Operation failed'**
  String get msgOperationFailed;

  /// No description provided for @msgDataLoaded.
  ///
  /// In en, this message translates to:
  /// **'Data loaded successfully'**
  String get msgDataLoaded;

  /// No description provided for @msgNoDataFound.
  ///
  /// In en, this message translates to:
  /// **'No data found'**
  String get msgNoDataFound;

  /// No description provided for @msgPleaseWait.
  ///
  /// In en, this message translates to:
  /// **'Please wait...'**
  String get msgPleaseWait;

  /// No description provided for @msgConfirmDelete.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this item?'**
  String get msgConfirmDelete;

  /// No description provided for @msgUnsavedChanges.
  ///
  /// In en, this message translates to:
  /// **'You have unsaved changes. Do you want to save them?'**
  String get msgUnsavedChanges;

  /// No description provided for @fieldRequired.
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get fieldRequired;

  /// No description provided for @fieldInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid format'**
  String get fieldInvalid;

  /// No description provided for @fieldTooShort.
  ///
  /// In en, this message translates to:
  /// **'Input is too short'**
  String get fieldTooShort;

  /// No description provided for @fieldTooLong.
  ///
  /// In en, this message translates to:
  /// **'Input is too long'**
  String get fieldTooLong;

  /// No description provided for @fieldInvalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Invalid email format'**
  String get fieldInvalidEmail;

  /// No description provided for @fieldInvalidPhone.
  ///
  /// In en, this message translates to:
  /// **'Invalid phone number format'**
  String get fieldInvalidPhone;

  /// No description provided for @fieldInvalidNumber.
  ///
  /// In en, this message translates to:
  /// **'Invalid number format'**
  String get fieldInvalidNumber;

  /// No description provided for @fieldInvalidDate.
  ///
  /// In en, this message translates to:
  /// **'Invalid date format'**
  String get fieldInvalidDate;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
