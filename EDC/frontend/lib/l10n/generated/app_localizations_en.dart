// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'BP EDC System';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get search => 'Search';

  @override
  String get refresh => 'Refresh';

  @override
  String get loading => 'Loading...';

  @override
  String get noData => 'No Data';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get retry => 'Retry';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get close => 'Close';

  @override
  String get settings => 'Settings';

  @override
  String get transactionTab => 'Transactions';

  @override
  String get ordersTab => 'Receipts';

  @override
  String get membersTab => 'Members';

  @override
  String get marketingTab => 'Marketing';

  @override
  String get settingsTab => 'Settings';

  @override
  String get shiftsTab => 'Shifts';

  @override
  String get analyticsTab => 'Analytics';

  @override
  String get devicesTab => 'Devices';

  @override
  String get staffTab => 'Staff';

  @override
  String get systemTab => 'System';

  @override
  String get transaction => 'Transaction';

  @override
  String get transactions => 'Transactions';

  @override
  String get transactionId => 'Transaction ID';

  @override
  String get transactionDate => 'Transaction Date';

  @override
  String get transactionTime => 'Transaction Time';

  @override
  String get transactionAmount => 'Amount';

  @override
  String get transactionStatus => 'Status';

  @override
  String get transactionDetails => 'Transaction Details';

  @override
  String get transactionHistory => 'Transaction History';

  @override
  String get fuelTransaction => 'Fuel Transaction';

  @override
  String get paymentTransaction => 'Payment Transaction';

  @override
  String get order => 'Receipt';

  @override
  String get orders => 'Receipts';

  @override
  String get orderId => 'Receipt ID';

  @override
  String get orderDate => 'Receipt Date';

  @override
  String get orderTime => 'Receipt Time';

  @override
  String get orderAmount => 'Receipt Amount';

  @override
  String get orderStatus => 'Receipt Status';

  @override
  String get orderDetails => 'Receipt Details';

  @override
  String get createOrder => 'Create Receipt';

  @override
  String get cancelOrder => 'Cancel Receipt';

  @override
  String get orderSearch => 'Receipt Search';

  @override
  String get orderList => 'Receipt List';

  @override
  String get member => 'Member';

  @override
  String get members => 'Members';

  @override
  String get memberId => 'Member ID';

  @override
  String get memberName => 'Member Name';

  @override
  String get memberPhone => 'Phone Number';

  @override
  String get memberEmail => 'Email Address';

  @override
  String get memberLevel => 'Member Level';

  @override
  String get memberPoints => 'Points';

  @override
  String get memberStatus => 'Member Status';

  @override
  String get memberRegistration => 'Member Registration';

  @override
  String get memberManagement => 'Member Management';

  @override
  String get memberInfo => 'Member Information';

  @override
  String get memberQuery => 'Member Query';

  @override
  String get pointsManagement => 'Points Management';

  @override
  String get payment => 'Payment';

  @override
  String get payments => 'Payments';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get paymentAmount => 'Payment Amount';

  @override
  String get paymentStatus => 'Payment Status';

  @override
  String get cashPayment => 'Cash Payment';

  @override
  String get cardPayment => 'Card Payment';

  @override
  String get bankCardPayment => 'Bank Card Payment';

  @override
  String get memberPayment => 'Member Payment';

  @override
  String get paymentSuccess => 'Payment Successful';

  @override
  String get paymentFailed => 'Payment Failed';

  @override
  String get refund => 'Refund';

  @override
  String get refundRequest => 'Refund Request';

  @override
  String get marketing => 'Marketing';

  @override
  String get promotion => 'Promotion';

  @override
  String get promotions => 'Promotions';

  @override
  String get coupon => 'Coupon';

  @override
  String get coupons => 'Coupons';

  @override
  String get discount => 'Discount';

  @override
  String get gift => 'Gift';

  @override
  String get giftRedemption => 'Gift Redemption';

  @override
  String get promotionDetails => 'Promotion Details';

  @override
  String get promotionHistory => 'Promotion History';

  @override
  String get couponVerification => 'Coupon Verification';

  @override
  String get promotionCalculation => 'Promotion Calculation';

  @override
  String get printing => 'Printing';

  @override
  String get printer => 'Printer';

  @override
  String get printerStatus => 'Printer Status';

  @override
  String get printerSettings => 'Printer Settings';

  @override
  String get receipt => 'Receipt';

  @override
  String get receiptPreview => 'Receipt Preview';

  @override
  String get receiptReprint => 'Receipt Reprint';

  @override
  String get printHistory => 'Print History';

  @override
  String get printTask => 'Print Task';

  @override
  String get statusActive => 'Active';

  @override
  String get statusInactive => 'Inactive';

  @override
  String get statusPending => 'Pending';

  @override
  String get statusCompleted => 'Completed';

  @override
  String get statusCancelled => 'Cancelled';

  @override
  String get statusFailed => 'Failed';

  @override
  String get statusProcessing => 'Processing';

  @override
  String get errorNetworkConnection => 'Network connection error';

  @override
  String get errorInvalidInput => 'Invalid input';

  @override
  String get errorUnauthorized => 'Unauthorized access';

  @override
  String get errorServerError => 'Server error';

  @override
  String get errorUnknown => 'Unknown error';

  @override
  String get msgOperationSuccess => 'Operation successful';

  @override
  String get msgOperationFailed => 'Operation failed';

  @override
  String get msgDataLoaded => 'Data loaded successfully';

  @override
  String get msgNoDataFound => 'No data found';

  @override
  String get msgPleaseWait => 'Please wait...';

  @override
  String get msgConfirmDelete => 'Are you sure you want to delete this item?';

  @override
  String get msgUnsavedChanges =>
      'You have unsaved changes. Do you want to save them?';

  @override
  String get fieldRequired => 'This field is required';

  @override
  String get fieldInvalid => 'Invalid format';

  @override
  String get fieldTooShort => 'Input is too short';

  @override
  String get fieldTooLong => 'Input is too long';

  @override
  String get fieldInvalidEmail => 'Invalid email format';

  @override
  String get fieldInvalidPhone => 'Invalid phone number format';

  @override
  String get fieldInvalidNumber => 'Invalid number format';

  @override
  String get fieldInvalidDate => 'Invalid date format';
}
