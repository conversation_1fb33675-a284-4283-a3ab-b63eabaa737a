{"@@locale": "zh", "@@last_modified": "2024-01-15T00:00:00.000000", "appName": "BP EDC System", "ok": "OK", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "refresh": "Refresh", "loading": "Loading...", "noData": "No Data", "error": "Error", "success": "Success", "warning": "Warning", "retry": "Retry", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "settings": "Settings", "transactionTab": "Transactions", "ordersTab": "Receipts", "membersTab": "Members", "marketingTab": "Marketing", "settingsTab": "Settings", "shiftsTab": "Shifts", "analyticsTab": "Analytics", "devicesTab": "Devices", "staffTab": "Staff", "systemTab": "System", "transaction": "Transaction", "transactions": "Transactions", "transactionId": "Transaction ID", "transactionDate": "Transaction Date", "transactionTime": "Transaction Time", "transactionAmount": "Amount", "transactionStatus": "Status", "transactionDetails": "Transaction Details", "transactionHistory": "Transaction History", "fuelTransaction": "Fuel Transaction", "paymentTransaction": "Payment Transaction", "order": "Receipt", "orders": "Receipts", "orderId": "Receipt ID", "orderDate": "Receipt Date", "orderTime": "Receipt Time", "orderAmount": "Receipt Amount", "orderStatus": "Receipt Status", "orderDetails": "Receipt Details", "createOrder": "Create Receipt", "cancelOrder": "Cancel Receipt", "orderSearch": "Receipt Search", "orderList": "Receipt List", "member": "Member", "members": "Members", "memberId": "Member ID", "memberName": "Member Name", "memberPhone": "Phone Number", "memberEmail": "Email Address", "memberLevel": "Member Level", "memberPoints": "Points", "memberStatus": "Member Status", "memberRegistration": "Member Registration", "memberManagement": "Member Management", "memberInfo": "Member Information", "memberQuery": "Member Query", "pointsManagement": "Points Management", "payment": "Payment", "payments": "Payments", "paymentMethod": "Payment Method", "paymentAmount": "Payment Amount", "paymentStatus": "Payment Status", "cashPayment": "Cash Payment", "cardPayment": "Card Payment", "bankCardPayment": "Bank Card Payment", "memberPayment": "Member Payment", "paymentSuccess": "Payment Successful", "paymentFailed": "Payment Failed", "refund": "Refund", "refundRequest": "Refund Request", "marketing": "Marketing", "promotion": "Promotion", "promotions": "Promotions", "coupon": "Coupon", "coupons": "Coupons", "discount": "Discount", "gift": "Gift", "giftRedemption": "Gift Redemption", "promotionDetails": "Promotion Details", "promotionHistory": "Promotion History", "couponVerification": "Coupon Verification", "promotionCalculation": "Promotion Calculation", "printing": "Printing", "printer": "Printer", "printerStatus": "Printer Status", "printerSettings": "Printer <PERSON>s", "receipt": "Receipt", "receiptPreview": "Receipt Preview", "receiptReprint": "Receipt Reprint", "printHistory": "Print History", "printTask": "Print Task", "statusActive": "Active", "statusInactive": "Inactive", "statusPending": "Pending", "statusCompleted": "Completed", "statusCancelled": "Cancelled", "statusFailed": "Failed", "statusProcessing": "Processing", "errorNetworkConnection": "Network connection error", "errorInvalidInput": "Invalid input", "errorUnauthorized": "Unauthorized access", "errorServerError": "Server error", "errorUnknown": "Unknown error", "msgOperationSuccess": "Operation successful", "msgOperationFailed": "Operation failed", "msgDataLoaded": "Data loaded successfully", "msgNoDataFound": "No data found", "msgPleaseWait": "Please wait...", "msgConfirmDelete": "Are you sure you want to delete this item?", "msgUnsavedChanges": "You have unsaved changes. Do you want to save them?", "fieldRequired": "This field is required", "fieldInvalid": "Invalid format", "fieldTooShort": "Input is too short", "fieldTooLong": "Input is too long", "fieldInvalidEmail": "Invalid email format", "fieldInvalidPhone": "Invalid phone number format", "fieldInvalidNumber": "Invalid number format", "fieldInvalidDate": "Invalid date format"}