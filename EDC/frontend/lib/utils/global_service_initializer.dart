/// 全局服务初始化器
/// 确保关键服务在应用启动后正确初始化

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/shift_service.dart';
import '../services/new_auth_service.dart';

class GlobalServiceInitializer {
  static bool _initialized = false;
  
  /// 初始化所有关键服务
  static Future<void> initializeServices(WidgetRef ref) async {
    if (_initialized) {
      debugPrint('🔍 [GlobalServiceInitializer] 服务已初始化，跳过');
      return;
    }
    
    try {
      debugPrint('🔍 [GlobalServiceInitializer] 开始初始化全局服务...');
      
      // 1. 获取认证服务
      final NewAuthService authService = ref.read(newAuthServiceProvider);
      debugPrint('🔍 [GlobalServiceInitializer] 认证服务状态: isLoggedIn=${authService.isLoggedIn}');
      
      // 2. 如果用户已登录，初始化ShiftService
      if (authService.isLoggedIn) {
        debugPrint('🔍 [GlobalServiceInitializer] 用户已登录，初始化ShiftService...');
        
        // 设置全局认证服务
        ShiftService.setGlobalAuthService(authService);
        
        // 初始化ShiftService
        if (!ShiftService().isInitialized) {
          await ShiftService().initialize();
          debugPrint('✅ [GlobalServiceInitializer] ShiftService初始化完成');
        } else {
          debugPrint('✅ [GlobalServiceInitializer] ShiftService已初始化');
        }
      } else {
        debugPrint('🔍 [GlobalServiceInitializer] 用户未登录，跳过ShiftService初始化');
      }
      
      _initialized = true;
      debugPrint('✅ [GlobalServiceInitializer] 全局服务初始化完成');
      
    } catch (e) {
      debugPrint('❌ [GlobalServiceInitializer] 全局服务初始化失败: $e');
      // 不抛出异常，避免影响应用启动
    }
  }
  
  /// 重置初始化状态（用于测试或重新初始化）
  static void reset() {
    _initialized = false;
    debugPrint('🔍 [GlobalServiceInitializer] 初始化状态已重置');
  }
  
  /// 检查是否已初始化
  static bool get isInitialized => _initialized;
}
