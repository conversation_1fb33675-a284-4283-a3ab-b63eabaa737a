/// 认证服务工厂
/// 提供全局认证服务实例的访问

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/new_auth_service.dart';
import '../main.dart';

class AuthServiceFactory {
  static NewAuthService? _globalInstance;
  
  /// 获取全局认证服务实例
  static NewAuthService? getGlobalInstance() {
    return _globalInstance;
  }
  
  /// 设置全局认证服务实例
  static void setGlobalInstance(NewAuthService instance) {
    _globalInstance = instance;
  }
  
  /// 从Provider容器获取认证服务实例
  static NewAuthService? getFromProvider(ProviderContainer container) {
    try {
      return container.read(newAuthServiceProvider);
    } catch (e) {
      print('❌ 无法从Provider获取认证服务: $e');
      return null;
    }
  }
}
