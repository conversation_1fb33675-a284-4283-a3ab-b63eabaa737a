import 'package:intl/intl.dart';

/// 格式化工具类
class FormatUtils {
  /// 格式化印尼盾金额 - 显示完整数字，不使用K或M缩写
  /// 例如：1000000 -> "1,000,000"
  static String formatRupiah(double amount) {
    final NumberFormat formatter = NumberFormat('#,##0', 'id_ID');
    return formatter.format(amount);
  }

  /// 格式化升数 - 保留3位小数
  /// 例如：123.456789 -> "123.457"
  static String formatLiters(double liters) {
    return liters.toStringAsFixed(3);
  }

  /// 格式化整数（如订单数量、交易数量等）
  /// 例如：1000 -> "1,000"
  static String formatInteger(int number) {
    final NumberFormat formatter = NumberFormat('#,##0', 'id_ID');
    return formatter.format(number);
  }

  /// 格式化百分比
  /// 例如：0.123 -> "12.3%"
  static String formatPercentage(double percentage) {
    final NumberFormat formatter = NumberFormat('#,##0.0%', 'id_ID');
    return formatter.format(percentage);
  }

  /// 格式化小数（通用，可指定小数位数）
  /// 例如：123.456789, 2 -> "123.46"
  static String formatDecimal(double number, int decimalPlaces) {
    return number.toStringAsFixed(decimalPlaces);
  }
} 