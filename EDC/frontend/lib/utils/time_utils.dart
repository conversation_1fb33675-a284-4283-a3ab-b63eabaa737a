import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../services/timezone_service.dart';

/// 时间格式化工具类
class TimeUtils {
  /// 常用时间格式
  static const String defaultFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm:ss';
  static const String dateTimeShortFormat = 'MM-dd HH:mm';
  static const String displayFormat = 'yyyy-MM-dd HH:mm';
  static const String transactionFormat = 'yyyy-MM-dd HH:mm:ss';

  /// 格式化日期时间（使用全局时区设置）
  static String formatDateTime(
    WidgetRef ref,
    DateTime dateTime, {
    String? format,
    bool isUtc = true,
  }) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);

    // 如果传入的是UTC时间，需要转换为本地时区
    DateTime targetTime = dateTime;
    if (isUtc) {
      targetTime = timezoneService.getLocalTime(dateTime);
    }

    final DateFormat formatter = DateFormat(format ?? defaultFormat);
    return formatter.format(targetTime);
  }

  /// 格式化当前时间
  static String formatNow(WidgetRef ref, {String? format}) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    return timezoneService.formatNow(pattern: format ?? defaultFormat);
  }

  /// 格式化交易时间
  static String formatTransactionTime(WidgetRef ref, DateTime dateTime) {
    return formatDateTime(ref, dateTime, format: transactionFormat);
  }

  /// 格式化显示时间（用户友好格式）
  static String formatDisplayTime(WidgetRef ref, DateTime dateTime) {
    return formatDateTime(ref, dateTime, format: displayFormat);
  }

  /// 格式化日期
  static String formatDate(WidgetRef ref, DateTime dateTime) {
    return formatDateTime(ref, dateTime, format: dateFormat);
  }

  /// 格式化时间
  static String formatTime(WidgetRef ref, DateTime dateTime) {
    return formatDateTime(ref, dateTime, format: timeFormat);
  }

  /// 格式化短时间（不含年份）
  static String formatShortTime(WidgetRef ref, DateTime dateTime) {
    return formatDateTime(ref, dateTime, format: dateTimeShortFormat);
  }

  /// Get relative time description (e.g., 2 hours ago, 3 days ago)
  static String getRelativeTime(WidgetRef ref, DateTime dateTime) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    final DateTime now = timezoneService.getLocalTime();
    final Duration difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 30) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 365) {
      final int months = (difference.inDays / 30).floor();
      return '$months months ago';
    } else {
      final int years = (difference.inDays / 365).floor();
      return '$years years ago';
    }
  }

  /// 判断是否为今天
  static bool isToday(WidgetRef ref, DateTime dateTime) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    final DateTime now = timezoneService.getLocalTime();
    return dateTime.year == now.year &&
        dateTime.month == now.month &&
        dateTime.day == now.day;
  }

  /// 判断是否为昨天
  static bool isYesterday(WidgetRef ref, DateTime dateTime) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    final DateTime now = timezoneService.getLocalTime();
    final DateTime yesterday = now.subtract(const Duration(days: 1));
    return dateTime.year == yesterday.year &&
        dateTime.month == yesterday.month &&
        dateTime.day == yesterday.day;
  }

  /// Smart time display (today shows time, yesterday shows "Yesterday", others show date)
  static String formatSmartTime(WidgetRef ref, DateTime dateTime) {
    if (isToday(ref, dateTime)) {
      return 'Today ${formatTime(ref, dateTime)}';
    } else if (isYesterday(ref, dateTime)) {
      return 'Yesterday ${formatTime(ref, dateTime)}';
    } else {
      return formatDateTime(ref, dateTime, format: 'MM-dd HH:mm');
    }
  }

  /// 将本地时间转换为UTC（用于API调用）
  static DateTime toUtc(WidgetRef ref, DateTime localTime) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    return timezoneService.toUtc(localTime);
  }

  /// 将UTC时间转换为本地时间（用于显示）
  static DateTime toLocal(WidgetRef ref, DateTime utcTime) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    return timezoneService.getLocalTime(utcTime);
  }

  /// 获取时区偏移描述
  static String getTimezoneOffset(WidgetRef ref) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    return timezoneService.currentTimezoneInfo.offset;
  }

  /// 获取当前时区名称
  static String getTimezoneName(WidgetRef ref) {
    final TimezoneService timezoneService = ref.read(timezoneServiceProvider);
    return timezoneService.currentTimezoneInfo.displayName;
  }

  /// 安全的日期格式化方法 - 将 DateTime 转换为 YYYY-MM-DD 格式
  /// 避免 substring RangeError 问题
  static String safeDateFormat(DateTime dateTime) {
    try {
      // 使用更安全的方式格式化日期，避免 substring RangeError
      final String isoString = dateTime.toIso8601String();
      if (isoString.length >= 10) {
        return isoString.substring(0, 10);
      } else {
        // 回退到手动格式化
        return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      // 如果所有方法都失败，使用当前日期作为回退
      final DateTime now = DateTime.now();
      return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
    }
  }
}

/// 时间格式化扩展方法
extension DateTimeFormatExtension on DateTime {
  /// 使用全局时区设置格式化时间
  String formatWith(WidgetRef ref, {String? format}) {
    return TimeUtils.formatDateTime(ref, this, format: format);
  }

  /// 获取相对时间
  String getRelativeTime(WidgetRef ref) {
    return TimeUtils.getRelativeTime(ref, this);
  }

  /// 智能时间显示
  String formatSmart(WidgetRef ref) {
    return TimeUtils.formatSmartTime(ref, this);
  }

  /// 转换为UTC
  DateTime toUtcWith(WidgetRef ref) {
    return TimeUtils.toUtc(ref, this);
  }

  /// 转换为本地时间
  DateTime toLocalWith(WidgetRef ref) {
    return TimeUtils.toLocal(ref, this);
  }
}
