import 'package:flutter/material.dart';
import 'dart:async';

import '../services/global_polling_service.dart';
import '../services/shift_service.dart';

/// 班次状态轮询调试页面
class ShiftPollingDebugPage extends StatefulWidget {
  const ShiftPollingDebugPage({super.key});

  @override
  State<ShiftPollingDebugPage> createState() => _ShiftPollingDebugPageState();
}

class _ShiftPollingDebugPageState extends State<ShiftPollingDebugPage> {
  Map<String, dynamic>? _pollingStatus;
  String _logs = '';
  StreamSubscription<ShiftInfo?>? _shiftSubscription;
  Timer? _statusUpdateTimer;

  @override
  void initState() {
    super.initState();
    _loadStatus();
    _subscribeToShiftChanges();
    _startStatusUpdates();
  }

  @override
  void dispose() {
    _shiftSubscription?.cancel();
    _statusUpdateTimer?.cancel();
    super.dispose();
  }

  /// 加载轮询状态
  Future<void> _loadStatus() async {
    try {
      final Map<String, dynamic> status = await GlobalPollingService().checkDetailedStatus();
      setState(() {
        _pollingStatus = status;
      });
      _addLog('状态已更新: ${DateTime.now()}');
    } catch (e) {
      _addLog('加载状态失败: $e');
    }
  }

  /// 订阅班次状态变化
  void _subscribeToShiftChanges() {
    _shiftSubscription = ShiftService().shiftStatusStream.listen((ShiftInfo? shift) {
      _addLog('🎯 班次状态变化: ${shift?.shiftId ?? '无'} (${shift?.status.name ?? '无'})');
    });
  }

  /// 启动定期状态更新
  void _startStatusUpdates() {
    _statusUpdateTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _loadStatus();
    });
  }

  /// 添加日志
  void _addLog(String message) {
    setState(() {
      final String timestamp = DateTime.now().toString().substring(11, 19);
      _logs = '[$timestamp] $message\n$_logs';
      
      // 限制日志长度
      final List<String> lines = _logs.split('\n');
      if (lines.length > 50) {
        _logs = lines.take(50).join('\n');
      }
    });
  }

  /// 手动触发轮询
  Future<void> _triggerPolling() async {
    try {
      _addLog('手动触发轮询...');
      await GlobalPollingService().pollOnce();
      _addLog('手动轮询完成');
    } catch (e) {
      _addLog('手动轮询失败: $e');
    }
  }

  /// 清空日志
  void _clearLogs() {
    setState(() {
      _logs = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('班次状态轮询调试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '轮询服务状态',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    if (_pollingStatus != null) ...[
                      ..._pollingStatus!.entries.map((entry) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                entry.key,
                                style: const TextStyle(fontWeight: FontWeight.w500),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Text(
                                entry.value.toString(),
                                style: TextStyle(
                                  color: entry.value == true 
                                    ? Colors.green 
                                    : entry.value == false 
                                      ? Colors.red 
                                      : Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                    ] else
                      const Text('加载中...'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 操作按钮
            Row(
              children: [
                ElevatedButton(
                  onPressed: _loadStatus,
                  child: const Text('刷新状态'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _triggerPolling,
                  child: const Text('手动轮询'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _clearLogs,
                  child: const Text('清空日志'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 日志区域
            const Text(
              '实时日志',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[50],
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _logs.isEmpty ? '暂无日志...' : _logs,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
