import 'package:flutter/material.dart';
import '../constants/bp_colors.dart';
import '../constants/customer_type_constants.dart';
import '../theme/app_theme.dart';
import '../models/member_model.dart';
import '../services/member_cache_service.dart';

/// 底部会员信息栏组件
/// 根据会员缓存状态显示不同内容：
/// - 无缓存：显示录入按钮
/// - 有缓存：显示会员关键信息（车牌、车型、客户类型）和删除按钮
class MemberInfoBottomBar extends StatefulWidget {
  const MemberInfoBottomBar({
    super.key,
    this.onMemberEntry,
    this.onMemberRemove,
    this.onMemberTap,
  });

  /// 点击录入会员按钮的回调
  final VoidCallback? onMemberEntry;

  /// 点击删除会员缓存的回调
  final VoidCallback? onMemberRemove;

  /// 点击会员信息区域的回调（查看详情）
  final VoidCallback? onMemberTap;

  @override
  State<MemberInfoBottomBar> createState() => _MemberInfoBottomBarState();
}

class _MemberInfoBottomBarState extends State<MemberInfoBottomBar> {
  late final MemberCacheService _cacheService;

  @override
  void initState() {
    super.initState();
    _cacheService = memberCacheService;
    // 监听缓存状态变化
    _cacheService.addListener(_onCacheChanged);
  }

  @override
  void dispose() {
    _cacheService.removeListener(_onCacheChanged);
    super.dispose();
  }

  void _onCacheChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: true,
        child: _cacheService.hasCachedMember
            ? _buildMemberInfoSection()
            : _buildEntrySection(),
      ),
    );
  }

  /// 构建无会员缓存时的录入区域
  Widget _buildEntrySection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: <Widget>[
          // 会员信息提示图标和文字
          const Icon(
            Icons.person_outline,
            color: BPColors.neutral,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Customer Info',
            style: EDCTextStyles.bodyText.copyWith(
              color: BPColors.neutral,
              fontSize: 16,
            ),
          ),
          const Spacer(),

          // 录入会员按钮
          ElevatedButton.icon(
            onPressed: () {
              debugPrint('🎯 点击录入会员按钮');
              widget.onMemberEntry?.call();
            },
            icon: const Icon(Icons.add_circle_outline, size: 18),
            label: const Text('Add Customer Info'),
            style: ElevatedButton.styleFrom(
              backgroundColor: BPColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              minimumSize: const Size(100, 40),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              textStyle: EDCTextStyles.buttonText.copyWith(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建有会员缓存时的信息显示区域
  Widget _buildMemberInfoSection() {
    final Member member = _cacheService.cachedMember!;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.5),
            width: 1,
          ),
        ),
      ),
      child: InkWell(
        onTap: () {
          debugPrint('🎯 点击会员信息区域');
          widget.onMemberTap?.call();
        },
        child: Row(
          children: <Widget>[
            // 优化后的客户信息分层显示
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  // 经典白底黑字车牌
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Colors.black87, width: 2),
                      borderRadius: BorderRadius.circular(4),
                      boxShadow: <BoxShadow>[
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    child: Text(
                      _getPlateNumberText(member),
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                        letterSpacing: 1.2,
                      ),
                    ),
                  ),
                  const SizedBox(width: 14),

                  // 客户与车辆类型信息
                  Flexible(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        _buildInfoTag(
                          _getCustomerTypeText(member),
                          CustomerType.isB2B(_getCustomerTypeText(member))
                              ? BPColors.accent // BP Yellow
                              : BPColors.primary, // BP Green
                        ),
                        const SizedBox(height: 4),
                        _buildInfoTag(
                          _getVehicleTypeText(member),
                          BPColors.neutral,
                        ),
                      ],
                    ),
                  ),

                  // Company Name (仅 B2B 客户显示，放在右边)
                  if (_isB2BCustomer(member) && _getCompanyName(member).isNotEmpty) ...<Widget>[
                    const SizedBox(width: 12),
                    Flexible(
                      flex: 3,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: BPColors.accent.withValues(alpha: 0.1),
                          border: Border.all(
                            color: BPColors.accent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Icon(
                              Icons.business,
                              size: 14,
                              color: BPColors.accent,
                            ),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                _getCompanyName(member),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: BPColors.accent,
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // 简洁的删除按钮
            TextButton(
              onPressed: () {
                debugPrint('🎯 点击删除会员缓存按钮');
                _showRemoveConfirmDialog();
              },
              style: TextButton.styleFrom(
                foregroundColor: BPColors.error,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                'Remove',
                style: EDCTextStyles.bodyText.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: BPColors.error,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建带颜色圆点的标签
  Widget _buildInfoTag(String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 6),
        Flexible(
          child: Text(
            text,
            style: EDCTextStyles.bodyText.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF4B5563),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// 获取车牌号显示文本
  String _getPlateNumberText(Member member) {
    // 从 metadata 中获取车牌号信息
    final List? plateNumbers =
        member.metadata['plateNumbers'] as List<dynamic>?;

    if (plateNumbers != null && plateNumbers.isNotEmpty) {
      return plateNumbers.first.toString();
    }

    // 如果没有车牌号，显示默认文本
    return 'Not Set';
  }

  /// 获取车辆类型显示文本
  String _getVehicleTypeText(Member member) {
    // 从 metadata 中获取车辆类型信息
    final String? vehicle = member.metadata['vehicle'] as String?;

    if (vehicle != null && vehicle.isNotEmpty && vehicle != 'Unknown Vehicle') {
      // 简化显示，只显示车型名称
      return vehicle;
    }

    // 如果没有车辆信息，显示默认值
    return 'Vehicle';
  }

  /// 获取客户类型显示文本
  String _getCustomerTypeText(Member member) {
    // 使用统一的客户类型检测工具
    final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(member.metadata, memberId: member.id);
    return detectedType.value;
  }

  /// 检查是否为 B2B 客户
  bool _isB2BCustomer(Member member) {
    final CustomerType detectedType = CustomerTypeUtils.detectCustomerType(member.metadata, memberId: member.id);
    return detectedType == CustomerType.b2b;
  }

  /// 获取公司名称（仅 B2B 客户）
  String _getCompanyName(Member member) {
    // 对于 B2B 客户，member.name 字段存储的是公司名称或联系人姓名
    if (_isB2BCustomer(member) && member.name.isNotEmpty && member.name != 'Not Set') {
      return member.name;
    }
    return '';
  }

  /// 显示删除确认对话框
  void _showRemoveConfirmDialog() {
    final Member member = _cacheService.cachedMember!;

    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.fromLTRB(20, 20, 20, 24),
          title: Row(
            children: <Widget>[
              const Icon(
                Icons.warning_amber_rounded,
                color: BPColors.warning,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Remove Customer Info',
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: BPColors.primary,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                'Are you sure you want to remove the current cached customer information?',
                style: EDCTextStyles.bodyText.copyWith(
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      _isB2BCustomer(member)
                          ? 'Company: ${member.name}'
                          : 'Customer: ${member.name}',
                      style: EDCTextStyles.bodyText.copyWith(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Customer Type: ${_getCustomerTypeText(member)}',
                      style: EDCTextStyles.bodyText.copyWith(fontSize: 13),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Licence Plate: ${_getPlateNumberText(member)}',
                      style: EDCTextStyles.bodyText.copyWith(fontSize: 13),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Vehicle: ${_getVehicleTypeText(member)}',
                      style: EDCTextStyles.bodyText.copyWith(fontSize: 13),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: EDCTextStyles.buttonText.copyWith(
                  color: BPColors.neutral,
                  fontSize: 14,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _cacheService.clearCache();
                widget.onMemberRemove?.call();

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text('Customer information removed: ${member.name}'),
                    backgroundColor: BPColors.primary,
                    behavior: SnackBarBehavior.floating,
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.error,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                'Remove',
                style: EDCTextStyles.buttonText.copyWith(fontSize: 14),
              ),
            ),
          ],
        );
      },
    );
  }
}
