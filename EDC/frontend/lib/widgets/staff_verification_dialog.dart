import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';
import '../models/dispenser_model.dart';
import '../models/staff_card.dart';
import '../services/api/service_registry.dart';

// NFC Card Info data model
class NFCCardInfo {
  NFCCardInfo({
    required this.cardType,
    required this.cardCategory,
    required this.uuid,
    required this.ats,
  });

  factory NFCCardInfo.empty() {
    return NFCCardInfo(
      cardType: 'Unknown',
      cardCategory: 'Unknown',
      uuid: 'Unknown',
      ats: 'Unknown',
    );
  }
  final String cardType;
  final String cardCategory;
  final String uuid;
  final String ats;
}

// NFC卡控制器
class NFCCardController extends StateNotifier<AsyncValue<NFCCardInfo>> {
  NFCCardController() : super(const AsyncValue.loading()) {
    try {
      _channel = const MethodChannel('com.example.edc_app/card');
      debugPrint('NFCCardController initialized.');
    } catch (e) {
      debugPrint('Error initializing NFCCardController: $e');
      // 即使初始化失败，也保持加载状态，以便Debug按钮能够显示
      state = const AsyncValue.loading();
    }
  }

  late final MethodChannel _channel;
  bool _isCheckingCard = false;

  // 开始检测NFC卡
  Future<void> startCheckCard() async {
    if (_isCheckingCard) return;

    _isCheckingCard = true;
    try {
      state = const AsyncValue.loading();

      // 监听卡检测结果
      _channel.setMethodCallHandler((MethodCall call) async {
        if (call.method == 'onCardDetected') {
          final Map<String, dynamic> cardInfo =
              Map<String, dynamic>.from(call.arguments as Map);
          _handleCardInfo(cardInfo);
        } else if (call.method == 'onCardError') {
          final Map<String, dynamic> errorInfo =
              Map<String, dynamic>.from(call.arguments as Map);
          _handleCardError(errorInfo);
        }
        return null;
      });

      await _channel.invokeMethod('startCheckNFCCard');
    } catch (e) {
      debugPrint('Error starting NFC card check: $e');
      // 即使启动检测失败，也保持加载状态，不要设置为错误状态
      state = const AsyncValue.loading();
      _isCheckingCard = false;
    }
  }

  // 停止检测NFC卡
  Future<void> stopCheckCard() async {
    if (!_isCheckingCard) return;

    _isCheckingCard = false;
    try {
      await _channel.invokeMethod('stopCheckNFCCard');
      _channel.setMethodCallHandler(null);
    } catch (e) {
      debugPrint('Error stopping NFC card check: $e');
    }
  }

  // 处理卡信息
  void _handleCardInfo(Map<String, dynamic> cardInfo) {
    final NFCCardInfo nfcCardInfo = NFCCardInfo(
      cardType: (cardInfo['cardType'] as String?) ?? '未知',
      cardCategory: (cardInfo['cardCategory'] as String?) ?? '未知',
      uuid: (cardInfo['uuid'] as String?) ?? '未知',
      ats: (cardInfo['ats'] as String?) ?? '未知',
    );

    state = AsyncValue.data(nfcCardInfo);
  }

  // 处理卡错误
  void _handleCardError(Map<String, dynamic> errorInfo) {
    final String message = (errorInfo['message'] as String?) ?? '未知错误';
    final int code = (errorInfo['code'] as int?) ?? -1;

    debugPrint('Card error: $message (Code: $code)');
    // 即使发生错误，也保持加载状态，这样Debug按钮始终可见
    state = const AsyncValue.loading();

    // 短暂延迟后重新开始检测
    Future.delayed(const Duration(seconds: 2), () {
      if (_isCheckingCard) {
        state = const AsyncValue.loading();
        try {
          _channel.invokeMethod('startCheckNFCCard');
        } catch (e) {
          debugPrint('Error restarting NFC card check: $e');
        }
      }
    });
  }

  @override
  void dispose() {
    stopCheckCard();
    super.dispose();
  }
}

// 创建Provider
final AutoDisposeStateNotifierProvider<NFCCardController,
        AsyncValue<NFCCardInfo>> staffVerificationProvider =
    StateNotifierProvider.autoDispose<NFCCardController,
        AsyncValue<NFCCardInfo>>((AutoDisposeStateNotifierProviderRef<
            NFCCardController, AsyncValue<NFCCardInfo>>
        ref) {
  return NFCCardController();
});

// 融合的员工验证弹窗
class StaffVerificationDialog extends ConsumerStatefulWidget {
  // 必需的nozzle信息

  const StaffVerificationDialog({
    super.key,
    this.title = 'Staff Verification',
    this.message = 'Please tap your staff card to authorize fuel dispensing',
    required this.onVerified,
    required this.authRequest,
    required this.nozzle,
  });
  final String title;
  final String message;
  final void Function(VerifiedStaffInfo) onVerified;
  final AuthorizationRequest authRequest; // 必需的授权请求信息
  final Nozzle nozzle;

  // 显示对话框的静态方法
  static Future<bool> show(
    BuildContext context, {
    String title = 'Staff Verification',
    String message = 'Please tap your staff card to authorize fuel dispensing',
    required void Function(VerifiedStaffInfo) onVerified,
    required AuthorizationRequest authRequest,
    required Nozzle nozzle,
  }) async {
    return await showModalBottomSheet<bool>(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          isDismissible: false,
          enableDrag: false,
          builder: (BuildContext context) => StaffVerificationDialog(
            title: title,
            message: message,
            onVerified: onVerified,
            authRequest: authRequest,
            nozzle: nozzle,
          ),
        ) ??
        false;
  }

  @override
  ConsumerState<StaffVerificationDialog> createState() =>
      _StaffVerificationDialogState();
}

class _StaffVerificationDialogState
    extends ConsumerState<StaffVerificationDialog> {
  bool _isVerifying = true;
  String? _authErrorMessage; // Authorization error message
  bool _isVerifyingCard = false; // Card verification in progress
  StaffCard? _verifiedStaffCard; // Verified staff card from BOS

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        ref.read(staffVerificationProvider.notifier).startCheckCard();
      } catch (e) {
        debugPrint('Error in startCheckCard: $e');
        // 即使出错，也不影响对话框的显示
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final AsyncValue<NFCCardInfo> nfcCardState =
        ref.watch(staffVerificationProvider);

    // When NFC card is detected
    ref.listen<AsyncValue<NFCCardInfo>>(
      staffVerificationProvider,
      (_, AsyncValue<NFCCardInfo> state) {
        state.whenData((NFCCardInfo cardInfo) {
          if (_isVerifying && !_isVerifyingCard) {
            _handleCardDetected(cardInfo);
          }
        });
      },
    );

    return Container(
      height: MediaQuery.of(context).size.height * 0.85, // 85%屏幕高度
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // 顶部拖拽指示器
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: BPColors.neutral.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // 标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              widget.title,
              style: EDCTextStyles.subTitle.copyWith(
                fontWeight: FontWeight.bold,
                color: BPColors.primary,
                fontSize: 20, // 增大标题字体
              ),
            ),
          ),

          // 内容区域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // 压缩版授权摘要
                  _buildCompactAuthorizationSummary(),
                  const SizedBox(height: 24),

                  // NFC验证区域
                  _buildNFCVerificationArea(nfcCardState),
                ],
              ),
            ),
          ),

          // 底部按钮
          _buildActionButtons(nfcCardState),
        ],
      ),
    );
  }

  /// 构建压缩版授权摘要
  Widget _buildCompactAuthorizationSummary() {
    final Nozzle nozzle = widget.nozzle;
    final AuthorizationRequest authRequest = widget.authRequest;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: BPColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: BPColors.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 标题
          Row(
            children: <Widget>[
              const Icon(
                Icons.assignment_outlined,
                color: BPColors.primary,
                size: 18,
              ),
              const SizedBox(width: 6),
              Text(
                'Authorization Summary',
                style: EDCTextStyles.bodyText.copyWith(
                  fontWeight: FontWeight.bold,
                  color: BPColors.primary,
                  fontSize: 16, // 稍微增大
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // 信息行
          _buildCompactSummaryRow(
            Icons.local_gas_station,
            'Pump',
            'Pump ${nozzle.pumpGroupId} • ${nozzle.name} • ${(nozzle.fuelGrade)}',
          ),
          const SizedBox(height: 8),

          _buildCompactSummaryRow(
            Icons.settings,
            'Mode',
            _getAuthModeDisplayName(authRequest.mode),
          ),
          const SizedBox(height: 8),

          if (authRequest.mode != AuthMode.full)
            Column(
              children: <Widget>[
                _buildCompactSummaryRow(
                  authRequest.mode == AuthMode.amount
                      ? Icons.attach_money
                      : Icons.local_gas_station,
                  authRequest.mode == AuthMode.amount ? 'Amount' : 'Volume',
                  authRequest.mode == AuthMode.amount
                      ? 'Rp ${_formatRupiah(authRequest.value!)}'
                      : '${authRequest.value!.toStringAsFixed(1)}L',
                ),
                const SizedBox(height: 8),
              ],
            ),

          _buildCompactSummaryRow(
            Icons.attach_money,
            'Price',
            nozzle.price <= 0
                ? 'Price Error'
                : 'Rp ${_formatRupiah(nozzle.price)}/L',
          ),
        ],
      ),
    );
  }

  /// 构建压缩版摘要行
  Widget _buildCompactSummaryRow(IconData icon, String label, String value) {
    return Row(
      children: <Widget>[
        Icon(
          icon,
          size: 16,
          color: BPColors.primary.withOpacity(0.7),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 68,
          child: Text(
            label,
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.grey.shade600,
              fontSize: 14, // 增大字体
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          ': ',
          style: EDCTextStyles.bodyText.copyWith(
            color: Colors.grey.shade400,
            fontSize: 14, // 增大字体
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: EDCTextStyles.bodyText.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 14, // 增大字体
              color: Colors.black87,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// 构建NFC验证区域
  Widget _buildNFCVerificationArea(AsyncValue<NFCCardInfo> state) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50, // 改用中性背景色
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200, // 改用中性边框
          width: 1,
        ),
      ),
      child: Column(
        children: <Widget>[
          // NFC状态显示
          _buildNFCStatus(state),
          const SizedBox(height: 16),

          // 授权错误信息
          if (_authErrorMessage != null) ...<Widget>[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.red.shade200,
                  width: 1,
                ),
              ),
              child: Row(
                children: <Widget>[
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _authErrorMessage!,
                      style: EDCTextStyles.bodyText.copyWith(
                        color: Colors.red.shade600,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Instruction text
          Text(
            _getInstructionMessage(),
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.grey.shade700,
              fontSize: 16,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build NFC status display
  Widget _buildNFCStatus(AsyncValue<NFCCardInfo> state) {
    // Show card verification progress
    if (_isVerifyingCard) {
      return Column(
        children: <Widget>[
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(40),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                SizedBox(
                  width: 72,
                  height: 72,
                  child: CircularProgressIndicator(
                    strokeWidth: 4,
                    color: Colors.orange.shade600,
                    backgroundColor: Colors.orange.withOpacity(0.2),
                  ),
                ),
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: <BoxShadow>[
                      BoxShadow(
                        color: Colors.orange.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.verified_user,
                    size: 24,
                    color: Colors.orange.shade600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Verifying Card...',
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.orange.shade600,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            'Checking permissions via BOS',
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
        ],
      );
    }

    // Show verification success with staff info
    if (_verifiedStaffCard != null) {
      return Column(
        children: <Widget>[
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 48,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Verification Success',
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.green,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            'Card: ${_verifiedStaffCard!.cardNumber}',
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.black87,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (_verifiedStaffCard!.department.isNotEmpty) ...<Widget>[
            const SizedBox(height: 2),
            Text(
              'Department: ${_verifiedStaffCard!.department}',
              style: EDCTextStyles.bodyText.copyWith(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
          ],
        ],
      );
    }

    return state.when(
      data: (NFCCardInfo cardInfo) => Column(
        children: <Widget>[
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 48,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Card Detected',
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.green,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            'Card ID: ${cardInfo.uuid}',
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.black87,
              fontSize: 14,
            ),
          ),
        ],
      ),
      loading: () => Column(
        children: <Widget>[
          Container(
            width: 80, // 增大容器
            height: 80, // 增大容器
            decoration: BoxDecoration(
              color: BPColors.primary.withOpacity(0.1), // 改用主色调背景
              borderRadius: BorderRadius.circular(40),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                // 大背景转圈动画
                SizedBox(
                  width: 72, // 更大的动画
                  height: 72,
                  child: CircularProgressIndicator(
                    strokeWidth: 4, // 更粗的线条
                    color: BPColors.primary.withOpacity(0.8), // 使用主色调
                    backgroundColor: BPColors.primary.withOpacity(0.2),
                  ),
                ),
                // 中央NFC图标
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: <BoxShadow>[
                      BoxShadow(
                        color: BPColors.primary.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.nfc,
                    size: 24, // 更大的图标
                    color: BPColors.primary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16), // 增大间距
          Text(
            'Waiting for staff card...',
            style: EDCTextStyles.bodyText.copyWith(
              color: BPColors.primary, // 改用更清晰的主色调
              fontWeight: FontWeight.bold,
              fontSize: 18, // 增大字体
            ),
          ),
        ],
      ),
      error: (Object error, _) => Column(
        children: <Widget>[
          Container(
            width: 80, // 保持一致大小
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.error,
              color: Colors.red,
              size: 48, // 更大的错误图标
            ),
          ),
          const SizedBox(height: 16), // 保持一致间距
          Text(
            'Detection Error',
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: 18, // 增大字体
            ),
          ),
          const SizedBox(height: 6), // 稍微增大间距
          Text(
            error.toString(),
            style: EDCTextStyles.bodyText.copyWith(
              color: Colors.red,
              fontSize: 14, // 增大字体
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 构建底部按钮
  Widget _buildActionButtons(AsyncValue<NFCCardInfo> state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: <Widget>[
            // 取消按钮
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  ref.read(staffVerificationProvider.notifier).stopCheckCard();
                  Navigator.of(context).pop(false);
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: const BorderSide(color: BPColors.primary),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Cancel',
                  style: EDCTextStyles.buttonText.copyWith(
                    color: BPColors.primary,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // 重试按钮
            if (state is AsyncLoading<NFCCardInfo> && _authErrorMessage != null)
              Expanded(
                child: ElevatedButton(
                  onPressed: _retryAuthorization,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Retry',
                    style: EDCTextStyles.buttonText,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Handle NFC card detection and BOS verification
  Future<void> _handleCardDetected(NFCCardInfo cardInfo) async {
    setState(() {
      _isVerifyingCard = true;
      _authErrorMessage = null;
      _verifiedStaffCard = null;
    });

    try {
      debugPrint('🔍 NFC card detected: ${cardInfo.uuid}');
      debugPrint('🔍 Starting BOS staff card verification...');

      // Call BOS API to verify staff card
      final StaffCard staffCard = await _verifyStaffCard(cardInfo.uuid);
      
      debugPrint('✅ BOS staff card verification successful');
      debugPrint('   Staff ID: ${staffCard.id}');
      debugPrint('   Card Number: ${staffCard.cardNumber}');
      debugPrint('   Department: ${staffCard.department}');
      debugPrint('   Can authorize fuel: ${staffCard.canAuthorizeFuel}');

      // Validate card for fuel dispensing
      final StaffCardValidationResult validationResult = 
          staffCard.validateForFuelDispensing();
      
      if (!validationResult.isValid) {
        throw Exception(validationResult.errorMessage);
      }

      // Verification successful
      setState(() {
        _verifiedStaffCard = staffCard;
        _isVerifyingCard = false;
        _isVerifying = false;
      });

      // Stop NFC detection
      ref.read(staffVerificationProvider.notifier).stopCheckCard();

      // Create verified staff info
      final VerifiedStaffInfo verifiedInfo = VerifiedStaffInfo(
        cardInfo: cardInfo,
        staffCard: staffCard,
      );

      // Call success callback
      widget.onVerified(verifiedInfo);

      // Show success message and close dialog
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      });

    } catch (e) {
      debugPrint('❌ Staff card verification failed: $e');
      setState(() {
        _isVerifyingCard = false;
        _authErrorMessage = e.toString();
      });

      // Continue waiting for next card tap
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted && _isVerifying) {
          ref.read(staffVerificationProvider.notifier).startCheckCard();
        }
      });
    }
  }

  /// Verify staff card through BOS API
  Future<StaffCard> _verifyStaffCard(String cardNumber) async {
    try {
      // Use ServiceRegistry to get BOS employee API
      final employeeApi = ServiceRegistry().baseEmployeeApi;
      return await employeeApi.getStaffCardByNumber(cardNumber);
    } catch (e) {
      debugPrint('❌ BOS API staff card verification failed: $e');
      throw Exception('Staff card verification failed: ${e.toString()}');
    }
  }

  /// Get instruction message based on current state
  String _getInstructionMessage() {
    if (_authErrorMessage != null) {
      return 'Please tap "Retry" to try again or scan your staff card';
    }
    
    if (_isVerifyingCard) {
      return 'Verifying staff card with BOS system...';
    }
    
    if (_verifiedStaffCard != null) {
      return 'Staff card verified successfully! Authorization in progress...';
    }
    
    return widget.message;
  }

  /// Retry authorization verification
  void _retryAuthorization() {
    setState(() {
      _authErrorMessage = null;
      _isVerifying = true;
      _isVerifyingCard = false;
      _verifiedStaffCard = null;
    });

    // Restart NFC detection
    ref.read(staffVerificationProvider.notifier).startCheckCard();
  }

  // === 辅助方法 ===

  /// 转换授权模式到API格式
  String _getPresetType(AuthMode mode) {
    switch (mode) {
      case AuthMode.amount:
        return 'amount';
      case AuthMode.volume:
        return 'volume';
      case AuthMode.full:
        return 'full';
    }
  }

  /// 获取授权模式显示名称
  String _getAuthModeDisplayName(AuthMode mode) {
    switch (mode) {
      case AuthMode.amount:
        return 'By Amount';
      case AuthMode.volume:
        return 'By Volume';
      case AuthMode.full:
        return 'Full Tank';
    }
  }

  /// 获取简化的油品名称
  String _getShortFuelGrade(String fuelGrade) {
    final String grade = fuelGrade.toLowerCase();
    if (grade.contains('92') || grade.contains('pertamax')) return 'Pertalite';
    if (grade.contains('95') || grade.contains('pertamax plus'))
      return 'Pertamax';
    if (grade.contains('98') || grade.contains('pertamax turbo'))
      return 'Turbo';
    if (grade.contains('diesel') || grade.contains('dex')) return 'Solar';
    final List<String> words = fuelGrade.split(' ');
    return words.isNotEmpty ? words.first : fuelGrade;
  }

  /// 格式化印尼盾金额
  String _formatRupiah(double amount) {
    if (amount.isNaN || amount.isInfinite) {
      return 'N/A';
    }

    if (amount < 0) {
      return '-${_formatRupiah(-amount)}';
    }

    if (amount == 0) {
      return '0';
    }

    final int intAmount = amount.toInt();
    final String str = intAmount.toString();
    if (str.length <= 3) return str;

    String result = '';
    int counter = 0;
    for (int i = str.length - 1; i >= 0; i--) {
      if (counter == 3) {
        result = '.$result';
        counter = 0;
      }
      result = str[i] + result;
      counter++;
    }
    return result;
  }
}
