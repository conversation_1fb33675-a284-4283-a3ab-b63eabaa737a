import 'package:flutter/material.dart';

class MemberLevelBadge extends StatelessWidget {
  const MemberLevelBadge({
    super.key,
    required this.level,
    this.size = 14.0,
    this.showIcon = true,
  });
  final String level;
  final double size;
  final bool showIcon;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: _getLevelColor().withOpacity(0.2),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: _getLevelColor()),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          if (showIcon) ...<Widget>[
            Icon(
              _getLevelIcon(),
              size: size,
              color: _getLevelColor(),
            ),
            const SizedBox(width: 4.0),
          ],
          Text(
            level,
            style: TextStyle(
              fontSize: size,
              color: _getLevelColor(),
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getLevelColor() {
    switch (level.toLowerCase()) {
      case 'platinum':
        return Colors.purple[700] ?? Colors.purple;
      case 'gold':
        return Colors.amber[700] ?? Colors.amber;
      case 'silver':
        return Colors.blueGrey[700] ?? Colors.blueGrey;
      case 'bronze':
        return Colors.brown[700] ?? Colors.brown;
      default:
        return Colors.teal;
    }
  }

  IconData _getLevelIcon() {
    switch (level.toLowerCase()) {
      case 'platinum':
        return Icons.diamond;
      case 'gold':
        return Icons.star;
      case 'silver':
        return Icons.workspace_premium;
      case 'bronze':
        return Icons.military_tech;
      default:
        return Icons.person;
    }
  }
}
