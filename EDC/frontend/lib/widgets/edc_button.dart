import 'package:flutter/material.dart';
import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';

/// EDC标准按钮组件
/// 遵循BP品牌设计规范和移动设备触摸优化标准
class EDCButton extends StatelessWidget {
  const EDCButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = EDCButtonType.primary,
    this.size = EDCButtonSize.medium,
    this.icon,
    this.iconPosition = EDCButtonIconPosition.left,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.customBackgroundColor,
  });

  /// 创建主要操作按钮
  factory EDCButton.primary({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
    EDCButtonSize size = EDCButtonSize.medium,
  }) {
    return EDCButton(
      text: text,
      onPressed: onPressed,
      type: EDCButtonType.primary,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// 创建次要操作按钮
  factory EDCButton.secondary({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
    EDCButtonSize size = EDCButtonSize.medium,
  }) {
    return EDCButton(
      text: text,
      onPressed: onPressed,
      type: EDCButtonType.secondary,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// 创建文本按钮
  factory EDCButton.text({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    EDCButtonSize size = EDCButtonSize.medium,
  }) {
    return EDCButton(
      text: text,
      onPressed: onPressed,
      type: EDCButtonType.text,
      size: size,
      icon: icon,
      isLoading: isLoading,
    );
  }

  /// 创建警告按钮
  factory EDCButton.warning({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
    EDCButtonSize size = EDCButtonSize.medium,
  }) {
    return EDCButton(
      text: text,
      onPressed: onPressed,
      type: EDCButtonType.warning,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// 创建危险操作按钮
  factory EDCButton.danger({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
    EDCButtonSize size = EDCButtonSize.medium,
  }) {
    return EDCButton(
      text: text,
      onPressed: onPressed,
      type: EDCButtonType.danger,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// 按钮文字
  final String text;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 按钮类型
  final EDCButtonType type;

  /// 按钮尺寸
  final EDCButtonSize size;

  /// 自定义图标
  final IconData? icon;

  /// 图标位置
  final EDCButtonIconPosition iconPosition;

  /// 是否正在加载
  final bool isLoading;

  /// 是否全宽
  final bool isFullWidth;

  /// 自定义颜色
  final Color? customColor;

  /// 自定义背景颜色
  final Color? customBackgroundColor;

  @override
  Widget build(BuildContext context) {
    final EDCButtonSizeConfig sizeConfig = _getSizeConfig();
    final EDCButtonColorConfig colorConfig = _getColorConfig();

    final Widget buttonChild = _buildButtonContent(sizeConfig);

    Widget button;

    switch (type) {
      case EDCButtonType.primary:
      case EDCButtonType.warning:
      case EDCButtonType.danger:
        button = ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: colorConfig.backgroundColor,
            foregroundColor: colorConfig.foregroundColor,
            disabledBackgroundColor: BPColors.neutral.withValues(alpha: 0.3),
            disabledForegroundColor: Colors.white.withValues(alpha: 0.6),
            padding: sizeConfig.padding,
            minimumSize: sizeConfig.minimumSize,
            maximumSize: isFullWidth ? Size.infinite : sizeConfig.maximumSize,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
            ),
            elevation: 0,
            textStyle: sizeConfig.textStyle,
          ),
          child: buttonChild,
        );
        break;

      case EDCButtonType.secondary:
        button = OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: colorConfig.foregroundColor,
            disabledForegroundColor: BPColors.neutral.withValues(alpha: 0.5),
            side: BorderSide(
              color: colorConfig.borderColor,
              width: 2,
            ),
            padding: sizeConfig.padding,
            minimumSize: sizeConfig.minimumSize,
            maximumSize: isFullWidth ? Size.infinite : sizeConfig.maximumSize,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
            ),
            textStyle: sizeConfig.textStyle,
          ),
          child: buttonChild,
        );
        break;

      case EDCButtonType.text:
        button = TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: colorConfig.foregroundColor,
            disabledForegroundColor: BPColors.neutral.withValues(alpha: 0.5),
            padding: sizeConfig.padding,
            minimumSize: sizeConfig.minimumSize,
            textStyle: sizeConfig.textStyle,
          ),
          child: buttonChild,
        );
        break;
    }

    if (isFullWidth && type != EDCButtonType.text) {
      return SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    return button;
  }

  Widget _buildButtonContent(EDCButtonSizeConfig sizeConfig) {
    if (isLoading) {
      return SizedBox(
        width: sizeConfig.loadingSize,
        height: sizeConfig.loadingSize,
        child: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation<Color>(
            type == EDCButtonType.secondary || type == EDCButtonType.text
                ? BPColors.primary
                : Colors.white,
          ),
        ),
      );
    }

    if (icon == null) {
      return Text(
        text,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      );
    }

    List<Widget> children;

    if (iconPosition == EDCButtonIconPosition.right) {
      children = <Widget>[
        Flexible(
          child: Text(
            text,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        SizedBox(width: sizeConfig.iconSpacing),
        Icon(icon, size: sizeConfig.iconSize),
      ];
    } else {
      children = <Widget>[
        Icon(icon, size: sizeConfig.iconSize),
        SizedBox(width: sizeConfig.iconSpacing),
        Flexible(
          child: Text(
            text,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ];
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  EDCButtonSizeConfig _getSizeConfig() {
    switch (size) {
      case EDCButtonSize.small:
        return const EDCButtonSizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          minimumSize: Size(80, 40),
          maximumSize: Size(200, 40),
          textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          iconSize: 20,
          iconSpacing: 8,
          loadingSize: 16,
        );

      case EDCButtonSize.medium:
        return const EDCButtonSizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          minimumSize: Size(120, 48),
          maximumSize: Size(300, 48),
          textStyle: EDCTextStyles.buttonText,
          iconSize: 24,
          iconSpacing: 12,
          loadingSize: 20,
        );

      case EDCButtonSize.large:
        return const EDCButtonSizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 40, vertical: 20),
          minimumSize: Size(160, 56),
          maximumSize: Size(400, 56),
          textStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          iconSize: 28,
          iconSpacing: 16,
          loadingSize: 24,
        );
    }
  }

  EDCButtonColorConfig _getColorConfig() {
    switch (type) {
      case EDCButtonType.primary:
        return EDCButtonColorConfig(
          backgroundColor: customBackgroundColor ?? BPColors.primary,
          foregroundColor: customColor ?? Colors.white,
          borderColor: customBackgroundColor ?? BPColors.primary,
        );

      case EDCButtonType.secondary:
        return EDCButtonColorConfig(
          backgroundColor: Colors.transparent,
          foregroundColor: customColor ?? BPColors.primary,
          borderColor: customColor ?? BPColors.primary,
        );

      case EDCButtonType.text:
        return EDCButtonColorConfig(
          backgroundColor: Colors.transparent,
          foregroundColor: customColor ?? BPColors.primary,
          borderColor: Colors.transparent,
        );

      case EDCButtonType.warning:
        return EDCButtonColorConfig(
          backgroundColor: customBackgroundColor ?? BPColors.warning,
          foregroundColor: customColor ?? const Color(0xFF333333),
          borderColor: customBackgroundColor ?? BPColors.warning,
        );

      case EDCButtonType.danger:
        return EDCButtonColorConfig(
          backgroundColor: customBackgroundColor ?? BPColors.error,
          foregroundColor: customColor ?? Colors.white,
          borderColor: customBackgroundColor ?? BPColors.error,
        );
    }
  }
}

/// 按钮类型枚举
enum EDCButtonType {
  primary, // 主要操作
  secondary, // 次要操作
  text, // 文本按钮
  warning, // 警告操作
  danger, // 危险操作
}

/// 按钮尺寸枚举
enum EDCButtonSize {
  small, // 小型按钮
  medium, // 中型按钮
  large, // 大型按钮
}

/// 图标位置枚举
enum EDCButtonIconPosition {
  left, // 图标在左侧
  right, // 图标在右侧
}

/// 按钮尺寸配置
class EDCButtonSizeConfig {
  const EDCButtonSizeConfig({
    required this.padding,
    required this.minimumSize,
    required this.maximumSize,
    required this.textStyle,
    required this.iconSize,
    required this.iconSpacing,
    required this.loadingSize,
  });
  final EdgeInsets padding;
  final Size minimumSize;
  final Size maximumSize;
  final TextStyle textStyle;
  final double iconSize;
  final double iconSpacing;
  final double loadingSize;
}

/// 按钮颜色配置
class EDCButtonColorConfig {
  const EDCButtonColorConfig({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.borderColor,
  });
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;
}
