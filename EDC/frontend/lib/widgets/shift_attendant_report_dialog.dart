import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';
import '../models/shift_attendant_model.dart';
import '../models/auth_models.dart' as auth_models;
import '../services/native/sunmi_printer_service.dart';
import '../utils/format_utils.dart';
import '../widgets/payment_method_icon.dart';
import '../services/auth_providers.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 班次员工报告弹窗组件
class ShiftAttendantReportDialog extends ConsumerStatefulWidget {
  const ShiftAttendantReportDialog({
    super.key,
    required this.shiftAttendantData,
    required this.shiftNumber,
  });

  final ShiftAttendantResponse shiftAttendantData;
  final String shiftNumber;

  @override
  ConsumerState<ShiftAttendantReportDialog> createState() => _ShiftAttendantReportDialogState();
}

class _ShiftAttendantReportDialogState extends ConsumerState<ShiftAttendantReportDialog> {
  bool _isPrinting = false;
  String? _printingAttendantId; // 跟踪正在打印的员工ID
  final SunmiPrinterService _printerService = SunmiPrinterService.instance;

  /// 检查当前用户是否为Employee角色
  bool _isEmployeeRole() {
    // 获取当前用户信息
    final auth_models.AuthUser? currentUser = ref.read(currentUserProvider);
    final auth_models.SystemAccess? systemAccess = ref.read(systemAccessProvider);

    // 添加调试信息
    debugPrint('🔍 ShiftAttendantReportDialog: 角色检查');
    debugPrint('  - 当前用户: ${currentUser?.username}');
    debugPrint('  - 用户角色: ${currentUser?.roles}');
    debugPrint('  - 系统访问级别: ${systemAccess?.accessLevel}');

    // 如果没有用户信息，默认为非员工角色
    if (currentUser == null || systemAccess == null) {
      debugPrint('  - 无用户信息，默认为非Employee角色');
      return false;
    }

    // 检查系统访问级别：admin、manager、supervisor 都不是普通员工
    final String accessLevel = systemAccess.accessLevel.toLowerCase();
    if (accessLevel == 'admin' || accessLevel == 'manager' || accessLevel == 'supervisor') {
      debugPrint('  - 管理级别用户 ($accessLevel)，非Employee角色');
      return false;
    }

    // 检查用户角色：如果包含管理相关角色，则不是普通员工
    final List<String> roles = currentUser.roles.map((String role) => role.toLowerCase()).toList();
    final bool hasManagementRole = roles.any((String role) =>
      role.contains('admin') ||
      role.contains('manager') ||
      role.contains('supervisor')
    );

    if (hasManagementRole) {
      debugPrint('  - 包含管理角色 ($roles)，非Employee角色');
      return false;
    }

    // 其他情况认为是普通员工
    final bool isEmployee = accessLevel == 'operator' || accessLevel == 'employee' || roles.contains('employee');
    debugPrint('  - 最终判断为Employee角色: $isEmployee');
    return isEmployee;
  }

  /// 获取当前登录用户的用户名
  String? _getCurrentUsername() {
    final String? username = ref.read(currentUsernameProvider);
    debugPrint('🔍 ShiftAttendantReportDialog: 获取当前用户名: $username');
    return username;
  }

  /// 检查指定员工是否为当前登录用户
  bool _isCurrentUserAttendant(Attendant attendant) {
    final String? currentUsername = _getCurrentUsername();
    if (currentUsername == null) return false;
    
    return attendant.attendantInfo.attendantName == currentUsername ||
           attendant.attendantInfo.staffCardId.toString() == currentUsername;
  }

  /// 根据用户角色过滤员工数据
  List<Attendant> _getFilteredAttendants() {
    final List<Attendant> allAttendants = widget.shiftAttendantData.data.attendants;

    // 添加调试信息
    debugPrint('🔍 ShiftAttendantReportDialog: 开始过滤员工数据');
    debugPrint('  - 总员工数: ${allAttendants.length}');
    debugPrint('  - 是否Employee角色: ${_isEmployeeRole()}');
    debugPrint('  - 当前用户名: ${_getCurrentUsername()}');

    // 打印所有员工信息用于调试
    for (int i = 0; i < allAttendants.length; i++) {
      final Attendant attendant = allAttendants[i];
      debugPrint('  - 员工${i + 1}: ${attendant.attendantInfo.attendantName} (ID: ${attendant.attendantInfo.staffCardId})');
    }

    if (!_isEmployeeRole()) {
      // 非Employee角色，显示所有员工数据
      debugPrint('  - 非Employee角色，返回所有员工数据');
      return allAttendants;
    }

    // Employee角色，只显示自己的数据
    final String? currentUsername = _getCurrentUsername();
    if (currentUsername == null) {
      debugPrint('  - Employee角色但无当前用户名，返回空列表');
      return [];
    }

    final List<Attendant> filteredAttendants = allAttendants.where((attendant) {
      // 检查员工姓名是否匹配当前登录用户名
      final bool nameMatch = attendant.attendantInfo.attendantName == currentUsername;
      final bool idMatch = attendant.attendantInfo.staffCardId.toString() == currentUsername;
      final bool isMatch = nameMatch || idMatch;

      debugPrint('  - 检查员工 ${attendant.attendantInfo.attendantName}:');
      debugPrint('    姓名匹配: $nameMatch (${attendant.attendantInfo.attendantName} == $currentUsername)');
      debugPrint('    ID匹配: $idMatch (${attendant.attendantInfo.staffCardId} == $currentUsername)');
      debugPrint('    最终匹配: $isMatch');

      return isMatch;
    }).toList();

    debugPrint('  - Employee角色过滤后员工数: ${filteredAttendants.length}');
    return filteredAttendants;
  }

  /// 计算过滤后的班次汇总数据
  ShiftSummary _getFilteredShiftSummary() {
    final List<Attendant> filteredAttendants = _getFilteredAttendants();
    
    if (!_isEmployeeRole()) {
      // 非Employee角色，返回原始汇总数据
      return widget.shiftAttendantData.data.shiftSummary;
    }
    
    // Employee角色，计算个人汇总数据
    if (filteredAttendants.isEmpty) {
      return ShiftSummary(
        totalAttendants: 0,
        totalTransactions: 0,
        totalSalesAmount: 0.0,
        totalSalesVolume: 0.0,
        totalCash: 0.0,
        totalNonCash: 0.0,
        totalPvc: 0.0,
        totalCimb: 0.0,
        totalBca: 0.0,
        totalMandiri: 0.0,
        totalBri: 0.0,
        totalBni: 0.0,
        totalVoucher: 0.0,
        totalB2b: 0.0,
        totalTera: 0.0,
        grandTotal: 0.0,
      );
    }
    
    final Attendant attendant = filteredAttendants.first;
    
    // 计算各种支付方式的金额
    double totalPvc = 0.0;
    double totalCimb = 0.0;
    double totalBca = 0.0;
    double totalMandiri = 0.0;
    double totalBri = 0.0;
    double totalBni = 0.0;
    double totalVoucher = 0.0;
    double totalB2b = 0.0;
    double totalTera = 0.0;
    double totalCash = 0.0;
    double totalNonCash = 0.0;
    
    for (final PaymentMethodData payment in attendant.paymentSummary.byMethod) {
      final String method = payment.paymentMethod.toLowerCase();
      final double amount = payment.totalAmount;
      
      if (method.contains('cash')) {
        totalCash += amount;
      } else {
        totalNonCash += amount;
      }
      
      if (method.contains('pvc')) {
        totalPvc += amount;
      } else if (method.contains('cimb')) {
        totalCimb += amount;
      } else if (method.contains('bca')) {
        totalBca += amount;
      } else if (method.contains('mandiri')) {
        totalMandiri += amount;
      } else if (method.contains('bri')) {
        totalBri += amount;
      } else if (method.contains('bni')) {
        totalBni += amount;
      } else if (method.contains('voucher')) {
        totalVoucher += amount;
      } else if (method.contains('b2b')) {
        totalB2b += amount;
      } else if (method.contains('tera')) {
        totalTera += amount;
      }
    }
    
    return ShiftSummary(
      totalAttendants: 1,
      totalTransactions: attendant.transactionCount,
      totalSalesAmount: attendant.salesAmountIdr,
      totalSalesVolume: attendant.salesVolumeLtr,
      totalCash: totalCash,
      totalNonCash: totalNonCash,
      totalPvc: totalPvc,
      totalCimb: totalCimb,
      totalBca: totalBca,
      totalMandiri: totalMandiri,
      totalBri: totalBri,
      totalBni: totalBni,
      totalVoucher: totalVoucher,
      totalB2b: totalB2b,
      totalTera: totalTera,
      grandTotal: attendant.grandTotal,
    );
  }

  @override
  Widget build(BuildContext context) {
    // 添加调试信息
    debugPrint('🔍 ShiftAttendantReportDialog: 构建UI');
    debugPrint('  - 班次号: ${widget.shiftNumber}');
    debugPrint('  - 原始员工数据: ${widget.shiftAttendantData.data.attendants.length}');
    debugPrint('  - 班次汇总销售额: ${widget.shiftAttendantData.data.shiftSummary.totalSalesAmount}');

    return Dialog.fullscreen(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Row(
            children: <Widget>[
              Image.asset(
                'assets/images/bp_logo.png',
                height: 32,
                fit: BoxFit.contain,
                errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'BP',
                      style: TextStyle(
                        color: BPColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      _isEmployeeRole() ? 'My Shift Report' : 'Previous Shift Report',
                      style: EDCTextStyles.appBarTitle.copyWith(fontSize: 16),
                    ),
                    Text(
                      widget.shiftNumber,
                      style: EDCTextStyles.hintText.copyWith(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: BPColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          toolbarHeight: 64,
        ),
        body: Column(
          children: <Widget>[
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    _buildShiftInfoSection(),
                    const SizedBox(height: 16),
                    _buildShiftSummarySection(),
                    const SizedBox(height: 16),
                    _buildAttendantsSection(),
                    const SizedBox(height: 80), // 为底部按钮留空间
                  ],
                ),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  /// 构建班次基本信息区域
  Widget _buildShiftInfoSection() {
    final ShiftAttendantData data = widget.shiftAttendantData.data;
    final ShiftInfo shiftInfo = data.shiftInfo;

    return _buildSection(
      title: 'Shift Information',
      icon: Icons.info_outline,
      child: Column(
        children: <Widget>[
          _buildInfoRow('Shift Number', widget.shiftNumber),
          _buildInfoRow('Station', shiftInfo.stationName),
          _buildInfoRow('Status', shiftInfo.status.toUpperCase()),
          _buildInfoRow('Start Time', _formatDateTime(shiftInfo.startTime)),
          _buildInfoRow('End Time', shiftInfo.endTime != null ? _formatDateTime(shiftInfo.endTime!) : 'N/A'),
          _buildInfoRow('Total Attendants', '${_getFilteredShiftSummary().totalAttendants}'),
        ],
      ),
    );
  }

  /// 构建班次汇总区域
  Widget _buildShiftSummarySection() {
    final ShiftSummary shiftSummary = _getFilteredShiftSummary();

    return _buildSection(
      title: _isEmployeeRole() ? 'My Summary' : 'Shift Summary',
      icon: Icons.analytics_outlined,
      child: Column(
        children: <Widget>[
          _buildInfoRow('Total Sales', 'Rp ${FormatUtils.formatRupiah(shiftSummary.totalSalesAmount)}'),
          _buildInfoRow('Total Transactions', FormatUtils.formatInteger(shiftSummary.totalTransactions)),
          _buildInfoRow('Total Volume', '${FormatUtils.formatLiters(shiftSummary.totalSalesVolume)} L'),
          _buildInfoRow('Cash Total', 'Rp ${FormatUtils.formatRupiah(shiftSummary.totalCash)}'),
          _buildInfoRow('Non-Cash Total', 'Rp ${FormatUtils.formatRupiah(shiftSummary.totalNonCash)}'),
          _buildInfoRow('Grand Total', 'Rp ${FormatUtils.formatRupiah(shiftSummary.grandTotal)}'),
        ],
      ),
    );
  }

  /// 构建员工详细信息区域
  Widget _buildAttendantsSection() {
    final List<Attendant> attendants = _getFilteredAttendants();

    if (attendants.isEmpty) {
      return _buildSection(
        title: _isEmployeeRole() ? 'My Details' : 'Attendants',
        icon: Icons.people_outline,
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: <Widget>[
              Icon(Icons.people_outline, color: Colors.grey[400], size: 48),
              const SizedBox(height: 12),
              Text(
                _isEmployeeRole() ? 'No data available for your account' : 'No attendant data available',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return _buildSection(
      title: _isEmployeeRole() ? 'My Details' : 'Attendants (${attendants.length})',
      icon: Icons.people_outline,
      child: Column(
        children: attendants.map((Attendant attendant) {
          return _buildAttendantCard(attendant);
        }).toList(),
      ),
    );
  }

  /// 构建单个员工卡片
  Widget _buildAttendantCard(Attendant attendant) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 员工基本信息
          Row(
            children: <Widget>[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: BPColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.person,
                  color: BPColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      attendant.attendantInfo.attendantName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: BPColors.primary,
                      ),
                    ),
                    Text(
                      'Staff Card: ${attendant.attendantInfo.staffCardId}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              // 添加打印按钮 - 只在非Employee角色或者是自己的数据时显示
              if (!_isEmployeeRole() || _isCurrentUserAttendant(attendant))
                Container(
                  margin: const EdgeInsets.only(left: 8),
                  child: _buildAttendantPrintButton(attendant),
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 销售统计
          _buildAttendantInfoRow('Transactions', '${attendant.transactionCount}'),
          _buildAttendantInfoRow('Sales Volume', '${FormatUtils.formatLiters(attendant.salesVolumeLtr)} L'),
          _buildAttendantInfoRow('Sales Amount', 'Rp ${FormatUtils.formatRupiah(attendant.salesAmountIdr)}'),
          _buildAttendantInfoRow('Grand Total', 'Rp ${FormatUtils.formatRupiah(attendant.grandTotal)}'),
          
          const SizedBox(height: 12),
          
          // 油品销售明细
          if (attendant.fuelSales.byGrade.isNotEmpty) ...[
            Text(
              'Fuel Sales:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            ...attendant.fuelSales.byGrade.map((FuelGradeData fuel) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    // 左对齐：油品名称
                    Flexible(
                      flex: 2,
                      child: Text(
                        fuel.fuelGrade,
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    // 右对齐：数量和金额
                    Flexible(
                      flex: 2,
                      child: Text(
                        '${FormatUtils.formatLiters(fuel.salesVolume)} L',
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                        textAlign: TextAlign.right,
                      ),
                    ),
                    // 右对齐：金额
                    Flexible(
                      flex: 2,
                      child: Text(
                        'Rp ${FormatUtils.formatRupiah(fuel.netAmount)}',
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            const SizedBox(height: 12),
          ],
          
          // 支付方式明细
          if (attendant.paymentSummary.byMethod.isNotEmpty) ...[
            Text(
              'Payment Methods:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            ...attendant.paymentSummary.byMethod.map((PaymentMethodData payment) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    // 左对齐：支付方式（带图标）
                    Flexible(
                      flex: 2,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          PaymentMethodIcon(
                            paymentMethod: payment.paymentMethod,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Flexible(
                            child: Text(
                              payment.paymentMethod,
                              style: const TextStyle(fontSize: 12),
                              textAlign: TextAlign.left,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 右对齐：金额
                    Flexible(
                      flex: 1,
                      child: Text(
                        'Rp ${FormatUtils.formatRupiah(payment.totalAmount)}',
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ],
      ),
    );
  }

  /// 构建员工信息行 - 小票样式（左对齐标签，右对齐值）
  Widget _buildAttendantInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // 左对齐标签
          Flexible(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.left,
            ),
          ),
          // 右对齐值
          Flexible(
            flex: 1,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close, size: 20),
              label: const Text('Close'),
              style: OutlinedButton.styleFrom(
                foregroundColor: BPColors.primary,
                side: const BorderSide(color: BPColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isPrinting ? null : _printReport,
              icon: _isPrinting 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.print, size: 20),
              label: Text(_isPrinting ? 'Printing...' : (_isEmployeeRole() ? 'Print My Report' : 'Print Report')),
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建区域组件
  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Icon(icon, color: BPColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: BPColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  /// 构建信息行 - 小票样式（左对齐标签，右对齐值）
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // 左对齐标签
          Flexible(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.left,
            ),
          ),
          // 右对齐值
          Flexible(
            flex: 1,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }

  /// 打印报告
  Future<void> _printReport() async {
    setState(() {
      _isPrinting = true;
    });

    try {
      // 检查打印机连接
      final bool isConnected = await _printerService.isPrinterConnected();
      if (!isConnected) {
        throw Exception('Printer not connected');
      }

      // 开始打印
      await _printerService.enterPrinterBuffer(clearBuffer: true);
      await _printShiftAttendantReport();
      await _printerService.exitPrinterBuffer(commit: true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEmployeeRole() ? 'My shift report printed successfully!' : 'Shift report printed successfully!'),
            backgroundColor: BPColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Print failed: $e'),
            backgroundColor: BPColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPrinting = false;
        });
      }
    }
  }

  /// 打印班次员工报告
  Future<void> _printShiftAttendantReport() async {
    final ShiftAttendantData data = widget.shiftAttendantData.data;
    final ShiftInfo shiftInfo = data.shiftInfo;
    final ShiftSummary shiftSummary = _getFilteredShiftSummary();
    final List<Attendant> attendants = _getFilteredAttendants();

    // 58mm纸张配置：使用小字体和紧凑排版
    const String separator = '--------------------------------'; // 32字符分隔线
    const String doubleSeparator = '================================'; // 32字符双分隔线
    const double smallFontSize = 24.0; // 小字体
    const double mediumFontSize = 28.0; // 中等字体

    // === HEADER SECTION ===
    await _printerService.setAlignment(PrintAlignment.center);
    await _printerService.setFontSize(mediumFontSize);
    await _printerService.printText('BP-AKR FUELS RETAIL\n');
    await _printerService.printText('${shiftInfo.stationName}\n');
    await _printerService.setFontSize(smallFontSize);
    await _printerService.printText('$doubleSeparator\n');

    // === SHIFT INFO SECTION ===
    await _printerService.setAlignment(PrintAlignment.left);
    await _printerService.setFontSize(smallFontSize);

    await _printerService.printText(_isEmployeeRole() ? 'MY SHIFT REPORT\n' : 'SHIFT ATTENDANT REPORT\n');
    await _printerService.printText('$separator\n');

    // 班次基本信息（紧凑格式）
    await _printerService.printText('Shift: ${widget.shiftNumber}\n');
    await _printerService.printText('Station: ${shiftInfo.stationName}\n');

    // 时间信息（单行显示）
    final DateFormat dateFormat = DateFormat('dd/MM/yy HH:mm', 'id_ID');
    final String startTime = dateFormat.format(shiftInfo.startTime);
    final String endTime = shiftInfo.endTime != null
        ? dateFormat.format(shiftInfo.endTime!)
        : 'Not ended';
    await _printerService.printText('Time: $startTime - $endTime\n');
    await _printerService.printText('Attendants: ${shiftSummary.totalAttendants}\n');

    await _printerService.printText('$separator\n');

    // === SHIFT SUMMARY SECTION ===
    await _printerService.setAlignment(PrintAlignment.center);
    await _printerService.printText('SHIFT SUMMARY\n');
    await _printerService.printText('$doubleSeparator\n');
    await _printerService.setAlignment(PrintAlignment.left);

    // 班次汇总信息
    await _printerService.printText('Sales: Rp ${FormatUtils.formatRupiah(shiftSummary.totalSalesAmount)}\n');
    await _printerService.printText('Transactions: ${shiftSummary.totalTransactions}\n');
    await _printerService.printText('Volume: ${FormatUtils.formatLiters(shiftSummary.totalSalesVolume)} L\n');
    await _printerService.printText('Cash: Rp ${FormatUtils.formatRupiah(shiftSummary.totalCash)}\n');
    await _printerService.printText('Non-Cash: Rp ${FormatUtils.formatRupiah(shiftSummary.totalNonCash)}\n');

    await _printerService.printText('$separator\n');
    await _printerService.printText('GRAND TOTAL: Rp ${FormatUtils.formatRupiah(shiftSummary.grandTotal)}\n');

    await _printerService.printText('$doubleSeparator\n');

    // === ATTENDANTS SECTION ===
    await _printerService.setAlignment(PrintAlignment.center);
    await _printerService.printText(_isEmployeeRole() ? 'MY DETAILS\n' : 'ATTENDANT DETAILS\n');
    await _printerService.printText('$doubleSeparator\n');
    await _printerService.setAlignment(PrintAlignment.left);

    for (final Attendant attendant in attendants) {
      final String attendantName = attendant.attendantInfo.attendantName.length > 15
          ? attendant.attendantInfo.attendantName.substring(0, 15)
          : attendant.attendantInfo.attendantName;
      await _printerService.printText('$attendantName\n');
      await _printerService.printText('Trans: ${attendant.transactionCount}\n');
      await _printerService.printText('Volume: ${FormatUtils.formatLiters(attendant.salesVolumeLtr)} L\n');
      await _printerService.printText('Amount: Rp ${FormatUtils.formatRupiah(attendant.salesAmountIdr)}\n');
      await _printerService.printText('Total: Rp ${FormatUtils.formatRupiah(attendant.grandTotal)}\n');
      await _printerService.printText('\n');
    }

    await _printerService.printText('$doubleSeparator\n');

    // === FOOTER SECTION ===
    await _printerService.setAlignment(PrintAlignment.center);
    await _printerService.printText('*** SHIFT END ***\n');
    await _printerService.printText('\n');

    final String printTime = DateFormat('dd/MM/yy HH:mm', 'id_ID').format(DateTime.now());
    await _printerService.printText('Print Time: $printTime\n');
    await _printerService.printText('\n');
    await _printerService.printText('Thank you!\n');
    await _printerService.lineWrap(3);
  }

  /// 构建员工打印按钮
  Widget _buildAttendantPrintButton(Attendant attendant) {
    final bool isCurrentlyPrinting = _printingAttendantId == attendant.attendantInfo.staffCardId.toString();
    
    return ElevatedButton.icon(
      onPressed: isCurrentlyPrinting ? null : () => _printAttendantReport(attendant),
      icon: isCurrentlyPrinting
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Icon(
              Icons.print,
              size: 16,
              color: Colors.white,
            ),
      label: Text(
        isCurrentlyPrinting ? 'Printing...' : 'Print',
        style: const TextStyle(
          fontSize: 12,
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isCurrentlyPrinting ? Colors.grey : BPColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        minimumSize: const Size(80, 32),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 1,
      ),
    );
  }

  /// 打印单个员工报告
  Future<void> _printAttendantReport(Attendant attendant) async {
    setState(() {
      _printingAttendantId = attendant.attendantInfo.staffCardId.toString();
    });

    try {
      // 检查打印机连接
      final bool isConnected = await _printerService.isPrinterConnected();
      if (!isConnected) {
        throw Exception('Printer not connected');
      }

      // 开始打印
      await _printerService.enterPrinterBuffer(clearBuffer: true);
      await _printSingleAttendantReport(attendant);
      await _printerService.exitPrinterBuffer(commit: true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${attendant.attendantInfo.attendantName} report printed successfully!'),
            backgroundColor: BPColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Print failed: $e'),
            backgroundColor: BPColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _printingAttendantId = null;
        });
      }
    }
  }

  /// 打印单个员工班结小票
  Future<void> _printSingleAttendantReport(Attendant attendant) async {
    final ShiftAttendantData data = widget.shiftAttendantData.data;
    final ShiftInfo shiftInfo = data.shiftInfo;
    
    // 58mm纸张配置：使用小字体和紧凑排版
    const String separator = '--------------------------------'; // 32字符分隔线
    const String doubleSeparator = '================================'; // 32字符双分隔线
    const double smallFontSize = 24.0; // 小字体
    const double mediumFontSize = 28.0; // 中等字体

    // === HEADER SECTION ===
    await _printerService.setAlignment(PrintAlignment.center);
    await _printerService.setFontSize(mediumFontSize);
    await _printerService.printText('BP-AKR FUELS RETAIL\n');
    await _printerService.printText('${shiftInfo.stationName}\n');
    await _printerService.setFontSize(smallFontSize);
    await _printerService.printText('$doubleSeparator\n');

    // === SHIFT & ATTENDANT INFO ===
    await _printerService.setAlignment(PrintAlignment.left);
    await _printerService.setFontSize(smallFontSize);
    
    await _printerService.printText(_isEmployeeRole() ? 'MY SHIFT REPORT\n' : 'ATTENDANT SHIFT REPORT\n');
    await _printerService.printText('$separator\n');
    
    // 班次信息
    await _printerService.printText('Shift: ${widget.shiftNumber}\n');
    await _printerService.printText('Station: ${shiftInfo.stationName}\n');
    
    // 时间信息
    final DateFormat dateFormat = DateFormat('dd/MM/yy HH:mm', 'id_ID');
    final String startTime = dateFormat.format(shiftInfo.startTime);
    final String endTime = shiftInfo.endTime != null
        ? dateFormat.format(shiftInfo.endTime!)
        : 'Not ended';
    await _printerService.printText('Time: $startTime - $endTime\n');
    
    await _printerService.printText('$separator\n');
    
    // === ATTENDANT DETAILS ===
    await _printerService.setAlignment(PrintAlignment.center);
    await _printerService.printText(_isEmployeeRole() ? 'MY DETAILS\n' : 'ATTENDANT DETAILS\n');
    await _printerService.printText('$doubleSeparator\n');
    await _printerService.setAlignment(PrintAlignment.left);
    
    // 员工基本信息
    final String attendantName = attendant.attendantInfo.attendantName.length > 20
        ? attendant.attendantInfo.attendantName.substring(0, 20)
        : attendant.attendantInfo.attendantName;
    await _printerService.printText('Name: $attendantName\n');
    await _printerService.printText('Staff ID: ${attendant.attendantInfo.staffCardId}\n');
    await _printerService.printText('$separator\n');
    
    // === SALES SUMMARY ===
    await _printerService.printText('SALES SUMMARY\n');
    await _printerService.printText('$separator\n');
    
    await _printerService.printText('Transactions: ${attendant.transactionCount}\n');
    await _printerService.printText('Volume: ${FormatUtils.formatLiters(attendant.salesVolumeLtr)} L\n');
    await _printerService.printText('Sales Amount: Rp ${FormatUtils.formatRupiah(attendant.salesAmountIdr)}\n');
    await _printerService.printText('Dry Income: Rp ${FormatUtils.formatRupiah(attendant.dryIncome)}\n');
    await _printerService.printText('$separator\n');
    await _printerService.printText('GRAND TOTAL: Rp ${FormatUtils.formatRupiah(attendant.grandTotal)}\n');
    await _printerService.printText('$separator\n');
    
    // === FUEL SALES BY GRADE ===
    if (attendant.fuelSales.byGrade.isNotEmpty) {
      await _printerService.printText('FUEL SALES BY GRADE\n');
      await _printerService.printText('$separator\n');
      
      for (final FuelGradeData fuelGrade in attendant.fuelSales.byGrade) {
        final String gradeName = fuelGrade.fuelGrade.length > 15
            ? fuelGrade.fuelGrade.substring(0, 15)
            : fuelGrade.fuelGrade;
        await _printerService.printText('$gradeName\n');
        await _printerService.printText('  Trans: ${fuelGrade.transactionCount}\n');
        await _printerService.printText('  Volume: ${FormatUtils.formatLiters(fuelGrade.salesVolume)} L\n');
        await _printerService.printText('  Amount: Rp ${FormatUtils.formatRupiah(fuelGrade.netAmount)}\n');
        await _printerService.printText('  Price: Rp ${FormatUtils.formatRupiah(fuelGrade.unitPrice)}\n');
        await _printerService.printText('\n');
      }
      await _printerService.printText('$separator\n');
    }
    
    // === PAYMENT METHODS ===
    if (attendant.paymentSummary.byMethod.isNotEmpty) {
      await _printerService.printText('PAYMENT METHODS\n');
      await _printerService.printText('$separator\n');
      
      for (final PaymentMethodData payment in attendant.paymentSummary.byMethod) {
        final String methodName = payment.paymentMethodName.length > 12
            ? payment.paymentMethodName.substring(0, 12)
            : payment.paymentMethodName.padRight(12);
        await _printerService.printText('$methodName: Rp ${FormatUtils.formatRupiah(payment.totalAmount)}\n');
        await _printerService.printText('  Trans: ${payment.transactionCount} (${payment.percentage.toStringAsFixed(1)}%)\n');
      }
      await _printerService.printText('$separator\n');
    }
    
    // === FOOTER ===
    await _printerService.setAlignment(PrintAlignment.center);
    await _printerService.printText('*** END OF REPORT ***\n');
    await _printerService.printText('\n');
    
    final String printTime = DateFormat('dd/MM/yy HH:mm', 'id_ID').format(DateTime.now());
    await _printerService.printText('Print Time: $printTime\n');
    await _printerService.printText('\n');
    await _printerService.printText('Thank you!\n');
    await _printerService.lineWrap(2);
  }
} 