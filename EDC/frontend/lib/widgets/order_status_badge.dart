import 'package:flutter/material.dart';
import '../models/order.dart';

/// Order status badge widget
/// Displays a color-coded badge with status text
class OrderStatusBadge extends StatelessWidget {
  const OrderStatusBadge({
    super.key,
    required this.status,
    this.compact = false,
  });
  final OrderStatus status;
  final bool compact;

  /// Static method to get the color for an order status
  static int getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.created:
        return 0xFF3498DB; // Blue
      case OrderStatus.processing:
        return 0xFFF39C12; // Orange
      case OrderStatus.completed:
        return 0xFF2ECC71; // Green
      case OrderStatus.cancelled:
        return 0xFFE74C3C; // Red
      default:
        return 0xFF95A5A6; // Grey
    }
  }

  /// Static method to get the status text
  static String getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.created:
        return 'Created'; // 已创建
      case OrderStatus.processing:
        return 'Processing'; // 处理中
      case OrderStatus.completed:
        return 'Completed'; // 已完成
      case OrderStatus.cancelled:
        return 'Cancelled'; // 已取消
      default:
        return 'Unknown'; // 未知
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color color = Color(getStatusColor(status));
    final String text = getStatusText(status);

    // Compact badges are smaller with no padding
    if (compact) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          border: Border.all(color: color.withOpacity(0.5), width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: color,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // Standard badge size
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color.withOpacity(0.5), width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
