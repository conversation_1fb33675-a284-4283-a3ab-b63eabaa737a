import 'package:flutter/material.dart';
import '../models/member_model.dart';
import '../constants/bp_colors.dart';

/// 会员状态徽章组件
/// 遵循BP品牌设计规范和EDC移动设备优化标准
class MemberStatusBadge extends StatelessWidget {
  const MemberStatusBadge({
    super.key,
    required this.status,
    this.size = MemberStatusBadgeSize.medium,
    this.showIcon = true,
    this.showBorder = true,
    this.borderRadius,
  });

  /// 创建小型状态徽章
  factory MemberStatusBadge.small({
    required MemberStatus status,
    bool showIcon = false,
    bool showBorder = true,
  }) {
    return MemberStatusBadge(
      status: status,
      size: MemberStatusBadgeSize.small,
      showIcon: showIcon,
      showBorder: showBorder,
    );
  }

  /// 创建大型状态徽章
  factory MemberStatusBadge.large({
    required MemberStatus status,
    bool showIcon = true,
    bool showBorder = true,
  }) {
    return MemberStatusBadge(
      status: status,
      size: MemberStatusBadgeSize.large,
      showIcon: showIcon,
      showBorder: showBorder,
    );
  }

  /// 会员状态
  final MemberStatus status;

  /// 徽章尺寸类型
  final MemberStatusBadgeSize size;

  /// 是否显示图标
  final bool showIcon;

  /// 是否显示边框
  final bool showBorder;

  /// 自定义圆角半径
  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    final MemberStatusBadgeSizeConfig config = _getSizeConfig();
    final MemberStatusBadgeColorConfig colorConfig = _getColorConfig();

    final List<Widget> children = <Widget>[];

    // 添加图标
    if (showIcon) {
      children.add(
        Icon(
          _getStatusIcon(),
          size: config.iconSize,
          color: colorConfig.iconColor,
        ),
      );
    }

    // 添加间距
    if (showIcon && _shouldShowText()) {
      children.add(SizedBox(width: config.spacing));
    }

    // 添加文本
    if (_shouldShowText()) {
      children.add(
        Text(
          _getStatusText(),
          style: config.textStyle.copyWith(
            color: colorConfig.textColor,
          ),
        ),
      );
    }

    return Container(
      padding: config.padding,
      decoration: BoxDecoration(
        color: colorConfig.backgroundColor,
        borderRadius: BorderRadius.circular(
          borderRadius ?? config.borderRadius,
        ),
        border: showBorder
            ? Border.all(
                color: colorConfig.borderColor,
                width: 1,
              )
            : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: children,
      ),
    );
  }

  bool _shouldShowText() {
    return size != MemberStatusBadgeSize.iconOnly;
  }

  /// 获取尺寸配置
  MemberStatusBadgeSizeConfig _getSizeConfig() {
    switch (size) {
      case MemberStatusBadgeSize.small:
        return const MemberStatusBadgeSizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          iconSize: 14,
          spacing: 4,
          textStyle: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          borderRadius: 8,
        );

      case MemberStatusBadgeSize.medium:
        return const MemberStatusBadgeSizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          iconSize: 16,
          spacing: 6,
          textStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          borderRadius: 10,
        );

      case MemberStatusBadgeSize.large:
        return const MemberStatusBadgeSizeConfig(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          iconSize: 20,
          spacing: 8,
          textStyle: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          borderRadius: 12,
        );

      case MemberStatusBadgeSize.iconOnly:
        return const MemberStatusBadgeSizeConfig(
          padding: EdgeInsets.all(6),
          iconSize: 16,
          spacing: 0,
          textStyle: TextStyle(),
          borderRadius: 8,
        );
    }
  }

  /// 获取颜色配置
  MemberStatusBadgeColorConfig _getColorConfig() {
    final Color statusColor = _getStatusColor();

    return MemberStatusBadgeColorConfig(
      backgroundColor: statusColor.withOpacity(0.1),
      borderColor: statusColor.withOpacity(0.3),
      iconColor: statusColor,
      textColor: statusColor,
    );
  }

  /// 获取状态颜色（使用BP色彩体系）
  Color _getStatusColor() {
    switch (status) {
      case MemberStatus.active:
      case MemberStatus.normal:
        return BPColors.success;
      case MemberStatus.inactive:
        return BPColors.neutral;
      case MemberStatus.suspended:
      case MemberStatus.frozen:
        return BPColors.error;
      case MemberStatus.expired:
        return BPColors.warning;
      case MemberStatus.temporary:
        return BPColors.accent;
    }
  }

  /// 获取状态图标
  IconData _getStatusIcon() {
    switch (status) {
      case MemberStatus.active:
      case MemberStatus.normal:
        return Icons.check_circle;
      case MemberStatus.inactive:
        return Icons.hide_source;
      case MemberStatus.suspended:
      case MemberStatus.frozen:
        return Icons.block;
      case MemberStatus.expired:
        return Icons.schedule;
      case MemberStatus.temporary:
        return Icons.hourglass_bottom;
    }
  }

  /// 获取状态文本
  String _getStatusText() {
    switch (status) {
      case MemberStatus.active:
      case MemberStatus.normal:
        return 'Active';
      case MemberStatus.inactive:
        return 'Inactive';
      case MemberStatus.suspended:
      case MemberStatus.frozen:
        return 'Suspended';
      case MemberStatus.expired:
        return 'Expired';
      case MemberStatus.temporary:
        return 'Temporary';
    }
  }
}

/// 会员状态徽章尺寸枚举
enum MemberStatusBadgeSize {
  small, // 小型徽章
  medium, // 中型徽章
  large, // 大型徽章
  iconOnly, // 仅图标
}

/// 会员状态徽章尺寸配置
class MemberStatusBadgeSizeConfig {
  const MemberStatusBadgeSizeConfig({
    required this.padding,
    required this.iconSize,
    required this.spacing,
    required this.textStyle,
    required this.borderRadius,
  });
  final EdgeInsets padding;
  final double iconSize;
  final double spacing;
  final TextStyle textStyle;
  final double borderRadius;
}

/// 会员状态徽章颜色配置
class MemberStatusBadgeColorConfig {
  const MemberStatusBadgeColorConfig({
    required this.backgroundColor,
    required this.borderColor,
    required this.iconColor,
    required this.textColor,
  });
  final Color backgroundColor;
  final Color borderColor;
  final Color iconColor;
  final Color textColor;
}
