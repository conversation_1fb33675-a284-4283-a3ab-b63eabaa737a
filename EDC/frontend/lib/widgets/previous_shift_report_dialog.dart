import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';
import '../models/shift_report.dart';
import '../services/native/sunmi_printer_service.dart';
import '../utils/format_utils.dart';

/// 前一个班次报表弹窗组件
class PreviousShiftReportDialog extends StatefulWidget {
  const PreviousShiftReportDialog({
    super.key,
    required this.shiftReport,
    required this.shiftNumber,
  });

  final ShiftReportResponse shiftReport;
  final String shiftNumber;

  @override
  State<PreviousShiftReportDialog> createState() => _PreviousShiftReportDialogState();
}

class _PreviousShiftReportDialogState extends State<PreviousShiftReportDialog> {
  bool _isPrinting = false;
  final SunmiPrinterService _printerService = SunmiPrinterService.instance;

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Row(
            children: <Widget>[
              Image.asset(
                'assets/images/bp_logo.png',
                height: 32,
                fit: BoxFit.contain,
                errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'BP',
                      style: TextStyle(
                        color: BPColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'Previous Shift Report',
                      style: EDCTextStyles.appBarTitle.copyWith(fontSize: 16),
                    ),
                    Text(
                      widget.shiftNumber,
                      style: EDCTextStyles.hintText.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: BPColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          toolbarHeight: 64,
        ),
        body: Column(
          children: <Widget>[
            Expanded(
                             child: SingleChildScrollView(
                 padding: const EdgeInsets.all(12.0),
                                 child: Column(
                   crossAxisAlignment: CrossAxisAlignment.start,
                   children: <Widget>[
                     _buildShiftInfoSection(),
                     const SizedBox(height: 16),
                     _buildSalesOverviewSection(),
                     const SizedBox(height: 16),
                     _buildPaymentMethodsSection(),
                     const SizedBox(height: 16),
                     _buildFuelSalesSection(),
                     const SizedBox(height: 16),
                     _buildMerchandiseSection(),
                     const SizedBox(height: 80), // 为底部按钮留空间
                   ],
                 ),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  /// 构建班次基本信息区域
  Widget _buildShiftInfoSection() {
    final ShiftReportData data = widget.shiftReport.data;
    final ShiftInfo shiftInfo = data.shiftInfo;

    return _buildSection(
      title: 'Shift Information',
      icon: Icons.info_outline,
      child: Column(
                 children: <Widget>[
           _buildInfoRow('Shift Number', widget.shiftNumber),
           _buildInfoRow('Start Time', _formatDateTime(shiftInfo.startTime)),
           _buildInfoRow('End Time', shiftInfo.endTime != null ? _formatDateTime(shiftInfo.endTime!) : 'N/A'),
           _buildInfoRow('Duration', _calculateDuration(shiftInfo.startTime, shiftInfo.endTime)),
           _buildInfoRow('Site Name', shiftInfo.stationName),
         ],
      ),
    );
  }

  /// 构建销售概览区域
  Widget _buildSalesOverviewSection() {
    final ShiftReportData data = widget.shiftReport.data;
    final PaymentSummary paymentSummary = data.paymentSummary;

    return _buildSection(
      title: 'Sales Overview',
      icon: Icons.analytics_outlined,
      child: Column(
        children: <Widget>[
                     _buildInfoRow('Total Sales', 'Rp ${FormatUtils.formatRupiah(paymentSummary.totalSales)}'),
           _buildInfoRow('Total Transactions', FormatUtils.formatInteger(paymentSummary.totalTransactions)),
           _buildInfoRow('Fuel Sales', 'Rp ${FormatUtils.formatRupiah(data.fuelSummary.totalNetSales)}'),
           _buildInfoRow('Merchandise Sales', 'Rp ${FormatUtils.formatRupiah(data.merchandiseSummary.totalNetSales)}'),
        ],
      ),
    );
  }

  /// 构建支付方式统计区域
  Widget _buildPaymentMethodsSection() {
    final ShiftReportData data = widget.shiftReport.data;
    final List<PaymentMethodSummary>? paymentMethods = data.paymentSummary.paymentMethods;

    if (paymentMethods == null || paymentMethods.isEmpty) {
      return _buildSection(
        title: 'Payment Methods',
        icon: Icons.payment,
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: <Widget>[
              Icon(Icons.payment_outlined, color: Colors.grey[400], size: 48),
              const SizedBox(height: 12),
              Text(
                'No payment method data',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return _buildSection(
      title: 'Payment Methods',
      icon: Icons.payment,
      child: Column(
        children: paymentMethods.map((PaymentMethodSummary method) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildInfoRow(
              method.methodName,
              'Rp ${FormatUtils.formatRupiah(method.amount)} (${FormatUtils.formatInteger(method.transactionCount)} transactions)',
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建燃油销售区域
  Widget _buildFuelSalesSection() {
    final ShiftReportData data = widget.shiftReport.data;
    final List<FuelGrade>? fuelGrades = data.fuelSummary.fuelGrades;

    if (fuelGrades == null || fuelGrades.isEmpty) {
      return _buildSection(
        title: 'Fuel Sales',
        icon: Icons.local_gas_station,
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: <Widget>[
              Icon(Icons.local_gas_station_outlined, color: Colors.grey[400], size: 48),
              const SizedBox(height: 12),
              Text(
                'No fuel sales data',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return _buildSection(
      title: 'Fuel Sales',
      icon: Icons.local_gas_station,
      child: Column(
        children: fuelGrades.map((FuelGrade fuel) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    fuel.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: BPColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                                      Row(
                      children: <Widget>[
                        Expanded(
                          child: _buildStatItem('Volume', '${FormatUtils.formatLiters(fuel.volume)} L'),
                        ),
                        Expanded(
                          child: _buildStatItem('Amount', 'Rp ${FormatUtils.formatRupiah(fuel.netAmount)}'),
                        ),
                        Expanded(
                          child: _buildStatItem('Transactions', FormatUtils.formatInteger(fuel.transactionCount)),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建商品销售区域
  Widget _buildMerchandiseSection() {
    final ShiftReportData data = widget.shiftReport.data;
    final List<TopProduct>? topProducts = data.merchandiseSummary.topProducts;

    if (topProducts == null || topProducts.isEmpty) {
      return _buildSection(
        title: 'Merchandise Sales',
        icon: Icons.shopping_cart,
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: <Widget>[
              Icon(Icons.shopping_cart_outlined, color: Colors.grey[400], size: 48),
              const SizedBox(height: 12),
              Text(
                'No merchandise sales',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return _buildSection(
      title: 'Merchandise Sales',
      icon: Icons.shopping_cart,
      child: Column(
        children: topProducts.map((TopProduct item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    item.productName,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: BPColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                                      Row(
                      children: <Widget>[
                        Expanded(
                          child: _buildStatItem('Quantity', FormatUtils.formatInteger(item.quantity)),
                        ),
                        Expanded(
                          child: _buildStatItem('Amount', 'Rp ${FormatUtils.formatRupiah(item.netAmount)}'),
                        ),
                        Expanded(
                          child: _buildStatItem('Transactions', FormatUtils.formatInteger(item.transactionCount)),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建区域容器
  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: BPColors.primary.withOpacity(0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: <Widget>[
                Icon(icon, color: BPColors.primary, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: BPColors.primary,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: child,
          ),
        ],
      ),
    );
  }

  /// 构建信息行（小票样式，左右对齐）
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.right,
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  /// 构建底部操作区域
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                foregroundColor: BPColors.primary,
                side: const BorderSide(color: BPColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Close',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _isPrinting ? null : _printReport,
              icon: _isPrinting
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.print, size: 20),
              label: Text(
                _isPrinting ? 'Printing...' : 'Print Report',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: BPColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 打印报表
  Future<void> _printReport() async {
    setState(() {
      _isPrinting = true;
    });

    try {
      await _printShiftReportCompact(widget.shiftReport);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Report printed successfully'),
            backgroundColor: BPColors.success,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Print failed: ${error.toString()}'),
            backgroundColor: BPColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPrinting = false;
        });
      }
    }
  }

  /// 打印班次报表（简化版）
  Future<void> _printShiftReportCompact(ShiftReportResponse reportResponse) async {
    final ShiftReportData data = reportResponse.data;
    final ShiftInfo shiftInfo = data.shiftInfo;
    final PaymentSummary paymentSummary = data.paymentSummary;

    await _printerService.enterPrinterBuffer(clearBuffer: true);

    try {
      // 58mm纸张配置：使用小字体和紧凑排版
      const String separator = '--------------------------------'; // 32字符分隔线
      const String doubleSeparator = '================================'; // 32字符双分隔线
      const double smallFontSize = 24.0; // 小字体
      const double mediumFontSize = 28.0; // 中等字体

      // === HEADER SECTION ===
      await _printerService.setAlignment(PrintAlignment.center);
      await _printerService.setFontSize(mediumFontSize);
      await _printerService.printText('BP-AKR FUELS RETAIL\n');
      await _printerService.printText('${shiftInfo.stationName}\n');
      await _printerService.setFontSize(smallFontSize);
      await _printerService.printText('$doubleSeparator\n');

      // === SHIFT INFO SECTION ===
      await _printerService.setAlignment(PrintAlignment.left);
      await _printerService.setFontSize(smallFontSize);

      await _printerService.printText('SHIFT REPORT\n');
      await _printerService.printText('$separator\n');

      // 班次基本信息（紧凑格式）
      await _printerService.printText('Shift: ${widget.shiftNumber}\n');
      await _printerService.printText('Station: ${shiftInfo.stationName}\n');
      await _printerService.printText('Employee: ${shiftInfo.staffName ?? "Unknown"}\n');

      // 时间信息（单行显示）
      final DateFormat dateFormat = DateFormat('dd/MM/yy HH:mm', 'id_ID');
      final String startTime = dateFormat.format(shiftInfo.startTime);
      final String endTime = shiftInfo.endTime != null
          ? dateFormat.format(shiftInfo.endTime!)
          : 'Not ended';
      await _printerService.printText('Time: $startTime - $endTime\n');
      await _printerService.printText('Duration: ${_calculateDuration(shiftInfo.startTime, shiftInfo.endTime)}\n');

      await _printerService.printText('$separator\n');

      // === SALES SUMMARY SECTION ===
      await _printerService.setAlignment(PrintAlignment.center);
      await _printerService.printText('SALES SUMMARY\n');
      await _printerService.printText('$doubleSeparator\n');
      await _printerService.setAlignment(PrintAlignment.left);

      // 销售汇总信息
      await _printerService.printText('Total Sales: Rp ${FormatUtils.formatRupiah(paymentSummary.totalSales)}\n');
      await _printerService.printText('Transactions: ${FormatUtils.formatInteger(paymentSummary.totalTransactions)}\n');
      await _printerService.printText('Fuel Sales: Rp ${FormatUtils.formatRupiah(data.fuelSummary.totalNetSales)}\n');
      await _printerService.printText('Merchandise: Rp ${FormatUtils.formatRupiah(data.merchandiseSummary.totalNetSales)}\n');

      await _printerService.printText('$separator\n');

      // === PAYMENT METHODS SECTION ===
      if (data.paymentSummary.paymentMethods != null && data.paymentSummary.paymentMethods!.isNotEmpty) {
        await _printerService.setAlignment(PrintAlignment.center);
        await _printerService.printText('PAYMENT METHODS\n');
        await _printerService.printText('$doubleSeparator\n');
        await _printerService.setAlignment(PrintAlignment.left);
        
        for (final PaymentMethodSummary method in data.paymentSummary.paymentMethods!) {
          final String methodName = method.methodName.length > 12
              ? method.methodName.substring(0, 12)
              : method.methodName.padRight(12);
          await _printerService.printText('$methodName: Rp ${FormatUtils.formatRupiah(method.amount)}\n');
          await _printerService.printText('  Trans: ${FormatUtils.formatInteger(method.transactionCount)}\n');
        }
        await _printerService.printText('$separator\n');
      }

      // === FUEL SALES SECTION ===
      if (data.fuelSummary.fuelGrades != null && data.fuelSummary.fuelGrades!.isNotEmpty) {
        await _printerService.setAlignment(PrintAlignment.center);
        await _printerService.printText('FUEL SALES BY GRADE\n');
        await _printerService.printText('$doubleSeparator\n');
        await _printerService.setAlignment(PrintAlignment.left);
        
        for (final FuelGrade fuel in data.fuelSummary.fuelGrades!) {
          final String fuelName = fuel.name.length > 15
              ? fuel.name.substring(0, 15)
              : fuel.name;
          await _printerService.printText('$fuelName\n');
          await _printerService.printText('  Volume: ${FormatUtils.formatLiters(fuel.volume)} L\n');
          await _printerService.printText('  Amount: Rp ${FormatUtils.formatRupiah(fuel.netAmount)}\n');
          await _printerService.printText('  Trans: ${FormatUtils.formatInteger(fuel.transactionCount)}\n');
          await _printerService.printText('\n');
        }
        await _printerService.printText('$separator\n');
      }

      // === MERCHANDISE SALES SECTION ===
      if (data.merchandiseSummary.topProducts != null && data.merchandiseSummary.topProducts!.isNotEmpty) {
        await _printerService.setAlignment(PrintAlignment.center);
        await _printerService.printText('MERCHANDISE SALES\n');
        await _printerService.printText('$doubleSeparator\n');
        await _printerService.setAlignment(PrintAlignment.left);
        
        for (final TopProduct item in data.merchandiseSummary.topProducts!) {
          final String productName = item.productName.length > 15
              ? item.productName.substring(0, 15)
              : item.productName;
          await _printerService.printText('$productName\n');
          await _printerService.printText('  Qty: ${FormatUtils.formatInteger(item.quantity)}\n');
          await _printerService.printText('  Amount: Rp ${FormatUtils.formatRupiah(item.netAmount)}\n');
          await _printerService.printText('  Trans: ${FormatUtils.formatInteger(item.transactionCount)}\n');
          await _printerService.printText('\n');
        }
        await _printerService.printText('$separator\n');
      }

      // === FOOTER SECTION ===
      await _printerService.setAlignment(PrintAlignment.center);
      await _printerService.printText('*** SHIFT END ***\n');
      await _printerService.printText('\n');

      final String printTime = DateFormat('dd/MM/yy HH:mm', 'id_ID').format(DateTime.now());
      await _printerService.printText('Print Time: $printTime\n');
      await _printerService.printText('\n');
      await _printerService.printText('Thank you!\n');
      await _printerService.lineWrap(2);

      // Commit print
      await _printerService.exitPrinterBuffer(commit: true);
    } catch (e) {
      // Rollback on error
      await _printerService.exitPrinterBuffer(commit: false);
      rethrow;
    }
  }



  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    return formatter.format(dateTime);
  }

  /// 计算班次持续时间
  String _calculateDuration(DateTime startTime, DateTime? endTime) {
    if (endTime == null) {
      return 'N/A';
    }
    final Duration duration = endTime.difference(startTime);
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  /// 格式化金额 - 已弃用，使用 FormatUtils.formatRupiah 替代
  String _formatAmount(double amount) {
    return FormatUtils.formatRupiah(amount);
  }
} 