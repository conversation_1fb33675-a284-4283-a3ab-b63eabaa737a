import 'package:flutter/material.dart';
import '../constants/bp_colors.dart';

/// 支付方式图标组件
class PaymentMethodIcon extends StatelessWidget {
  const PaymentMethodIcon({
    super.key,
    required this.paymentMethod,
    this.size = 24,
    this.color,
  });

  final String paymentMethod;
  final double size;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(size / 8),
      ),
      child: Icon(
        _getIcon(),
        size: size * 0.6,
        color: color ?? _getIconColor(),
      ),
    );
  }

  /// 获取支付方式图标
  IconData _getIcon() {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return Icons.attach_money;
      case 'mandiri':
        return Icons.credit_card;
      case 'bca':
        return Icons.credit_card;
      case 'bri':
        return Icons.credit_card;
      case 'bni':
        return Icons.credit_card;
      case 'cimb':
        return Icons.credit_card;
      case 'pvc':
        return Icons.card_membership;
      case 'voucher':
        return Icons.local_offer;
      case 'b2b':
        return Icons.business;
      case 'tera':
        return Icons.account_balance;
      default:
        return Icons.payment;
    }
  }

  /// 获取背景颜色
  Color _getBackgroundColor() {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return Colors.green.withOpacity(0.1);
      case 'mandiri':
        return Colors.yellow.withOpacity(0.1);
      case 'bca':
        return Colors.blue.withOpacity(0.1);
      case 'bri':
        return Colors.indigo.withOpacity(0.1);
      case 'bni':
        return Colors.orange.withOpacity(0.1);
      case 'cimb':
        return Colors.red.withOpacity(0.1);
      case 'pvc':
        return Colors.purple.withOpacity(0.1);
      case 'voucher':
        return Colors.pink.withOpacity(0.1);
      case 'b2b':
        return Colors.brown.withOpacity(0.1);
      case 'tera':
        return BPColors.primary.withOpacity(0.1);
      default:
        return Colors.grey.withOpacity(0.1);
    }
  }

  /// 获取图标颜色
  Color _getIconColor() {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return Colors.green;
      case 'mandiri':
        return Colors.yellow.shade700;
      case 'bca':
        return Colors.blue;
      case 'bri':
        return Colors.indigo;
      case 'bni':
        return Colors.orange;
      case 'cimb':
        return Colors.red;
      case 'pvc':
        return Colors.purple;
      case 'voucher':
        return Colors.pink;
      case 'b2b':
        return Colors.brown;
      case 'tera':
        return BPColors.primary;
      default:
        return Colors.grey;
    }
  }
} 