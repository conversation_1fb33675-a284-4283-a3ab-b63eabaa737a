import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/api_constants.dart';
import '../main.dart';

/// BOS and FCC Server Address Configuration Dialog
class AddressConfigurationDialog extends ConsumerStatefulWidget {
  const AddressConfigurationDialog({super.key});

  @override
  ConsumerState<AddressConfigurationDialog> createState() =>
      _AddressConfigurationDialogState();
}

class _AddressConfigurationDialogState
    extends ConsumerState<AddressConfigurationDialog> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _bosController = TextEditingController();
  final TextEditingController _fccController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentAddresses();
  }

  @override
  void dispose() {
    _bosController.dispose();
    _fccController.dispose();
    super.dispose();
  }

  /// Load current address configuration
  Future<void> _loadCurrentAddresses() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      
      // Use custom configuration first
      final customBosUrl = await storageService.getCustomBosUrl();
      final customFccUrl = await storageService.getCustomFccUrl();
      
      if (customBosUrl != null && customFccUrl != null) {
        // Use custom configuration
        setState(() {
          _bosController.text = customBosUrl;
          _fccController.text = customFccUrl;
        });
      } else {
        // Use default environment configuration
        final currentEnv = await storageService.getApiEnvironment();
        final bosUrl = ApiConstants.getServiceUrl(BackendService.base, currentEnv);
        final fccUrl = ApiConstants.getServiceUrl(BackendService.fcc, currentEnv);
        
        setState(() {
          _bosController.text = bosUrl;
          _fccController.text = fccUrl;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load address configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Save address configuration
  Future<void> _saveConfiguration() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final bosUrl = _bosController.text.trim();
      final fccUrl = _fccController.text.trim();
      final storageService = ref.read(storageServiceProvider);

      // Save to local storage
      await storageService.setCustomBosUrl(bosUrl);
      await storageService.setCustomFccUrl(fccUrl);

      // Also set to ApiConstants memory cache
      ApiConstants.setCustomServiceUrl(BackendService.base, bosUrl);
      ApiConstants.setCustomServiceUrl(BackendService.fcc, fccUrl);

      // 重新配置所有API服务以使用新的URL
      try {
        final currentEnv = await storageService.getApiEnvironment();
        await ApiConstants.updateAllApiConfigurations(currentEnv);
        debugPrint('所有API服务重新配置成功');
      } catch (e) {
        debugPrint('API服务重新配置失败: $e');
        // 即使重新配置失败，也不阻止保存操作的完成
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true indicating configuration updated
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configuration saved and applied successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Reset to default configuration
  Future<void> _resetToDefault() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      final currentEnv = await storageService.getApiEnvironment();
      
      // Get default environment configuration
      final bosUrl = ApiConstants.getServiceUrl(BackendService.base, currentEnv);
      final fccUrl = ApiConstants.getServiceUrl(BackendService.fcc, currentEnv);
      
      setState(() {
        _bosController.text = bosUrl;
        _fccController.text = fccUrl;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Reset to default configuration'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 1),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Reset failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Clear custom configuration
  Future<void> _clearCustomConfiguration() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final storageService = ref.read(storageServiceProvider);
      
      // Clear custom configuration from local storage
      await storageService.clearCustomUrls();
      
      // Clear custom configuration from memory
      ApiConstants.clearAllCustomServiceUrls();
      
      // Reload default configuration
      await _loadCurrentAddresses();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Custom configuration cleared'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to clear configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Validate URL format
  String? _validateUrl(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter server address';
    }

    final url = value.trim();
    
    // Check protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return 'Address must start with http:// or https://';
    }
    
    // Simple but accurate URL validation
    try {
      final uri = Uri.parse(url);
      
      // Check if host is empty
      if (uri.host.isEmpty) {
        return 'Please enter valid host address';
      }
      
      // Check if it's IP address format
      final ipPattern = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$');
      if (ipPattern.hasMatch(uri.host)) {
        // Validate each IP segment is in 0-255 range
        final parts = uri.host.split('.');
        for (final part in parts) {
          final num = int.tryParse(part);
          if (num == null || num < 0 || num > 255) {
            return 'Invalid IP address';
          }
        }
      }
      
      // Validate port number
      if (uri.hasPort && (uri.port < 1 || uri.port > 65535)) {
        return 'Port must be between 1-65535';
      }
      
      return null;
    } catch (e) {
      return 'Please enter valid URL (e.g. http://*************:8080)';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Color(0xFF00A650), size: 20),
            SizedBox(width: 8),
            Text(
              'Server Configuration',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Color(0xFF666666)),
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Description
                const Text(
                  'Configure BOS and FCC server addresses:',
                  style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF666666),
                  ),
                ),
                const SizedBox(height: 24),
                
                // BOS Server Input
                const Text(
                  'BOS Server',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                const SizedBox(height: 6),
                TextFormField(
                  controller: _bosController,
                  style: const TextStyle(fontSize: 14),
                  decoration: InputDecoration(
                    hintText: 'http://*************:8080',
                    hintStyle: const TextStyle(fontSize: 14, color: Color(0xFF999999)),
                    prefixIcon: const Icon(Icons.cloud, color: Color(0xFF00A650), size: 18),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFF00A650), width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                    isDense: true,
                  ),
                  validator: _validateUrl,
                  enabled: !_isLoading,
                ),
                
                const SizedBox(height: 20),
                
                // FCC Server Input
                const Text(
                  'FCC Server',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                const SizedBox(height: 6),
                TextFormField(
                  controller: _fccController,
                  style: const TextStyle(fontSize: 14),
                  decoration: InputDecoration(
                    hintText: 'http://192.168.1.101:8081',
                    hintStyle: const TextStyle(fontSize: 14, color: Color(0xFF999999)),
                    prefixIcon: const Icon(Icons.local_gas_station, color: Color(0xFF00A650), size: 18),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFF00A650), width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                    isDense: true,
                  ),
                  validator: _validateUrl,
                  enabled: !_isLoading,
                ),
                
                const SizedBox(height: 16),
                
                // Info Banner
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Configuration takes effect immediately',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.blue[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Reset Button
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: _isLoading ? null : _resetToDefault,
                    icon: Icon(
                      Icons.refresh,
                      size: 16,
                      color: Colors.orange[600],
                    ),
                    label: Text(
                      'Reset to Default',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.orange[600],
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      side: BorderSide(color: Colors.orange[300]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                ),
                
                const Spacer(),
                
                // Bottom Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _isLoading ? null : _clearCustomConfiguration,
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          side: BorderSide(color: Colors.orange[300]!),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        child: Text(
                          'Clear Config',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.orange[600],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveConfiguration,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF00A650),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                          elevation: 0,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text(
                                'Save Configuration',
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 