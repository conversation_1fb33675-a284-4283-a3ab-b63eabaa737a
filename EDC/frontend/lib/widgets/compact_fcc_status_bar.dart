import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';
import '../controllers/fcc_status_controller.dart';

/// 紧凑型FCC状态栏
/// 显示连接状态、设备数量和基本统计信息
class CompactFccStatusBar extends ConsumerWidget {
  const CompactFccStatusBar({
    super.key,
    this.showPollingControls = false,
    this.onTap,
  });
  final bool showPollingControls;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final FccStatusState fccState = ref.watch(fccStatusControllerProvider);
    final Map<String, int> deviceStats = fccState.deviceStats;
    final Map<String, int> nozzleStats = fccState.nozzleStats;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: _getBackgroundColor(fccState),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _getStatusColor(fccState).withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: <Widget>[
            // 状态指示器
            _buildStatusIndicator(fccState),
            const SizedBox(width: 8),

            // 主要信息
            Expanded(
              child: _buildMainInfo(fccState, deviceStats, nozzleStats),
            ),

            // 轮询控制
            if (showPollingControls) ...<Widget>[
              const SizedBox(width: 8),
              _buildPollingControls(context, ref, fccState),
            ],

            // 展开指示器
            if (onTap != null) ...<Widget>[
              const SizedBox(width: 8),
              const Icon(
                Icons.keyboard_arrow_down,
                size: 16,
                color: BPColors.neutral,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(FccStatusState fccState) {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: _getStatusColor(fccState),
        shape: BoxShape.circle,
        boxShadow: fccState.isConnected
            ? <BoxShadow>[
                BoxShadow(
                  color: _getStatusColor(fccState).withOpacity(0.3),
                  blurRadius: 3,
                  spreadRadius: 1,
                ),
              ]
            : null,
      ),
    );
  }

  Widget _buildMainInfo(FccStatusState fccState, Map<String, int> deviceStats,
      Map<String, int> nozzleStats) {
    // 检查是否有缓存数据
    final bool hasValidData =
        deviceStats.isNotEmpty || fccState.dispensers.isNotEmpty;

    if (!fccState.isConnected) {
      return Row(
        children: <Widget>[
          // 如果有缓存数据，显示设备信息；否则显示离线状态
          Text(
            hasValidData
                ? 'FCC: ${deviceStats['online_devices'] ?? 0}/${deviceStats['total_devices'] ?? 0}'
                : 'FCC: Offline',
            style: EDCTextStyles.hintText.copyWith(
              fontSize: 12,
              color: hasValidData ? BPColors.warning : BPColors.error,
              fontWeight: FontWeight.w500,
            ),
          ),

          // 显示缓存标签
          if (hasValidData) ...<Widget>[
            const SizedBox(width: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
              decoration: BoxDecoration(
                color: BPColors.warning.withOpacity(0.15),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'CACHED',
                style: EDCTextStyles.hintText.copyWith(
                  fontSize: 8,
                  color: BPColors.warning,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.3,
                ),
              ),
            ),
          ],

          // 轮询指示器
          if (fccState.isPolling) ...<Widget>[
            const SizedBox(width: 6),
            SizedBox(
              width: 10,
              height: 10,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                valueColor: AlwaysStoppedAnimation<Color>(
                  hasValidData ? BPColors.warning : BPColors.error,
                ),
              ),
            ),
          ],
        ],
      );
    }

    return Row(
      children: <Widget>[
        // 设备状态
        Text(
          'FCC: ${deviceStats['online_devices'] ?? 0}/${deviceStats['total_devices'] ?? 0}',
          style: EDCTextStyles.hintText.copyWith(
            fontSize: 12,
            color: BPColors.success,
            fontWeight: FontWeight.w500,
          ),
        ),

        const SizedBox(width: 12),

        // 喷嘴状态摘要
        if (nozzleStats.isNotEmpty) _buildNozzleSummary(nozzleStats),

        // 轮询指示器
        if (fccState.isPolling) ...<Widget>[
          const SizedBox(width: 6),
          const SizedBox(
            width: 10,
            height: 10,
            child: CircularProgressIndicator(
              strokeWidth: 1.5,
              valueColor: AlwaysStoppedAnimation<Color>(BPColors.success),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNozzleSummary(Map<String, int> nozzleStats) {
    final int activeNozzles = (nozzleStats['auth_nozzles'] ?? 0) +
        (nozzleStats['fuelling_nozzles'] ?? 0) +
        (nozzleStats['complete_nozzles'] ?? 0);

    final int totalNozzles = nozzleStats['total_nozzles'] ?? 0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: BPColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        'N: $activeNozzles/$totalNozzles',
        style: EDCTextStyles.hintText.copyWith(
          fontSize: 10,
          color: BPColors.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildPollingControls(
      BuildContext context, WidgetRef ref, FccStatusState fccState) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        // 播放/暂停按钮
        InkWell(
          onTap: () {
            if (fccState.isPolling) {
              ref.read(fccStatusControllerProvider.notifier).stopPolling();
            } else {
              ref.read(fccStatusControllerProvider.notifier).startPolling();
            }
          },
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: BPColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              fccState.isPolling ? Icons.pause : Icons.play_arrow,
              size: 12,
              color: BPColors.primary,
            ),
          ),
        ),

        const SizedBox(width: 4),

        // 刷新按钮
        InkWell(
          onTap: () {
            ref.read(fccStatusControllerProvider.notifier).refresh();
          },
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: BPColors.accent.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(
              Icons.refresh,
              size: 12,
              color: BPColors.accent,
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(FccStatusState fccState) {
    if (fccState.hasError) return BPColors.error;
    if (!fccState.isConnected) {
      // 检查是否有缓存数据
      final bool hasValidData =
          fccState.deviceStats.isNotEmpty || fccState.dispensers.isNotEmpty;
      return hasValidData ? BPColors.warning : BPColors.error;
    }
    return BPColors.success;
  }

  Color _getBackgroundColor(FccStatusState fccState) {
    return _getStatusColor(fccState).withOpacity(0.05);
  }
}

/// FCC状态详情对话框
class FccStatusDetailDialog extends ConsumerWidget {
  const FccStatusDetailDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final FccStatusState fccState = ref.watch(fccStatusControllerProvider);

    return AlertDialog(
      title: Row(
        children: <Widget>[
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: _getStatusColor(fccState),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          const Text('FCC Service Status'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              // 连接信息
              _buildInfoSection('Connection', <String>[
                'Status: ${fccState.isConnected ? "Connected" : "Disconnected"}',
                'Polling: ${fccState.isPolling ? "Active" : "Stopped"}',
                'Interval: ${fccState.pollingInterval.inSeconds}s',
                if (fccState.lastUpdateTime != null)
                  'Last Update: ${_formatTime(fccState.lastUpdateTime!)}',
              ]),

              const SizedBox(height: 16),

              // 设备统计
              if (fccState.deviceStats.isNotEmpty)
                _buildInfoSection('Devices', <String>[
                  'Total: ${fccState.deviceStats['total_devices']}',
                  'Online: ${fccState.deviceStats['online_devices']}',
                  'Offline: ${fccState.deviceStats['offline_devices']}',
                ]),

              const SizedBox(height: 16),

              // 喷嘴统计
              if (fccState.nozzleStats.isNotEmpty)
                _buildInfoSection('Nozzles', <String>[
                  'Total: ${fccState.nozzleStats['total_nozzles']}',
                  'Idle: ${fccState.nozzleStats['idle_nozzles']}',
                  'Auth: ${fccState.nozzleStats['auth_nozzles']}',
                  'Fuelling: ${fccState.nozzleStats['fuelling_nozzles']}',
                  'Complete: ${fccState.nozzleStats['complete_nozzles']}',
                  'Offline: ${fccState.nozzleStats['offline_nozzles']}',
                ]),

              // 错误信息
              if (fccState.hasError) ...<Widget>[
                const SizedBox(height: 16),
                _buildInfoSection(
                    'Error',
                    <String>[
                      fccState.errorMessage!,
                    ],
                    isError: true),
              ],
            ],
          ),
        ),
      ),
      actions: <Widget>[
        // 手动刷新
        TextButton.icon(
          onPressed: () {
            ref.read(fccStatusControllerProvider.notifier).refresh();
          },
          icon: const Icon(Icons.refresh),
          label: const Text('Refresh'),
        ),

        // 开始/停止轮询
        TextButton.icon(
          onPressed: () {
            if (fccState.isPolling) {
              ref.read(fccStatusControllerProvider.notifier).stopPolling();
            } else {
              ref.read(fccStatusControllerProvider.notifier).startPolling();
            }
          },
          icon: Icon(fccState.isPolling ? Icons.pause : Icons.play_arrow),
          label: Text(fccState.isPolling ? 'Stop' : 'Start'),
        ),

        // 关闭
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildInfoSection(String title, List<String> items,
      {bool isError = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          title,
          style: EDCTextStyles.subTitle.copyWith(
            fontSize: 14,
            color: isError ? BPColors.error : BPColors.primary,
          ),
        ),
        const SizedBox(height: 8),
        ...items.map((String item) => Padding(
              padding: const EdgeInsets.only(left: 8, bottom: 4),
              child: Text(
                '• $item',
                style: EDCTextStyles.bodyText.copyWith(
                  fontSize: 12,
                  color: isError ? BPColors.error : null,
                ),
              ),
            )),
      ],
    );
  }

  String _formatTime(DateTime time) {
    final DateTime now = DateTime.now();
    final Duration diff = now.difference(time);

    if (diff.inSeconds < 60) {
      return '${diff.inSeconds}s ago';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}m ago';
    } else {
      return '${diff.inHours}h ago';
    }
  }

  Color _getStatusColor(FccStatusState fccState) {
    if (fccState.hasError) return BPColors.error;
    if (!fccState.isConnected) {
      // 检查是否有缓存数据
      final bool hasValidData =
          fccState.deviceStats.isNotEmpty || fccState.dispensers.isNotEmpty;
      return hasValidData ? BPColors.warning : BPColors.error;
    }
    return BPColors.success;
  }
}
