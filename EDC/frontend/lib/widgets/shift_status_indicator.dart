import 'package:flutter/material.dart';
import 'dart:async';

import '../constants/bp_colors.dart';
import '../services/shift_service.dart';

/// 响应式班次状态指示器
/// 监听ShiftService的状态流，实时更新班次状态显示
class ShiftStatusIndicator extends StatefulWidget {
  const ShiftStatusIndicator({
    super.key,
    this.onTap,
    this.compact = false,
  });

  /// 点击回调
  final VoidCallback? onTap;
  
  /// 是否使用紧凑模式
  final bool compact;

  @override
  State<ShiftStatusIndicator> createState() => _ShiftStatusIndicatorState();
}

class _ShiftStatusIndicatorState extends State<ShiftStatusIndicator>
    with TickerProviderStateMixin {
  final ShiftService _shiftService = ShiftService();
  StreamSubscription<ShiftInfo?>? _shiftSubscription;
  ShiftInfo? _currentShift;

  // 动画控制器
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // 时长计时器
  Timer? _durationTimer;
  String _durationText = '';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _subscribeToShiftStatus();
    _startDurationTimer();
  }

  @override
  void dispose() {
    _shiftSubscription?.cancel();
    _durationTimer?.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // 如果有活跃班次，启动脉冲动画
    if (_shiftService.hasActiveShift) {
      _pulseController.repeat(reverse: true);
    }
  }

  /// 订阅班次状态流
  void _subscribeToShiftStatus() {
    // 获取当前状态
    _currentShift = _shiftService.currentShift;
    
    // 订阅状态变化
    _shiftSubscription = _shiftService.shiftStatusStream.listen((ShiftInfo? shift) {
      if (mounted) {
        setState(() {
          _currentShift = shift;
        });
        
        // 根据状态控制动画
        if (shift?.status == ShiftStatus.active) {
          _pulseController.repeat(reverse: true);
        } else {
          _pulseController.stop();
          _pulseController.reset();
        }
      }
    });
  }

  /// 启动时长计时器
  void _startDurationTimer() {
    _durationTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted && _currentShift?.status == ShiftStatus.active) {
        setState(() {
          _durationText = _currentShift!.elapsedTime;
        });
      }
    });
  }



  /// 获取状态文本
  String _getStatusText() {
    if (_currentShift?.status == ShiftStatus.active) {
      return 'ON SHIFT';
    }
    return 'OFF SHIFT';
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    if (_currentShift?.status == ShiftStatus.active) {
      return BPColors.success;
    }
    return BPColors.error;
  }

  /// 获取状态图标
  IconData _getStatusIcon() {
    if (_currentShift?.status == ShiftStatus.active) {
      return Icons.work;
    }
    return Icons.work_off;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.compact) {
      return _buildCompactIndicator();
    }
    return _buildFullIndicator();
  }

  /// 构建紧凑模式指示器
  Widget _buildCompactIndicator() {
    final Color statusColor = _getStatusColor();
    final bool isActive = _currentShift?.status == ShiftStatus.active;

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: statusColor.withOpacity(0.15),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: statusColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 状态指示点（带动画）
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: isActive ? _pulseAnimation.value : 1.0,
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(width: 6),
            // 状态文本
            Text(
              _getStatusText(),
              style: TextStyle(
                color: statusColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建完整模式指示器
  Widget _buildFullIndicator() {
    final Color statusColor = _getStatusColor();
    final bool isActive = _currentShift?.status == ShiftStatus.active;

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              statusColor.withOpacity(0.1),
              statusColor.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: statusColor.withOpacity(0.3),
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 第一行：状态图标和文本
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 状态图标（带动画）
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: isActive ? _pulseAnimation.value : 1.0,
                      child: Icon(
                        _getStatusIcon(),
                        size: 16,
                        color: statusColor,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 6),
                // 状态文本
                Text(
                  _getStatusText(),
                  style: TextStyle(
                    color: statusColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            
            // 第二行：班次详情（仅在有班次时显示）
            if (isActive && _currentShift != null) ...[
              const SizedBox(height: 4),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 班次编号
                  Text(
                    'ID: ${_currentShift!.shiftId}',
                    style: TextStyle(
                      color: statusColor.withOpacity(0.8),
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 时长
                  Text(
                    _durationText,
                    style: TextStyle(
                      color: statusColor.withOpacity(0.8),
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
} 