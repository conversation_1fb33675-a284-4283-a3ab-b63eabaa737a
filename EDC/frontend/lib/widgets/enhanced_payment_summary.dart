import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';
import '../models/promotion_response.dart';

/// 增强的支付摘要组件
/// 专门优化有促销时的显示效果
class EnhancedPaymentSummary extends StatelessWidget {
  const EnhancedPaymentSummary({
    super.key,
    required this.originalAmount,
    this.promotionResponse,
    this.isLoading = false,
  });

  final double originalAmount;
  final PromotionResponse? promotionResponse;
  final bool isLoading;

  static final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'id_ID',
    symbol: 'Rp',
    decimalDigits: 0,
  );

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingState();
    }

    final bool hasPromotion = promotionResponse != null && 
                             promotionResponse!.discountAmount > 0;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: hasPromotion 
              ? BPColors.primary.withOpacity(0.3)  // 使用BP主绿色
              : BPColors.neutral.withOpacity(0.2),
          width: hasPromotion ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: hasPromotion
                ? BPColors.primary.withOpacity(0.1)  // 使用BP主绿色
                : Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(hasPromotion),
          if (hasPromotion) ...[
            _buildPromotionDetails(),
            _buildDivider(),
          ],
          _buildAmountBreakdown(hasPromotion),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: BPColors.neutral.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'Calculating promotions...',
            style: EDCTextStyles.bodyText.copyWith(
              color: BPColors.neutral,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(bool hasPromotion) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: hasPromotion 
            ? BPColors.primary.withOpacity(0.05)  // 使用BP主绿色
            : Colors.transparent,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: hasPromotion ? BPColors.primary : BPColors.primary,  // 统一使用BP主绿色
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              hasPromotion ? Icons.local_offer : Icons.receipt_long,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  hasPromotion ? 'Payment Summary' : 'Payment Summary',
                  style: EDCTextStyles.subTitle.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: hasPromotion ? BPColors.primary : BPColors.primary,  // 统一使用BP主绿色
                  ),
                ),
                if (hasPromotion) ...[
                  const SizedBox(height: 4),
                  Text(
                    '${promotionResponse!.appliedPromotions.length} promotion(s) applied',
                    style: EDCTextStyles.bodyText.copyWith(
                      fontSize: 12,
                      color: BPColors.primary.withOpacity(0.8),  // 使用BP主绿色
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (hasPromotion)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: BPColors.primary,  // 使用BP主绿色
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'SAVINGS',
                style: EDCTextStyles.bodyText.copyWith(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPromotionDetails() {
    if (promotionResponse == null || promotionResponse!.appliedPromotions.isEmpty) {
      return const SizedBox.shrink();
    }

    // 简化显示：只显示第一个主要促销活动
    final AppliedPromotion mainPromotion = promotionResponse!.appliedPromotions.first;
    final int totalPromotions = promotionResponse!.appliedPromotions.length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Row(
        children: [
          Icon(
            Icons.local_offer,
            size: 16,
            color: BPColors.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              totalPromotions > 1 
                ? '${mainPromotion.promotionName} +${totalPromotions - 1} more'
                : mainPromotion.promotionName,
              style: EDCTextStyles.bodyText.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: BPColors.neutral,
              ),
            ),
          ),
          Text(
            '- ${_currencyFormatter.format(promotionResponse!.discountAmount)}',
            style: EDCTextStyles.bodyText.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: BPColors.primary,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 1,
      color: BPColors.neutral.withOpacity(0.2),
    );
  }

  Widget _buildAmountBreakdown(bool hasPromotion) {
    final double finalAmount = hasPromotion 
        ? promotionResponse!.discountedAmount 
        : originalAmount;
    final double savings = hasPromotion 
        ? promotionResponse!.discountAmount 
        : 0.0;
    
    // Only show subtotal breakdown if there's actually a discount
    final bool hasActualDiscount = hasPromotion && savings > 0;

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Original amount (only show if there's actual discount)
          if (hasActualDiscount) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Subtotal',
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 14,
                    color: BPColors.neutral,
                  ),
                ),
                Text(
                  _currencyFormatter.format(originalAmount),
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 14,
                    color: BPColors.neutral,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Discount amount
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.local_offer,
                      size: 16,
                      color: BPColors.primary,  // 使用BP主绿色
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Discount',
                      style: EDCTextStyles.bodyText.copyWith(
                        fontSize: 14,
                        color: BPColors.primary,  // 使用BP主绿色
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                Text(
                  '- ${_currencyFormatter.format(savings)}',
                  style: EDCTextStyles.bodyText.copyWith(
                    fontSize: 14,
                    color: BPColors.primary,  // 使用BP主绿色
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Divider before total
            Container(
              height: 1,
              color: BPColors.neutral.withOpacity(0.3),
            ),
            const SizedBox(height: 12),
          ],

          // Final amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                hasActualDiscount ? 'Total Amount' : 'Amount Due',
                style: EDCTextStyles.bodyText.copyWith(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: BPColors.neutral,
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _currencyFormatter.format(finalAmount),
                    style: EDCTextStyles.emphasizedText.copyWith(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: BPColors.primary,  // 统一使用BP主绿色
                    ),
                  ),
                  if (hasActualDiscount) ...[
                    const SizedBox(height: 2),
                    Text(
                      'You save ${_currencyFormatter.format(savings)}',
                      style: EDCTextStyles.bodyText.copyWith(
                        fontSize: 12,
                        color: BPColors.primary,  // 使用BP主绿色
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }


} 