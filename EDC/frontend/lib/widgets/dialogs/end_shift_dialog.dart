import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/end_shift_validation.dart';
import '../../constants/bp_colors.dart';

/// Unified end shift confirmation dialog
/// Integrates basic information display and anomaly alerts
class EndShiftDialog extends StatelessWidget {
  const EndShiftDialog({
    super.key,
    required this.validationData,
    required this.onConfirm,
    required this.onCancel,
    this.isLoading = false,
  });

  final EndShiftValidationResponse validationData;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(8),
      child: SizedBox(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: <Widget>[
            // Title bar
            Container(
              padding: const EdgeInsets.fromLTRB(20, 12, 8, 12),
              decoration: BoxDecoration(
                color: validationData.totalizerContinuity.hasAbnormal
                    ? Colors.red[50]
                    : Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: _buildTitle(),
                  ),
                  IconButton(
                    onPressed: isLoading ? null : onCancel,
                    icon: const Icon(Icons.close),
                    color: Colors.grey[600],
                    padding: const EdgeInsets.all(4),
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),
            ),
            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    _buildShiftInfo(),
                    if (validationData.totalizerContinuity.hasAbnormal) ...<Widget>[
                      const SizedBox(height: 24),
                      _buildAnomalyWarning(),
                    ],
                  ],
                ),
              ),
            ),
            // Action buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: _buildActions(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build title
  Widget _buildTitle() {
    return Row(
      children: <Widget>[
        Icon(
          Icons.stop_circle_outlined,
          color: validationData.totalizerContinuity.hasAbnormal
              ? Colors.red
              : BPColors.error,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          validationData.totalizerContinuity.hasAbnormal
              ? 'End Shift - Anomaly Alert'
              : 'End Shift',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: validationData.totalizerContinuity.hasAbnormal
                ? Colors.red
                : Colors.black87,
          ),
        ),
      ],
    );
  }

  /// Build shift basic information
  Widget _buildShiftInfo() {
    final ShiftInfo shiftInfo = validationData.shiftInfo;
    final DateFormat timeFormat = DateFormat('HH:mm:ss');
    final DateFormat dateFormat = DateFormat('yyyy-MM-dd');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            'Shift Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          _buildInfoRow('Shift Number', shiftInfo.shiftNumber),
          _buildInfoRow('Start Date', dateFormat.format(shiftInfo.startTime)),
          _buildInfoRow('Start Time', timeFormat.format(shiftInfo.startTime)),
          _buildInfoRow('Current Time', timeFormat.format(shiftInfo.currentTime)),
          _buildInfoRow('Duration', '${shiftInfo.durationHours.toStringAsFixed(1)} hours'),
        ],
      ),
    );
  }

  /// Build information row
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: <Widget>[
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build anomaly warning information
  Widget _buildAnomalyWarning() {
    final TotalizerContinuity continuity = validationData.totalizerContinuity;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Totalizer Continuity Anomaly',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildAnomalyStats(continuity),
          if (continuity.abnormalTransactions.isNotEmpty) ...<Widget>[
            const SizedBox(height: 16),
            Text(
              'Affected Transactions:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            _buildAnomalyTransactions(continuity.abnormalTransactions),
          ],
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Totalizer discontinuity detected. Please confirm if you still want to proceed with shift end.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.red[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build anomaly statistics
  Widget _buildAnomalyStats(TotalizerContinuity continuity) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Column(
              children: <Widget>[
                Text(
                  '${continuity.abnormalTransactionCount}',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Abnormal Orders',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.red[200],
          ),
          Expanded(
            child: Column(
              children: <Widget>[
                Text(
                  '${continuity.totalDiscrepancyAmount.toStringAsFixed(1)}L',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Total Discrepancy',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build anomaly transaction list
  Widget _buildAnomalyTransactions(List<AbnormalTransaction> transactions) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Column(
        children: transactions.map(_buildTransactionItem).toList(),
      ),
    );
  }

  /// Build transaction item
  Widget _buildTransactionItem(AbnormalTransaction transaction) {
    final DateFormat timeFormat = DateFormat('HH:mm');

    // Extract last 2 digits from pump and nozzle IDs, removing special characters
    final String pumpDisplay = _extractLastTwoDigits(transaction.pumpId);
    final String nozzleDisplay = _extractLastTwoDigits(transaction.nozzleId);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.red[100]!, width: 0.5),
        ),
      ),
      child: Row(
        children: <Widget>[
          // Pump-Nozzle
          SizedBox(
            width: 60,
            child: Text(
              '$pumpDisplay-$nozzleDisplay',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red[700],
              ),
            ),
          ),
          // Time
          Expanded(
            child: Text(
              timeFormat.format(transaction.transactionTime),
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[600],
              ),
            ),
          ),
          // Discrepancy amount
          Text(
            '${transaction.discrepancyAmount.toStringAsFixed(1)}L',
            style: TextStyle(
              fontSize: 14,
              color: Colors.red[600],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Extract last 2 digits from ID, removing special characters
  String _extractLastTwoDigits(String id) {
    // Remove all non-digit characters
    final String digitsOnly = id.replaceAll(RegExp(r'[^0-9]'), '');

    // Return last 2 digits, or the whole string if less than 2 digits
    if (digitsOnly.length >= 2) {
      return digitsOnly.substring(digitsOnly.length - 2);
    } else if (digitsOnly.isNotEmpty) {
      return digitsOnly;
    } else {
      // Fallback: return last 2 characters of original string
      return id.length >= 2 ? id.substring(id.length - 2) : id;
    }
  }

  /// Build action buttons
  List<Widget> _buildActions(BuildContext context) {
    return <Widget>[
      TextButton(
        onPressed: isLoading ? null : onCancel,
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
        child: Text(
          'Cancel',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 16,
          ),
        ),
      ),
      const SizedBox(width: 12),
      ElevatedButton(
        onPressed: isLoading ? null : onConfirm,
        style: ElevatedButton.styleFrom(
          backgroundColor: validationData.totalizerContinuity.hasAbnormal
              ? Colors.red
              : BPColors.error,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                validationData.totalizerContinuity.hasAbnormal
                    ? 'End Anyway'
                    : 'Confirm End Shift',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    ];
  }
}
