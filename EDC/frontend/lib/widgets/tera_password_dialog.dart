import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';
import '../screens/auth/auth_service.dart';
import '../services/api/api_client.dart';
import '../services/api/auth_api.dart';
import '../models/auth_models.dart';
import '../constants/api_constants.dart';
import '../services/shared/storage_service.dart';
import '../main.dart';

/// Tera支付密码验证对话框
/// 用于Tera支付方式的密码验证
class TeraPasswordDialog extends ConsumerStatefulWidget {
  const TeraPasswordDialog({super.key});

  @override
  ConsumerState<TeraPasswordDialog> createState() => _TeraPasswordDialogState();
}

class _TeraPasswordDialogState extends ConsumerState<TeraPasswordDialog> {
  final TextEditingController _passwordController = TextEditingController();
  bool _isObscured = true;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  /// 使用临时API客户端验证密码，不影响当前登录状态
  Future<void> _verifyPasswordWithTempClient(String employeeNo, String password) async {
    // 获取当前的服务器配置
    final StorageService storageService = ref.read(storageServiceProvider);

    // 1. 优先使用自定义BOS服务器地址
    String? baseUrl = await storageService.getCustomBosUrl();

    if (baseUrl == null || baseUrl.isEmpty) {
      // 2. 使用当前API环境的默认配置
      final ApiEnvironment currentEnv = await storageService.getApiEnvironment();
      baseUrl = ApiConstants.getServiceUrl(BackendService.base, currentEnv);
    }

    // 创建临时的API客户端（不影响当前登录状态）
    final ApiClient tempApiClient = ApiClient(baseUrl: '$baseUrl/api/v1');
    final AuthApi tempAuthApi = AuthApi(apiClient: tempApiClient);

    // 创建登录请求
    final LoginRequest request = LoginRequest(
      username: employeeNo,
      password: password,
      system: 'EDC',
      authType: 'local',
      rememberMe: false, // 临时验证不需要记住
    );

    // 调用登录API进行密码验证（只验证，不保存状态）
    await tempAuthApi.login(request);
    // 如果到这里没有抛出异常，说明密码验证成功
  }

  /// 验证密码
  Future<void> _validatePassword() async {
    final String password = _passwordController.text.trim();

    if (password.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter your password';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 获取当前登录用户的用户名
      final AuthService authService = ref.read(authServiceProvider);
      final String? employeeNo = authService.currentEmployee?.employeeNo;

      if (employeeNo == null) {
        setState(() {
          _errorMessage = 'Unable to get current user information';
          _isLoading = false;
        });
        return;
      }

      // 创建临时的API客户端进行密码验证，不影响当前登录状态
      await _verifyPasswordWithTempClient(employeeNo, password);

      // 验证成功，显示简短的成功提示
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // 显示成功状态
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: <Widget>[
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text('Password verified. Processing payment...'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // 稍微延迟一下让用户看到成功提示，然后关闭对话框
        await Future<void>.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      }
    } catch (e) {
      // 验证失败
      if (mounted) {
        setState(() {
          if (e is ApiException) {
            _errorMessage = e.message;
          } else {
            _errorMessage = 'Invalid password. Please try again.';
          }
          _isLoading = false;
        });
        _passwordController.clear();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 300,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            // 图标和标题
            const Icon(
              Icons.security,
              color: BPColors.primary,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Tera Payment Verification',
              style: EDCTextStyles.subTitle.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: BPColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Please enter your login password to proceed with Tera payment',
              style: EDCTextStyles.bodyText.copyWith(
                fontSize: 14,
                color: BPColors.neutral,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // 密码输入框
            TextFormField(
              controller: _passwordController,
              obscureText: _isObscured,
              enabled: !_isLoading,
              style: EDCTextStyles.bodyText,
              decoration: InputDecoration(
                labelText: 'Password',
                labelStyle: EDCTextStyles.bodyText.copyWith(
                  color: BPColors.neutral,
                ),
                hintText: 'Enter your password',
                hintStyle: EDCTextStyles.bodyText.copyWith(
                  color: BPColors.neutral.withValues(alpha: 0.7),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                      color: BPColors.neutral.withValues(alpha: 0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      const BorderSide(color: BPColors.primary, width: 2),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: BPColors.error, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                suffixIcon: IconButton(
                  icon: Icon(
                    _isObscured ? Icons.visibility : Icons.visibility_off,
                    color: BPColors.neutral,
                  ),
                  onPressed: () {
                    setState(() {
                      _isObscured = !_isObscured;
                    });
                  },
                ),
                errorText: _errorMessage,
              ),
              onFieldSubmitted: (_) => _validatePassword(),
            ),
            const SizedBox(height: 24),

            // 按钮区域
            Row(
              children: <Widget>[
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading
                        ? null
                        : () => Navigator.of(context).pop(false),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: BPColors.neutral,
                      side: const BorderSide(color: BPColors.neutral),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: EDCTextStyles.bodyText.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _validatePassword,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: BPColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                              strokeWidth: 2,
                            ),
                          )
                        : Text(
                            'Verify',
                            style: EDCTextStyles.bodyText.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 显示Tera密码验证对话框的辅助方法
Future<bool> showTeraPasswordDialog(BuildContext context) async {
  final bool result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const TeraPasswordDialog();
        },
      ) ??
      false;

  return result;
}
