import 'package:flutter/material.dart';
import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';
import 'dart:async'; // Added for Timer

/// Unified loading indicator component for the application
/// Following BP brand design guidelines and EDC mobile device optimization standards
class AppLoadingIndicator extends StatelessWidget {
  const AppLoadingIndicator({
    super.key,
    this.size = 32.0,
    this.strokeWidth = 3.0,
    this.showText = true,
    this.loadingText,
    this.backgroundColor,
    this.showOverlay = false,
  });

  /// Create a full-screen loading indicator
  factory AppLoadingIndicator.fullScreen({
    String? loadingText,
  }) {
    return AppLoadingIndicator(
      size: 48.0,
      strokeWidth: 4.0,
      showText: true,
      loadingText: loadingText,
      showOverlay: true,
    );
  }

  /// Create a small loading indicator (for use inside buttons, etc.)
  factory AppLoadingIndicator.small() {
    return const AppLoadingIndicator(
      size: 24.0,
      strokeWidth: 2.5,
      showText: false,
      showOverlay: false,
    );
  }

  /// Create a loading indicator for buttons
  factory AppLoadingIndicator.button() {
    return const AppLoadingIndicator(
      size: 20.0,
      strokeWidth: 2.0,
      showText: false,
      showOverlay: false,
    );
  }

  /// Loading indicator size
  final double size;

  /// Stroke width
  final double strokeWidth;

  /// Whether to show loading text
  final bool showText;

  /// Custom loading text
  final String? loadingText;

  /// Background color
  final Color? backgroundColor;

  /// Whether to show background overlay
  final bool showOverlay;

  @override
  Widget build(BuildContext context) {
    final Widget loadingWidget = Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: strokeWidth,
            valueColor: const AlwaysStoppedAnimation<Color>(BPColors.primary),
            backgroundColor: Colors.grey[300],
          ),
        ),
        if (showText) ...<Widget>[
          const SizedBox(height: 16),
          Text(
            loadingText ?? 'Loading...',
            style: EDCTextStyles.hintText,
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    if (showOverlay) {
      return Material(
        color: backgroundColor ?? Colors.black.withOpacity(0.3),
        child: Center(child: loadingWidget),
      );
    }

    return Center(child: loadingWidget);
  }
}

/// Loading indicator with animation effects
class AnimatedLoadingIndicator extends StatefulWidget {
  const AnimatedLoadingIndicator({
    super.key,
    this.loadingTexts = const <String>[
      'Loading...',
      'Please wait...',
      'Processing...'
    ],
    this.textInterval = const Duration(seconds: 2),
    this.size = 32.0,
  });

  /// List of loading texts that will be displayed in rotation
  final List<String> loadingTexts;

  /// Text switching interval
  final Duration textInterval;

  /// Indicator size
  final double size;

  @override
  State<AnimatedLoadingIndicator> createState() =>
      _AnimatedLoadingIndicatorState();
}

class _AnimatedLoadingIndicatorState extends State<AnimatedLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _textController;
  late Animation<double> _textOpacity;
  int _currentTextIndex = 0;

  @override
  void initState() {
    super.initState();
    _textController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _textOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    _startTextAnimation();
  }

  void _startTextAnimation() {
    _textController.forward().then((_) {
      Future.delayed(widget.textInterval, () {
        if (mounted) {
          _textController.reverse().then((_) {
            if (mounted) {
              setState(() {
                _currentTextIndex =
                    (_currentTextIndex + 1) % widget.loadingTexts.length;
              });
              _startTextAnimation();
            }
          });
        }
      });
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          SizedBox(
            width: widget.size,
            height: widget.size,
            child: CircularProgressIndicator(
              strokeWidth: 3.0,
              valueColor: const AlwaysStoppedAnimation<Color>(BPColors.primary),
              backgroundColor: Colors.grey[300],
            ),
          ),
          const SizedBox(height: 16),
          AnimatedBuilder(
            animation: _textOpacity,
            builder: (BuildContext context, Widget? child) {
              return Opacity(
                opacity: _textOpacity.value,
                child: Text(
                  widget.loadingTexts[_currentTextIndex],
                  style: EDCTextStyles.hintText,
                  textAlign: TextAlign.center,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// Page-level loading state management
class LoadingOverlay {
  static OverlayEntry? _overlayEntry;

  /// Show full-screen loading overlay
  static void show(BuildContext context, {String? message}) {
    hide(); // Ensure previous overlay is hidden first

    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) => AppLoadingIndicator.fullScreen(
        loadingText: message,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  /// Hide loading overlay
  static void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

/// Loading state component with retry functionality
class LoadingStateWidget extends StatelessWidget {
  const LoadingStateWidget({
    super.key,
    required this.isLoading,
    required this.hasError,
    required this.child,
    this.errorMessage,
    this.onRetry,
    this.loadingText,
  });

  /// Whether currently loading
  final bool isLoading;

  /// Whether there's an error
  final bool hasError;

  /// Error message
  final String? errorMessage;

  /// Retry callback
  final VoidCallback? onRetry;

  /// Child widget
  final Widget child;

  /// Loading text
  final String? loadingText;

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return AppLoadingIndicator(
        loadingText: loadingText,
        showOverlay: true,
      );
    }

    if (hasError) {
      return EDCComponentStyles.buildEmptyState(
        message: errorMessage ?? 'An error occurred',
        description: 'Please try again',
        icon: Icons.error_outline,
        onRetry: onRetry,
      );
    }

    return child;
  }
}

/// Cancellable loading overlay
class CancellableLoadingOverlay {
  static OverlayEntry? _overlayEntry;
  static VoidCallback? _onCancel;

  /// Show cancellable full-screen loading overlay
  static void show(
    BuildContext context, {
    String? message,
    VoidCallback? onCancel,
    Duration? timeout,
  }) {
    hide(); // Ensure previous overlay is hidden first

    _onCancel = onCancel;

    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) => CancellableLoadingWidget(
        message: message ?? 'Loading...',
        onCancel: onCancel,
        timeout: timeout,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  /// Hide loading overlay
  static void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _onCancel = null;
  }
}

/// Cancellable loading widget
class CancellableLoadingWidget extends StatefulWidget {
  const CancellableLoadingWidget({
    super.key,
    required this.message,
    this.onCancel,
    this.timeout,
  });

  final String message;
  final VoidCallback? onCancel;
  final Duration? timeout;

  @override
  State<CancellableLoadingWidget> createState() => _CancellableLoadingWidgetState();
}

class _CancellableLoadingWidgetState extends State<CancellableLoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  Timer? _timeoutTimer;
  bool _isTimedOut = false;

  @override
  void initState() {
    super.initState();

    _progressController = AnimationController(
      duration: widget.timeout ?? const Duration(seconds: 30),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.linear,
    ));

    // Start progress animation
    _progressController.forward();

    // Set timeout timer
    if (widget.timeout != null) {
      _timeoutTimer = Timer(widget.timeout!, () {
        if (mounted) {
          setState(() {
            _isTimedOut = true;
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _timeoutTimer?.cancel();
    super.dispose();
  }

  void _handleCancel() {
    if (widget.onCancel != null) {
      widget.onCancel!();
    }
    CancellableLoadingOverlay.hide();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withOpacity(0.6),
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 320),
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Loading indicator
              if (!_isTimedOut) ...[
                SizedBox(
                  width: 48,
                  height: 48,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Main loading animation
                      const CircularProgressIndicator(
                        strokeWidth: 4,
                        valueColor: AlwaysStoppedAnimation<Color>(BPColors.primary),
                      ),
                      // Timeout progress ring (if timeout is set)
                      if (widget.timeout != null)
                        AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            return CircularProgressIndicator(
                              value: _progressAnimation.value,
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                BPColors.warning.withOpacity(0.6),
                              ),
                              backgroundColor: Colors.transparent,
                            );
                          },
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                // Loading message
                Text(
                  widget.message,
                  style: EDCTextStyles.bodyText,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                // Cancel button
                if (widget.onCancel != null)
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: _handleCancel,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: BPColors.neutral,
                        side: BorderSide(color: BPColors.neutral.withOpacity(0.5)),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
              ] else ...[
                // Timeout state
                Icon(
                  Icons.access_time,
                  size: 48,
                  color: BPColors.warning,
                ),
                const SizedBox(height: 16),
                Text(
                  'Request Timeout',
                  style: EDCTextStyles.subTitle.copyWith(
                    color: BPColors.warning,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Network request timeout, please check your network connection',
                  style: EDCTextStyles.hintText,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _handleCancel,
                        style: OutlinedButton.styleFrom(
                          foregroundColor: BPColors.neutral,
                          side: BorderSide(color: BPColors.neutral.withOpacity(0.5)),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('Close'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          // Reset state and start again
                          setState(() {
                            _isTimedOut = false;
                          });
                          _progressController.reset();
                          _progressController.forward();
                          
                          if (widget.timeout != null) {
                            _timeoutTimer?.cancel();
                            _timeoutTimer = Timer(widget.timeout!, () {
                              if (mounted) {
                                setState(() {
                                  _isTimedOut = true;
                                });
                              }
                            });
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: BPColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('Retry'),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
