import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../models/promotion_response.dart';
import '../controllers/promotion_controller.dart';

/// 优惠信息显示组件
/// 
/// 显示优惠信息，包括原价、折扣价、折扣金额和折扣项目列表
class PromotionWidget extends ConsumerWidget {
  // BP绿色主题色
  static const Color bpGreenColor = Color(0xFF2E8B57);
  
  // 货币格式化器 - 使用印尼盾（Rp）
  static final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'id_ID', 
    symbol: 'Rp', 
    decimalDigits: 0
  );
  
  const PromotionWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 获取优惠状态
    final promotionState = ref.watch(promotionControllerProvider);
    
    // 判断是否显示加载中状态
    if (promotionState.isLoading) {
      return _buildLoadingState();
    }
    
    // 判断是否有错误信息
    if (promotionState.errorMessage != null) {
      return _buildErrorState(promotionState.errorMessage!);
    }
    
    // 判断是否有优惠数据
    if (promotionState.promotionData == null) {
      return _buildEmptyState();
    }
    
    // 显示优惠信息
    return _buildPromotionInfo(promotionState.promotionData!);
  }
  
  // 构建加载中状态
  Widget _buildLoadingState() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      color: Colors.white,
      child: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(bpGreenColor),
              ),
              SizedBox(height: 16),
              Text(
                'Loading promotion information...',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // 构建错误状态
  Widget _buildErrorState(String errorMessage) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: Colors.red.withOpacity(0.2),
          width: 1,
        ),
      ),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.red),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Failed to load promotion: $errorMessage',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // 构建空状态
  Widget _buildEmptyState() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      color: Colors.white,
      child: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.grey),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'No promotion data available',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // 构建优惠信息
  Widget _buildPromotionInfo(PromotionResponse promotion) {
    // 判断是否有折扣
    final bool hasDiscount = promotion.discountAmount > 0;
    final Color titleColor = hasDiscount ? bpGreenColor : Colors.grey;
    final IconData titleIcon = hasDiscount ? Icons.discount_outlined : Icons.receipt_outlined;
    final String titleText = hasDiscount ? 'Promotion Applied' : 'Payment Summary';
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Icon(titleIcon, color: titleColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  titleText,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: titleColor,
                  ),
                ),
                const Spacer(),
                if (hasDiscount)
                  Text(
                    '- ${_currencyFormatter.format(promotion.discountAmount)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: bpGreenColor,
                    ),
                  )
                else
                  Text(
                    _currencyFormatter.format(promotion.originalAmount),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
              ],
            ),
            
            // 显示新增的促销信息
            if (promotion.orderId != null || promotion.vehicleType != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if (promotion.orderId != null) ...[
                    Icon(Icons.receipt_long, color: Colors.grey, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'Order: ${promotion.orderId}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                  if (promotion.orderId != null && promotion.vehicleType != null)
                    const SizedBox(width: 16),
                  if (promotion.vehicleType != null) ...[
                    Icon(Icons.directions_car, color: Colors.grey, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'Vehicle: ${promotion.vehicleType}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ],
              ),
            ],
            
            // 显示商品统计信息
            if (promotion.totalItems != null && promotion.totalQuantity != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.shopping_cart, color: Colors.grey, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    'Items: ${promotion.totalItems} (Qty: ${promotion.totalQuantity})',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
            
            const Divider(),
            // 价格信息
            if (hasDiscount) ...[
              // 有折扣时显示原价和折后价
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Original Price:',
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                    Text(
                      _currencyFormatter.format(promotion.originalAmount),
                      style: TextStyle(
                        color: Colors.grey[800],
                        decoration: TextDecoration.lineThrough,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Final Price:',
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                    Text(
                      _currencyFormatter.format(promotion.discountedAmount),
                      style: TextStyle(
                        color: bpGreenColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'You Save:',
                      style: TextStyle(
                        color: bpGreenColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _currencyFormatter.format(promotion.discountAmount),
                      style: const TextStyle(
                        color: bpGreenColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              // 无折扣时只显示付款金额
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Payment Amount:',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      _currencyFormatter.format(promotion.originalAmount),
                      style: TextStyle(
                        color: Colors.grey[800],
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Discount:',
                      style: TextStyle(
                        color: Colors.grey,
                      ),
                    ),
                    const Text(
                      'No discount available',
                      style: TextStyle(
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // 优惠项目列表 (如果有项目的话)
            if (promotion.items.isNotEmpty) ...[
              const Divider(),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  'Discount Details',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
              ),
              ...promotion.items.map((item) => _buildDiscountItem(item)).toList(),
            ],
            
            // 优惠消息
            if (promotion.message.isNotEmpty) ...[
              const Divider(),
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    const Icon(Icons.info_outline, size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        promotion.message,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  // 构建单个优惠项目
  Widget _buildDiscountItem(DiscountedItem item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(width: 16),
          Expanded(
            flex: 3,
            child: Text(
              item.name,
              style: TextStyle(
                color: Colors.grey[800],
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'x${item.quantity}',
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              _currencyFormatter.format(item.originalPrice - item.discountedPrice),
              style: const TextStyle(
                color: bpGreenColor,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
} 