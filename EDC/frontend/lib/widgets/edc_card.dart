import 'package:flutter/material.dart';
import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';

/// EDC标准卡片组件
/// 遵循BP品牌设计规范和移动设备优化标准
class EDCCard extends StatelessWidget {
  const EDCCard({
    super.key,
    this.title,
    this.subtitle,
    this.child,
    this.actions,
    this.padding,
    this.margin,
    this.onTap,
    this.showShadow = false,
    this.headerIcon,
    this.isClickable = false,
  });

  /// 创建信息展示卡片
  factory EDCCard.info({
    String? title,
    String? subtitle,
    Widget? child,
    EdgeInsets? padding,
    EdgeInsets? margin,
    IconData? icon,
  }) {
    return EDCCard(
      title: title,
      subtitle: subtitle,
      padding: padding,
      margin: margin,
      headerIcon: icon,
      showShadow: false,
      child: child,
    );
  }

  /// 创建操作卡片
  factory EDCCard.action({
    String? title,
    String? subtitle,
    Widget? child,
    List<Widget>? actions,
    VoidCallback? onTap,
    EdgeInsets? padding,
    EdgeInsets? margin,
    IconData? icon,
  }) {
    return EDCCard(
      title: title,
      subtitle: subtitle,
      actions: actions,
      onTap: onTap,
      padding: padding,
      margin: margin,
      headerIcon: icon,
      isClickable: onTap != null,
      showShadow: true,
      child: child,
    );
  }

  /// 创建列表项卡片
  factory EDCCard.listItem({
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    IconData? leading,
  }) {
    return EDCCard(
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      headerIcon: leading,
      isClickable: onTap != null,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: trailing != null
          ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(child: Container()),
                trailing,
              ],
            )
          : null,
    );
  }

  /// 卡片标题
  final String? title;

  /// 卡片子标题
  final String? subtitle;

  /// 卡片内容
  final Widget? child;

  /// 卡片操作按钮
  final List<Widget>? actions;

  /// 自定义内边距
  final EdgeInsets? padding;

  /// 自定义外边距
  final EdgeInsets? margin;

  /// 点击回调
  final VoidCallback? onTap;

  /// 是否显示阴影
  final bool showShadow;

  /// 卡片头部图标
  final IconData? headerIcon;

  /// 是否可点击样式
  final bool isClickable;

  @override
  Widget build(BuildContext context) {
    final Widget cardContent = _buildCardContent();

    final Widget card = Container(
      margin: margin ?? const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: showShadow
            ? <BoxShadow>[
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: cardContent,
    );

    if (onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: card,
        ),
      );
    }

    return card;
  }

  Widget _buildCardContent() {
    final List<Widget> children = <Widget>[];

    // 构建卡片头部
    if (title != null || headerIcon != null) {
      children.add(_buildHeader());
    }

    // 构建卡片内容
    if (child != null) {
      children.add(
        Padding(
          padding: EdgeInsets.only(
            top: (title != null || headerIcon != null) ? 16 : 0,
          ),
          child: child!,
        ),
      );
    }

    // 构建操作按钮区域
    if (actions != null && actions!.isNotEmpty) {
      children.add(
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: _buildActions(),
        ),
      );
    }

    return Padding(
      padding: padding ?? const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  Widget _buildHeader() {
    final List<Widget> headerChildren = <Widget>[];

    if (headerIcon != null) {
      headerChildren.add(
        Icon(
          headerIcon,
          size: 28,
          color: BPColors.primary,
        ),
      );
      headerChildren.add(const SizedBox(width: 12));
    }

    if (title != null || subtitle != null) {
      headerChildren.add(
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              if (title != null)
                Text(
                  title!,
                  style: EDCTextStyles.cardTitle,
                ),
              if (subtitle != null) ...<Widget>[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: EDCTextStyles.hintText,
                ),
              ],
            ],
          ),
        ),
      );
    }

    if (isClickable && onTap != null) {
      headerChildren.add(
        const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: BPColors.neutral,
        ),
      );
    }

    return Row(
      children: headerChildren,
    );
  }

  Widget _buildActions() {
    if (actions!.length == 1) {
      return Align(
        alignment: Alignment.centerRight,
        child: actions!.first,
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: actions!
          .map((Widget action) => Padding(
                padding: const EdgeInsets.only(left: 8),
                child: action,
              ))
          .toList(),
    );
  }
}

/// 数据展示卡片
class EDCDataCard extends StatelessWidget {
  const EDCDataCard({
    super.key,
    required this.title,
    required this.value,
    this.unit,
    this.change,
    this.isPositiveChange,
    this.icon,
    this.onTap,
  });

  /// 数据标题
  final String title;

  /// 数据值
  final String value;

  /// 数据单位
  final String? unit;

  /// 变化值
  final String? change;

  /// 是否为正向变化
  final bool? isPositiveChange;

  /// 图标
  final IconData? icon;

  /// 点击回调
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return EDCCard(
      onTap: onTap,
      isClickable: onTap != null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              if (icon != null) ...<Widget>[
                Icon(
                  icon,
                  size: 24,
                  color: BPColors.primary,
                ),
                const SizedBox(width: 8),
              ],
              Expanded(
                child: Text(
                  title,
                  style: EDCTextStyles.hintText,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: <Widget>[
              Text(
                value,
                style: EDCTextStyles.emphasizedText.copyWith(
                  fontSize: 24,
                  color: BPColors.primary,
                ),
              ),
              if (unit != null) ...<Widget>[
                const SizedBox(width: 4),
                Text(
                  unit!,
                  style: EDCTextStyles.hintText.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ],
          ),
          if (change != null) ...<Widget>[
            const SizedBox(height: 4),
            Row(
              children: <Widget>[
                Icon(
                  isPositiveChange == true
                      ? Icons.trending_up
                      : Icons.trending_down,
                  size: 16,
                  color: isPositiveChange == true
                      ? BPColors.success
                      : BPColors.error,
                ),
                const SizedBox(width: 4),
                Text(
                  change!,
                  style: EDCTextStyles.hintText.copyWith(
                    color: isPositiveChange == true
                        ? BPColors.success
                        : BPColors.error,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

/// 状态卡片
class EDCStatusCard extends StatelessWidget {
  const EDCStatusCard({
    super.key,
    required this.title,
    required this.status,
    this.description,
    this.statusType = EDCStatusType.normal,
    this.action,
    this.onTap,
  });

  /// 状态标题
  final String title;

  /// 状态值
  final String status;

  /// 状态描述
  final String? description;

  /// 状态类型
  final EDCStatusType statusType;

  /// 操作按钮
  final Widget? action;

  /// 点击回调
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return EDCCard(
      onTap: onTap,
      isClickable: onTap != null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            title,
            style: EDCTextStyles.bodyText,
          ),
          const SizedBox(height: 8),
          Row(
            children: <Widget>[
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _getStatusColor(),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                status,
                style: EDCTextStyles.cardTitle.copyWith(
                  color: _getStatusColor(),
                ),
              ),
            ],
          ),
          if (description != null) ...<Widget>[
            const SizedBox(height: 8),
            Text(
              description!,
              style: EDCTextStyles.hintText,
            ),
          ],
          if (action != null) ...<Widget>[
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: action!,
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (statusType) {
      case EDCStatusType.success:
        return BPColors.success;
      case EDCStatusType.warning:
        return BPColors.warning;
      case EDCStatusType.error:
        return BPColors.error;
      case EDCStatusType.normal:
        return BPColors.primary;
    }
  }
}

/// 状态类型枚举
enum EDCStatusType {
  normal, // 正常状态
  success, // 成功状态
  warning, // 警告状态
  error, // 错误状态
}
