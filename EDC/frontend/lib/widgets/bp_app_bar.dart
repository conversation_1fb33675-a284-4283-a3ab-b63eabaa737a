import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/bp_colors.dart';
import '../l10n/generated/app_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// BP标准化AppBar组件
/// 统一BP品牌体验，集成Logo和标准色彩
class BPAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const BPAppBar({
    super.key,
    this.title,
    this.showLogo = false,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.toolbarHeight,
    this.elevation,
    this.onBack,
  });

  /// AppBar标题文本
  final String? title;

  /// 是否显示BP Logo
  final bool showLogo;

  /// 自定义操作按钮
  final List<Widget>? actions;

  /// 自定义leading按钮
  final Widget? leading;

  /// 是否自动显示返回按钮
  final bool automaticallyImplyLeading;

  /// 背景颜色，默认使用BP主色
  final Color? backgroundColor;

  /// 前景色，默认白色
  final Color? foregroundColor;

  /// AppBar高度
  final double? toolbarHeight;

  /// 阴影高度
  final double? elevation;

  /// 当返回时的回调
  final VoidCallback? onBack;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 获取国际化文本
    final AppLocalizations localizations = AppLocalizations.of(context);

    // 计算实际颜色
    final Color actualBgColor = backgroundColor ?? BPColors.primary;
    final Color actualFgColor = foregroundColor ?? Colors.white;

    return AppBar(
      title: _buildTitle(context, localizations),
      backgroundColor: actualBgColor,
      foregroundColor: actualFgColor,
      elevation: elevation ?? 0,
      toolbarHeight: toolbarHeight,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading ??
          (automaticallyImplyLeading && Navigator.canPop(context)
              ? IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: onBack ?? () => Navigator.pop(context),
                )
              : null),
      actions: actions,
      iconTheme: IconThemeData(
        color: actualFgColor,
      ),
      titleTextStyle: TextStyle(
        color: actualFgColor,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
      // 统一设置系统状态栏样式
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: actualBgColor, // 状态栏背景色与AppBar一致
        statusBarIconBrightness: _getStatusBarIconBrightness(actualBgColor),
        statusBarBrightness: _getStatusBarBrightness(actualBgColor),
      ),
    );
  }

  /// 构建标题部分
  Widget _buildTitle(BuildContext context, AppLocalizations localizations) {
    if (!showLogo && title != null) {
      // 仅显示文字标题
      return Text(title!);
    }

    if (showLogo && title != null) {
      // 显示Logo + 文字标题
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Image.asset(
            'assets/images/bp_logo.png',
            height: 28,
            fit: BoxFit.contain,
            errorBuilder:
                (BuildContext context, Object error, StackTrace? stackTrace) {
              // Logo加载失败时显示文字替代
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'BP',
                  style: TextStyle(
                    color: BPColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Text(title!),
        ],
      );
    }

    if (showLogo && title == null) {
      // 仅显示Logo
      return Image.asset(
        'assets/images/bp_logo.png',
        height: 28,
        fit: BoxFit.contain,
        errorBuilder:
            (BuildContext context, Object error, StackTrace? stackTrace) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              'BP',
              style: TextStyle(
                color: BPColors.primary,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          );
        },
      );
    }

    // 默认显示应用名称
    return Text(localizations.appName);
  }

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight ?? kToolbarHeight);

  /// 根据背景色计算状态栏图标亮度
  Brightness _getStatusBarIconBrightness(Color backgroundColor) {
    // 计算颜色亮度，判断使用深色还是浅色图标
    final double luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Brightness.dark : Brightness.light;
  }

  /// 根据背景色计算状态栏亮度（iOS专用）
  Brightness _getStatusBarBrightness(Color backgroundColor) {
    // iOS状态栏亮度与图标亮度相反
    final Brightness iconBrightness =
        _getStatusBarIconBrightness(backgroundColor);
    return iconBrightness == Brightness.light
        ? Brightness.dark
        : Brightness.light;
  }
}

/// BP AppBar的预设配置
class BPAppBarPresets {
  /// 主页AppBar配置 - 显示Logo和应用名
  static BPAppBar home(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context);
    return BPAppBar(
      title: localizations.appName,
      showLogo: false,
      automaticallyImplyLeading: false,
    );
  }

  /// 详情页AppBar配置 - 显示标题和返回按钮
  static BPAppBar detail({
    required String title,
    List<Widget>? actions,
    VoidCallback? onBack,
  }) {
    return BPAppBar(
      title: title,
      showLogo: false,
      actions: actions,
      onBack: onBack,
    );
  }

  /// 列表页AppBar配置 - 标题 + 搜索/刷新操作
  static BPAppBar list({
    required String title,
    VoidCallback? onSearch,
    VoidCallback? onRefresh,
    VoidCallback? onBack,
  }) {
    final List<Widget> defaultActions = <Widget>[];

    if (onSearch != null) {
      defaultActions.add(
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: onSearch,
        ),
      );
    }

    if (onRefresh != null) {
      defaultActions.add(
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: onRefresh,
        ),
      );
    }

    return BPAppBar(
      title: title,
      showLogo: false,
      actions: defaultActions.isNotEmpty ? defaultActions : null,
      onBack: onBack,
    );
  }

  /// 表单页AppBar配置 - 标题 + 保存操作
  static BPAppBar form({
    required String title,
    VoidCallback? onSave,
    VoidCallback? onBack,
    bool showSave = true,
  }) {
    return BPAppBar(
      title: title,
      showLogo: false,
      actions: showSave && onSave != null
          ? <Widget>[
              IconButton(
                icon: const Icon(Icons.save),
                onPressed: onSave,
              ),
            ]
          : null,
      onBack: onBack,
    );
  }
}
