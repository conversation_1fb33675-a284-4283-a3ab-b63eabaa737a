import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../constants/bp_colors.dart';

import '../models/dispenser_model.dart';
import '../controllers/dispenser_controller.dart';

/// Ultra compact dispenser selector
/// Minimal height for maximum screen space
class DispenserTabSelector extends ConsumerWidget {
  const DispenserTabSelector({
    super.key,
    this.onDispenserSelected,
  });
  final void Function(String dispenserId)? onDispenserSelected; // 优化：改为 String 类型

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final DispenserState state = ref.watch(dispenserControllerProvider);

    if (state.dispensers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.fromLTRB(8, 4, 8, 4),
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              offset: const Offset(0, 1),
              blurRadius: 2,
              spreadRadius: 0,
            ),
          ],
          border: Border.all(
            color: Colors.grey.withOpacity(0.1),
            width: 0.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: state.dispensers
              .asMap()
              .entries
              .map((MapEntry<int, Dispenser> entry) {
            final int index = entry.key;
            final Dispenser dispenser = entry.value;
            final bool isSelected = dispenser.id == state.selectedDispenserId;

            return SizedBox(
              width: 80, // 固定宽度
              child: _EnterpriseDispenserTab(
                dispenser: dispenser,
                isSelected: isSelected,
                onTap: () {
                  ref
                      .read(dispenserControllerProvider.notifier)
                      .selectDispenser(dispenser.id);
                  onDispenserSelected?.call(dispenser.id);
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

/// Clean and modern dispenser tab
class _EnterpriseDispenserTab extends StatelessWidget {
  const _EnterpriseDispenserTab({
    required this.dispenser,
    required this.isSelected,
    required this.onTap,
  });
  final Dispenser dispenser;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final int activeCount = _getActiveTransactionCount();

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(2),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          height: 32,
          decoration: BoxDecoration(
            color: isSelected ? BPColors.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(2),
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                // Status dot
                Container(
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: _getStatusColor(),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 6),

                // Station number
                Flexible(
                  child: Text(
                    'Disp${dispenser.id}',
                    style: TextStyle(
                      color:
                          isSelected ? Colors.white : const Color(0xFF1A1A1A),
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                      letterSpacing: -0.1,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Activity badge
                if (activeCount > 0) ...<Widget>[
                  const SizedBox(width: 4),
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.white.withOpacity(0.8)
                          : BPColors.warning,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Get status color based on dispenser state
  Color _getStatusColor() {
    if (!dispenser.isOnline) return BPColors.error;

    final int activeCount = _getActiveTransactionCount();
    if (activeCount > 0) return BPColors.warning;

    return BPColors.success;
  }

  /// Get active transaction count
  int _getActiveTransactionCount() {
    int count = 0;
    for (final PumpGroup pumpGroup in dispenser.pumpGroups) {
      count += pumpGroup.activeTransactionCount;
    }
    return count;
  }
}
