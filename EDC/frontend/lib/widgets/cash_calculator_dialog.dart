import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../constants/bp_colors.dart';
import '../theme/app_theme.dart';

/// Cash change calculator dialog
/// Used for quick calculation of cash payment change amount
class CashCalculatorDialog extends StatefulWidget {
  const CashCalculatorDialog({
    super.key,
    required this.totalAmount,
  });
  final double totalAmount;

  @override
  State<CashCalculatorDialog> createState() => _CashCalculatorDialogState();
}

class _CashCalculatorDialogState extends State<CashCalculatorDialog>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  double _receivedAmount = 0.0;
  double _changeAmount = 0.0;

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late AnimationController _buttonController;
  late Animation<double> _buttonAnimation;

  final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'id_ID',
    symbol: 'Rp ',
    decimalDigits: 0,
  );

  final NumberFormat _numberFormatter = NumberFormat('#,###', 'id_ID');

  @override
  void initState() {
    super.initState();

    // 初始化滑动动画控制器
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // 初始化按钮动画控制器
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _buttonAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.easeOutBack,
    ));

    // 启动动画
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 100), () {
      _buttonController.forward();
    });

    // 默认设置为应付金额（收足额）
    _setExactAmount();

    // 延迟显示键盘
    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) {
        _focusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _slideController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  /// 计算找零
  void _calculateChange() {
    _changeAmount = _receivedAmount - widget.totalAmount;
    setState(() {});
  }

  /// 输入金额变化
  void _onAmountChanged(String value) {
    // 移除所有非数字字符
    final String cleanValue = value.replaceAll(RegExp(r'[^\d.]'), '');
    _receivedAmount = double.tryParse(cleanValue) ?? 0.0;
    _calculateChange();
  }

  /// 快速设置收足额
  void _setExactAmount() {
    HapticFeedback.selectionClick();

    setState(() {
      _receivedAmount = widget.totalAmount;
      _textController.text = widget.totalAmount.toStringAsFixed(0);
      _calculateChange();
    });
  }

  /// 快速设置常用金额
  void _setQuickAmount(double amount) {
    HapticFeedback.selectionClick();

    setState(() {
      _receivedAmount = amount;
      _textController.text = amount.toStringAsFixed(0);
      _calculateChange();
    });
  }

  /// 清空输入
  void _clearInput() {
    HapticFeedback.mediumImpact();
    setState(() {
      _textController.clear();
      _receivedAmount = 0.0;
      _calculateChange();
    });
  }

  /// 关闭对话框
  void _closeDialog() async {
    await _slideController.reverse();
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;
    final double statusBarHeight = MediaQuery.of(context).padding.top;
    final double bottomPadding = MediaQuery.of(context).padding.bottom;

    return Material(
      color: Colors.black.withValues(alpha: 0.5),
      child: SlideTransition(
        position: _slideAnimation,
        child: Stack(
          children: <Widget>[
            // 主要内容
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: <Widget>[
                  // 状态栏占位
                  SizedBox(height: statusBarHeight),

                  // 标题栏
                  _buildCompactHeader(),

                  // 主要内容区域
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.fromLTRB(
                          16, 0, 16, 60), // 进一步减少底部空间
                      child: Column(
                        children: <Widget>[
                          const SizedBox(height: 8), // 减少顶部间距

                          // 金额信息卡片
                          _buildCompactAmountCard(),
                          const SizedBox(height: 12), // 减少间距

                          // 收到金额输入框
                          _buildCompactInputField(),
                          const SizedBox(height: 12), // 减少间距

                          // 快捷金额按钮
                          _buildCompactQuickAmounts(),
                          const SizedBox(height: 16), // 减少底部间距
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 悬浮操作按钮
            Positioned(
              left: 0,
              right: 0,
              bottom: bottomPadding,
              child: _buildCompactFloatingButtons(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建紧凑标题栏
  Widget _buildCompactHeader() {
    return Container(
      padding:
          const EdgeInsets.symmetric(horizontal: 16, vertical: 12), // 减少内边距
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Row(
        children: <Widget>[
          // 左侧图标和标题
          Expanded(
            child: Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.all(6), // 减少图标内边距
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: <Color>[
                        BPColors.primary,
                        BPColors.primary.withValues(alpha: 0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(10), // 减少圆角
                    boxShadow: <BoxShadow>[
                      BoxShadow(
                        color: BPColors.primary.withValues(alpha: 0.3),
                        offset: const Offset(0, 2),
                        blurRadius: 6,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.calculate_rounded,
                    color: Colors.white,
                    size: 20, // 减少图标大小
                  ),
                ),
                const SizedBox(width: 12), // 减少间距
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        'Cash Calculator',
                        style: EDCTextStyles.subTitle.copyWith(
                          fontSize: 16, // 减少字体大小
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        'Calculate change amount quickly',
                        style: EDCTextStyles.bodyText.copyWith(
                          fontSize: 11, // 减少字体大小
                          color: BPColors.neutral,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 右侧关闭按钮
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _closeDialog,
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(6), // 减少内边距
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.close_rounded,
                  color: Colors.grey[600],
                  size: 18, // 减少图标大小
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建紧凑金额信息卡片
  Widget _buildCompactAmountCard() {
    return Container(
      padding: const EdgeInsets.all(12), // 进一步减少内边距
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: <Color>[
            Colors.white,
            BPColors.primary.withValues(alpha: 0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12), // 减少圆角
        border: Border.all(
          color: BPColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            offset: const Offset(0, 2), // 减少阴影偏移
            blurRadius: 8, // 减少模糊半径
          ),
        ],
      ),
      child: Column(
        children: <Widget>[
          // 应付金额
          _buildCompactAmountRow(
            'Amount Due',
            widget.totalAmount,
            BPColors.primary,
            Icons.receipt_long_rounded,
          ),

          const SizedBox(height: 8), // 进一步减少间距

          // 分隔线
          Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: <Color>[
                  Colors.transparent,
                  Colors.grey[300]!,
                  Colors.transparent,
                ],
              ),
            ),
          ),

          const SizedBox(height: 8), // 进一步减少间距

          // 找零金额
          _buildCompactAmountRow(
            'Change Amount',
            _changeAmount,
            _changeAmount >= 0 ? BPColors.success : BPColors.warning,
            _changeAmount >= 0
                ? Icons.check_circle_rounded
                : Icons.warning_amber_rounded,
          ),
        ],
      ),
    );
  }

  /// 构建紧凑金额行
  Widget _buildCompactAmountRow(
    String label,
    double amount,
    Color color,
    IconData icon,
  ) {
    return Row(
      children: <Widget>[
        Container(
          padding: const EdgeInsets.all(6), // 进一步减少内边距
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8), // 减少圆角
            border: Border.all(
              color: color.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Icon(
            icon,
            color: color,
            size: 16, // 减少图标大小
          ),
        ),
        const SizedBox(width: 10), // 减少间距
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                label,
                style: EDCTextStyles.bodyText.copyWith(
                  fontSize: 13, // 增大字体大小
                  color: BPColors.neutral,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
              ),
              const SizedBox(height: 1), // 减少间距
              Text(
                _currencyFormatter.format(amount),
                style: EDCTextStyles.subTitle.copyWith(
                  fontSize: 20, // 增大字体大小
                  fontWeight: FontWeight.bold,
                  color: color,
                  letterSpacing: 0.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建紧凑输入框
  Widget _buildCompactInputField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14), // 减少圆角
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: BPColors.primary.withValues(alpha: 0.1),
            offset: const Offset(0, 3), // 减少阴影偏移
            blurRadius: 10, // 减少模糊半径
          ),
        ],
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14), // 减少圆角
          border: Border.all(
            color: _focusNode.hasFocus
                ? BPColors.primary
                : BPColors.primary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // 标签
            Padding(
              padding: const EdgeInsets.fromLTRB(14, 10, 14, 4), // 进一步减少内边距
              child: Row(
                children: <Widget>[
                  Container(
                    padding: const EdgeInsets.all(4), // 减少内边距
                    decoration: BoxDecoration(
                      color: BPColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.payments_rounded,
                      color: BPColors.primary,
                      size: 12, // 减少图标大小
                    ),
                  ),
                  const SizedBox(width: 6), // 减少间距
                  Text(
                    'Received Amount',
                    style: EDCTextStyles.bodyText.copyWith(
                      fontSize: 14, // 增大字体大小
                      color: BPColors.neutral,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.3,
                    ),
                  ),
                ],
              ),
            ),

            // 输入框
            Padding(
              padding: const EdgeInsets.fromLTRB(14, 0, 14, 10), // 进一步减少内边距
              child: Row(
                children: <Widget>[
                  // 货币符号
                  Text(
                    'Rp',
                    style: EDCTextStyles.subTitle.copyWith(
                      fontSize: 24, // 增大字体大小
                      fontWeight: FontWeight.bold,
                      color: BPColors.primary,
                    ),
                  ),
                  const SizedBox(width: 6), // 减少间距

                  // 输入框
                  Expanded(
                    child: TextField(
                      controller: _textController,
                      focusNode: _focusNode,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: <TextInputFormatter>[
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d*')),
                        LengthLimitingTextInputFormatter(12),
                      ],
                      onChanged: _onAmountChanged,
                      style: EDCTextStyles.subTitle.copyWith(
                        fontSize: 24, // 增大字体大小
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                        letterSpacing: 0.3,
                      ),
                      decoration: const InputDecoration(
                        hintText: '0',
                        hintStyle: TextStyle(
                          color: Colors.grey,
                          fontSize: 24, // 增大字体大小
                          fontWeight: FontWeight.w400,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),

                  // 清空按钮
                  if (_textController.text.isNotEmpty)
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _clearInput,
                        borderRadius: BorderRadius.circular(14),
                        child: Container(
                          padding: const EdgeInsets.all(5), // 减少内边距
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(14),
                          ),
                          child: Icon(
                            Icons.clear_rounded,
                            color: Colors.grey[600],
                            size: 14, // 减少图标大小
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建紧凑快捷金额按钮
  Widget _buildCompactQuickAmounts() {
    // 生成基于应付金额的智能快捷金额
    final Set<num> quickAmountsSet = <num>{
      // 向上取整到最近的1万
      (widget.totalAmount / 10000).ceil() * 10000,
      // 向上取整到最近的5万
      (widget.totalAmount / 50000).ceil() * 50000,
      // 向上取整到最近的10万
      (widget.totalAmount / 100000).ceil() * 100000,
    };

    // 如果去重后少于3个，添加一些常用的固定金额
    if (quickAmountsSet.length < 3) {
      quickAmountsSet.addAll(<num>[100000, 200000, 500000]);
    }

    final List<num> quickAmounts = quickAmountsSet.toList()
      ..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.only(left: 2),
          child: Text(
            'Quick Amounts',
            style: EDCTextStyles.bodyText.copyWith(
              fontSize: 15, // 增大字体大小
              fontWeight: FontWeight.bold,
              color: Colors.black87,
              letterSpacing: 0.3,
            ),
          ),
        ),
        const SizedBox(height: 6), // 减少间距
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: quickAmounts.map((num amount) {
            final bool isSelected = _receivedAmount == amount;

            return Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _setQuickAmount(amount.toDouble()),
                borderRadius: BorderRadius.circular(16),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(
                      horizontal: 14, vertical: 8), // 减少内边距
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(
                            colors: <Color>[
                              BPColors.primary,
                              BPColors.primary.withValues(alpha: 0.8)
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : null,
                    color: isSelected
                        ? null
                        : Colors.grey[50],
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected
                          ? BPColors.primary
                          : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: isSelected
                        ? <BoxShadow>[
                            BoxShadow(
                              color: BPColors.primary.withValues(alpha: 0.3),
                              offset: const Offset(0, 1),
                              blurRadius: 4, // 减少模糊半径
                            ),
                          ]
                        : null,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      if (isSelected) ...<Widget>[
                        const Icon(
                          Icons.check_rounded,
                          size: 12, // 减少图标大小
                          color: Colors.white,
                        ),
                        const SizedBox(width: 3), // 减少间距
                      ],
                      Text(
                        _numberFormatter.format(amount),
                        style: TextStyle(
                          fontSize: 13, // 增大字体大小
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? Colors.white
                              : Colors.grey[700],
                          letterSpacing: 0.3,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建紧凑悬浮操作按钮
  Widget _buildCompactFloatingButtons() {
    return AnimatedBuilder(
      animation: _buttonAnimation,
      builder: (BuildContext context, Widget? child) {
        return Transform.scale(
          scale: _buttonAnimation.value,
          child: Container(
            margin: const EdgeInsets.all(12), // 进一步减少外边距
            child: Row(
              children: <Widget>[
                // 只保留 Confirm 按钮，移除 Exact Amount 按钮
                Expanded(
                  child: Container(
                    height: 52, // 增加高度以避免文字被遮挡
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12), // 减少圆角
                      boxShadow: <BoxShadow>[
                        BoxShadow(
                          color: (_changeAmount >= 0
                                  ? BPColors.primary
                                  : BPColors.neutral)
                              .withValues(alpha: 0.3),
                          offset: const Offset(0, 2), // 减少阴影偏移
                          blurRadius: 8, // 减少模糊半径
                        ),
                      ],
                    ),
                    child: ElevatedButton.icon(
                      onPressed: _changeAmount >= 0
                          ? () async {
                              HapticFeedback.selectionClick();
                              await _slideController.reverse();
                              if (mounted) {
                                Navigator.of(context).pop(<String, double>{
                                  'receivedAmount': _receivedAmount,
                                  'changeAmount': _changeAmount,
                                });
                              }
                            }
                          : null,
                      icon: Icon(
                        _changeAmount >= 0
                            ? Icons.done_rounded
                            : Icons.warning_amber_rounded,
                        size: 18, // 增大图标大小
                        color: Colors.white,
                      ),
                      label: Text(
                        'Confirm Payment',
                        style: EDCTextStyles.bodyText.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontSize: 15, // 增大字体大小
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _changeAmount >= 0
                            ? BPColors.primary
                            : BPColors.neutral,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14), // 减少圆角
                        ),
                        elevation: 0,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 显示现金计算器对话框的辅助方法
Future<Map<String, double>?> showCashCalculatorDialog(
  BuildContext context,
  double totalAmount,
) async {
  return showGeneralDialog<Map<String, double>>(
    context: context,
    barrierDismissible: true,
    barrierLabel: 'Cash Calculator',
    barrierColor: Colors.transparent,
    transitionDuration: const Duration(milliseconds: 300),
    pageBuilder: (BuildContext context, Animation<double> animation,
        Animation<double> secondaryAnimation) {
      return CashCalculatorDialog(totalAmount: totalAmount);
    },
  );
}
