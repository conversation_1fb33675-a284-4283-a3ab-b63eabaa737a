import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/order.dart';
import 'order_status_badge.dart';

/// Order list item (optimized layout)
/// Clearly displays core order information: order ID, status, time and amount
class OrderListItem extends StatelessWidget {
  const OrderListItem({
    super.key,
    required this.order,
    this.onTap,
    this.onLongPress,
  });
  final Order order;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  @override
  Widget build(BuildContext context) {
    final DateFormat dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final NumberFormat currencyFormat = NumberFormat(
        '#,##0.00', 'en_US'); // Use locale for formatting if needed
    // BP green color
    const Color bpGreenColor = Color(0xFF2E8B57);

    return Card(
      elevation: 0,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      color: Colors.white,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(8), // Match Card shape
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // --- First row: Order ID and status ---
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start, // Align items top
                children: <Widget>[
                  // Order ID (takes available space)
                  Expanded(
                    child: Text(
                      'Order #${order.orderId}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16, // Slightly larger font for ID
                      ),
                      maxLines: 2, // Allow wrapping if extremely long
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8), // Spacing between ID and Badge
                  // Status badge
                  OrderStatusBadge(status: order.status),
                ],
              ),
              const SizedBox(height: 12), // Vertical spacing

              // --- Second row: Creation time and amount ---
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  // Creation time
                  Text(
                    dateFormat.format(order.createTime),
                    style: const TextStyle(fontSize: 13, color: Colors.grey),
                  ),
                  // Order amount
                  Text(
                    // Example: $1,234.56
                    'Rp${currencyFormat.format(order.amount)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16, // Make amount stand out
                      color: bpGreenColor, // BP green color
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 保留 OrderStatusBadge 的导入和使用
// 移除 _buildInfoRow 和 _maskPhoneNumber 方法，因为它们不再需要
