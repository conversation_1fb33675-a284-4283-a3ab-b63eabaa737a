import 'package:flutter/foundation.dart';
import 'dart:async';

import 'bos_transaction_polling_service.dart';
import 'shift_service.dart';

/// 全局轮询服务管理器
/// 负责管理应用级别的轮询服务，确保轮询不会因页面切换而停止
class GlobalPollingService {
  static final GlobalPollingService _instance = GlobalPollingService._internal();
  factory GlobalPollingService() => _instance;
  GlobalPollingService._internal();

  BosTransactionPollingService? _pollingService;
  bool _isStarted = false;

  /// 启动全局轮询服务
  void startGlobalPolling() {
    if (_isStarted) {
      debugPrint('⚠️ 全局轮询服务已在运行中');
      return;
    }

    try {
      debugPrint('🚀 正在启动全局班次状态轮询服务...');

      _pollingService = BosTransactionPollingService(
        // 只启用班次状态轮询，交易轮询由页面级服务处理
        enableTransactionPolling: false, // 禁用交易轮询
        enableShiftPolling: true, // 启用班次状态轮询
        shiftPollingInterval: const Duration(seconds: 15), // 班次状态轮询15秒
      );

      // 设置班次状态变化回调
      _pollingService!.onShiftStatusChanged = (ShiftInfo? shift) {
        debugPrint('🌐 全局班次状态变化检测到！');
        debugPrint('   班次ID: ${shift?.shiftId ?? '无'}');
        debugPrint('   状态: ${shift?.status.name ?? '无'}');
        debugPrint('   操作员: ${shift?.operatorId ?? '无'}');
        // 班次状态变化会自动通过ShiftService的流传播到UI组件
      };

      _pollingService!.onError = (String error) {
        debugPrint('❌ 全局轮询错误: $error');
      };

      // 启动轮询
      _pollingService!.startPolling();
      _isStarted = true;

      debugPrint('✅ 全局班次状态轮询已启动');
      debugPrint('   - 轮询间隔: 15秒');
      debugPrint('   - 交易轮询: 禁用');
      debugPrint('   - 班次轮询: 启用');
    } catch (e) {
      debugPrint('❌ 全局轮询服务启动失败: $e');
      debugPrint('   错误详情: ${e.toString()}');
    }
  }

  /// 停止全局轮询服务
  void stopGlobalPolling() {
    if (!_isStarted || _pollingService == null) {
      return;
    }

    try {
      _pollingService!.stopPolling();
      _pollingService = null;
      _isStarted = false;
      debugPrint('⏹️ 全局班次状态轮询已停止');
    } catch (e) {
      debugPrint('❌ 停止全局轮询服务失败: $e');
    }
  }

  /// 获取轮询服务状态
  bool get isRunning => _isStarted;

  /// 手动触发一次轮询
  Future<void> pollOnce() async {
    if (_pollingService != null) {
      await _pollingService!.pollOnce();
    }
  }

  /// 获取服务状态信息
  Map<String, dynamic> getServiceStatus() {
    if (_pollingService != null) {
      final Map<String, dynamic> status = _pollingService!.getServiceStatus();
      status['global_service_running'] = _isStarted;
      return status;
    }
    return {
      'is_running': false,
      'global_service_running': _isStarted,
      'message': 'Service not initialized'
    };
  }

  /// 检查轮询服务的详细状态
  Future<Map<String, dynamic>> checkDetailedStatus() async {
    final Map<String, dynamic> status = {
      'global_polling_started': _isStarted,
      'polling_service_exists': _pollingService != null,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (_pollingService != null) {
      status.addAll(_pollingService!.getServiceStatus());
    }

    // 检查ShiftService状态
    try {
      final ShiftService shiftService = ShiftService();
      status['shift_service_initialized'] = shiftService.isInitialized;
      status['shift_service_station_id'] = shiftService.currentStationId;
      status['shift_service_has_active_shift'] = shiftService.hasActiveShift;
      status['current_shift_status'] = shiftService.currentStatus.name;
    } catch (e) {
      status['shift_service_error'] = e.toString();
    }

    return status;
  }
}
