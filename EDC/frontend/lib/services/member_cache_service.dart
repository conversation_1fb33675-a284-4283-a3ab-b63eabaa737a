import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/customer_type_constants.dart';
import '../models/member_model.dart';

/// 会员内存缓存服务
/// 用于在交易流程中临时存储会员信息，直到订单结算完成后消费
class MemberCacheService extends ChangeNotifier {
  factory MemberCacheService() => _instance;
  MemberCacheService._internal();
  // 单例模式
  static final MemberCacheService _instance = MemberCacheService._internal();

  // 缓存的会员信息
  Member? _cachedMember;

  // 缓存创建时间，用于判断缓存是否过期
  DateTime? _cacheTime;

  // 缓存有效期（10分钟）
  static const Duration _cacheExpiration = Duration(minutes: 10);

  /// 获取缓存的会员信息
  Member? get cachedMember {
    if (_cachedMember != null && _isCacheValid()) {
      debugPrint('✅ 获取有效的缓存会员信息: ${_cachedMember!.name} (${_cachedMember!.id})');
      final CustomerType customerType = CustomerTypeUtils.detectCustomerType(_cachedMember!.metadata, memberId: _cachedMember!.id);
      debugPrint('   客户类型: ${customerType.value}');
      return _cachedMember;
    }

    // 如果缓存过期，自动清除
    if (_cachedMember != null && !_isCacheValid()) {
      debugPrint('🗑️ 会员缓存已过期，自动清除: ${_cachedMember!.name}');
      clearCache();
    } else if (_cachedMember == null) {
      debugPrint('❌ 没有缓存的会员信息');
    }

    return null;
  }

  /// 检查是否有有效的缓存会员信息
  bool get hasCachedMember {
    return cachedMember != null;
  }

  /// 获取缓存时间
  DateTime? get cacheTime => _cacheTime;

  /// 缓存会员信息
  void cacheMember(Member member) {
    _cachedMember = member;
    _cacheTime = DateTime.now();

    // 检测客户类型
    final CustomerType customerType = CustomerTypeUtils.detectCustomerType(member.metadata, memberId: member.id);

    debugPrint('💾 缓存会员信息: ${member.name} (${member.phone})');
    debugPrint('   会员ID: ${member.id}');
    debugPrint('   会员等级: ${member.levelDisplayName}');
    debugPrint('   客户类型: ${customerType.value}');
    debugPrint('   缓存时间: ${_cacheTime!.toIso8601String()}');
    debugPrint('   完整metadata: ${member.metadata}');

    // 通知监听者
    notifyListeners();
  }

  /// 清除缓存
  void clearCache() {
    if (_cachedMember != null) {
      debugPrint('🗑️ 清除会员缓存: ${_cachedMember!.name}');
    }

    _cachedMember = null;
    _cacheTime = null;

    // 通知监听者
    notifyListeners();
  }

  /// 消费缓存（用于订单结算完成后）
  Member? consumeCache() {
    final Member? member = _cachedMember;
    if (member != null) {
      debugPrint('🍽️ 消费会员缓存: ${member.name}');
      clearCache();
    }
    return member;
  }

  /// 更新缓存的会员信息
  void updateCachedMember(Member updatedMember) {
    if (_cachedMember != null && _cachedMember!.id == updatedMember.id) {
      _cachedMember = updatedMember;
      _cacheTime = DateTime.now(); // 更新缓存时间

      debugPrint('🔄 更新会员缓存: ${updatedMember.name}');
      notifyListeners();
    }
  }

  /// 检查缓存是否有效（未过期）
  bool _isCacheValid() {
    if (_cacheTime == null) return false;

    final DateTime now = DateTime.now();
    final Duration difference = now.difference(_cacheTime!);

    return difference < _cacheExpiration;
  }

  /// 获取缓存剩余有效时间
  Duration? getRemainingCacheTime() {
    if (_cacheTime == null) return null;

    final DateTime now = DateTime.now();
    final Duration elapsed = now.difference(_cacheTime!);
    final Duration remaining = _cacheExpiration - elapsed;

    return remaining.isNegative ? null : remaining;
  }

  /// 检查特定会员是否已被缓存
  bool isMemberCached(String memberId) {
    return _cachedMember?.id == memberId && hasCachedMember;
  }

  /// 获取缓存信息的调试字符串
  String getCacheDebugInfo() {
    if (!hasCachedMember) {
      return 'No cached member';
    }

    final Member member = _cachedMember!;
    final Duration? remaining = getRemainingCacheTime();

    return '''
Cached Member: ${member.name} (${member.phone})
Member ID: ${member.id}
Level: ${member.levelDisplayName}
Cached at: ${_cacheTime!.toIso8601String()}
Remaining time: ${remaining != null ? '${remaining.inMinutes} minutes' : 'Expired'}
''';
  }

  /// 强制刷新缓存时间（延长有效期）
  void refreshCacheTime() {
    if (_cachedMember != null) {
      _cacheTime = DateTime.now();
      debugPrint('🔄 刷新会员缓存时间: ${_cachedMember!.name}');
      notifyListeners();
    }
  }

  /// 清理过期缓存（可以在应用启动时调用）
  void cleanupExpiredCache() {
    if (_cachedMember != null && !_isCacheValid()) {
      debugPrint('🧹 清理过期的会员缓存');
      clearCache();
    }
  }
}

/// 全局会员缓存服务实例
final MemberCacheService memberCacheService = MemberCacheService();

/// Riverpod Provider for MemberCacheService
final memberCacheServiceProvider = ChangeNotifierProvider<MemberCacheService>((ref) {
  return memberCacheService;
});
