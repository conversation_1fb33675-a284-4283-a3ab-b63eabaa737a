import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';
import 'dart:math';
import '../services/log_service.dart';

/// 设备ID服务
///
/// 使用 UUID + 持久化存储 的方案生成设备唯一标识符
///
/// **新方案说明：**
/// 1. 首次启动时生成 UUID 作为设备ID
/// 2. 持久化保存到 SharedPreferences
/// 3. 后续启动直接读取缓存的ID
/// 4. 添加设备指纹作为辅助验证
///
/// **优势：**
/// - 真正的唯一性（UUID v4）
/// - 跨应用重装保持一致（除非清除应用数据）
/// - 不依赖平台敏感API
/// - 符合隐私保护要求
class DeviceIdService {
  DeviceIdService._();

  static DeviceIdService? _instance;
  static DeviceIdService get instance => _instance ??= DeviceIdService._();

  String? _deviceId;
  String? _deviceFingerprint;
  bool _isInitialized = false;
  
  /// 获取设备ID（如果未初始化则返回null）
  String? get deviceId => _deviceId;

  /// 获取设备指纹（用于辅助验证）
  String? get deviceFingerprint => _deviceFingerprint;

  /// 获取简写的设备ID（用于UI显示）
  ///
  /// 格式：前8位-后4位，例如：12345678-abcd
  /// 如果设备ID为空或格式不正确，返回 "Unknown"
  String get shortDeviceId {
    if (_deviceId == null || _deviceId!.isEmpty) {
      return 'Unknown';
    }

    // 如果是UUID格式（包含连字符）
    if (_deviceId!.contains('-') && _deviceId!.length >= 13) {
      final String cleanId = _deviceId!.replaceAll('-', '');
      if (cleanId.length >= 12) {
        return '${cleanId.substring(0, 8)}-${cleanId.substring(cleanId.length - 4)}';
      }
    }

    // 如果是其他格式，取前8位和后4位
    if (_deviceId!.length >= 12) {
      return '${_deviceId!.substring(0, 8)}-${_deviceId!.substring(_deviceId!.length - 4)}';
    }

    // 如果长度不够，直接返回原始ID
    return _deviceId!;
  }

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化设备ID服务
  ///
  /// 在应用启动时调用，生成或获取设备唯一标识符
  /// 并打印相关信息用于调试
  Future<void> initialize() async {
    if (_isInitialized) {
      logInfo('DeviceIdService', '设备ID服务已初始化，当前ID: $_deviceId');
      return;
    }

    try {
      logInfo('DeviceIdService', '开始初始化设备ID服务...');

      final SharedPreferences prefs = await SharedPreferences.getInstance();

      // 获取或生成设备ID
      _deviceId = await _getOrGenerateDeviceId(prefs);

      // 生成设备指纹（用于辅助验证）
      _deviceFingerprint = await _generateDeviceFingerprint();

      _isInitialized = true;

      // 打印设备ID信息
      logInfo('DeviceIdService', '✅ 设备ID初始化完成');
      logInfo('DeviceIdService', '📱 设备ID: $_deviceId');
      logInfo('DeviceIdService', '📏 设备ID长度: ${_deviceId?.length ?? 0} 字符');
      logInfo('DeviceIdService', '🔍 设备指纹: $_deviceFingerprint');

      // 检查是否为UUID格式
      if (_deviceId?.contains('-') == true && _deviceId?.length == 36) {
        logInfo('DeviceIdService', '✅ 使用UUID格式的设备ID');
      } else if (_deviceId?.startsWith('EDC_') == true) {
        logInfo('DeviceIdService', '⚠️  使用时间戳格式的设备ID');
      } else {
        logInfo('DeviceIdService', '❓ 未知格式的设备ID');
      }

    } catch (e, stackTrace) {
      logError('DeviceIdService', '设备ID初始化失败: $e', stackTrace);
      // 即使失败也标记为已初始化，避免重复尝试
      _isInitialized = true;
      _deviceId = 'ERROR_${DateTime.now().millisecondsSinceEpoch}';
      _deviceFingerprint = 'ERROR';
    }
  }
  
  /// 获取或生成设备ID
  ///
  /// 新的设备ID策略：
  /// 1. 优先从缓存读取已保存的UUID
  /// 2. 如果没有缓存，生成新的UUID并保存
  /// 3. UUID确保真正的唯一性，不依赖平台API
  ///
  /// [prefs] SharedPreferences实例，用于缓存设备ID
  /// 返回唯一的设备标识符
  Future<String> _getOrGenerateDeviceId(SharedPreferences prefs) async {
    const String deviceIdKey = 'edc_device_uuid';

    // 1. 尝试从缓存获取已保存的UUID
    final String? cachedDeviceId = prefs.getString(deviceIdKey);
    if (cachedDeviceId?.isNotEmpty == true) {
      logInfo('DeviceIdService', '使用缓存的设备UUID: $cachedDeviceId');
      return cachedDeviceId!;
    }

    // 2. 生成新的UUID
    final String newDeviceId = _generateUUID();

    // 3. 保存到缓存
    await prefs.setString(deviceIdKey, newDeviceId);
    logInfo('DeviceIdService', '生成新的设备UUID: $newDeviceId');

    return newDeviceId;
  }

  /// 生成设备指纹
  ///
  /// 设备指纹用于辅助验证，包含设备的基本信息
  /// 不用于唯一标识，仅用于调试和验证
  Future<String> _generateDeviceFingerprint() async {
    try {
      final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      final List<String> fingerprintParts = <String>[];

      if (Platform.isAndroid) {
        final AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
        fingerprintParts.addAll(<String>[
          'Android',
          androidInfo.brand,
          androidInfo.model,
          androidInfo.version.release,
        ]);

        logInfo('DeviceIdService', 'Android设备指纹信息:');
        logInfo('DeviceIdService', '  品牌: ${androidInfo.brand}');
        logInfo('DeviceIdService', '  型号: ${androidInfo.model}');
        logInfo('DeviceIdService', '  Android版本: ${androidInfo.version.release}');

      } else if (Platform.isIOS) {
        final IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        fingerprintParts.addAll(<String>[
          'iOS',
          iosInfo.model,
          iosInfo.systemVersion,
        ]);

        logInfo('DeviceIdService', 'iOS设备指纹信息:');
        logInfo('DeviceIdService', '  型号: ${iosInfo.model}');
        logInfo('DeviceIdService', '  系统版本: ${iosInfo.systemVersion}');
      }

      return fingerprintParts.where((String part) => part.isNotEmpty).join('_');

    } catch (e) {
      logWarning('DeviceIdService', '设备指纹生成失败: $e');
      return 'UNKNOWN_DEVICE';
    }
  }
  
  /// 生成UUID格式的设备ID
  ///
  /// 使用 Dart 内置的随机数生成器创建 UUID v4 格式的标识符
  /// 格式：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  ///
  /// 返回UUID格式的唯一设备标识符
  String _generateUUID() {
    final Random random = Random();

    // 生成32个十六进制字符
    String generateHex(int length) {
      const String chars = '0123456789abcdef';
      return List<String>.generate(length, (int index) => chars[random.nextInt(16)]).join();
    }

    // UUID v4 格式：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    final String uuid = '${generateHex(8)}-${generateHex(4)}-4${generateHex(3)}-${(8 + random.nextInt(4)).toRadixString(16)}${generateHex(3)}-${generateHex(12)}';

    return uuid;
  }
  
  /// 强制重新生成设备ID（用于测试或重置）
  Future<void> regenerateDeviceId() async {
    logInfo('DeviceIdService', '强制重新生成设备ID...');
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('cached_device_id');
    _deviceId = null;
    _isInitialized = false;
    await initialize();
  }
}
