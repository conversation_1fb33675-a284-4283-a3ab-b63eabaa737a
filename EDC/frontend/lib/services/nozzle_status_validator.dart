import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../models/fuel_transaction.dart';
import '../models/dispenser_model.dart';
import '../services/api/api_service.dart';
import '../services/api/fuel_transaction_api.dart';
import '../services/bos_transaction_polling_service.dart';

/// Nozzle状态验证服务
/// 当FCC报告complete状态时，主动查询BOS确认是否存在pending交易
/// 根据查询结果决定nozzle的真实业务状态
class NozzleStatusValidator {
  NozzleStatusValidator({
    ApiService? apiService,
    BosTransactionPollingService? bosPollingService,
    Duration cacheTimeout = const Duration(seconds: 10), // 缓存10秒避免频繁查询
  })  : _apiService = apiService ?? ApiService(),
        _bosPollingService = bosPollingService,
        _cacheTimeout = cacheTimeout;

  final ApiService _apiService;
  final BosTransactionPollingService? _bosPollingService;
  final Duration _cacheTimeout;

  /// 状态验证结果缓存
  final Map<String, _ValidationResult> _validationCache = <String, _ValidationResult>{};

  /// 验证nozzle的真实状态
  /// 当FCC状态为complete时，查询BOS确认是否存在pending交易
  /// 返回最终应该显示的状态
  Future<NozzleStatusValidationResult> validateNozzleStatus(
    String nozzleId,
    NozzleStatus fccStatus, {
    bool useShortCache = false, // 是否使用短缓存（用于同步验证）
  }) async {
    try {
      debugPrint('🔍 [NozzleStatusValidator] 开始验证 nozzle $nozzleId 状态');
      debugPrint('   FCC状态: ${fccStatus.name}');
      debugPrint('   使用短缓存: $useShortCache');

      // 如果FCC状态不是complete，直接返回FCC状态
      if (fccStatus != NozzleStatus.complete) {
        debugPrint('   ✅ FCC状态非complete，直接使用: ${fccStatus.name}');
        return NozzleStatusValidationResult(
          finalStatus: fccStatus,
          hasPendingTransaction: false,
          reason: 'FCC状态非complete，无需验证',
        );
      }

      // 检查缓存（同步验证时使用更短的缓存时间）
      final _ValidationResult? cachedResult = _getCachedResult(nozzleId, useShortCache: useShortCache);
      if (cachedResult != null) {
        debugPrint('   📋 使用缓存结果: ${cachedResult.hasPendingTransaction ? "有pending交易" : "无pending交易"}');
        return _buildResult(cachedResult.hasPendingTransaction, '使用缓存结果');
      }

      // 查询BOS确认是否存在pending交易
      final bool hasPendingTransaction = await _checkPendingTransaction(nozzleId);

      // 缓存结果（同步验证时使用更短的缓存时间）
      _cacheResult(nozzleId, hasPendingTransaction, useShortCache: useShortCache);

      final NozzleStatusValidationResult result = _buildResult(
        hasPendingTransaction,
        hasPendingTransaction ? 'BOS确认存在pending交易' : 'BOS确认无pending交易',
      );

      debugPrint('   🎯 验证完成: ${result.finalStatus.name} (${result.reason})');
      return result;

    } catch (e) {
      debugPrint('   ❌ 验证失败: $e');
      // 验证失败时，保守地返回complete状态
      return NozzleStatusValidationResult(
        finalStatus: NozzleStatus.complete,
        hasPendingTransaction: false,
        reason: '验证失败，保守返回complete: $e',
      );
    }
  }

  /// 查询BOS确认指定nozzle是否存在pending交易
  Future<bool> _checkPendingTransaction(String nozzleId) async {
    try {
      // 🎯 优先使用BOS轮询服务的精确查询方法
      if (_bosPollingService != null) {
        debugPrint('   🚀 使用BOS轮询服务查询nozzle $nozzleId');
        final List<FuelTransaction> transactions =
            await _bosPollingService!.queryNozzlePendingTransactions(nozzleId);
        return transactions.isNotEmpty;
      }

      // 回退到直接API查询
      debugPrint('   📡 使用直接API查询nozzle $nozzleId');
      final FuelTransactionApi fuelTransactionApi = _apiService.fuelTransactionApi;

      // 获取查询时间范围（最近1小时）
      final DateTime now = DateTime.now();
      final DateTime oneHourAgo = now.subtract(const Duration(hours: 1));

      final FuelTransactionQueryParams queryParams = FuelTransactionQueryParams(
        nozzleId: nozzleId, // 使用新增的nozzle_id筛选
        status: 'pending',
        dateFrom: oneHourAgo.toIso8601String().split('T')[0], // 格式: 2025-07-22
        dateTo: now.toIso8601String().split('T')[0],
        page: 1,
        limit: 1, // 只需要确认是否存在，不需要获取所有数据
        sortBy: 'created_at',
        sortDir: 'desc',
      );

      debugPrint('   📤 BOS查询参数: ${queryParams.toJson()}');

      final FuelTransactionResponse response =
          await fuelTransactionApi.getFuelTransactions(queryParams);

      final bool hasPending = response.items.isNotEmpty;

      if (hasPending) {
        debugPrint('   ✅ 找到 ${response.items.length} 条pending交易');
        for (final FuelTransaction transaction in response.items) {
          debugPrint('     - 交易 ${transaction.transactionNumber}: volume=${transaction.volume}L, amount=${transaction.amount}');
        }
      } else {
        debugPrint('   📭 未找到pending交易');
      }

      return hasPending;

    } catch (e) {
      debugPrint('   ❌ BOS查询失败: $e');
      // 查询失败时，保守地认为有pending交易
      return true;
    }
  }

  /// 构建验证结果
  NozzleStatusValidationResult _buildResult(bool hasPendingTransaction, String reason) {
    if (hasPendingTransaction) {
      // 有pending交易，显示为complete状态（Tap to Pay）
      return NozzleStatusValidationResult(
        finalStatus: NozzleStatus.complete,
        hasPendingTransaction: true,
        reason: reason,
      );
    } else {
      // 无pending交易，显示为idle状态（Ready）
      return NozzleStatusValidationResult(
        finalStatus: NozzleStatus.idle,
        hasPendingTransaction: false,
        reason: reason,
      );
    }
  }

  /// 获取缓存结果
  _ValidationResult? _getCachedResult(String nozzleId, {bool useShortCache = false}) {
    final _ValidationResult? cached = _validationCache[nozzleId];
    if (cached != null) {
      final DateTime now = DateTime.now();
      // 同步验证时使用更短的缓存时间（2秒），避免过时数据
      final Duration effectiveTimeout = useShortCache
          ? const Duration(seconds: 2)
          : _cacheTimeout;

      final DateTime effectiveExpireTime = cached.cacheTime.add(effectiveTimeout);

      if (now.isBefore(effectiveExpireTime)) {
        final int remainingSeconds = effectiveExpireTime.difference(now).inSeconds;
        debugPrint('   📋 缓存命中: ${cached.hasPendingTransaction ? "有pending" : "无pending"} (${remainingSeconds}s剩余)');
        return cached;
      }
    }

    // 清理过期缓存
    if (cached != null) {
      _validationCache.remove(nozzleId);
      debugPrint('   🧹 清理过期缓存: $nozzleId');
    }
    return null;
  }

  /// 缓存验证结果
  void _cacheResult(String nozzleId, bool hasPendingTransaction, {bool useShortCache = false}) {
    final Duration cacheTime = useShortCache
        ? const Duration(seconds: 2)  // 同步验证使用短缓存
        : _cacheTimeout;              // 异步验证使用长缓存

    _validationCache[nozzleId] = _ValidationResult(
      hasPendingTransaction: hasPendingTransaction,
      expireTime: DateTime.now().add(cacheTime),
      cacheTime: DateTime.now(),
    );

    debugPrint('   💾 缓存结果: $nozzleId -> ${hasPendingTransaction ? "有pending" : "无pending"} (${cacheTime.inSeconds}s)');
  }

  /// 清理所有缓存
  void clearCache() {
    final int cacheSize = _validationCache.length;
    _validationCache.clear();
    debugPrint('🧹 [NozzleStatusValidator] 清理缓存: $cacheSize 条记录');
  }

  /// 清理指定nozzle的缓存
  void clearNozzleCache(String nozzleId) {
    if (_validationCache.remove(nozzleId) != null) {
      debugPrint('🧹 [NozzleStatusValidator] 清理nozzle $nozzleId 缓存');
    }
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    final DateTime now = DateTime.now();
    int validCount = 0;
    int expiredCount = 0;

    for (final _ValidationResult result in _validationCache.values) {
      if (now.isBefore(result.expireTime)) {
        validCount++;
      } else {
        expiredCount++;
      }
    }

    return <String, dynamic>{
      'total': _validationCache.length,
      'valid': validCount,
      'expired': expiredCount,
    };
  }

  /// 🔍 调试方法：打印详细的验证统计信息
  void printValidationStats() {
    final Map<String, dynamic> stats = getCacheStats();
    debugPrint('📊 [NozzleStatusValidator] 验证统计信息:');
    debugPrint('   总缓存数: ${stats['total']}');
    debugPrint('   有效缓存: ${stats['valid']}');
    debugPrint('   过期缓存: ${stats['expired']}');

    if (_validationCache.isNotEmpty) {
      debugPrint('   缓存详情:');
      final DateTime now = DateTime.now();
      _validationCache.forEach((String nozzleId, _ValidationResult result) {
        final bool isValid = now.isBefore(result.expireTime);
        final int remainingSeconds = isValid
            ? result.expireTime.difference(now).inSeconds
            : 0;
        debugPrint('     - $nozzleId: ${result.hasPendingTransaction ? "有pending" : "无pending"} (${isValid ? "${remainingSeconds}s剩余" : "已过期"})');
      });
    }
  }
}

/// 缓存结果内部类
class _ValidationResult {
  const _ValidationResult({
    required this.hasPendingTransaction,
    required this.expireTime,
    required this.cacheTime,
  });

  final bool hasPendingTransaction;
  final DateTime expireTime;
  final DateTime cacheTime; // 新增：缓存创建时间，用于计算有效期
}

/// Nozzle状态验证结果
class NozzleStatusValidationResult {
  const NozzleStatusValidationResult({
    required this.finalStatus,
    required this.hasPendingTransaction,
    required this.reason,
  });

  /// 最终应该显示的状态
  final NozzleStatus finalStatus;
  
  /// 是否存在pending交易
  final bool hasPendingTransaction;
  
  /// 验证原因/说明
  final String reason;

  /// 是否应该显示"Tap to Pay"
  bool get shouldShowTapToPay => finalStatus == NozzleStatus.complete && hasPendingTransaction;

  /// 是否应该显示"Ready"
  bool get shouldShowReady => finalStatus == NozzleStatus.idle && !hasPendingTransaction;

  @override
  String toString() {
    return 'NozzleStatusValidationResult(finalStatus: ${finalStatus.name}, hasPendingTransaction: $hasPendingTransaction, reason: $reason)';
  }
}

/// Provider for NozzleStatusValidator
final nozzleStatusValidatorProvider = Provider<NozzleStatusValidator>((ref) {
  return NozzleStatusValidator(
    // 注意：这里不直接注入BosTransactionPollingService，因为它可能导致循环依赖
    // 在实际使用中，可以通过其他方式获取BOS轮询服务实例
  );
});
