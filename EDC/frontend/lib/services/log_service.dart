import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

/// 日志服务类
/// 提供控制台和文件双重输出功能
/// debug模式下保存到电脑，生产模式下保存到设备
class LogService {
  LogService._internal();
  static LogService? _instance;
  static LogService get instance => _instance ??= LogService._internal();

  // 日志配置
  static const String _logFileName = 'edc_app.log';
  static const int _maxLogFileSize = 5 * 1024 * 1024; // 5MB
  static const int _maxLogFiles = 3; // 保留最多3个日志文件

  // 日志格式化器
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss.SSS');

  // 日志写入器
  File? _logFile;
  IOSink? _logSink;

  // 初始化状态
  bool _isInitialized = false;
  bool _isWriting = false; // 写入状态标志，防止并发写入

  // 根日志记录器
  final Logger _rootLogger = Logger.root;

  // 保存原始的 debugPrint 函数，用于恢复
  void Function(String?, {int? wrapWidth})? _originalDebugPrint;

  // 定期flush计时器
  Timer? _flushTimer;

  /// 初始化日志服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 设置日志级别
      Logger.root.level = kDebugMode ? Level.ALL : Level.INFO;

      // 初始化文件输出
      await _initializeFileOutput();

      // 设置日志监听器
      _setupLogListener();

      // 覆盖 debugPrint 函数
      _overrideDebugPrint();

      // 启动定期flush
      _startPeriodicFlush();

      _isInitialized = true;

      // 记录初始化成功
      info('LogService', '日志服务初始化成功');
      info('LogService', '日志文件路径: ${_logFile?.path ?? "未设置"}');
      info('LogService', '调试模式: $kDebugMode');
      info('LogService', 'debugPrint 已覆盖，所有调试输出将自动保存到文件');
    } catch (e, stackTrace) {
      // 使用原始的 debugPrint，因为此时覆盖可能还没生效
      final void Function(String? p1, {int? wrapWidth}) originalPrint =
          _originalDebugPrint ?? debugPrint;
      originalPrint('❌ LogService初始化失败: $e');
      originalPrint('❌ 堆栈跟踪: $stackTrace');
    }
  }

  /// 初始化文件输出
  Future<void> _initializeFileOutput() async {
    try {
      Directory logDir;

      if (kDebugMode) {
        // Debug模式：保存到电脑上（应用程序目录）
        logDir = await getApplicationDocumentsDirectory();
        // 创建logs子目录
        logDir = Directory('${logDir.path}/edc_logs');
      } else {
        // 生产模式：保存到设备上（应用程序的支持目录）
        logDir = await getApplicationSupportDirectory();
        // 创建logs子目录
        logDir = Directory('${logDir.path}/logs');
      }

      // 确保目录存在
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }

      // 创建日志文件
      _logFile = File('${logDir.path}/$_logFileName');

      // 检查文件大小，如果太大则轮转
      await _rotateLogFileIfNeeded();

      // 打开文件写入流
      _logSink = _logFile!.openWrite(mode: FileMode.append);
    } catch (e) {
      final void Function(String? p1, {int? wrapWidth}) originalPrint =
          _originalDebugPrint ?? debugPrint;
      originalPrint('❌ 初始化日志文件失败: $e');
      // 即使文件初始化失败，也继续使用控制台输出
    }
  }

  /// 设置日志监听器
  void _setupLogListener() {
    _rootLogger.onRecord.listen((LogRecord record) {
      final String message = _formatLogMessage(record);

      // 输出到控制台
      _outputToConsole(record, message);

      // 输出到文件
      _outputToFile(message);
    });
  }

  /// 覆盖 debugPrint 函数，使所有 debugPrint 调用都通过日志服务
  void _overrideDebugPrint() {
    // 保存原始的 debugPrint 函数，只保存一次
    _originalDebugPrint ??= debugPrint;

    // 覆盖 debugPrint
    debugPrint = (String? message, {int? wrapWidth}) {
      if (message != null) {
        // 直接输出到文件，避免通过Logger再次触发StreamController
        final String timestamp = _dateFormat.format(DateTime.now());
        final String formattedMessage =
            '$timestamp INFO    [DebugPrint] $message';

        // 直接写入文件
        _outputToFile(formattedMessage);

        // 使用原始的 debugPrint 输出到控制台
        if (kDebugMode && _originalDebugPrint != null) {
          _originalDebugPrint!('ℹ️ $formattedMessage', wrapWidth: wrapWidth);
        }
      }
    };
  }

  /// 格式化日志消息
  String _formatLogMessage(LogRecord record) {
    final String timestamp = _dateFormat.format(record.time);
    final String level = record.level.name.padRight(7);
    final String loggerName =
        record.loggerName.isNotEmpty ? '[${record.loggerName}]' : '';

    String message = '$timestamp $level $loggerName ${record.message}';

    // 如果有异常信息，添加到消息中
    if (record.error != null) {
      message += '\n  Error: ${record.error}';
    }

    // 如果有堆栈跟踪，添加到消息中
    if (record.stackTrace != null) {
      message += '\n  StackTrace: ${record.stackTrace}';
    }

    return message;
  }

  /// 输出到控制台
  void _outputToConsole(LogRecord record, String message) {
    // 使用原始的 debugPrint 避免循环调用
    final void Function(String? p1, {int? wrapWidth}) originalPrint =
        _originalDebugPrint ?? debugPrint;

    // 根据日志级别使用不同的输出方式
    if (record.level >= Level.SEVERE) {
      originalPrint('❌ $message');
    } else if (record.level >= Level.WARNING) {
      originalPrint('⚠️ $message');
    } else if (record.level >= Level.INFO) {
      originalPrint('ℹ️ $message');
    } else {
      originalPrint('🔍 $message');
    }
  }

  /// 输出到文件
  void _outputToFile(String message) {
    // 防止并发写入
    if (_isWriting) return;

    // 检查文件写入器状态
    if (_logSink == null) return;

    try {
      _isWriting = true;

      // 直接尝试写入，如果失败则在catch块中处理
      _logSink!.writeln(message);
      // 不立即flush，减少IO操作
    } catch (e) {
      // 出错时尝试重新初始化
      _reinitializeLogSink();

      // 如果重新初始化成功，尝试再次写入
      if (_logSink != null) {
        try {
          _logSink!.writeln(message);
        } catch (e2) {
          // 第二次写入失败，放弃并记录错误
          final void Function(String? p1, {int? wrapWidth}) originalPrint =
              _originalDebugPrint ?? debugPrint;
          if (originalPrint != debugPrint) {
            originalPrint('❌ 写入日志文件失败: $e2');
          }
        }
      } else {
        // 重新初始化失败，记录错误
        final void Function(String? p1, {int? wrapWidth}) originalPrint =
            _originalDebugPrint ?? debugPrint;
        if (originalPrint != debugPrint) {
          originalPrint('❌ 写入日志文件失败: $e');
        }
      }
    } finally {
      _isWriting = false;
    }
  }

  /// 重新初始化日志写入器
  void _reinitializeLogSink() {
    try {
      // 关闭已有的sink
      _logSink?.close();
      _logSink = null;

      // 重新创建文件写入流
      if (_logFile != null) {
        _logSink = _logFile!.openWrite(mode: FileMode.append);
      }
    } catch (e) {
      // 静默处理重新初始化失败
      _logSink = null;
    }
  }

  /// 启动定期flush
  void _startPeriodicFlush() {
    _flushTimer?.cancel();
    _flushTimer = Timer.periodic(const Duration(seconds: 5), (Timer timer) {
      _flushToFile();
    });
  }

  /// 手动flush到文件
  void _flushToFile() {
    if (_logSink != null && !_isWriting) {
      try {
        _logSink!.flush();
      } catch (e) {
        // 静默处理flush失败
      }
    }
  }

  /// 检查并轮转日志文件
  Future<void> _rotateLogFileIfNeeded() async {
    if (_logFile == null || !await _logFile!.exists()) return;

    try {
      final int fileSize = await _logFile!.length();
      if (fileSize > _maxLogFileSize) {
        await _rotateLogFiles();
      }
    } catch (e) {
      final void Function(String? p1, {int? wrapWidth}) originalPrint =
          _originalDebugPrint ?? debugPrint;
      originalPrint('❌ 检查日志文件大小失败: $e');
    }
  }

  /// 轮转日志文件
  Future<void> _rotateLogFiles() async {
    try {
      final Directory logDir = _logFile!.parent;
      final String baseName = _logFileName.split('.').first;
      final String extension = _logFileName.split('.').last;

      // 防止轮转过程中的写入操作
      _isWriting = true;

      // 关闭当前文件流
      await _logSink?.close();
      _logSink = null;

      // 重命名现有文件
      for (int i = _maxLogFiles - 1; i >= 1; i--) {
        final File oldFile = File('${logDir.path}/${baseName}_$i.$extension');
        final File newFile =
            File('${logDir.path}/${baseName}_${i + 1}.$extension');

        if (await oldFile.exists()) {
          if (i == _maxLogFiles - 1) {
            // 删除最老的日志文件
            await oldFile.delete();
          } else {
            await oldFile.rename(newFile.path);
          }
        }
      }

      // 重命名当前日志文件
      final File backupFile = File('${logDir.path}/${baseName}_1.$extension');
      await _logFile!.rename(backupFile.path);

      // 创建新的日志文件
      _logFile = File('${logDir.path}/$_logFileName');
      _logSink = _logFile!.openWrite(mode: FileMode.write);
    } catch (e) {
      final void Function(String? p1, {int? wrapWidth}) originalPrint =
          _originalDebugPrint ?? debugPrint;
      originalPrint('❌ 轮转日志文件失败: $e');

      // 即使轮转失败，也要确保有可用的写入器
      _reinitializeLogSink();
    } finally {
      _isWriting = false;
    }
  }

  // =========================== 便捷方法 ===========================

  /// 调试日志
  void debug(String tag, String message) {
    Logger(tag).fine(message);
  }

  /// 信息日志
  void info(String tag, String message) {
    Logger(tag).info(message);
  }

  /// 警告日志
  void warning(String tag, String message) {
    Logger(tag).warning(message);
  }

  /// 错误日志
  void error(String tag, String message,
      [Object? error, StackTrace? stackTrace]) {
    Logger(tag).severe(message, error, stackTrace);
  }

  /// API请求日志
  void apiRequest(String method, String url, Map<String, dynamic>? data) {
    final Logger logger = Logger('API');
    logger.info('🚀 请求 $method $url');
    if (data != null && data.isNotEmpty) {
      logger.fine('   请求数据: ${jsonEncode(data)}');
    }
  }

  /// API响应日志
  void apiResponse(String method, String url, int statusCode, dynamic data) {
    final Logger logger = Logger('API');
    if (statusCode >= 200 && statusCode < 300) {
      logger.info('✅ 响应 $statusCode $method $url');
      logger.fine('   响应数据: ${jsonEncode(data)}');
    } else {
      logger.warning('⚠️ 响应 $statusCode $method $url');
      logger.warning('   响应数据: ${jsonEncode(data)}');
    }
  }

  /// API错误日志
  void apiError(String method, String url, Object error,
      [StackTrace? stackTrace]) {
    final Logger logger = Logger('API');
    logger.severe('❌ 错误 $method $url', error, stackTrace);
  }

  /// 交易相关日志
  void transaction(String action, String transactionId, String message) {
    Logger('Transaction').info('$action [ID:$transactionId] $message');
  }

  /// 打印相关日志
  void printer(String action, String message) {
    Logger('Printer').info('🖨️ $action: $message');
  }

  /// 设备相关日志
  void device(String deviceType, String action, String message) {
    Logger('Device').info('📱 $deviceType $action: $message');
  }

  // =========================== 工具方法 ===========================

  /// 获取日志文件路径
  String? get logFilePath => _logFile?.path;

  /// 获取日志文件大小
  Future<int> getLogFileSize() async {
    if (_logFile == null || !await _logFile!.exists()) return 0;
    return await _logFile!.length();
  }

  /// 清理日志文件
  Future<void> clearLogs() async {
    try {
      await _logSink?.close();

      if (_logFile != null && await _logFile!.exists()) {
        await _logFile!.delete();
      }

      // 重新初始化文件输出
      await _initializeFileOutput();
      info('LogService', '日志文件已清理');
    } catch (e) {
      error('LogService', '清理日志文件失败', e);
    }
  }

  /// 获取最近的日志内容
  Future<String> getRecentLogs({int lines = 100}) async {
    if (_logFile == null || !await _logFile!.exists()) {
      return '日志文件不存在';
    }

    try {
      final String content = await _logFile!.readAsString();
      final List<String> logLines = content.split('\n');

      // 获取最后N行
      final List<String> recentLines = logLines.length > lines
          ? logLines.sublist(logLines.length - lines)
          : logLines;

      return recentLines.join('\n');
    } catch (e) {
      return '读取日志文件失败: $e';
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    try {
      // 设置写入标志，防止dispose过程中的写入操作
      _isWriting = true;

      // 取消定期flush
      _flushTimer?.cancel();
      _flushTimer = null;

      // 最后一次flush
      _flushToFile();

      // 关闭文件流
      await _logSink?.close();
      _logSink = null;
      _logFile = null;

      // 恢复原始的 debugPrint 函数
      if (_originalDebugPrint != null) {
        debugPrint = _originalDebugPrint!;
        _originalDebugPrint = null;
      }

      _isInitialized = false;
    } catch (e) {
      final void Function(String? p1, {int? wrapWidth}) originalPrint =
          _originalDebugPrint ?? debugPrint;
      originalPrint('❌ 释放日志服务资源失败: $e');
    } finally {
      _isWriting = false;
    }
  }
}

// =========================== 全局便捷方法 ===========================

/// 全局调试日志
void logDebug(String tag, String message) {
  LogService.instance.debug(tag, message);
}

/// 全局信息日志
void logInfo(String tag, String message) {
  LogService.instance.info(tag, message);
}

/// 全局警告日志
void logWarning(String tag, String message) {
  LogService.instance.warning(tag, message);
}

/// 全局错误日志
void logError(String tag, String message,
    [Object? error, StackTrace? stackTrace]) {
  LogService.instance.error(tag, message, error, stackTrace);
}
