import 'dart:async';
import 'dart:convert';
import 'dart:io' show Platform; // 用于平台检查
import 'dart:typed_data';
import 'dart:ui' as ui;


import 'package:flutter/services.dart';
import 'package:logging/logging.dart'; // 使用 logging 包记录日志
// 添加日期和数字格式化

// 定义打印机相关的异常类型
class PrinterException implements Exception {
  PrinterException(this.code, [this.message, this.details]);
  final String code;
  final String? message;
  final dynamic details;

  @override
  String toString() {
    return 'PrinterException(code: $code, message: $message, details: $details)';
  }
}

// 定义打印机状态枚举 (可以根据原生端的实际返回值补充)
// 参考 SunmiPrintHelper.kt 和文档中的状态码
enum PrinterStatus {
  normal(1), // 正常
  preparing(2), // 准备中
  abnormalCommunication(3), // 通讯异常
  outOfPaper(4), // 缺纸
  overheated(5), // 过热
  coverOpen(6), // 开盖
  cutterError(7), // 切刀异常
  cutterRecovered(8), // 切刀恢复
  noBlackMark(9), // 未检测到黑标
  printerNotDetected(505), // 未检测到打印机
  firmwareUpdateFailed(507), // 固件升级失败
  serviceDisconnected(-1001), // 服务未连接 (自定义)
  remoteException(-1002), // 远程异常 (自定义)
  genericException(-1003), // 通用异常 (自定义)
  commandFailed(-1004), // 命令执行失败 (自定义)
  parseError(-1005), // 解析错误 (自定义)
  unknown(-9999); // 未知状态

  final int code;
  const PrinterStatus(this.code);

  static PrinterStatus fromCode(int? code) {
    if (code == null) return PrinterStatus.unknown;
    for (PrinterStatus status in PrinterStatus.values) {
      if (status.code == code) {
        return status;
      }
    }
    return PrinterStatus.unknown;
  }
}

// 定义打印对齐方式
enum PrintAlignment {
  left(0),
  center(1),
  right(2);

  final int value;
  const PrintAlignment(this.value);
}

// 定义条码文字位置
enum BarcodeTextPosition {
  none(0),
  above(1),
  below(2),
  both(3);

  final int value;
  const BarcodeTextPosition(this.value);
}

// 定义条码类型 (参考文档)
enum BarcodeType {
  upcA(0),
  upcE(1),
  jan13ean13(2),
  jan8ean8(3),
  code39(4),
  itf(5),
  codabar(6),
  code93(7),
  code128(8);

  final int value;
  const BarcodeType(this.value);
}

// 定义二维码纠错级别
enum QRCodeErrorLevel {
  l(0), // 7%
  m(1), // 15%
  q(2), // 25%
  h(3); // 30%

  final int value;
  const QRCodeErrorLevel(this.value);
}

// 定义表格列对齐方式常量
class TableAlignment {
  static const int left = 0;
  static const int center = 1;
  static const int right = 2;
}

/// Service class for interacting with the Sunmi printer via platform channel.
/// Only works on Android.
class SunmiPrinterService {
  SunmiPrinterService._();
  // Define the method channel name (must match the one in MainActivity.kt)
  static const MethodChannel _channel =
      MethodChannel('com.example.edc_app/printer');
  static final Logger _logger = Logger(
      'SunmiPrinterService'); // Private constructor for potential singleton pattern if needed

  static final SunmiPrinterService instance = SunmiPrinterService._();

  /// Checks if the printer service is connected.
  /// Returns `true` if connected, `false` otherwise.
  /// Throws [PrinterException] on platform errors.
  Future<bool> isPrinterConnected() async {
    // Only attempt on Android
    if (!Platform.isAndroid) {
      _logger.warning('Sunmi printer service is only available on Android.');
      return false;
    }
    try {
      final bool? connected =
          await _channel.invokeMethod<bool>('isPrinterConnected');
      _logger.fine('Printer connected status: $connected');
      return connected ?? false;
    } on PlatformException catch (e) {
      _logger.severe(
          'Failed to check printer connection: ${e.code} - ${e.message}', e);
      throw PrinterException(e.code, e.message, e.details);
    } catch (e) {
      _logger.severe('Unexpected error checking printer connection', e);
      throw PrinterException(
          PrinterStatus.genericException.code.toString(), e.toString());
    }
  }

  /// Gets the current status of the printer.
  /// Returns a [PrinterStatus] enum value.
  /// Throws [PrinterException] on platform errors.
  Future<PrinterStatus> getPrinterStatus() async {
    if (!Platform.isAndroid) {
      _logger.warning('Sunmi printer service is only available on Android.');
      return PrinterStatus.unknown; // Or throw specific exception
    }
    try {
      // Receive the status code as a String? from native
      final String? statusString =
          await _channel.invokeMethod<String>('getPrinterStatus');
      _logger.fine('Received printer status string: $statusString');
      // Parse the string to an int
      final int? statusCode = int.tryParse(statusString ?? '');
      _logger.fine('Parsed printer status code: $statusCode');
      return PrinterStatus.fromCode(statusCode);
    } on PlatformException catch (e) {
      _logger.severe(
          'Failed to get printer status: ${e.code} - ${e.message}', e);
      // Map platform error codes to PrinterStatus if needed
      final int code = int.tryParse(e.code) ?? PrinterStatus.unknown.code;
      return PrinterStatus.fromCode(code); // Return status based on error code
      // Alternatively, rethrow as PrinterException:
      // throw PrinterException(e.code, e.message, e.details);
    } catch (e) {
      _logger.severe('Unexpected error getting printer status', e);
      // Ensure the thrown exception code is a string if originating from parsing
      throw PrinterException(PrinterStatus.parseError.code.toString(),
          'Failed to parse status: ${e.toString()}');
    }
  }

  /// Prints plain text.
  /// Remember to add '\n' at the end of the text for immediate printing.
  /// Throws [PrinterException] on platform errors.
  Future<void> printText(String text) async {
    await _invokeVoidMethod('printText', <String, String>{'text': text});
  }

  /// Prints an image from assets.
  /// [assetPath] - The path to the image in assets folder
  /// [width] - Target width for the image (optional, will maintain aspect ratio)
  /// Throws [PrinterException] on platform errors.
  Future<void> printImageFromAssets(String assetPath, {int? width}) async {
    try {
      // Load image from assets
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();
      
      // Decode image
      final ui.Codec codec = await ui.instantiateImageCodec(
        bytes,
        targetWidth: width,
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;
      
      // Convert to byte data
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        throw PrinterException('IMAGE_PROCESSING_ERROR', 'Failed to convert image to bytes');
      }
      
      // Convert to base64 for sending to native
      final String base64Image = base64Encode(byteData.buffer.asUint8List());
      
      // Send to native
      await _invokeVoidMethod('printBitmap', <String, String>{'bitmap': base64Image});
      
    } catch (e) {
      if (e is PrinterException) rethrow;
      throw PrinterException('IMAGE_PRINT_ERROR', 'Failed to print image: $e');
    }
  }

  /// Prints a bitmap image.
  /// [bitmap] - The bitmap data as Uint8List
  /// [width] - Target width for the image (optional, will maintain aspect ratio)
  /// Throws [PrinterException] on platform errors.
  Future<void> printBitmap(Uint8List bitmap, {int? width}) async {
    try {
      // Decode image
      final ui.Codec codec = await ui.instantiateImageCodec(
        bitmap,
        targetWidth: width,
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;
      
      // Convert to byte data
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        throw PrinterException('IMAGE_PROCESSING_ERROR', 'Failed to convert image to bytes');
      }
      
      // Convert to base64 for sending to native
      final String base64Image = base64Encode(byteData.buffer.asUint8List());
      
      // Send to native
      await _invokeVoidMethod('printBitmap', <String, String>{'bitmap': base64Image});
      
    } catch (e) {
      if (e is PrinterException) rethrow;
      throw PrinterException('IMAGE_PRINT_ERROR', 'Failed to print bitmap: $e');
    }
  }

  /// Prints a bitmap image with custom type.
  /// [bitmap] - The bitmap data as Uint8List
  /// [type] - Print type: 0=same as printBitmap(), 1=black&white with threshold 200, 2=grayscale
  /// [width] - Target width for the image (optional, will maintain aspect ratio)
  /// Throws [PrinterException] on platform errors.
  Future<void> printBitmapCustom(Uint8List bitmap, int type, {int? width}) async {
    try {
      // Decode image
      final ui.Codec codec = await ui.instantiateImageCodec(
        bitmap,
        targetWidth: width,
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;
      
      // Convert to byte data
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        throw PrinterException('IMAGE_PROCESSING_ERROR', 'Failed to convert image to bytes');
      }
      
      // Convert to base64 for sending to native
      final String base64Image = base64Encode(byteData.buffer.asUint8List());
      
      // Send to native
      await _invokeVoidMethod('printBitmapCustom', <String, dynamic>{
        'bitmap': base64Image,
        'type': type,
      });
      
    } catch (e) {
      if (e is PrinterException) rethrow;
      throw PrinterException('IMAGE_PRINT_ERROR', 'Failed to print bitmap custom: $e');
    }
  }

  /// Prints an image from assets with custom type.
  /// [assetPath] - The path to the image in assets folder
  /// [type] - Print type: 0=same as printBitmap(), 1=black&white with threshold 200, 2=grayscale
  /// [width] - Target width for the image (optional, will maintain aspect ratio)
  /// Throws [PrinterException] on platform errors.
  Future<void> printImageFromAssetsCustom(String assetPath, int type, {int? width}) async {
    try {
      // Load image from assets
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();
      
      // Use printBitmapCustom method
      await printBitmapCustom(bytes, type, width: width);
      
    } catch (e) {
      if (e is PrinterException) rethrow;
      throw PrinterException('IMAGE_PRINT_ERROR', 'Failed to print image from assets custom: $e');
    }
  }

  /// Sets the alignment for subsequent print operations.
  /// Throws [PrinterException] on platform errors.
  Future<void> setAlignment(PrintAlignment alignment) async {
    await _invokeVoidMethod(
        'setAlignment', <String, int>{'alignment': alignment.value});
  }

  /// Sets the font size for subsequent print operations.
  /// Throws [PrinterException] on platform errors.
  Future<void> setFontSize(double size) async {
    await _invokeVoidMethod('setFontSize', <String, double>{'size': size});
  }

  /// Sets text bold style using ESC/POS commands.
  /// [bold] - true to enable bold, false to disable
  /// Uses raw ESC/POS commands: {0x1B, 0x45, 0x1} for bold, {0x1B, 0x45, 0x0} for normal
  /// Throws [PrinterException] on platform errors.
  Future<void> setBold(bool bold) async {
    if (!Platform.isAndroid) {
      _logger.warning(
          'setBold: Sunmi printer service is only available on Android.');
      return;
    }

    // ESC/POS commands for bold: ESC E 1 (enable) or ESC E 0 (disable)
    final Uint8List boldCommand = bold
        ? Uint8List.fromList(<int>[0x1B, 0x45, 0x01])
        : Uint8List.fromList(<int>[0x1B, 0x45, 0x00]);

    try {
      _logger.fine('Setting bold to: $bold');
      await _channel.invokeMethod<dynamic>(
          'sendRAWData', <String, Uint8List>{'data': boldCommand});
      _logger.fine('Bold setting applied successfully');
    } on PlatformException catch (e) {
      _logger.severe(
          'PlatformException setting bold: ${e.code} - ${e.message}', e);
      throw PrinterException(e.code, e.message, e.details);
    } catch (e) {
      _logger.severe('Unexpected error setting bold', e);
      throw PrinterException(
          PrinterStatus.genericException.code.toString(), e.toString());
    }
  }

  /// Prints a QR code.
  /// Throws [PrinterException] on platform errors.
  Future<void> printQRCode(
    String data, {
    int moduleSize = 8, // Default size
    QRCodeErrorLevel errorLevel = QRCodeErrorLevel.m, // Default level
  }) async {
    await _invokeVoidMethod('printQRCode', <String, Object>{
      'data': data,
      'moduleSize': moduleSize,
      'errorLevel': errorLevel.value,
    });
  }

  /// Prints a 1D barcode.
  /// Throws [PrinterException] on platform errors.
  Future<void> printBarCode(
    String data, {
    BarcodeType symbology = BarcodeType.code128, // Default type
    int height = 162, // Default height
    int width = 2, // Default width
    BarcodeTextPosition textPosition =
        BarcodeTextPosition.below, // Default position
  }) async {
    await _invokeVoidMethod('printBarCode', <String, Object>{
      'data': data,
      'symbology': symbology.value,
      'height': height,
      'width': width,
      'textPosition': textPosition.value,
    });
  }

  /// Feeds the paper by [n] lines.
  /// Throws [PrinterException] on platform errors.
  Future<void> lineWrap(int n) async {
    await _invokeVoidMethod('lineWrap', <String, int>{'n': n});
  }

  /// Cuts the paper (for devices with a cutter).
  /// Throws [PrinterException] on platform errors.
  Future<void> cutPaper() async {
    await _invokeVoidMethod('cutPaper');
  }

  /// Prints a table row using fixed column widths (does not support Arabic characters).
  /// Column widths are calculated based on English characters (1 Chinese character = 2 English characters).
  /// [colsTextArr]: Array of text strings for each column.
  /// [colsWidthArr]: Array of column widths in English character units, each width must be > 0.
  /// [colsAlign]: Array of alignment for each column (0=left, 1=center, 2=right).
  /// All three arrays must have the same length.
  /// If colsText[i] width > colsWidth[i], the text will wrap to next line.
  /// Throws [PrinterException] on platform errors.
  ///
  /// Example:
  /// ```dart
  /// await printerService.printColumnsText(
  ///   ['商米', '商米', '商米'],
  ///   [4, 4, 8],
  ///   [1, 1, 1], // All center aligned
  /// );
  /// ```
  Future<void> printColumnsText(
    List<String> colsTextArr,
    List<int> colsWidthArr,
    List<int> colsAlign,
  ) async {
    if (colsTextArr.length != colsWidthArr.length ||
        colsTextArr.length != colsAlign.length) {
      throw PrinterException(
        PrinterStatus.parseError.code.toString(),
        'All arrays (texts, widths, aligns) must have the same length',
      );
    }

    if (colsWidthArr.any((int width) => width <= 0)) {
      throw PrinterException(
        PrinterStatus.parseError.code.toString(),
        'All column widths must be greater than 0',
      );
    }

    if (colsAlign.any((int align) => align < 0 || align > 2)) {
      throw PrinterException(
        PrinterStatus.parseError.code.toString(),
        'Column alignment must be 0 (left), 1 (center), or 2 (right)',
      );
    }

    await _invokeVoidMethod('printColumnsText', <String, List<Object>>{
      'texts': colsTextArr,
      'widths': colsWidthArr,
      'aligns': colsAlign,
    });
  }

  /// Prints a table row using column width ratios.
  /// [colsTextArr]: Array of text strings for each column.
  /// [colsWidthArr]: Array of column width ratios/weights (proportional widths).
  /// [colsAlign]: Array of alignment for each column (0=left, 1=center, 2=right).
  /// All three arrays must have the same length.
  /// If colsText[i] width > colsWidth[i], the text will wrap to next line.
  /// Throws [PrinterException] on platform errors.
  ///
  /// Example:
  /// ```dart
  /// await printerService.printColumnsString(
  ///   ['商米', '商米', '商米'],
  ///   [1, 1, 2], // Third column is twice as wide as first two
  ///   [1, 1, 1], // All center aligned
  /// );
  /// ```
  Future<void> printColumnsString(
    List<String> colsTextArr,
    List<int> colsWidthArr,
    List<int> colsAlign,
  ) async {
    if (colsTextArr.length != colsWidthArr.length ||
        colsTextArr.length != colsAlign.length) {
      throw PrinterException(
        PrinterStatus.parseError.code.toString(),
        'All arrays (texts, widths, aligns) must have the same length',
      );
    }

    if (colsWidthArr.any((int width) => width <= 0)) {
      throw PrinterException(
        PrinterStatus.parseError.code.toString(),
        'All column width ratios must be greater than 0',
      );
    }

    if (colsAlign.any((int align) => align < 0 || align > 2)) {
      throw PrinterException(
        PrinterStatus.parseError.code.toString(),
        'Column alignment must be 0 (left), 1 (center), or 2 (right)',
      );
    }

    await _invokeVoidMethod('printColumnsString', <String, List<Object>>{
      'texts': colsTextArr,
      'widths': colsWidthArr,
      'aligns': colsAlign,
    });
  }

  // --- Transaction Printing Methods ---

  /// Enters transaction print mode. Commands are buffered.
  /// [clearBuffer]: If true, clears any previous uncommitted commands.
  /// Throws [PrinterException] on platform errors.
  Future<void> enterPrinterBuffer({bool clearBuffer = true}) async {
    await _invokeVoidMethod(
        'enterPrinterBuffer', <String, bool>{'clearBuffer': clearBuffer});
  }

  /// Commits the buffered print commands in transaction mode.
  /// The printer remains in transaction mode after this call.
  /// Use this for intermediate commits within a larger transaction.
  /// The Future completes when the commit command is acknowledged,
  /// but the actual printing happens asynchronously. The result indicates
  /// if the **entire transaction print job** succeeded or failed.
  /// Throws [PrinterException] on platform errors or print failures.
  Future<void> commitPrinterBuffer() async {
    await _invokeVoidMethod('commitPrinterBuffer');
  }

  /// Exits transaction print mode.
  /// [commit]: If true, prints any remaining buffered commands before exiting.
  /// The Future completes when the exit command is acknowledged. If [commit] is true,
  /// the result indicates if the **final print job** succeeded or failed.
  /// Throws [PrinterException] on platform errors or print failures (if commit=true).
  Future<void> exitPrinterBuffer({bool commit = true}) async {
    await _invokeVoidMethod(
        'exitPrinterBuffer', <String, bool>{'commit': commit});
  }

  // --- Helper Method ---

  /// Helper to invoke methods that don't return a specific value (void).
  /// Handles platform checks and exceptions.
  Future<void> _invokeVoidMethod(String method, [dynamic arguments]) async {
    if (!Platform.isAndroid) {
      _logger.warning(
          "Cannot call '$method': Sunmi printer service is only available on Android.");
      // Optionally throw a specific exception for non-Android platforms
      // throw UnsupportedError("Sunmi printer service is only available on Android.");
      return; // Or simply do nothing on non-Android
    }
    try {
      _logger.fine("Invoking method '$method' with args: $arguments");
      // We expect 'true' for success from most void methods after the callback bridge
      final dynamic result =
          await _channel.invokeMethod<dynamic>(method, arguments);
      _logger.fine("Method '$method' invocation result: $result");
      // Check result if necessary, some might return bool
      if (result is bool && !result) {
        // This case might happen if onRunResult was false but no exception was raised
        throw PrinterException(
          PrinterStatus.commandFailed.code.toString(),
          "Platform method '$method' reported failure.",
        );
      }
      // Handle specific non-boolean results if any void method returns something else on success
    } on PlatformException catch (e) {
      _logger.severe(
          'PlatformException invoking $method: ${e.code} - ${e.message}', e);
      throw PrinterException(e.code, e.message, e.details);
    } catch (e) {
      _logger.severe('Unexpected error invoking $method', e);
      throw PrinterException(
          PrinterStatus.genericException.code.toString(), e.toString());
    }
  }

  // --- Convenience Methods for Receipts (Example) ---
  // You can add higher-level methods here to print common receipt formats

  /// Example: Prints a simple receipt using transaction mode.
  Future<void> printDemoReceipt() async {
    if (!Platform.isAndroid) return;

    _logger.info('Printing demo receipt...');
    try {
      // Start transaction
      await enterPrinterBuffer(clearBuffer: true);

      // Header
      await setAlignment(PrintAlignment.center);
      await setFontSize(36.0); // Larger font for title
      await printText('Your Store\n');
      await setFontSize(24.0); // Normal font size
      await printText('欢迎光临\n');
      await printText('--------------------------------\n');

      // Items using table format
      await setAlignment(PrintAlignment.left);
      await setFontSize(20.0);

      // Table header using printColumnsText with fixed widths
      await printColumnsText(
        <String>['商品', '数量', '单价', '金额'],
        <int>[12, 4, 6, 6], // Fixed character widths
        <int>[0, 1, 2, 2], // left, center, right, right
      );
      await printText('--------------------------------\n');

      // Table rows using printColumnsText
      await printColumnsText(
        <String>['测试商品A', '1', '10.00', '10.00'],
        <int>[12, 4, 6, 6],
        <int>[0, 1, 2, 2],
      );

      await printColumnsText(
        <String>['测试商品B', '2', '5.50', '11.00'],
        <int>[12, 4, 6, 6],
        <int>[0, 1, 2, 2],
      );

      await printColumnsText(
        <String>['长名称测试商品C', '1', '20.00', '20.00'],
        <int>[12, 4, 6, 6],
        <int>[0, 1, 2, 2],
      );

      await printText('--------------------------------\n');

      // Totals using printColumnsString with proportional widths
      await printColumnsString(
        <String>['', '总计:', '41.00'],
        <int>[2, 1, 1], // Proportional widths: 2:1:1
        <int>[0, 1, 2], // left, center, right
      );

      await printColumnsString(
        <String>['支付方式:', 'Cash', ''],
        <int>[2, 1, 1],
        <int>[0, 1, 0],
      );

      await printText('--------------------------------\n');

      // Footer
      await setAlignment(PrintAlignment.center);
      await printText('谢谢惠顾，欢迎再次光临!\n');
      await printText('--------------------------------\n');
      await lineWrap(3); // Feed paper before cutting

      // --- End Transaction ---
      // Commit and Cut
      // await cutPaper(); // Request cut - Commented out as P2 A11 likely doesn't support it
      await exitPrinterBuffer(
          commit: true); // Commit all buffered data and exit transaction mode

      _logger.info('Demo receipt print command sequence finished.');
    } on PrinterException catch (e) {
      _logger.severe('Failed to print demo receipt: ${e.code} - ${e.message}');
      // Optionally try to exit buffer without committing if error occurred mid-transaction
      try {
        await exitPrinterBuffer(commit: false);
      } catch (_) {}
      rethrow; // Rethrow the exception for UI handling
    } catch (e) {
      _logger.severe('Unexpected error printing demo receipt', e);
      try {
        await exitPrinterBuffer(commit: false);
      } catch (_) {}
      throw PrinterException(PrinterStatus.genericException.code.toString(),
          'Unexpected error: ${e.toString()}');
    }
  }

  /// Helper method to print a simple receipt table with headers and rows.
  /// Uses [printColumnsText] for consistent formatting.
  /// [headers]: Column headers
  /// [rows]: List of row data (each row should have same length as headers)
  /// [columnWidths]: Width for each column in characters
  /// [alignments]: Alignment for each column (optional, defaults to left)
  Future<void> printTable({
    required List<String> headers,
    required List<List<String>> rows,
    required List<int> columnWidths,
    List<int>? alignments,
  }) async {
    final List<int> defaultAlignments =
        List.filled(headers.length, TableAlignment.left);
    final List<int> colAlignments = alignments ?? defaultAlignments;

    // Validate input
    if (headers.length != columnWidths.length) {
      throw PrinterException(
        PrinterStatus.parseError.code.toString(),
        'Headers and column widths must have the same length',
      );
    }

    if (colAlignments.length != headers.length) {
      throw PrinterException(
        PrinterStatus.parseError.code.toString(),
        'Alignments must have the same length as headers',
      );
    }

    for (final List<String> row in rows) {
      if (row.length != headers.length) {
        throw PrinterException(
          PrinterStatus.parseError.code.toString(),
          'All rows must have the same number of columns as headers',
        );
      }
    }

    try {
      // Print headers
      await printColumnsText(headers, columnWidths, colAlignments);

      // Print separator
      final int totalWidth = columnWidths.reduce((int a, int b) => a + b);
      await printText('${'-' * totalWidth}\n');

      // Print rows
      for (final List<String> row in rows) {
        await printColumnsText(row, columnWidths, colAlignments);
      }

      // Print bottom separator
      await printText('${'-' * totalWidth}\n');
    } catch (e) {
      _logger.severe('Error printing table', e);
      rethrow;
    }
  }

  /// Demonstrates table printing capabilities using both printColumnsText and printColumnsString.
  Future<void> printTableDemo() async {
    if (!Platform.isAndroid) return;

    _logger.info('Printing table demo...');
    try {
      await enterPrinterBuffer(clearBuffer: true);

      // Title
      await setAlignment(PrintAlignment.center);
      await setFontSize(32.0);
      await printText('表格打印演示\n');
      await printText('================\n');

      // Demo 1: Fixed width columns (printColumnsText)
      await setAlignment(PrintAlignment.left);
      await setFontSize(24.0);
      await printText('1. 固定宽度列 (printColumnsText):\n');

      await printColumnsText(
        <String>['商品名称', '数量', '单价'],
        <int>[8, 4, 6], // 8 chars, 4 chars, 6 chars
        <int>[0, 1, 2], // left, center, right
      );

      await printColumnsText(
        <String>['苹果', '5kg', '12.50'],
        <int>[8, 4, 6],
        <int>[0, 1, 2],
      );

      await printColumnsText(
        <String>['很长的商品名称测试', '10', '25.00'],
        <int>[8, 4, 6], // Long text will wrap
        <int>[0, 1, 2],
      );

      await printText('----------------\n');

      // Demo 2: Proportional width columns (printColumnsString)
      await printText('2. 比例宽度列 (printColumnsString):\n');

      await printColumnsString(
        <String>['商品', '描述', '价格'],
        <int>[1, 2, 1], // Ratio 1:2:1
        <int>[0, 0, 2], // left, left, right
      );

      await printColumnsString(
        <String>['橙子', '新鲜进口橙子，味道甜美', '8.90'],
        <int>[1, 2, 1],
        <int>[0, 0, 2],
      );

      await printColumnsString(
        <String>['香蕉', '优质香蕉，营养丰富', '6.50'],
        <int>[1, 2, 1],
        <int>[0, 0, 2],
      );

      await printText('================\n');
      await setAlignment(PrintAlignment.center);
      await printText('演示完成\n');
      await lineWrap(2);

      await exitPrinterBuffer(commit: true);
      _logger.info('Table demo printed successfully.');
    } on PrinterException catch (e) {
      _logger.severe('Failed to print table demo: ${e.code} - ${e.message}');
      try {
        await exitPrinterBuffer(commit: false);
      } catch (_) {}
      rethrow;
    } catch (e) {
      _logger.severe('Unexpected error printing table demo', e);
      try {
        await exitPrinterBuffer(commit: false);
      } catch (_) {}
      throw PrinterException(PrinterStatus.genericException.code.toString(),
          'Unexpected error: ${e.toString()}');
    }
  }
}
