/// 数据服务抽象层
/// 提供统一的数据访问接口，支持Mock数据和真实API的无缝切换
abstract class DataService<T> {
  /// 获取所有数据
  Future<List<T>> getAll({Map<String, dynamic>? params});

  /// 根据ID获取单个数据
  Future<T?> getById(String id);

  /// 创建新数据
  Future<T> create(Map<String, dynamic> data);

  /// 更新数据
  Future<T> update(String id, Map<String, dynamic> data);

  /// 删除数据
  Future<bool> delete(String id);

  /// 搜索数据
  Future<List<T>> search(String query, {Map<String, dynamic>? filters});

  /// 分页获取数据
  Future<PagedResult<T>> getPaged({
    int page = 1,
    int pageSize = 20,
    Map<String, dynamic>? filters,
    String? sortBy,
    SortOrder sortOrder = SortOrder.asc,
  });
}

/// 分页结果数据结构
class PagedResult<T> {
  const PagedResult({
    required this.data,
    required this.totalCount,
    required this.currentPage,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PagedResult.fromData({
    required List<T> allData,
    required int page,
    required int pageSize,
  }) {
    final int totalCount = allData.length;
    final int totalPages = (totalCount / pageSize).ceil();
    final int startIndex = (page - 1) * pageSize;
    final int endIndex = (startIndex + pageSize).clamp(0, totalCount);

    final List<T> data =
        startIndex < totalCount ? allData.sublist(startIndex, endIndex) : <T>[];

    return PagedResult<T>(
      data: data,
      totalCount: totalCount,
      currentPage: page,
      pageSize: pageSize,
      totalPages: totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    );
  }
  final List<T> data;
  final int totalCount;
  final int currentPage;
  final int pageSize;
  final int totalPages;
  final bool hasNext;
  final bool hasPrevious;

  @override
  String toString() {
    return 'PagedResult(page: $currentPage/$totalPages, items: ${data.length}/$totalCount)';
  }
}

/// 排序方向枚举
enum SortOrder {
  asc, // 升序
  desc, // 降序
}

/// 数据服务异常
class DataServiceException implements Exception {
  const DataServiceException(
    this.message, {
    this.errorCode,
    this.originalError,
  });
  final String message;
  final String? errorCode;
  final dynamic originalError;

  @override
  String toString() => 'DataServiceException: $message';
}

/// 数据验证异常
class ValidationException extends DataServiceException {
  const ValidationException(
    super.message, {
    required this.fieldErrors,
    super.errorCode,
    super.originalError,
  });
  final Map<String, List<String>> fieldErrors;

  @override
  String toString() {
    final String errors = fieldErrors.entries
        .map((MapEntry<String, List<String>> e) =>
            '${e.key}: ${e.value.join(', ')}')
        .join('; ');
    return 'ValidationException: $message ($errors)';
  }
}

/// 网络异常
class NetworkException extends DataServiceException {
  const NetworkException(
    super.message, {
    this.statusCode,
    this.endpoint,
    super.errorCode,
    super.originalError,
  });
  final int? statusCode;
  final String? endpoint;

  @override
  String toString() {
    return 'NetworkException: $message (${statusCode ?? 'N/A'}) at $endpoint';
  }
}

/// 数据未找到异常
class NotFoundException extends DataServiceException {
  const NotFoundException(
    this.resourceType,
    this.resourceId, {
    String? errorCode,
    dynamic originalError,
  }) : super(
          '$resourceType with ID $resourceId not found',
          errorCode: errorCode,
          originalError: originalError,
        );
  final String resourceType;
  final String resourceId;
}

/// API响应包装器
class ApiResponse<T> {
  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.errorCode,
    this.metadata,
  });

  factory ApiResponse.success(T data,
      {String? message, Map<String, dynamic>? metadata}) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      metadata: metadata,
    );
  }

  factory ApiResponse.error(String message,
      {String? errorCode, Map<String, dynamic>? metadata}) {
    return ApiResponse<T>(
      success: false,
      message: message,
      errorCode: errorCode,
      metadata: metadata,
    );
  }
  final bool success;
  final T? data;
  final String? message;
  final String? errorCode;
  final Map<String, dynamic>? metadata;

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, hasData: ${data != null})';
  }
}
