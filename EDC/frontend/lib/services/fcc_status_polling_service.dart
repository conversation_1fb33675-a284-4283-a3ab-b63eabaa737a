import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/fcc_device.dart';
import '../models/dispenser_model.dart';
import 'fcc_device_service.dart';
import 'adapters/fcc_device_adapter_v2.dart';

/// FCC状态轮询服务 - 重构版本
///
/// 职责：
/// 1. 低频轮询设备配置 (每5分钟)
/// 2. 高频轮询喷嘴状态 (每2秒)
/// 3. 直接操作FCC数据，避免ID映射错误
/// 4. 提供统一的EDC格式数据流给UI
class FccStatusPollingService {
  FccStatusPollingService({required FCCDeviceService fccDeviceService})
      : _fccDeviceService = fccDeviceService;
  final FCCDeviceService _fccDeviceService;

  // 轮询控制
  Timer? _configPollTimer; // 低频配置轮询
  Timer? _statusPollTimer; // 高频状态轮询
  bool _isActive = false;

  // 缓存数据 - 直接缓存FCC数据
  List<FCCDevice> _cachedFccDevices = <FCCDevice>[];
  bool _isConnected = false;

  // 数据流
  final StreamController<List<Dispenser>> _dispenserStreamController =
      StreamController<List<Dispenser>>.broadcast();
  final StreamController<bool> _connectionStreamController =
      StreamController<bool>.broadcast();

  // === 公共API ===

  /// 开始轮询
  Future<void> startPolling({
    Duration configInterval = const Duration(seconds: 5), // 5s
    Duration statusInterval = const Duration(seconds: 1), // 2s
  }) async {
    if (_isActive) return;

    _isActive = true;

    // 立即执行一次配置轮询
    await _pollConfig();

    // 启动定时器
    _configPollTimer = Timer.periodic(configInterval, (_) => _pollConfig());
    _statusPollTimer = Timer.periodic(statusInterval, (_) => _pollStatus());
  }

  /// 停止轮询
  void stopPolling() {
    if (!_isActive) return;

    _isActive = false;
    _configPollTimer?.cancel();
    _statusPollTimer?.cancel();
  }

  /// 手动刷新
  Future<void> refresh() async {
    await _pollConfig();
  }

  // === 数据流访问 ===

  /// 获取Dispenser状态流
  Stream<List<Dispenser>> get dispensersStream =>
      _dispenserStreamController.stream;

  /// 获取连接状态流
  Stream<bool> get connectionStream => _connectionStreamController.stream;

  // === 当前状态访问 ===

  /// 获取当前设备列表
  List<Dispenser> get currentDispensers {
    return FCCDeviceAdapterV2.fccDevicesToDispensers(_cachedFccDevices);
  }

  /// 获取当前连接状态
  bool get isConnected => _isConnected;

  /// 是否正在轮询
  bool get isPolling => _isActive;

  // === 私有轮询方法 ===

  /// 低频轮询设备配置
  Future<void> _pollConfig() async {
    if (!_isActive) return;

    try {
      debugPrint('🔄 FccStatusPollingService: 开始轮询FCC设备配置...');

      // 直接获取原始FCC设备数据
      final List<FCCDevice> fccDevices =
          await _fccDeviceService.getFccDevices();
      // debugPrint('📥 FccStatusPollingService: 获取到 ${fccDevices.length} 个FCC设备');

      _cachedFccDevices = fccDevices;
      _isConnected = true;

      // 转换并发布EDC格式数据
      // debugPrint('🔄 FccStatusPollingService: 开始转换为EDC格式...');
      final List<Dispenser> edcDispensers =
          FCCDeviceAdapterV2.fccDevicesToDispensers(_cachedFccDevices);
      // debugPrint('✅ FccStatusPollingService: 转换完成，发布 ${edcDispensers.length} 个Dispenser');

      _dispenserStreamController.add(edcDispensers);
      _connectionStreamController.add(true);
    } catch (e) {
      debugPrint('❌ FCC config polling failed: $e');
      _isConnected = false;
      _connectionStreamController.add(false);

      // 如果有缓存数据，标记为离线后发布
      if (_cachedFccDevices.isNotEmpty) {
        _markAllAsOfflineAndEmit();
      }
    }
  }

  /// 高频轮询喷嘴状态
  Future<void> _pollStatus() async {
    if (!_isActive || _cachedFccDevices.isEmpty) return;

    try {
      // 为每个FCC设备获取实时喷嘴状态
      final List<Future<Map<String, Object>>> futures =
          _cachedFccDevices.map((FCCDevice fccDevice) async {
        try {
          final List<FCCNozzle> nozzles =
              await _fccDeviceService.getDeviceNozzles(fccDevice.id);
          return <String, Object>{
            'deviceId': fccDevice.id,
            'nozzles': nozzles,
            'success': true
          };
        } catch (e) {
          return <String, Object>{
            'deviceId': fccDevice.id,
            'nozzles': <FCCNozzle>[],
            'success': false
          };
        }
      }).toList();

      // 并发执行所有API调用
      final List<Map<String, Object>> results = await Future.wait(futures);

      // 合并实时状态到缓存的FCC设备中
      final List<FCCDevice> updatedFccDevices =
          _mergeStatusDataToFccDevices(results);

      _cachedFccDevices = updatedFccDevices;
      _isConnected = true;

      // 转换为EDC格式并发布
      final List<Dispenser> edcDispensers =
          FCCDeviceAdapterV2.fccDevicesToDispensers(_cachedFccDevices);
      _dispenserStreamController.add(edcDispensers);
      _connectionStreamController.add(true);
    } catch (e) {
      debugPrint('❌ FCC status polling failed: $e');
      _isConnected = false;
      _connectionStreamController.add(false);

      // 状态轮询失败时，将设备标记为离线
      _markAllAsOfflineAndEmit();
    }
  }

  /// 合并实时状态数据到FCC设备中
  List<FCCDevice> _mergeStatusDataToFccDevices(
      List<Map<String, dynamic>> statusResults) {
    final List<FCCDevice> updatedFccDevices = <FCCDevice>[];

    for (final FCCDevice fccDevice in _cachedFccDevices) {
      try {
        // 找到对应设备的状态数据
        Map<String, dynamic>? statusData;

        for (final Map<String, dynamic> result in statusResults) {
          if (result['deviceId'] == fccDevice.id) {
            statusData = result;
            break;
          }
        }

        // 如果没找到，创建默认的状态数据
        statusData ??= <String, dynamic>{
          'deviceId': fccDevice.id,
          'nozzles': <FCCNozzle>[],
          'success': false,
        };

        final List<FCCNozzle> nozzles =
            statusData['nozzles'] as List<FCCNozzle>;
        final bool success = statusData['success'] as bool;

        if (!success || nozzles.isEmpty) {
          // 没有状态数据或查询失败，保持设备原状态，但标记喷嘴为错误
          final FCCDevice deviceWithErrorNozzles = fccDevice.copyWith(
            nozzles: fccDevice.nozzles
                .map((FCCNozzle nozzle) => nozzle.copyWith(
                      status: FCCNozzleStatus.error,
                      errorMessage: success ? '无喷嘴数据' : 'API调用失败',
                      lastUpdateTime: DateTime.now(),
                    ))
                .toList(),
            lastSeen: DateTime.now(),
          );
          updatedFccDevices.add(deviceWithErrorNozzles);
        } else {
          // 有状态数据，只更新喷嘴，保持设备原有的online/offline状态
          final FCCDevice updatedFccDevice = fccDevice.copyWith(
            nozzles: nozzles,
            lastSeen: DateTime.now(),
          );
          updatedFccDevices.add(updatedFccDevice);
        }
      } catch (e) {
        debugPrint('❌ Error merging status for device ${fccDevice.id}: $e');

        // 出错时标记为离线
        final FCCDevice offlineFccDevice = _markFccDeviceAsOffline(fccDevice);
        updatedFccDevices.add(offlineFccDevice);
      }
    }

    return updatedFccDevices;
  }

  /// 将所有设备标记为离线
  void _markAllAsOfflineAndEmit() {
    final List<FCCDevice> offlineFccDevices =
        _cachedFccDevices.map(_markFccDeviceAsOffline).toList();

    _cachedFccDevices = offlineFccDevices;

    // 转换为EDC格式并发布
    final List<Dispenser> edcDispensers =
        FCCDeviceAdapterV2.fccDevicesToDispensers(_cachedFccDevices);
    _dispenserStreamController.add(edcDispensers);
  }

  /// 将单个FCC设备标记为离线
  FCCDevice _markFccDeviceAsOffline(FCCDevice fccDevice) {
    final List<FCCNozzle> offlineNozzles =
        fccDevice.nozzles.map((FCCNozzle nozzle) {
      return nozzle.copyWith(
        status: FCCNozzleStatus.error,
        errorMessage: 'Disconnected',
        lastUpdateTime: DateTime.now(),
      );
    }).toList();

    return fccDevice.copyWith(
      nozzles: offlineNozzles,
      status: FCCDeviceStatus.offline,
      lastSeen: DateTime.now(),
    );
  }

  /// 清理资源
  void dispose() {
    stopPolling();
    _dispenserStreamController.close();
    _connectionStreamController.close();
    _fccDeviceService.dispose();
  }
}
