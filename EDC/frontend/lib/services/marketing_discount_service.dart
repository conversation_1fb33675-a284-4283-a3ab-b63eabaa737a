import 'package:flutter/foundation.dart';
import '../constants/customer_type_constants.dart';
import '../models/payment_transaction_data.dart';
import '../models/promotion_request.dart';
import '../models/promotion_response.dart';
import '../models/create_order_request.dart';
import '../models/fuel_transaction.dart';
import '../services/api/api_service.dart';

/// 营销折扣计算服务
/// 用于在支付流程中计算折扣并更新订单请求
class MarketingDiscountService {
  static final MarketingDiscountService _instance = MarketingDiscountService._internal();
  factory MarketingDiscountService() => _instance;
  MarketingDiscountService._internal();

  /// 检查是否为B2B客户
  bool _isB2BCustomer(Map<String, dynamic>? memberInfo) {
    if (memberInfo == null || memberInfo.isEmpty) {
      return false;
    }

    // 使用统一的客户类型检测工具
    return CustomerTypeUtils.isB2BCustomer(memberInfo);
  }

  /// 根据支付交易数据计算折扣
  /// 
  /// [paymentData] 支付交易数据
  /// [memberInfo] 会员信息（如果有）
  /// 返回折扣计算结果
  Future<PromotionResponse?> calculateDiscount({
    required PaymentTransactionData paymentData,
    Map<String, dynamic>? memberInfo,
  }) async {
    // B2B客户直接返回null，跳过优惠计算
    if (_isB2BCustomer(memberInfo)) {
      debugPrint('🏢 B2B客户，跳过优惠计算');
      debugPrint('   customer_type: ${memberInfo?['customer_type']}');
      debugPrint('   is_b2b: ${memberInfo?['is_b2b']}');
      return null;
    }

    try {
      debugPrint('🎯 开始计算营销折扣...');
      debugPrint('   交易金额: ${paymentData.totalAmount}');
      debugPrint('   燃油类型: ${paymentData.fuelType} ${paymentData.fuelGrade}');
      debugPrint('   油量: ${paymentData.volume}L');
      
      if (memberInfo != null && memberInfo.isNotEmpty) {
        debugPrint('   会员ID: ${memberInfo['member_id']}');
        debugPrint('   会员等级: ${memberInfo['membership_level']}');
      }

      // 构建折扣计算请求
      final request = _buildDiscountRequest(paymentData, memberInfo);
      
      // 调用促销API
      final promotionApi = ApiService().promotionApi;
      final promotionResponse = await promotionApi.calculateFuelDiscount(
        _createFuelTransactionFromPayment(paymentData, memberInfo),
      );
      
      if (promotionResponse.success) {
        debugPrint('✅ 折扣计算成功:');
        debugPrint('   原价: ${promotionResponse.originalAmount}');
        debugPrint('   折后价: ${promotionResponse.discountedAmount}');
        debugPrint('   折扣金额: ${promotionResponse.discountAmount}');
        debugPrint('   折扣商品: ${promotionResponse.items.length}个');
        
        return promotionResponse;
      } else {
        debugPrint('⚠️ 折扣计算失败: ${promotionResponse.message}');
        return null;
      }
      
    } catch (e) {
      debugPrint('❌ 营销折扣计算异常: $e');
      // 不抛出异常，返回null表示无折扣
      return null;
    }
  }
  
  /// 将折扣信息应用到订单请求中
  /// 
  /// [orderRequest] 原始订单请求
  /// [promotionResponse] 折扣计算结果
  /// 返回更新后的订单请求
  CreateOrderRequest applyDiscountToOrder({
    required CreateOrderRequest orderRequest,
    required PromotionResponse promotionResponse,
  }) {
    debugPrint('🎁 应用折扣到订单...');
    debugPrint('   原始金额: ${orderRequest.allocatedAmount}');
    debugPrint('   折扣金额: ${promotionResponse.discountAmount}');
    debugPrint('   最终金额: ${promotionResponse.discountedAmount}');
    
    // 更新订单金额
    final updatedOrderRequest = CreateOrderRequest(
      fuelTransactionId: orderRequest.fuelTransactionId,
      paymentType: orderRequest.paymentType,
      paymentMethod: orderRequest.paymentMethod,
      stationId: orderRequest.stationId,
      allocatedAmount: promotionResponse.discountedAmount, // 使用折后价
      employeeNo: orderRequest.employeeNo,
      customerId: orderRequest.customerId,
      customerName: orderRequest.customerName,
      customerPhone: orderRequest.customerPhone,
      vehicleType: orderRequest.vehicleType,
      licensePlate: orderRequest.licensePlate,
      terminalId: orderRequest.terminalId,
      metadata: {
        ...(orderRequest.metadata ?? {}),
        // 添加折扣信息到metadata
        'discount_info': {
          'original_amount': promotionResponse.originalAmount,
          'discounted_amount': promotionResponse.discountedAmount,
          'discount_amount': promotionResponse.discountAmount,
          'discount_items': promotionResponse.items.map((item) => {
            'item_id': item.itemId,
            'name': item.name,
            'original_price': item.originalPrice,
            'discounted_price': item.discountedPrice,
            'quantity': item.quantity,
          }).toList(),
          'promotion_applied_at': DateTime.now().toIso8601String(),
        },
      },
    );
    
    debugPrint('✅ 折扣已应用到订单请求');
    return updatedOrderRequest;
  }
  
  /// 构建折扣计算请求
  PromotionRequest _buildDiscountRequest(
    PaymentTransactionData paymentData,
    Map<String, dynamic>? memberInfo,
  ) {
    // 构建油品商品项
    final fuelItem = OrderItem(
      itemId: paymentData.fuelType, // 使用 fuelType（油品 ID）而不是 fuelGrade（油品名称）
      name: 'BP ${paymentData.fuelGrade}', // 显示名称可以继续使用 fuelGrade
      category: 'fuel',
      price: paymentData.unitPrice,
      quantity: (paymentData.volume * 100).toInt(), // 转换为厘升
    );
    
    return PromotionRequest(
      userId: memberInfo?['member_id']?.toString() ?? 'anonymous',
      orderAmount: paymentData.totalAmount,
      orderTime: paymentData.createdAt,
      items: [fuelItem],
    );
  }
  
  /// 从支付数据创建燃油交易对象（用于API调用）
  FuelTransaction _createFuelTransactionFromPayment(
    PaymentTransactionData paymentData,
    Map<String, dynamic>? memberInfo,
  ) {
    return FuelTransaction(
      id: paymentData.transactionId,
      transactionNumber: paymentData.transactionRef,
      stationId: paymentData.stationId,
      pumpId: paymentData.pumpId,
      nozzleId: paymentData.nozzleId,
      fuelType: paymentData.fuelType,
      fuelGrade: paymentData.fuelGrade,
      volume: paymentData.volume,
      unitPrice: paymentData.unitPrice,
      amount: paymentData.totalAmount,
      status: paymentData.status,
      memberId: int.tryParse(memberInfo?['member_id']?.toString() ?? ''),
      metadata: paymentData.metadata,
      createdAt: paymentData.createdAt,
      updatedAt: paymentData.createdAt,
    );
  }
}

 
