import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 时区信息类
class TimezoneInfo {
  const TimezoneInfo({
    required this.id,
    required this.displayName,
    required this.offset,
  });
  final String id;
  final String displayName;
  final String offset;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimezoneInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 时区管理服务
class TimezoneService extends ChangeNotifier {
  static const String _timezoneKey = 'selected_timezone';
  static const String _defaultTimezone = 'Asia/Jakarta';

  String _selectedTimezone = _defaultTimezone;
  SharedPreferences? _prefs;

  /// 获取当前选择的时区
  String get selectedTimezone => _selectedTimezone;

  /// 常用时区列表
  static const List<TimezoneInfo> availableTimezones = <TimezoneInfo>[
    TimezoneInfo(
      id: 'Asia/Jakarta',
      displayName: 'Indonesia - Jakarta (WIB)',
      offset: 'UTC+7',
    ),
    TimezoneInfo(
      id: 'Asia/Makassar',
      displayName: 'Indonesia - Makassar (WITA)',
      offset: 'UTC+8',
    ),
    TimezoneInfo(
      id: 'Asia/Jayapura',
      displayName: 'Indonesia - Jayapura (WIT)',
      offset: 'UTC+9',
    ),
    TimezoneInfo(
      id: 'Asia/Shanghai',
      displayName: 'China - Shanghai (CST)',
      offset: 'UTC+8',
    ),
    TimezoneInfo(
      id: 'Asia/Singapore',
      displayName: 'Singapore (SGT)',
      offset: 'UTC+8',
    ),
    TimezoneInfo(
      id: 'Asia/Kuala_Lumpur',
      displayName: 'Malaysia - Kuala Lumpur (MYT)',
      offset: 'UTC+8',
    ),
    TimezoneInfo(
      id: 'UTC',
      displayName: 'Coordinated Universal Time (UTC)',
      offset: 'UTC+0',
    ),
  ];

  /// 初始化服务
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _selectedTimezone = _prefs?.getString(_timezoneKey) ?? _defaultTimezone;
    notifyListeners();
  }

  /// 设置时区
  Future<void> setTimezone(String timezoneId) async {
    if (_selectedTimezone != timezoneId) {
      _selectedTimezone = timezoneId;
      await _prefs?.setString(_timezoneKey, timezoneId);
      notifyListeners();
    }
  }

  /// 获取当前时区信息
  TimezoneInfo get currentTimezoneInfo {
    return availableTimezones.firstWhere(
      (TimezoneInfo timezone) => timezone.id == _selectedTimezone,
      orElse: () => availableTimezones.first,
    );
  }

  /// 格式化日期时间（根据当前时区）
  String formatDateTime(DateTime dateTime, {String? pattern}) {
    // 注意：在Flutter中，DateTime对象本身不包含时区信息
    // 这里我们假设传入的DateTime是UTC时间，然后转换为目标时区
    // 实际项目中可能需要使用timezone包来处理复杂的时区转换

    final DateFormat formatter = DateFormat(pattern ?? 'yyyy-MM-dd HH:mm:ss');

    // 简单的时区偏移计算（仅作示例，实际应用中建议使用timezone包）
    final int offset = _getTimezoneOffset(_selectedTimezone);
    final DateTime localTime = dateTime.add(Duration(hours: offset));

    return formatter.format(localTime);
  }

  /// 格式化当前时间
  String formatNow({String? pattern}) {
    return formatDateTime(DateTime.now().toUtc(), pattern: pattern);
  }

  /// 获取时区偏移量（简化版本）
  int _getTimezoneOffset(String timezoneId) {
    switch (timezoneId) {
      case 'Asia/Jakarta':
        return 7;
      case 'Asia/Makassar':
      case 'Asia/Shanghai':
      case 'Asia/Singapore':
      case 'Asia/Kuala_Lumpur':
        return 8;
      case 'Asia/Jayapura':
        return 9;
      case 'UTC':
      default:
        return 0;
    }
  }

  /// 获取本地时间（考虑时区）
  DateTime getLocalTime([DateTime? utcTime]) {
    final DateTime baseTime = utcTime ?? DateTime.now().toUtc();
    final int offset = _getTimezoneOffset(_selectedTimezone);
    return baseTime.add(Duration(hours: offset));
  }

  /// 将本地时间转换为UTC
  DateTime toUtc(DateTime localTime) {
    final int offset = _getTimezoneOffset(_selectedTimezone);
    return localTime.subtract(Duration(hours: offset));
  }
}

/// Riverpod Provider
final ChangeNotifierProvider<TimezoneService> timezoneServiceProvider =
    ChangeNotifierProvider<TimezoneService>(
        (ChangeNotifierProviderRef<TimezoneService> ref) {
  return TimezoneService();
});

/// 用于监听时区变化的Provider
final Provider<String> selectedTimezoneProvider =
    Provider<String>((ProviderRef<String> ref) {
  return ref.watch(timezoneServiceProvider).selectedTimezone;
});

/// 用于获取当前时区信息的Provider
final Provider<TimezoneInfo> currentTimezoneInfoProvider =
    Provider<TimezoneInfo>((ProviderRef<TimezoneInfo> ref) {
  return ref.watch(timezoneServiceProvider).currentTimezoneInfo;
});
