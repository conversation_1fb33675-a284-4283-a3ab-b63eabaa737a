import 'package:dio/dio.dart';

import '../../models/order.dart';
import '../../models/fuel_transaction.dart';
import '../../models/promotion_request.dart';
import '../../models/promotion_response.dart';
import 'api_client.dart';

/// 促销服务API
class PromotionApi {
  PromotionApi({required ApiClient apiClient}) : _apiClient = apiClient;
  final ApiClient _apiClient;

  /// 计算订单折扣
  Future<PromotionResponse> calculateDiscount(Order order) async {
    try {
      // 将订单转换为促销服务需要的请求格式
      final request = PromotionRequest.fromOrder(order);

      // 调用促销服务API - 使用新的 /process 端点
      final response = await _apiClient.post(
        '/api/v1/calculator/process',
        data: request.toJson(),
      );
      
      return PromotionResponse.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: '调用促销服务失败',
        statusCode: e.response?.statusCode,
        data: e.response?.data,
      );
    } catch (e) {
      throw ApiException(
        message: '处理促销信息时发生错误',
        data: null,
      );
    }
  }

  /// 计算燃油交易折扣 - 使用预构造的 PromotionRequest
  /// 这样可以保持调用者构造的正确参数（车辆类型、orderId等）
  Future<PromotionResponse> calculateFuelDiscountFromRequest(
      PromotionRequest request) async {
    try {
      // 直接使用传入的 PromotionRequest，避免重新构造导致参数丢失
      final response = await _apiClient.post(
        '/api/v1/calculator/process',
        data: request.toJson(),
      );
      
      return PromotionResponse.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: '调用燃油促销服务失败',
        statusCode: e.response?.statusCode,
        data: e.response?.data,
      );
    } catch (e) {
      throw ApiException(
        message: '处理燃油促销信息时发生错误',
        data: null,
      );
    }
  }

  /// 计算燃油交易折扣 - 兼容旧版本，从 FuelTransaction 构造请求
  Future<PromotionResponse> calculateFuelDiscount(
      FuelTransaction transaction) async {
    try {
      // 将燃油交易转换为促销服务需要的请求格式
      final request = PromotionRequest.fromFuelTransaction(transaction);
      // 调用促销服务API - 也使用新的 /process 端点
      final response = await _apiClient.post(
        '/api/v1/calculator/process',
        data: request.toJson(),
      );
      
      return PromotionResponse.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: '调用燃油促销服务失败',
        statusCode: e.response?.statusCode,
        data: e.response?.data,
      );
    } catch (e) {
      throw ApiException(
        message: '处理燃油促销信息时发生错误',
        data: null,
      );
    }
  }
}
