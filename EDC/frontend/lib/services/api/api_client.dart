import 'package:dio/dio.dart';
import '../log_service.dart';

class ApiClient {
  ApiClient({required this.baseUrl})
      : _dio = Dio(BaseOptions(
          baseUrl: baseUrl,
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          responseType: ResponseType.json,
        )) {
    // 初始化时添加日志拦截器
    _dio.interceptors.add(_createLogInterceptor());
  }
  final Dio _dio;
  final String baseUrl;

  // 提供对Dio实例的访问（用于ServiceRegistry获取认证token）
  Dio get dio => _dio;

  // 创建日志拦截器
  Interceptor _createLogInterceptor() {
    return InterceptorsWrapper(
      onRequest: (RequestOptions options, RequestInterceptorHandler handler) {
        _printRequestLog(options);
        handler.next(options);
      },
      onResponse: (Response response, ResponseInterceptorHandler handler) {
        _printResponseLog(response);
        handler.next(response);
      },
      onError: (DioException error, ErrorInterceptorHandler handler) {
        _printErrorLog(error);
        handler.next(error);
      },
    );
  }

  // 打印请求日志
  void _printRequestLog(RequestOptions options) {
    final Map<String, dynamic> requestData = <String, dynamic>{};
    if (options.data != null) {
      requestData['body'] = options.data;
    }
    if (options.queryParameters.isNotEmpty) {
      requestData['query'] = options.queryParameters;
    }
    if (options.headers.isNotEmpty) {
      requestData['headers'] = options.headers;
    }

    LogService.instance.apiRequest(options.method, options.uri.toString(),
        requestData.isNotEmpty ? requestData : null);
  }

  // 打印响应日志
  void _printResponseLog(Response response) {
    LogService.instance.apiResponse(
      response.requestOptions.method,
      response.requestOptions.uri.toString(),
      response.statusCode ?? 0,
      response.data,
    );
  }

  // 打印错误日志
  void _printErrorLog(DioException error) {
    LogService.instance.apiError(
      error.requestOptions.method,
      error.requestOptions.uri.toString(),
      error,
      error.stackTrace,
    );
  }

  // 配置API客户端
  void configureClient({String? token}) {
    if (token != null) {
      _dio.options.headers['Authorization'] = 'Bearer $token';
    }
    _dio.options.headers['Content-Type'] = 'application/json';
    _dio.options.headers['Accept'] = 'application/json';
  }

  // GET 请求
  Future<Response> get(String path,
      {Map<String, dynamic>? queryParameters}) async {
    try {
      final Response response = await _dio.get(
        path,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      // 重新抛出经过处理的异常
      throw _handleError(e);
    }
  }

  // POST 请求
  Future<Response> post(String path,
      {dynamic data, Map<String, dynamic>? queryParameters}) async {
    try {
      final Response response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // PUT 请求
  Future<Response> put(String path,
      {dynamic data, Map<String, dynamic>? queryParameters}) async {
    try {
      final Response response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // DELETE 请求
  Future<Response> delete(String path,
      {Map<String, dynamic>? queryParameters}) async {
    try {
      final Response response = await _dio.delete(
        path,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // 处理错误
  Exception _handleError(DioException e) {
    String errorMessage = '未知错误';
    int? statusCode;

    if (e.response != null) {
      // 尝试从响应中获取错误信息
      statusCode = e.response!.statusCode;
      try {
        if (e.response!.data is Map && e.response!.data['message'] != null) {
          errorMessage = e.response!.data['message'] as String;
        } else {
          errorMessage = '接口响应错误: ${e.response!.statusCode}';
        }
      } catch (_) {
        errorMessage = '接口响应格式错误';
      }
    } else if (e.type == DioExceptionType.connectionTimeout) {
      errorMessage = '连接超时';
    } else if (e.type == DioExceptionType.receiveTimeout) {
      errorMessage = '接收数据超时';
    } else if (e.type == DioExceptionType.sendTimeout) {
      errorMessage = '发送数据超时';
    } else if (e.type == DioExceptionType.badResponse) {
      errorMessage = '服务器返回错误状态码';
    } else if (e.type == DioExceptionType.cancel) {
      errorMessage = '请求已取消';
    } else {
      errorMessage = '网络错误: ${e.message}';
    }

    return ApiException(
      message: errorMessage,
      statusCode: statusCode,
      data: e.response?.data,
    );
  }
}

// API异常类
class ApiException implements Exception {
  ApiException({
    required this.message,
    this.statusCode,
    this.data,
  });
  final String message;
  final int? statusCode;
  final dynamic data;

  @override
  String toString() {
    return 'ApiException: $message (Status code: $statusCode)';
  }
}

/// 统一API响应处理器
class ApiResponseHandler {
  /// 解析统一格式的API响应
  /// 新格式: {code: 0, message: "...", data: {...}}
  /// 旧格式: 直接返回数据 {...}
  static T parseResponse<T>(
    Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (response.data == null) {
      throw ApiException(
        message: '响应数据为空',
        statusCode: response.statusCode,
      );
    }

    final Map<String, dynamic> responseData =
        response.data as Map<String, dynamic>;

    // 检查是否是新的统一格式
    if (responseData.containsKey('code') && responseData.containsKey('data')) {
      final int code = responseData['code'] as int? ?? -1;
      final String message = responseData['message'] as String? ?? '';

      if (code == 0) {
        // 成功响应，解析data字段
        final data = responseData['data'];
        if (data is Map<String, dynamic>) {
          return fromJson(data);
        } else {
          throw ApiException(
            message: '响应数据格式错误',
            statusCode: response.statusCode,
            data: responseData,
          );
        }
      } else {
        // API返回业务错误
        throw ApiException(
          message: message.isNotEmpty ? message : '操作失败',
          statusCode: response.statusCode,
          data: responseData,
        );
      }
    } else {
      // 旧格式或直接数据格式
      return fromJson(responseData);
    }
  }

  /// 解析列表响应（带分页信息）
  static PaginatedResponse<T> parsePaginatedResponse<T>(
    Response response,
    T Function(Map<String, dynamic>) itemFromJson,
  ) {
    if (response.data == null) {
      throw ApiException(
        message: '响应数据为空',
        statusCode: response.statusCode,
      );
    }

    final Map<String, dynamic> responseData =
        response.data as Map<String, dynamic>;
    Map<String, dynamic> actualData;

    // 检查是否是新的统一格式
    if (responseData.containsKey('code') && responseData.containsKey('data')) {
      final int code = responseData['code'] as int? ?? -1;
      final String message = responseData['message'] as String? ?? '';

      if (code == 0) {
        actualData = responseData['data'] as Map<String, dynamic>;
      } else {
        throw ApiException(
          message: message.isNotEmpty ? message : '操作失败',
          statusCode: response.statusCode,
          data: responseData,
        );
      }
    } else {
      actualData = responseData;
    }

    // 解析分页数据
    final List<T> items = <T>[];
    if (actualData['items'] != null && actualData['items'] is List) {
      items.addAll(
        (actualData['items'] as List)
            .map((item) => itemFromJson(item as Map<String, dynamic>))
            .toList(),
      );
    }

    return PaginatedResponse<T>(
      items: items,
      total: actualData['total'] as int? ?? 0,
      page: actualData['page'] as int? ?? 1,
      pageSize: actualData['page_size'] as int? ?? 10,
      totalPages: actualData['total_page'] as int? ?? 0,
    );
  }
}

/// 分页响应数据模型
class PaginatedResponse<T> {
  PaginatedResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });
  final List<T> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  bool get hasMore => page < totalPages;
}
