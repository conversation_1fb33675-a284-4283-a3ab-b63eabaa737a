import 'package:dio/src/response.dart';
import 'package:flutter/foundation.dart';
import 'api_client.dart';
import '../../models/staff_card.dart';

// 员工数据模型
class Employee {
  Employee({
    required this.id,
    required this.employeeNo,
    required this.name,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.username,
    this.email,
    this.fullName,
    this.roles,
    this.permissions,
    this.lastLoginAt,
    this.stationIds,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    // 调试日志：打印原始JSON数据的完整内容
    debugPrint('🔍 Employee.fromJson 开始解析...');
    debugPrint('📋 JSON Keys: ${json.keys.toList()}');
    debugPrint('📋 完整JSON内容: ${json.toString()}');
    
    // 尝试从多个可能的字段名中解析stationIds
    List<int>? stationIds;
    
    // 尝试多个可能的字段名
    final List<String> possibleStationFields = [
      'stationIds', 'station_ids', 'stations', 'site_ids', 'sites', 
      'stationId', 'station_id', 'siteId', 'site_id'
    ];
    
    debugPrint('🔍 开始搜索stationIds相关字段...');
    for (final String field in possibleStationFields) {
      debugPrint('🔍 检查字段: $field');
      if (json.containsKey(field)) {
        final fieldValue = json[field];
        debugPrint('🎯 找到站点字段: $field = $fieldValue (类型: ${fieldValue.runtimeType})');
        try {
          if (fieldValue is List) {
            stationIds = List<int>.from(fieldValue);
            debugPrint('✅ 成功解析List类型stationIds: $stationIds');
            break;
          } else if (fieldValue is int) {
            stationIds = [fieldValue];
            debugPrint('✅ 成功解析int类型stationId: $stationIds');
            break;
          } else if (fieldValue is String) {
            final int? parsed = int.tryParse(fieldValue);
            if (parsed != null) {
              stationIds = [parsed];
              debugPrint('✅ 字符串站点ID转换为列表: $stationIds');
              break;
            } else {
              debugPrint('⚠️ 字符串站点ID无法转换为数字: $fieldValue');
            }
          } else {
            debugPrint('⚠️ 站点字段类型不支持: ${fieldValue.runtimeType}');
          }
        } catch (e) {
          debugPrint('❌ 解析站点字段 $field 失败: $e');
        }
      } else {
        debugPrint('❌ 字段 $field 不存在');
      }
    }
    
    if (stationIds == null) {
      debugPrint('⚠️ 所有可能的stationIds字段都未找到或解析失败');
      debugPrint('📋 可用字段列表: ${json.keys.toList()}');
    } else {
      debugPrint('🎉 最终解析到的stationIds: $stationIds');
    }
    
    // 处理角色字段 - 从对象数组中提取角色名称
    List<String>? roles;
    if (json['roles'] != null) {
      try {
        final rolesList = json['roles'] as List<dynamic>;
        roles = rolesList.map((role) {
          if (role is Map<String, dynamic>) {
            return (role['name'] ?? role['code'] ?? '') as String;
          } else {
            return role.toString();
          }
        }).toList();
        debugPrint('✅ 成功解析角色: $roles');
      } catch (e) {
        debugPrint('⚠️ 解析角色字段失败: $e');
        roles = null;
      }
    }

    // 处理权限字段
    List<String>? permissions;
    if (json['permissions'] != null) {
      try {
        final permissionsList = json['permissions'] as List<dynamic>;
        permissions = permissionsList.map((permission) {
          if (permission is Map<String, dynamic>) {
            return (permission['name'] ?? permission['code'] ?? '') as String;
          } else {
            return permission.toString();
          }
        }).toList();
        debugPrint('✅ 成功解析权限: $permissions');
      } catch (e) {
        debugPrint('⚠️ 解析权限字段失败: $e');
        permissions = null;
      }
    }

    return Employee(
      id: (json['id'] ?? json['user_id'] ?? '') as String,
      employeeNo: (json['employeeNo'] ?? json['employee_no'] ?? json['username'] ?? '') as String,
      name: (json['name'] ?? json['fullName'] ?? '') as String,
      username: json['username'] as String?,
      email: json['email'] as String?,
      fullName: json['fullName'] as String?,
      roles: roles,
      permissions: permissions,
      lastLoginAt: json['lastLoginAt'] != null ? DateTime.parse(json['lastLoginAt'] as String) : null,
      stationIds: stationIds,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at'] as String) : DateTime.now(),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at'] as String) : DateTime.now(),
      deletedAt: json['deleted_at'] != null ? DateTime.parse(json['deleted_at'] as String) : null,
    );
  }
  
  final String id;
  final String employeeNo;
  final String name;
  final String? username;
  final String? email;
  final String? fullName;
  final List<String>? roles;
  final List<String>? permissions;
  final DateTime? lastLoginAt;
  final List<int>? stationIds;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
}

// 系统访问权限数据模型
class SystemAccess {
  SystemAccess({
    required this.system,
    required this.accessLevel,
    required this.stationIds,
    required this.stationCount,
  });

  factory SystemAccess.fromJson(Map<String, dynamic> json) {
    debugPrint('🔍 SystemAccess.fromJson 开始解析...');
    debugPrint('📋 SystemAccess JSON: ${json.toString()}');
    
    List<int> stationIds = [];
    if (json['stationIds'] != null) {
      try {
        stationIds = List<int>.from(json['stationIds'] as List<dynamic>);
        debugPrint('✅ 成功解析SystemAccess stationIds: $stationIds');
      } catch (e) {
        debugPrint('❌ 解析SystemAccess stationIds失败: $e');
      }
    }
    
    return SystemAccess(
      system: (json['system'] ?? '') as String,
      accessLevel: (json['accessLevel'] ?? '') as String,
      stationIds: stationIds,
      stationCount: (json['stationCount'] ?? 0) as int,
    );
  }
  
  final String system;
  final String accessLevel;
  final List<int> stationIds;
  final int stationCount;
}

// 登录响应数据模型
class LoginResponse {
  LoginResponse({
    required this.token,
    required this.employee,
    this.refreshToken,
    this.tokenType,
    this.expiresIn,
    this.systemAccess,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    debugPrint('🔍 LoginResponse.fromJson 开始解析...');
    debugPrint('📋 登录响应JSON Keys: ${json.keys.toList()}');
    debugPrint('📋 登录响应完整内容: ${json.toString()}');
    
    final userOrEmployeeData = (json['user'] ?? json['employee'] ?? <String, dynamic>{}) as Map<String, dynamic>;
    debugPrint('🔍 用户数据字段: ${userOrEmployeeData.keys.toList()}');
    
    // 解析系统访问权限
    SystemAccess? systemAccess;
    if (json['systemAccess'] != null) {
      try {
        systemAccess = SystemAccess.fromJson(json['systemAccess'] as Map<String, dynamic>);
        debugPrint('✅ 成功解析systemAccess: ${systemAccess.system}');
      } catch (e) {
        debugPrint('❌ 解析systemAccess失败: $e');
      }
    }
    
    // 检查根级别的 stationIds（向后兼容）
    List<int>? rootStationIds;
    if (json['stationIds'] != null) {
      debugPrint('🔍 发现根级别的stationIds: ${json['stationIds']}');
      try {
        if (json['stationIds'] is List) {
          rootStationIds = List<int>.from(json['stationIds'] as List<dynamic>);
          debugPrint('✅ 成功解析根级别stationIds: $rootStationIds');
        }
      } catch (e) {
        debugPrint('❌ 解析根级别stationIds失败: $e');
      }
    }
    
    // 将stationIds合并到用户数据中，优先使用systemAccess中的数据
    final Map<String, dynamic> mergedUserData = Map<String, dynamic>.from(userOrEmployeeData);
    if (systemAccess != null && systemAccess.stationIds.isNotEmpty) {
      // 优先使用systemAccess中的stationIds（API v2）
      mergedUserData['stationIds'] = systemAccess.stationIds;
      debugPrint('✅ 已将systemAccess中的stationIds合并到用户数据中: ${systemAccess.stationIds}');
    } else if (rootStationIds != null) {
      // 向后兼容：使用根级别的stationIds（API v1）
      mergedUserData['stationIds'] = rootStationIds;
      debugPrint('✅ 已将根级别stationIds合并到用户数据中: $rootStationIds');
    } else {
      debugPrint('⚠️ 未找到任何stationIds数据');
    }
    
    return LoginResponse(
      token: (json['accessToken'] ?? json['token'] ?? '') as String,
      refreshToken: json['refreshToken'] as String?,
      tokenType: (json['tokenType'] ?? 'Bearer') as String,
      expiresIn: json['expiresIn'] as int?,
      employee: Employee.fromJson(mergedUserData),
      systemAccess: systemAccess,
    );
  }
  
  final String token;
  final String? refreshToken;
  final String? tokenType;
  final int? expiresIn;
  final Employee employee;
  final SystemAccess? systemAccess;
}

// 员工API服务类
class EmployeeApi {
  EmployeeApi({required ApiClient apiClient}) : _apiClient = apiClient;
  final ApiClient _apiClient;

  // 员工登录
  Future<LoginResponse> login(String employeeNo, String password) async {
    debugPrint('正在尝试登录，员工编号: $employeeNo');
    try {
      final Response response = await _apiClient.post(
        '/api/v1/auth/login',
        data: <String, String>{
          'username': employeeNo,
          'password': password,
          'system': 'EDC', // 指定系统为EDC
        },
      );

      // 判断响应是否成功
      if (response.statusCode == 200 && response.data['code'] == 0) {
        debugPrint('登录成功: ${response.data['message']}');
        return LoginResponse.fromJson(response.data['data'] as Map<String, dynamic>);
      } else {
        // 处理业务逻辑错误
        final errorCode = response.data['code'] ?? 'UNKNOWN_ERROR';
        final errorMessage = response.data['message'] ?? '未知错误';

        throw ApiException(
          message: errorMessage.toString(),
          statusCode: response.statusCode,
          data: <String, dynamic>{'code': errorCode},
        );
      }
    } catch (e) {
      // 记录错误并重新抛出
      debugPrint('登录失败: $e');
      if (e is ApiException) {
        rethrow;
      } else {
        throw ApiException(
          message: '登录请求失败',
          statusCode: 500,
          data: <String, String>{'error': e.toString()},
        );
      }
    }
  }

  // 获取员工详情
  Future<Employee> getEmployeeDetail(String employeeId) async {
    debugPrint('正在获取员工详情，员工ID: $employeeId');
    try {
      final Response response =
          await _apiClient.get('/api/v1/users/$employeeId');

      // 判断响应是否成功
      if (response.statusCode == 200 && response.data['code'] == 0) {
        debugPrint('获取员工详情成功');
        return Employee.fromJson(response.data['data'] as Map<String, dynamic>);
      } else {
        // 处理业务逻辑错误
        final errorCode = response.data['code'] ?? 'UNKNOWN_ERROR';
        final errorMessage = response.data['message'] ?? '未知错误';

        throw ApiException(
          message: errorMessage.toString(),
          statusCode: response.statusCode,
          data: <String, dynamic>{'code': errorCode},
        );
      }
    } catch (e) {
      // 记录错误并重新抛出
      debugPrint('获取员工详情失败: $e');
      if (e is ApiException) {
        rethrow;
      } else {
        throw ApiException(
          message: '获取员工详情失败',
          statusCode: 500,
          data: <String, String>{'error': e.toString()},
        );
      }
    }
  }

  // Token刷新
  Future<LoginResponse> refreshToken(String refreshToken) async {
    debugPrint('正在刷新Token...');
    try {
      final Response response = await _apiClient.post(
        '/api/v1/auth/refresh',
        data: <String, String>{
          'refreshToken': refreshToken,
        },
      );

      // 判断响应是否成功
      if (response.statusCode == 200 && response.data['code'] == 0) {
        debugPrint('Token刷新成功: ${response.data['message']}');
        return LoginResponse.fromJson(response.data['data'] as Map<String, dynamic>);
      } else {
        // 处理业务逻辑错误
        final errorCode = response.data['code'] ?? 'UNKNOWN_ERROR';
        final errorMessage = response.data['message'] ?? '未知错误';

        throw ApiException(
          message: errorMessage.toString(),
          statusCode: response.statusCode,
          data: <String, dynamic>{'code': errorCode},
        );
      }
    } catch (e) {
      // 记录错误并重新抛出
      debugPrint('Token刷新失败: $e');
      if (e is ApiException) {
        rethrow;
      } else {
        throw ApiException(
          message: 'Token刷新请求失败',
          statusCode: 500,
          data: <String, String>{'error': e.toString()},
        );
      }
    }
  }

  // 用户登出
  Future<void> logout() async {
    debugPrint('正在尝试登出...');
    try {
      final Response response = await _apiClient.post('/api/v1/auth/logout');

      // 判断响应是否成功
      if (response.statusCode == 200 && response.data['code'] == 0) {
        debugPrint('登出成功: ${response.data['message']}');
        return;
      } else {
        // 处理业务逻辑错误
        final errorCode = response.data['code'] ?? 'UNKNOWN_ERROR';
        final errorMessage = response.data['message'] ?? '未知错误';

        throw ApiException(
          message: errorMessage.toString(),
          statusCode: response.statusCode,
          data: <String, dynamic>{'code': errorCode},
        );
      }
    } catch (e) {
      // 记录错误并重新抛出
      debugPrint('登出失败: $e');
      if (e is ApiException) {
        rethrow;
      } else {
        throw ApiException(
          message: '登出请求失败',
          statusCode: 500,
          data: <String, String>{'error': e.toString()},
        );
      }
    }
  }

  /// Get staff card by card number
  /// Calls BOS API: GET /api/v1/staff-cards/number/{cardNumber}
  Future<StaffCard> getStaffCardByNumber(String cardNumber) async {
    debugPrint('Fetching staff card by number: $cardNumber');
    try {
      final Response response = await _apiClient.get(
        '/api/v1/staff-cards/number/$cardNumber',
      );

      // Check if response is successful
      if (response.statusCode == 200) {
        debugPrint('Staff card fetched successfully');
        
        // Parse the response data
        final Map<String, dynamic> responseData = response.data is Map<String, dynamic>
            ? response.data as Map<String, dynamic>
            : <String, dynamic>{};
        
        return StaffCard.fromJson(responseData);
      } else if (response.statusCode == 404) {
        // Handle 404 - card not found
        debugPrint('Staff card not found: $cardNumber');
        throw ApiException(
          message: 'Card not authorized, please contact administrator',
          statusCode: 404,
          data: <String, String>{'card_number': cardNumber},
        );
      } else {
        // Handle other HTTP error responses
        final String errorMessage = response.data is Map<String, dynamic> &&
                response.data['message'] != null
            ? response.data['message'].toString()
            : 'Staff card verification failed';

        throw ApiException(
          message: errorMessage,
          statusCode: response.statusCode,
          data: response.data,
        );
      }
    } catch (e) {
      // Log error and rethrow
      debugPrint('Failed to fetch staff card: $e');
      if (e is ApiException) {
        rethrow;
      } else {
        throw ApiException(
          message: 'Failed to fetch staff card: ${e.toString()}',
          statusCode: 500,
          data: <String, String>{'error': e.toString()},
        );
      }
    }
  }
}
