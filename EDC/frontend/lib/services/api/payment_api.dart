import 'package:dio/src/response.dart';
import 'package:flutter/material.dart';
import '../../models/payment_method.dart';
import 'api_client.dart';

class PaymentApi {
  PaymentApi({required ApiClient apiClient}) : _apiClient = apiClient;
  final ApiClient _apiClient;

  /// 获取支付方式列表
  /// [enabled] - 是否只获取启用的支付方式
  /// [stationId] - 站点ID，用于筛选特定站点可用的支付方式
  Future<PaymentMethodResponse> getPaymentMethods({
    bool? enabled,
    int? stationId,
  }) async {
    try {
      // 构建查询参数
      final Map<String, dynamic> queryParams = <String, dynamic>{};
      if (enabled != null) {
        queryParams['enabled'] = enabled.toString();
      }
      if (stationId != null) {
        queryParams['station_id'] = stationId.toString();
      }

      // 发送请求
      final Response response = await _apiClient.get(
        '/api/v1/payment-methods',
        queryParameters: queryParams,
      );

      // 解析响应
      return PaymentMethodResponse.fromJson(response.data as Map<String, dynamic>);
    } catch (e) {
      debugPrint('获取支付方式列表失败: $e');
      rethrow;
    }
  }
}
