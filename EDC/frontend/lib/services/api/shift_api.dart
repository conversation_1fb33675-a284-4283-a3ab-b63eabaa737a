import 'package:dio/src/response.dart';
import 'package:flutter/foundation.dart';
import 'api_client.dart';
import '../../models/shift_report.dart';
import '../../constants/api_constants.dart';

/// 班次报表API服务
///
/// 专门用于班次报表相关的API调用，不包含班次管理功能
/// 班次管理功能请使用 ShiftApiService
/// 基于文档: docs/班次报表接口API文档.md
class ShiftApi {
  ShiftApi({required ApiClient apiClient}) : _apiClient = apiClient;

  /// 工厂构造函数，创建基于指定环境的实例
  factory ShiftApi.withEnvironment(ApiEnvironment env) {
    final String baseUrl = ApiConstants.getServiceUrl(BackendService.base, env);
    final ApiClient apiClient = ApiClient(baseUrl: baseUrl);
    return ShiftApi(apiClient: apiClient);
  }
  final ApiClient _apiClient;

  /// 获取班次报表数据
  ///
  /// [shiftId] 班次ID
  /// [format] 响应格式: 'json' 或 'receipt'
  /// [currency] 货币格式: 'IDR', 'USD', 'MYR' 等
  /// [timezone] 时区: 'Asia/Jakarta', 'Asia/Kuala_Lumpur' 等
  /// [includeDetails] 是否包含明细数据
  Future<ShiftReportResponse> getShiftReport(
    int shiftId, {
    String format = 'json',
    String? currency,
    String? timezone,
    bool includeDetails = true,
  }) async {
    try {
      final Map<String, dynamic> queryParams = <String, dynamic>{
        'format': format,
        'include_details': includeDetails.toString(),
      };

      if (currency != null) {
        queryParams['currency'] = currency;
      }

      if (timezone != null) {
        queryParams['timezone'] = timezone;
      }

      debugPrint('ShiftApi: 获取班次报表 - ID: $shiftId, 参数: $queryParams');

      final Response response = await _apiClient.get(
        '/api/v1/shifts/report/$shiftId',
        queryParameters: queryParams,
      );

      debugPrint('ShiftApi: 班次报表响应状态: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData =
            response.data as Map<String, dynamic>;
        final ShiftReportResponse shiftReport =
            ShiftReportResponse.fromJson(responseData);

        debugPrint(
            'ShiftApi: 班次报表获取成功 - 班次: ${shiftReport.data.shiftInfo.shiftNumber}');
        return shiftReport;
      } else {
        throw _handleErrorResponse(response);
      }
    } catch (e) {
      debugPrint('ShiftApi: 获取班次报表失败 - $e');
      rethrow;
    }
  }

  /// 根据班次编号获取班次报表数据
  ///
  /// [shiftNumber] 班次编号
  /// [format] 响应格式: 'json' 或 'receipt'
  /// [currency] 货币格式: 'IDR', 'USD', 'MYR' 等
  /// [timezone] 时区: 'Asia/Jakarta', 'Asia/Kuala_Lumpur' 等
  /// [includeDetails] 是否包含明细数据
  Future<ShiftReportResponse> getShiftReportByNumber(
    String shiftNumber, {
    String format = 'json',
    String? currency,
    String? timezone,
    bool includeDetails = true,
  }) async {
    try {
      final Map<String, dynamic> queryParams = <String, dynamic>{
        'format': format,
        'include_details': includeDetails.toString(),
      };

      if (currency != null) {
        queryParams['currency'] = currency;
      }

      if (timezone != null) {
        queryParams['timezone'] = timezone;
      }

      debugPrint('ShiftApi: 获取班次报表 - 班次号: $shiftNumber, 参数: $queryParams');

      // 尝试使用不同的API端点来获取班次报表
      final Response response = await _apiClient.get(
        '/api/v1/shifts/report/number/$shiftNumber',
        queryParameters: queryParams,
      );

      debugPrint('ShiftApi: 班次报表响应状态: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData =
            response.data as Map<String, dynamic>;
        final ShiftReportResponse shiftReport =
            ShiftReportResponse.fromJson(responseData);

        debugPrint(
            'ShiftApi: 班次报表获取成功 - 班次: ${shiftReport.data.shiftInfo.shiftNumber}');
        return shiftReport;
      } else {
        throw _handleErrorResponse(response);
      }
    } catch (e) {
      debugPrint('ShiftApi: 根据班次编号获取班次报表失败 - $e');
      rethrow;
    }
  }

  /// 获取班次小票格式数据
  ///
  /// [shiftId] 班次ID
  /// [currency] 货币格式
  /// [timezone] 时区
  Future<ShiftReceiptResponse> getShiftReceipt(
    int shiftId, {
    String? currency,
    String? timezone,
  }) async {
    try {
      final Map<String, dynamic> queryParams = <String, dynamic>{
        'format': 'receipt',
      };

      if (currency != null) {
        queryParams['currency'] = currency;
      }

      if (timezone != null) {
        queryParams['timezone'] = timezone;
      }

      debugPrint('ShiftApi: 获取班次小票 - ID: $shiftId, 参数: $queryParams');

      final Response response = await _apiClient.get(
        '/api/v1/shifts/report/$shiftId',
        queryParameters: queryParams,
      );

      debugPrint('ShiftApi: 班次小票响应状态: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData =
            response.data as Map<String, dynamic>;
        final ShiftReceiptResponse receiptResponse =
            ShiftReceiptResponse.fromJson(responseData);

        debugPrint(
            'ShiftApi: 班次小票获取成功 - 小票号: ${receiptResponse.data.receipt.footer.receiptNumber}');
        return receiptResponse;
      } else {
        throw _handleErrorResponse(response);
      }
    } catch (e) {
      debugPrint('ShiftApi: 获取班次小票失败 - $e');
      rethrow;
    }
  }

  /// 处理错误响应
  Exception _handleErrorResponse(dynamic response) {
    try {
      final Map<String, dynamic> errorBody =
          response.data as Map<String, dynamic>;
      final errorCode = errorBody['error']?['code'] ?? 'UNKNOWN_ERROR';
      final errorMessage =
          errorBody['error']?['message'] ?? 'Unknown error occurred';

      debugPrint(
          'ShiftApi: API错误 - 状态码: ${response.statusCode}, 错误码: $errorCode, 消息: $errorMessage');

      switch (response.statusCode) {
        case 400:
          return Exception('请求参数错误: $errorMessage');
        case 401:
          return Exception('未授权访问: $errorMessage');
        case 403:
          return Exception('权限不足: $errorMessage');
        case 404:
          return Exception('班次不存在: $errorMessage');
        case 409:
          return Exception('班次状态冲突: $errorMessage');
        case 500:
          return Exception('服务器内部错误: $errorMessage');
        default:
          return Exception('请求失败 (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      debugPrint('ShiftApi: 解析错误响应失败 - $e');
      return Exception('请求失败 (${response.statusCode}): ${response.data}');
    }
  }
}
