package dto

import (
	"time"

	"fcc-service/pkg/models"
)

// DeviceRequest 设备注册请求
type DeviceRequest struct {
	ID            string               `json:"id" validate:"required"`
	Name          string               `json:"name" validate:"required"`
	Type          models.DeviceType    `json:"type" validate:"required"`
	ControllerID  string               `json:"controller_id" validate:"required"`
	DeviceAddress int                  `json:"device_address" validate:"min=0"`
	StationID     string               `json:"station_id" validate:"required"`
	IslandID      string               `json:"island_id,omitempty"`
	Position      string               `json:"position,omitempty"`
	Config        *models.DeviceConfig `json:"config,omitempty"`
}

// DeviceResponse 设备响应
type DeviceResponse struct {
	ID            string                    `json:"id"`
	Name          string                    `json:"name"`
	Type          string                    `json:"type"`
	ControllerID  string                    `json:"controller_id"`
	DeviceAddress int                       `json:"device_address"`
	StationID     string                    `json:"station_id"`
	IslandID      string                    `json:"island_id,omitempty"`
	Position      string                    `json:"position,omitempty"`
	Config        models.DeviceConfig       `json:"config"`
	Status        models.DeviceStatus       `json:"status"`
	Health        models.DeviceHealth       `json:"health"`
	Capabilities  models.DeviceCapabilities `json:"capabilities"`
	LastSeen      *time.Time                `json:"last_seen,omitempty"`
	CreatedAt     time.Time                 `json:"created_at"`
	UpdatedAt     time.Time                 `json:"updated_at"`
}

// DeviceListResponse 设备列表响应
type DeviceListResponse struct {
	Devices  []DeviceResponse `json:"devices"`
	Total    int              `json:"total"`
	Page     int              `json:"page,omitempty"`
	PageSize int              `json:"page_size,omitempty"`
}

// DeviceStatusResponse 设备状态响应
type DeviceStatusResponse struct {
	DeviceID string     `json:"device_id"`
	Status   string     `json:"status"`
	LastSeen *time.Time `json:"last_seen,omitempty"`
}

// ControllerStatusResponse 控制器状态响应
type ControllerStatusResponse struct {
	ControllerID string     `json:"controller_id"`
	Status       string     `json:"status"`
	LastSeen     *time.Time `json:"last_seen,omitempty"`
}

// AddDeviceRequest 添加设备请求
type AddDeviceRequest struct {
	DeviceID string `json:"device_id" validate:"required"`
}

// WayneDeviceResponse Wayne设备响应
type WayneDeviceResponse struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Address     string     `json:"address"`
	DeviceType  string     `json:"device_type"`
	Status      string     `json:"status"`
	LastUpdated *time.Time `json:"last_updated,omitempty"`
}

// ToModel 转换为模型对象
func (req *DeviceRequest) ToModel() *models.Device {
	var config models.DeviceConfig
	if req.Config != nil {
		config = *req.Config
	}

	return &models.Device{
		ID:            req.ID,
		Name:          req.Name,
		Type:          req.Type,
		ControllerID:  req.ControllerID,
		DeviceAddress: req.DeviceAddress,
		StationID:     req.StationID,
		IslandID:      req.IslandID,
		Position:      req.Position,
		Config:        config,
		Status:        models.DeviceStatusOffline,
		Health:        models.DeviceHealthUnknown,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
}

// FromModel 从模型对象转换
func (resp *DeviceResponse) FromModel(device *models.Device) *DeviceResponse {
	resp.ID = device.ID
	resp.Name = device.Name
	resp.Type = string(device.Type)
	resp.ControllerID = device.ControllerID
	resp.DeviceAddress = device.DeviceAddress
	resp.StationID = device.StationID
	resp.IslandID = device.IslandID
	resp.Position = device.Position
	resp.Config = device.Config
	resp.Status = device.Status
	resp.Health = device.Health
	resp.Capabilities = device.Capabilities
	if device.LastSeen != nil && !device.LastSeen.IsZero() {
		resp.LastSeen = device.LastSeen
	}
	resp.CreatedAt = device.CreatedAt
	resp.UpdatedAt = device.UpdatedAt
	return resp
}
