package dto

import (
	"time"

	"fcc-service/pkg/api"
)

// CommandRequest 命令执行请求
type CommandRequest struct {
	CommandID   string                 `json:"command_id,omitempty"`
	DeviceID    string                 `json:"device_id" validate:"required"`
	CommandType string                 `json:"command_type" validate:"required"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
	Priority    string                 `json:"priority,omitempty"`
	Timeout     int                    `json:"timeout,omitempty"` // 超时时间（秒）
	Async       bool                   `json:"async,omitempty"`
}

// CommandResponse 命令执行响应
type CommandResponse struct {
	CommandID     string                 `json:"command_id"`
	DeviceID      string                 `json:"device_id"`
	CommandType   string                 `json:"command_type"`
	Success       bool                   `json:"success"`
	Data          map[string]interface{} `json:"data,omitempty"`
	Error         string                 `json:"error,omitempty"`
	ExecutionTime int64                  `json:"execution_time_ms"`
	SubmittedAt   time.Time              `json:"submitted_at"`
	CompletedAt   *time.Time             `json:"completed_at,omitempty"`
}

// AsyncCommandResponse 异步命令响应
type AsyncCommandResponse struct {
	CommandID string `json:"command_id"`
	Status    string `json:"status"`
	Message   string `json:"message"`
}

// CommandStatusResponse 命令状态响应
type CommandStatusResponse struct {
	CommandID     string                 `json:"command_id"`
	Status        string                 `json:"status"`
	DeviceID      string                 `json:"device_id"`
	CommandType   string                 `json:"command_type"`
	Success       *bool                  `json:"success,omitempty"`
	Data          map[string]interface{} `json:"data,omitempty"`
	Error         string                 `json:"error,omitempty"`
	Progress      int                    `json:"progress,omitempty"` // 0-100
	ExecutionTime *int64                 `json:"execution_time_ms,omitempty"`
	SubmittedAt   time.Time              `json:"submitted_at"`
	StartedAt     *time.Time             `json:"started_at,omitempty"`
	CompletedAt   *time.Time             `json:"completed_at,omitempty"`
}

// BatchCommandRequest 批量命令请求
type BatchCommandRequest struct {
	Commands []CommandRequest `json:"commands" validate:"required,min=1,max=100"`
	Parallel bool             `json:"parallel,omitempty"`
}

// BatchCommandResponse 批量命令响应
type BatchCommandResponse struct {
	BatchID     string            `json:"batch_id"`
	Total       int               `json:"total"`
	Successful  int               `json:"successful"`
	Failed      int               `json:"failed"`
	Results     []CommandResponse `json:"results"`
	TotalTime   int64             `json:"total_time_ms"`
	SubmittedAt time.Time         `json:"submitted_at"`
	CompletedAt time.Time         `json:"completed_at"`
}

// CommandHistoryRequest 命令历史查询请求
type CommandHistoryRequest struct {
	DeviceID    string    `json:"device_id,omitempty"`
	CommandType string    `json:"command_type,omitempty"`
	Status      string    `json:"status,omitempty"`
	StartTime   time.Time `json:"start_time,omitempty"`
	EndTime     time.Time `json:"end_time,omitempty"`
	Page        int       `json:"page,omitempty"`
	PageSize    int       `json:"page_size,omitempty"`
}

// CommandHistoryResponse 命令历史响应
type CommandHistoryResponse struct {
	Commands []CommandResponse `json:"commands"`
	Total    int               `json:"total"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
}

// ToAPIRequest 转换为API请求对象
func (req *CommandRequest) ToAPIRequest() *api.CommandRequest {
	priority := api.CommandPriorityNormal
	switch req.Priority {
	case "high":
		priority = api.CommandPriorityHigh
	case "low":
		priority = api.CommandPriorityLow
	}

	timeout := 30 * time.Second
	if req.Timeout > 0 {
		timeout = time.Duration(req.Timeout) * time.Second
	}

	return &api.CommandRequest{
		CommandID:   req.CommandID,
		DeviceID:    req.DeviceID,
		CommandType: req.CommandType,
		Parameters:  req.Parameters,
		Priority:    priority,
		Timeout:     timeout,
	}
}

// FromAPIResponse 从API响应转换
func (resp *CommandResponse) FromAPIResponse(apiResp *api.CommandResponse) *CommandResponse {
	resp.CommandID = apiResp.CommandID
	resp.DeviceID = apiResp.DeviceID
	resp.Success = apiResp.Success
	resp.Data = apiResp.Result
	resp.Error = apiResp.Error
	resp.ExecutionTime = apiResp.Duration.Milliseconds()
	resp.SubmittedAt = apiResp.ExecutedAt
	if !apiResp.ExecutedAt.IsZero() {
		resp.CompletedAt = &apiResp.ExecutedAt
	}
	return resp
}
