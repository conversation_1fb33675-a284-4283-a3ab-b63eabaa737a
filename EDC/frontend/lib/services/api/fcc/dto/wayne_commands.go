package dto

import (
	"errors"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
)

// WayneCommandType Wayne命令类型
type WayneCommandType string

const (
	// CD1 - 泵控制命令
	WayneCommandAuthorize         WayneCommandType = "authorize"           // CD1: 0x06 授权
	WayneCommandReset             WayneCommandType = "reset"               // CD1: 0x05 复位
	WayneCommandStop              WayneCommandType = "stop"                // CD1: 0x08 停止
	WayneCommandReturnStatus      WayneCommandType = "return_status"       // CD1: 0x00 返回状态
	WayneCommandReturnFillingInfo WayneCommandType = "return_filling_info" // CD1: 0x04 返回填充信息

	// CD2 - 允许的喷嘴号
	WayneCommandConfigureNozzles WayneCommandType = "configure_nozzles" // CD2: 配置允许的喷嘴

	// CD3 - 预设体积
	WayneCommandPresetVolume WayneCommandType = "preset_volume" // CD3: 预设体积

	// CD4 - 预设金额
	WayneCommandPresetAmount WayneCommandType = "preset_amount" // CD4: 预设金额

	// CD5 - 价格更新
	WayneCommandUpdatePrices WayneCommandType = "update_prices" // CD5: 价格更新

	// CD14 - 暂停请求
	WayneCommandSuspendNozzle WayneCommandType = "suspend_nozzle" // CD14: 暂停喷嘴

	// CD15 - 恢复请求
	WayneCommandResumeNozzle WayneCommandType = "resume_nozzle" // CD15: 恢复喷嘴

	// CD101 - 请求总计数器
	WayneCommandRequestCounters WayneCommandType = "request_counters" // CD101: 请求计数器
)

// WayneCommandRequest Wayne协议命令请求基础结构
type WayneCommandRequest struct {
	CommandID string           `json:"command_id,omitempty"`
	DeviceID  string           `json:"device_id" validate:"required" example:"device_001"`
	Command   WayneCommandType `json:"command" validate:"required" example:"authorize"`
	Priority  string           `json:"priority,omitempty" example:"normal"` // high, normal, low
	Timeout   int              `json:"timeout,omitempty" example:"30"`      // 超时时间（秒）
	Async     bool             `json:"async,omitempty" example:"false"`
}

// AuthorizeRequest CD1: 授权命令 (0x06)
// 授权设备开始加油操作
type AuthorizeRequest struct {
	WayneCommandRequest
	// 授权命令无需额外参数
}

// ResetRequest CD1: 复位命令 (0x05)
// 复位设备到初始状态
type ResetRequest struct {
	WayneCommandRequest
	// 复位命令无需额外参数
}

// StopRequest CD1: 停止命令 (0x08)
// 停止当前加油操作
type StopRequest struct {
	WayneCommandRequest
	// 停止命令无需额外参数
}

// ReturnStatusRequest CD1: 返回状态命令 (0x00)
// 查询设备当前状态
type ReturnStatusRequest struct {
	WayneCommandRequest
	// 状态查询命令无需额外参数
}

// ReturnFillingInfoRequest CD1: 返回填充信息命令 (0x04)
// 查询当前加油信息
type ReturnFillingInfoRequest struct {
	WayneCommandRequest
	// 填充信息查询命令无需额外参数
}

// ConfigureNozzlesRequest CD2: 配置允许的喷嘴号
// 指定允许使用的喷嘴号列表
type ConfigureNozzlesRequest struct {
	WayneCommandRequest
	NozzleNumbers []int `json:"nozzle_numbers" validate:"required,min=1,max=8" example:"[1,2,3]"` // 允许的喷嘴号列表 (1-8)
}

// PresetVolumeRequest CD3: 预设体积
// 设置加油停止的体积阈值
type PresetVolumeRequest struct {
	WayneCommandRequest
	Volume   decimal.Decimal `json:"volume" validate:"required" example:"50.00" format:"decimal"` // 预设体积 (升)
	Decimals int             `json:"decimals,omitempty" example:"2"`                              // 小数位数 (0-3)
}

// PresetAmountRequest CD4: 预设金额
// 设置加油停止的金额阈值
type PresetAmountRequest struct {
	WayneCommandRequest
	Amount   decimal.Decimal `json:"amount" validate:"required" example:"100.00" format:"decimal"` // 预设金额
	Decimals int             `json:"decimals,omitempty" example:"2"`                               // 小数位数 (0-3)
}

// NozzlePriceInfo 喷嘴价格信息
type NozzlePriceInfo struct {
	NozzleNumber int             `json:"nozzle_number" validate:"required,min=1,max=8" example:"1"` // 喷嘴号 (1-8)
	Price        decimal.Decimal `json:"price" validate:"required" example:"6.58" format:"decimal"` // 单价
	Decimals     int             `json:"decimals,omitempty" example:"2"`                            // 小数位数 (0-4)
}

// UpdatePricesRequest CD5: 价格更新
// 更新喷嘴油品价格
type UpdatePricesRequest struct {
	WayneCommandRequest
	Prices []NozzlePriceInfo `json:"prices" validate:"required,min=1,max=8"` // 价格列表
}

// SuspendNozzleRequest CD14: 暂停喷嘴
// 暂停指定喷嘴的加油操作
type SuspendNozzleRequest struct {
	WayneCommandRequest
	NozzleNumber int `json:"nozzle_number" validate:"required,min=1,max=8" example:"1"` // 要暂停的喷嘴号 (1-8)
}

// ResumeNozzleRequest CD15: 恢复喷嘴
// 恢复指定喷嘴的加油操作
type ResumeNozzleRequest struct {
	WayneCommandRequest
	NozzleNumber int `json:"nozzle_number" validate:"required,min=1,max=8" example:"1"` // 要恢复的喷嘴号 (1-8)
}

// CounterType 计数器类型
type CounterType string

const (
	// 体积计数器
	CounterTypeVolumeNozzle1 CounterType = "volume_nozzle_1" // 0x01: 喷嘴1体积计数器
	CounterTypeVolumeNozzle2 CounterType = "volume_nozzle_2" // 0x02: 喷嘴2体积计数器
	CounterTypeVolumeNozzle3 CounterType = "volume_nozzle_3" // 0x03: 喷嘴3体积计数器
	CounterTypeVolumeNozzle4 CounterType = "volume_nozzle_4" // 0x04: 喷嘴4体积计数器
	CounterTypeVolumeNozzle5 CounterType = "volume_nozzle_5" // 0x05: 喷嘴5体积计数器
	CounterTypeVolumeNozzle6 CounterType = "volume_nozzle_6" // 0x06: 喷嘴6体积计数器
	CounterTypeVolumeNozzle7 CounterType = "volume_nozzle_7" // 0x07: 喷嘴7体积计数器
	CounterTypeVolumeNozzle8 CounterType = "volume_nozzle_8" // 0x08: 喷嘴8体积计数器
	CounterTypeVolumeTotal   CounterType = "volume_total"    // 0x09: 总体积计数器

	// 金额计数器
	CounterTypeAmountNozzle1 CounterType = "amount_nozzle_1" // 0x11: 喷嘴1金额计数器
	CounterTypeAmountNozzle2 CounterType = "amount_nozzle_2" // 0x12: 喷嘴2金额计数器
	CounterTypeAmountNozzle3 CounterType = "amount_nozzle_3" // 0x13: 喷嘴3金额计数器
	CounterTypeAmountNozzle4 CounterType = "amount_nozzle_4" // 0x14: 喷嘴4金额计数器
	CounterTypeAmountNozzle5 CounterType = "amount_nozzle_5" // 0x15: 喷嘴5金额计数器
	CounterTypeAmountNozzle6 CounterType = "amount_nozzle_6" // 0x16: 喷嘴6金额计数器
	CounterTypeAmountNozzle7 CounterType = "amount_nozzle_7" // 0x17: 喷嘴7金额计数器
	CounterTypeAmountNozzle8 CounterType = "amount_nozzle_8" // 0x18: 喷嘴8金额计数器
	CounterTypeAmountTotal   CounterType = "amount_total"    // 0x19: 总金额计数器
)

// RequestCountersRequest CD101: 请求总计数器
// 查询设备计数器数据
type RequestCountersRequest struct {
	WayneCommandRequest
	CounterType CounterType `json:"counter_type" validate:"required" example:"amount_total"` // 计数器类型
}

// WayneCommandResponse Wayne命令统一响应格式
type WayneCommandResponse struct {
	CommandID     string                 `json:"command_id"`
	DeviceID      string                 `json:"device_id"`
	Command       WayneCommandType       `json:"command"`
	Success       bool                   `json:"success"`
	Data          map[string]interface{} `json:"data,omitempty"`
	Error         string                 `json:"error,omitempty"`
	ExecutionTime int64                  `json:"execution_time_ms"`
	SubmittedAt   time.Time              `json:"submitted_at"`
	CompletedAt   *time.Time             `json:"completed_at,omitempty"`
	ProtocolInfo  *ProtocolInfo          `json:"protocol_info,omitempty"`
}

// ProtocolInfo 协议相关信息
type ProtocolInfo struct {
	Protocol        string `json:"protocol" example:"Wayne DART v1.3"`
	TransactionType string `json:"transaction_type" example:"CD1"`
	TransactionCode string `json:"transaction_code" example:"0x06"`
	ResponseTime    int64  `json:"response_time_ms" example:"25"`
}

// GetCounterTypeByte 获取计数器类型对应的字节值
func (ct CounterType) GetCounterTypeByte() byte {
	counterTypeMap := map[CounterType]byte{
		CounterTypeVolumeNozzle1: 0x01,
		CounterTypeVolumeNozzle2: 0x02,
		CounterTypeVolumeNozzle3: 0x03,
		CounterTypeVolumeNozzle4: 0x04,
		CounterTypeVolumeNozzle5: 0x05,
		CounterTypeVolumeNozzle6: 0x06,
		CounterTypeVolumeNozzle7: 0x07,
		CounterTypeVolumeNozzle8: 0x08,
		CounterTypeVolumeTotal:   0x09,
		CounterTypeAmountNozzle1: 0x11,
		CounterTypeAmountNozzle2: 0x12,
		CounterTypeAmountNozzle3: 0x13,
		CounterTypeAmountNozzle4: 0x14,
		CounterTypeAmountNozzle5: 0x15,
		CounterTypeAmountNozzle6: 0x16,
		CounterTypeAmountNozzle7: 0x17,
		CounterTypeAmountNozzle8: 0x18,
		CounterTypeAmountTotal:   0x19,
	}
	return counterTypeMap[ct]
}

// Validate 验证Wayne命令请求
func (req *WayneCommandRequest) Validate() error {
	if req.DeviceID == "" {
		return fmt.Errorf("device_id is required")
	}
	if req.Command == "" {
		return fmt.Errorf("command is required")
	}
	return nil
}

// Validate 验证配置喷嘴请求
func (req *ConfigureNozzlesRequest) Validate() error {
	if err := req.WayneCommandRequest.Validate(); err != nil {
		return err
	}
	if len(req.NozzleNumbers) == 0 {
		return fmt.Errorf("nozzle_numbers is required and cannot be empty")
	}
	for i, nozzle := range req.NozzleNumbers {
		if nozzle < 1 || nozzle > 8 {
			return fmt.Errorf("nozzle_numbers[%d]: nozzle number must be between 1 and 8, got %d", i, nozzle)
		}
	}
	return nil
}

// Validate 验证预设体积请求
func (req *PresetVolumeRequest) Validate() error {
	if err := req.WayneCommandRequest.Validate(); err != nil {
		return err
	}
	if req.Volume.IsZero() || req.Volume.IsNegative() {
		return fmt.Errorf("volume must be greater than 0")
	}
	if req.Decimals < 0 || req.Decimals > 3 {
		return fmt.Errorf("decimals must be between 0 and 3")
	}
	return nil
}

// Validate 验证预设金额请求
func (req *PresetAmountRequest) Validate() error {
	if err := req.WayneCommandRequest.Validate(); err != nil {
		return err
	}
	if req.Amount.IsZero() || req.Amount.IsNegative() {
		return fmt.Errorf("amount must be greater than 0")
	}
	if req.Decimals < 0 || req.Decimals > 3 {
		return fmt.Errorf("decimals must be between 0 and 3")
	}
	return nil
}

// Validate 验证价格更新请求
func (req *UpdatePricesRequest) Validate() error {
	if err := req.WayneCommandRequest.Validate(); err != nil {
		return err
	}
	if len(req.Prices) == 0 {
		return fmt.Errorf("prices is required and cannot be empty")
	}
	for i, price := range req.Prices {
		if price.NozzleNumber < 1 || price.NozzleNumber > 8 {
			return fmt.Errorf("prices[%d]: nozzle_number must be between 1 and 8, got %d", i, price.NozzleNumber)
		}
		if price.Price.IsZero() || price.Price.IsNegative() {
			return fmt.Errorf("prices[%d]: price must be greater than 0", i)
		}
		if price.Decimals < 0 || price.Decimals > 4 {
			return fmt.Errorf("prices[%d]: decimals must be between 0 and 4", i)
		}
	}
	return nil
}

// Validate 验证暂停喷嘴请求
func (req *SuspendNozzleRequest) Validate() error {
	if err := req.WayneCommandRequest.Validate(); err != nil {
		return err
	}
	if req.NozzleNumber < 1 || req.NozzleNumber > 8 {
		return fmt.Errorf("nozzle_number must be between 1 and 8, got %d", req.NozzleNumber)
	}
	return nil
}

// Validate 验证恢复喷嘴请求
func (req *ResumeNozzleRequest) Validate() error {
	if err := req.WayneCommandRequest.Validate(); err != nil {
		return err
	}
	if req.NozzleNumber < 1 || req.NozzleNumber > 8 {
		return fmt.Errorf("nozzle_number must be between 1 and 8, got %d", req.NozzleNumber)
	}
	return nil
}

// Validate 验证请求计数器请求
func (req *RequestCountersRequest) Validate() error {
	if err := req.WayneCommandRequest.Validate(); err != nil {
		return err
	}
	if req.CounterType == "" {
		return fmt.Errorf("counter_type is required")
	}
	// 验证计数器类型是否有效
	if req.CounterType.GetCounterTypeByte() == 0 {
		return fmt.Errorf("invalid counter_type: %s", req.CounterType)
	}
	return nil
}

// UpdateSingleNozzlePriceRequest 单喷嘴价格更新请求 - 最常用的接口调价场景
type UpdateSingleNozzlePriceRequest struct {
	WayneCommandRequest
	NozzleNumber byte            `json:"nozzle_number" validate:"required,min=1,max=15" example:"1"`
	Price        decimal.Decimal `json:"price" validate:"required" example:"12840.0"` // 支持高面值货币如印尼盾
}

// Validate 验证单喷嘴价格更新请求
func (r *UpdateSingleNozzlePriceRequest) Validate() error {
	if err := r.WayneCommandRequest.Validate(); err != nil {
		return err
	}

	if r.NozzleNumber < 1 || r.NozzleNumber > 15 {
		return errors.New("nozzle_number must be between 1 and 15")
	}

	priceFloat, _ := r.Price.Float64()
	if priceFloat <= 0 {
		return errors.New("price must be positive")
	}

	// 🔧 支持高面值货币的价格验证
	if priceFloat > 999999.999 {
		return errors.New("price exceeds BCD 3-byte maximum (999999.999)")
	}

	return nil
}

// UpdateMultipleNozzlePricesRequest 多喷嘴选择性价格更新请求 - 批量调价场景
type UpdateMultipleNozzlePricesRequest struct {
	WayneCommandRequest
	PriceUpdates map[byte]decimal.Decimal `json:"price_updates" validate:"required,min=1,max=8" example:"{\"1\":12840.0,\"2\":13000.0}"`
}

// Validate 验证多喷嘴价格更新请求
func (r *UpdateMultipleNozzlePricesRequest) Validate() error {
	if err := r.WayneCommandRequest.Validate(); err != nil {
		return err
	}

	if len(r.PriceUpdates) == 0 {
		return errors.New("price_updates cannot be empty")
	}

	if len(r.PriceUpdates) > 8 {
		return errors.New("price_updates cannot exceed 8 nozzles")
	}

	for nozzleNumber, price := range r.PriceUpdates {
		if nozzleNumber < 1 || nozzleNumber > 15 {
			return fmt.Errorf("nozzle number %d must be between 1 and 15", nozzleNumber)
		}

		priceFloat, _ := price.Float64()
		if priceFloat <= 0 {
			return fmt.Errorf("price for nozzle %d must be positive", nozzleNumber)
		}

		// 🔧 支持高面值货币的价格验证
		if priceFloat > 999999.999 {
			return fmt.Errorf("price for nozzle %d exceeds BCD 3-byte maximum (999999.999)", nozzleNumber)
		}
	}

	return nil
}
