package dto

import (
	"time"

	"github.com/shopspring/decimal"
	"fcc-service/pkg/models"
)

// NozzleStatusResponse 喷嘴状态响应
type NozzleStatusResponse struct {
	NozzleNumber  byte            `json:"nozzle_number"`  // 喷嘴编号
	Status        string          `json:"status"`         // 状态：idle, selected, authorized, out, filling, completed, suspended, error, maintenance
	IsOut         bool            `json:"is_out"`         // 是否拔出
	IsSelected    bool            `json:"is_selected"`    // 是否选中
	IsEnabled     bool            `json:"is_enabled"`     // 是否启用
	CurrentPrice  decimal.Decimal `json:"current_price"`  // 当前单价
	CurrentVolume decimal.Decimal `json:"current_volume"` // 当前体积
	CurrentAmount decimal.Decimal `json:"current_amount"` // 当前金额
	TotalVolume   decimal.Decimal `json:"total_volume"`   // 累计体积
	TotalAmount   decimal.Decimal `json:"total_amount"`   // 累计金额
	LastUpdate    time.Time       `json:"last_update"`    // 最后更新时间
	
	// 扩展信息
	FuelGradeName    *string          `json:"fuel_grade_name,omitempty"`    // 油品名称
	TransactionCount int64            `json:"transaction_count"`            // 交易次数
	PresetVolume     *decimal.Decimal `json:"preset_volume,omitempty"`     // 预设体积
	PresetAmount     *decimal.Decimal `json:"preset_amount,omitempty"`     // 预设金额
}

// DeviceNozzlesResponse 设备喷嘴列表响应
type DeviceNozzlesResponse struct {
	DeviceID     string                  `json:"device_id"`     // 设备ID
	DeviceName   string                  `json:"device_name"`   // 设备名称
	TotalNozzles int                     `json:"total_nozzles"` // 总喷嘴数
	ActiveCount  int                     `json:"active_count"`  // 活跃喷嘴数
	Nozzles      []NozzleStatusResponse  `json:"nozzles"`       // 喷嘴列表
	LastUpdate   time.Time               `json:"last_update"`   // 最后更新时间
}

// NozzleTransactionResponse 喷嘴当前交易信息
type NozzleTransactionResponse struct {
	NozzleNumber    byte            `json:"nozzle_number"`    // 喷嘴编号
	TransactionID   *string         `json:"transaction_id"`   // 交易ID
	Status          string          `json:"status"`           // 交易状态
	StartTime       *time.Time      `json:"start_time"`       // 开始时间
	CurrentVolume   decimal.Decimal `json:"current_volume"`   // 当前体积
	CurrentAmount   decimal.Decimal `json:"current_amount"`   // 当前金额
	UnitPrice       decimal.Decimal `json:"unit_price"`       // 单价
	PresetVolume    *decimal.Decimal `json:"preset_volume,omitempty"`  // 预设体积
	PresetAmount    *decimal.Decimal `json:"preset_amount,omitempty"`  // 预设金额
	Duration        *time.Duration  `json:"duration,omitempty"`       // 交易持续时间
	IsActive        bool            `json:"is_active"`        // 是否活跃交易
}

// NozzleStatsResponse 喷嘴统计信息
type NozzleStatsResponse struct {
	NozzleNumber     byte            `json:"nozzle_number"`     // 喷嘴编号
	TotalVolume      decimal.Decimal `json:"total_volume"`      // 累计体积
	TotalAmount      decimal.Decimal `json:"total_amount"`      // 累计金额
	TransactionCount int64           `json:"transaction_count"` // 交易次数
	AverageVolume    decimal.Decimal `json:"average_volume"`    // 平均体积
	AverageAmount    decimal.Decimal `json:"average_amount"`    // 平均金额
	LastTransaction  *time.Time      `json:"last_transaction"`  // 最后交易时间
	Utilization      float64         `json:"utilization"`       // 使用率（0-1）
}

// 转换函数：从模型转换为DTO

// FromNozzleModel 从Nozzle模型转换为NozzleStatusResponse
func FromNozzleModel(nozzle *models.Nozzle) NozzleStatusResponse {
	response := NozzleStatusResponse{
		NozzleNumber:     nozzle.Number,
		Status:           string(nozzle.Status),
		IsOut:            nozzle.IsOut,
		IsSelected:       nozzle.IsSelected,
		IsEnabled:        nozzle.IsEnabled,
		CurrentPrice:     nozzle.CurrentPrice,
		CurrentVolume:    nozzle.CurrentVolume,
		CurrentAmount:    nozzle.CurrentAmount,
		TotalVolume:      nozzle.TotalVolume,
		TotalAmount:      nozzle.TotalAmount,
		TransactionCount: nozzle.TransactionCount,
		PresetVolume:     nozzle.PresetVolume,
		PresetAmount:     nozzle.PresetAmount,
		LastUpdate:       nozzle.UpdatedAt,
	}

	// 添加油品信息
	if nozzle.FuelGrade != nil {
		response.FuelGradeName = &nozzle.FuelGrade.Name
	}

	return response
}

// FromNozzleModels 从Nozzle模型列表转换为DeviceNozzlesResponse
func FromNozzleModels(deviceID, deviceName string, nozzles []*models.Nozzle) DeviceNozzlesResponse {
	response := DeviceNozzlesResponse{
		DeviceID:     deviceID,
		DeviceName:   deviceName,
		TotalNozzles: len(nozzles),
		ActiveCount:  0,
		Nozzles:      make([]NozzleStatusResponse, len(nozzles)),
		LastUpdate:   time.Now(),
	}

	for i, nozzle := range nozzles {
		response.Nozzles[i] = FromNozzleModel(nozzle)
		
		// 统计活跃喷嘴
		if nozzle.IsActive() {
			response.ActiveCount++
		}
		
		// 更新最后更新时间
		if nozzle.UpdatedAt.After(response.LastUpdate) {
			response.LastUpdate = nozzle.UpdatedAt
		}
	}

	return response
}

// FromNozzleTransaction 从交易信息转换为NozzleTransactionResponse
func FromNozzleTransaction(nozzle *models.Nozzle, transactionID *string, startTime *time.Time) NozzleTransactionResponse {
	response := NozzleTransactionResponse{
		NozzleNumber:  nozzle.Number,
		TransactionID: transactionID,
		Status:        string(nozzle.Status),
		StartTime:     startTime,
		CurrentVolume: nozzle.CurrentVolume,
		CurrentAmount: nozzle.CurrentAmount,
		UnitPrice:     nozzle.CurrentPrice,
		PresetVolume:  nozzle.PresetVolume,
		PresetAmount:  nozzle.PresetAmount,
		IsActive:      nozzle.IsTransactionInProgress(),
	}

	// 计算交易持续时间
	if startTime != nil && response.IsActive {
		duration := time.Since(*startTime)
		response.Duration = &duration
	}

	// 计算单价（如果有体积数据）
	if !nozzle.CurrentVolume.IsZero() && !nozzle.CurrentAmount.IsZero() {
		response.UnitPrice = nozzle.CurrentAmount.Div(nozzle.CurrentVolume)
	}

	return response
} 