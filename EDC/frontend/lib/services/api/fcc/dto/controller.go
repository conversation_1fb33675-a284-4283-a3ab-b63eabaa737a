package dto

import (
	"time"

	"fcc-service/pkg/models"
)

// ControllerRequest 控制器注册请求
type ControllerRequest struct {
	ID        string                  `json:"id" validate:"required"`
	Protocol  models.ProtocolType     `json:"protocol" validate:"required"`
	Type      models.DeviceType       `json:"type" validate:"required"`
	StationID string                  `json:"station_id" validate:"required"`
	Address   string                  `json:"address" validate:"required"`
	Name      string                  `json:"name,omitempty"`
	Config    ControllerConfigRequest `json:"config" validate:"required"`
}

// ControllerConfigRequest 控制器配置请求
type ControllerConfigRequest struct {
	SerialPort   string                 `json:"serial_port,omitempty"`
	BaudRate     int                    `json:"baud_rate,omitempty"`
	DataBits     int                    `json:"data_bits,omitempty"`
	StopBits     int                    `json:"stop_bits,omitempty"`
	Parity       string                 `json:"parity,omitempty"`
	Host         string                 `json:"host,omitempty"`
	Port         int                    `json:"port,omitempty"`
	Timeout      int                    `json:"timeout,omitempty"`
	ReadTimeout  int                    `json:"read_timeout,omitempty"`
	WriteTimeout int                    `json:"write_timeout,omitempty"`
	MaxRetries   int                    `json:"max_retries,omitempty"`
	ExtraConfig  map[string]interface{} `json:"extra_config,omitempty"`
}

// ControllerResponse 控制器响应
type ControllerResponse struct {
	ID        string                   `json:"id"`
	Protocol  models.ProtocolType      `json:"protocol"`
	Type      models.DeviceType        `json:"type"`
	StationID string                   `json:"station_id"`
	Address   string                   `json:"address"`
	Name      string                   `json:"name"`
	Status    models.DeviceStatus      `json:"status"`
	Health    models.DeviceHealth      `json:"health"`
	Config    ControllerConfigResponse `json:"config"`
	LastSeen  *time.Time               `json:"last_seen,omitempty"`
	Metrics   *ControllerMetrics       `json:"metrics,omitempty"`
	CreatedAt time.Time                `json:"created_at"`
	UpdatedAt time.Time                `json:"updated_at"`
}

// ControllerConfigResponse 控制器配置响应
type ControllerConfigResponse struct {
	SerialPort   string                 `json:"serial_port,omitempty"`
	BaudRate     int                    `json:"baud_rate,omitempty"`
	DataBits     int                    `json:"data_bits,omitempty"`
	StopBits     int                    `json:"stop_bits,omitempty"`
	Parity       string                 `json:"parity,omitempty"`
	Host         string                 `json:"host,omitempty"`
	Port         int                    `json:"port,omitempty"`
	Timeout      int                    `json:"timeout,omitempty"`
	ReadTimeout  int                    `json:"read_timeout,omitempty"`
	WriteTimeout int                    `json:"write_timeout,omitempty"`
	MaxRetries   int                    `json:"max_retries,omitempty"`
	ExtraConfig  map[string]interface{} `json:"extra_config,omitempty"`
}

// ControllerMetrics 控制器指标
type ControllerMetrics struct {
	TotalCommands    int64      `json:"total_commands"`
	SuccessfulCmds   int64      `json:"successful_commands"`
	FailedCommands   int64      `json:"failed_commands"`
	AvgResponseTime  float64    `json:"avg_response_time_ms"`
	LastCommandTime  *time.Time `json:"last_command_time,omitempty"`
	ConnectionUptime string     `json:"connection_uptime"`
}

// ControllerListResponse 控制器列表响应
type ControllerListResponse struct {
	Controllers []ControllerResponse `json:"controllers"`
	Total       int                  `json:"total"`
	Page        int                  `json:"page,omitempty"`
	PageSize    int                  `json:"page_size,omitempty"`
}

// ToModel 转换为模型对象
func (req *ControllerRequest) ToModel() *models.Controller {
	return &models.Controller{
		ID:        req.ID,
		Protocol:  req.Protocol,
		Type:      req.Type,
		StationID: req.StationID,
		Address:   req.Address,
		Name:      req.Name,
		Status:    models.DeviceStatusOffline,
		Health:    models.DeviceHealthUnknown,
		Config: models.ControllerConfig{
			SerialPort:   req.Config.SerialPort,
			BaudRate:     req.Config.BaudRate,
			DataBits:     req.Config.DataBits,
			StopBits:     req.Config.StopBits,
			Parity:       req.Config.Parity,
			Host:         req.Config.Host,
			Port:         req.Config.Port,
			Timeout:      req.Config.Timeout,
			ReadTimeout:  req.Config.ReadTimeout,
			WriteTimeout: req.Config.WriteTimeout,
			MaxRetries:   req.Config.MaxRetries,
			ExtraConfig:  req.Config.ExtraConfig,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// FromModel 从模型对象转换
func (resp *ControllerResponse) FromModel(controller *models.Controller) *ControllerResponse {
	resp.ID = controller.ID
	resp.Protocol = controller.Protocol
	resp.Type = controller.Type
	resp.StationID = controller.StationID
	resp.Address = controller.Address
	resp.Name = controller.Name
	resp.Status = controller.Status
	resp.Health = controller.Health
	resp.Config = ControllerConfigResponse{
		SerialPort:   controller.Config.SerialPort,
		BaudRate:     controller.Config.BaudRate,
		DataBits:     controller.Config.DataBits,
		StopBits:     controller.Config.StopBits,
		Parity:       controller.Config.Parity,
		Host:         controller.Config.Host,
		Port:         controller.Config.Port,
		Timeout:      controller.Config.Timeout,
		ReadTimeout:  controller.Config.ReadTimeout,
		WriteTimeout: controller.Config.WriteTimeout,
		MaxRetries:   controller.Config.MaxRetries,
		ExtraConfig:  controller.Config.ExtraConfig,
	}
	if controller.LastSeen != nil && !controller.LastSeen.IsZero() {
		resp.LastSeen = controller.LastSeen
	}
	resp.CreatedAt = controller.CreatedAt
	resp.UpdatedAt = controller.UpdatedAt
	return resp
}
