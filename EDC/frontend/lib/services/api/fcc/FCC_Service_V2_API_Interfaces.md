# FCC Service V2 API 接口清单

**生成时间**: 2024年
**总接口数**: 39个API接口

---

## 📋 核心模型路径索引

| 分类 | 文件路径 | 主要结构 | 说明 |
|------|----------|----------|------|
| 设备管理 | `internal/server/dto/device.go` | `DeviceRequest`, `DeviceResponse` | 设备CRUD操作 |
| 喷嘴管理 | `internal/server/dto/nozzle.go` | `NozzleStatusResponse`, `DeviceNozzlesResponse` | 喷嘴状态和交易信息 |
| Wayne协议 | `internal/server/dto/wayne_commands.go` | `WayneCommandRequest`, `WayneCommandResponse` | Wayne DART协议命令 |
| 通用命令 | `internal/server/dto/command.go` | `CommandRequest`, `CommandResponse` | 通用设备命令 |
| 设备模型 | `pkg/models/device.go` | `Device`, `Controller` | 设备和控制器核心模型 |
| 喷嘴模型 | `pkg/models/nozzle.go` | `Nozzle`, `Transaction` | 喷嘴和交易模型 |
| V2状态模型 | `pkg/models/v2/device_state_nozzle.go` | `NozzleStateData`, `DeviceStateData` | V2架构状态管理 |

---

## 1. 系统状态 API

### 基础路径: `/api/v2`

| HTTP方法 | 完整路径 | 功能描述 |
|---------|----------|----------|
| GET | `/api/v2/health` | 系统健康检查 |
| GET | `/api/v2/status` | 系统运行状态 |
| GET | `/api/v2/metrics` | 系统性能指标 |
| GET | `/api/v2/cache/stats` | 缓存状态统计 |

---

## 2. 设备管理 API

### 基础路径: `/api/v2/devices`
### 处理器: `DeviceEchoHandlerV2`

| HTTP方法 | 完整路径 | 请求DTO | 响应DTO | 功能描述 |
|---------|----------|---------|---------|----------|
| GET | `/api/v2/devices` | 查询参数 | `DeviceListResponse` | 获取所有设备列表 |
| POST | `/api/v2/devices` | `DeviceRequest` | `DeviceResponse` | 创建新设备 |
| GET | `/api/v2/devices/:id` | - | `DeviceResponse` | 获取指定设备详细信息 |
| PUT | `/api/v2/devices/:id` | `DeviceRequest` | `DeviceResponse` | 更新设备配置 |
| DELETE | `/api/v2/devices/:id` | - | - | 删除指定设备 |
| GET | `/api/v2/devices/:id/status` | - | `DeviceStatusResponse` | 获取设备实时状态 |
| POST | `/api/v2/devices/:id/commands` | `CommandRequest` | `CommandResponse` | 向设备发送控制命令 |
| GET | `/api/v2/devices/:id/pump/status` | - | 内联结构 | 获取泵设备状态信息 |
| GET | `/api/v2/devices/:id/pump/totals` | - | 内联结构 | 获取泵设备总量数据 |

---

## 3. 喷嘴管理 API

### 基础路径: `/api/v2/devices/:id/nozzles`
### 处理器: `NozzleEchoHandlerV2`
### DTO结构: `internal/server/dto/nozzle.go`

| HTTP方法 | 完整路径 | 响应DTO | 功能描述 |
|---------|----------|---------|----------|
| GET | `/api/v2/devices/:id/nozzles` | `DeviceNozzlesResponse` | 获取设备的所有喷嘴状态 |
| GET | `/api/v2/devices/:id/nozzles/:number` | `NozzleStatusResponse` | 获取单个喷嘴状态 |
| GET | `/api/v2/devices/:id/nozzles/:number/transaction` | `NozzleTransactionResponse` | 获取喷嘴当前交易信息 |
| GET | `/api/v2/devices/:id/nozzles/:number/stats` | `NozzleStatsResponse` | 获取喷嘴统计信息 |

**核心响应结构**:
- `NozzleStatusResponse`: 喷嘴状态（编号、状态、价格、体积、金额等）
- `DeviceNozzlesResponse`: 设备喷嘴列表（总数、活跃数、喷嘴列表）
- `NozzleTransactionResponse`: 当前交易信息（交易ID、开始时间、持续时间等）
- `NozzleStatsResponse`: 统计信息（累计数据、使用率、平均值等）

---

## 4. 设备交易 API

### 基础路径: `/api/v2/devices/:id/transactions`
### 处理器: `TransactionHandler`

| HTTP方法 | 完整路径 | 响应模型 | 功能描述 |
|---------|----------|----------|----------|
| GET | `/api/v2/devices/:id/transactions` | `[]Transaction` | 获取指定设备的所有交易记录 |
| GET | `/api/v2/devices/:id/transactions/summary` | 内联统计结构 | 获取设备交易数据汇总统计 |

---

## 5. 交易管理 API

### 基础路径: `/api/v2/transactions`
### 处理器: `TransactionHandler`

| HTTP方法 | 完整路径 | 响应模型 | 功能描述 |
|---------|----------|----------|----------|
| GET | `/api/v2/transactions` | `[]Transaction` | 获取系统所有交易列表 |
| GET | `/api/v2/transactions/:id` | `Transaction` | 获取指定交易的详细信息 |
| GET | `/api/v2/transactions/stats` | 内联统计结构 | 获取交易统计分析数据 |
| GET | `/api/v2/transactions/:id/pump-readings` | `Transaction` + 泵码字段 | 获取交易的泵码读数数据 |
| GET | `/api/v2/transactions/pump-issues` | `[]Transaction` | 获取存在泵码问题的交易列表 |

---

## 6. 调度任务管理 API

### 基础路径: `/api/v2/dispatch`

| HTTP方法 | 完整路径 | 功能描述 |
|---------|----------|----------|
| GET | `/api/v2/dispatch/status` | 获取调度任务运行状态 |
| POST | `/api/v2/dispatch/start` | 启动所有设备调度任务 |
| POST | `/api/v2/dispatch/stop` | 停止所有设备调度任务 |
| GET | `/api/v2/dispatch/devices` | 获取调度中的设备列表和状态 |

---

## 7. Wayne DART 协议 API

### 基础路径: `/api/v2/wayne`
### 处理器: `WayneCommandHandler`
### DTO结构: `internal/server/dto/wayne_commands.go`

### 7.1 CD1 类型命令 (泵控制)

| HTTP方法 | 完整路径 | 请求DTO | DART命令 | 功能描述 |
|---------|----------|---------|----------|----------|
| POST | `/api/v2/wayne/authorize` | `AuthorizeRequest` | CD1(0x06) | 授权命令 - 允许加油操作 |
| POST | `/api/v2/wayne/reset` | `ResetRequest` | CD1(0x05) | 复位命令 - 重置泵状态 |
| POST | `/api/v2/wayne/stop` | `StopRequest` | CD1(0x08) | 停止命令 - 停止当前操作 |
| POST | `/api/v2/wayne/status` | `ReturnStatusRequest` | CD1(0x00) | 返回状态命令 - 获取泵状态 |
| POST | `/api/v2/wayne/filling-info` | `ReturnFillingInfoRequest` | CD1(0x04) | 返回填充信息命令 |

### 7.2 CD2-CD5 类型命令 (配置和预设)

| HTTP方法 | 完整路径 | 请求DTO | DART命令 | 功能描述 |
|---------|----------|---------|----------|----------|
| POST | `/api/v2/wayne/configure-nozzles` | `ConfigureNozzlesRequest` | CD2 | 配置允许的喷嘴 |
| POST | `/api/v2/wayne/preset-volume` | `PresetVolumeRequest` | CD3 | 预设体积命令 |
| POST | `/api/v2/wayne/preset-amount` | `PresetAmountRequest` | CD4 | 预设金额命令 |
| POST | `/api/v2/wayne/update-prices` | `UpdatePricesRequest` | CD5 | 价格更新命令 |

### 7.3 CD14-CD15 类型命令 (喷嘴控制)

| HTTP方法 | 完整路径 | 请求DTO | DART命令 | 功能描述 |
|---------|----------|---------|----------|----------|
| POST | `/api/v2/wayne/suspend-nozzle` | `SuspendNozzleRequest` | CD14 | 暂停指定喷嘴 |
| POST | `/api/v2/wayne/resume-nozzle` | `ResumeNozzleRequest` | CD15 | 恢复指定喷嘴 |

### 7.4 CD101 类型命令 (计数器请求)

| HTTP方法 | 完整路径 | 请求DTO | DART命令 | 功能描述 |
|---------|----------|---------|----------|----------|
| POST | `/api/v2/wayne/request-counters` | `RequestCountersRequest` | CD101 | 请求计数器数据 |

**Wayne命令统一响应**: `WayneCommandResponse`

---

## 接口统计

| 分类 | 接口数量 | 占比 |
|------|----------|------|
| 系统状态 | 4个 | 10% |
| 设备管理 | 9个 | 23% |
| 喷嘴管理 | 4个 | 10% |
| 交易管理 | 7个 | 18% |
| 调度任务 | 4个 | 10% |
| Wayne协议 | 11个 | 28% |

**架构特点**:
- 分层架构：表现层 → 服务层 → 基础设施层
- Wayne DART v1.3协议完整支持
- 实时状态与数据库数据合并
- V2架构状态管理和并发安全

**使用说明**:
- 基础URL: `http://host:port`
- 数据格式: JSON
- 错误码: 标准HTTP状态码
- Wayne协议: 25ms响应超时，9600/19200波特率 