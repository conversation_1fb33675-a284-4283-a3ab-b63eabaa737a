import 'api_client.dart';
import 'fuel_transaction_api.dart';
import 'employee_api.dart';
import 'order_api.dart';
import 'promotion_api.dart';
import 'payment_api.dart';
import 'enhanced_reports_api.dart';
import 'shift_api.dart';
import 'shift_attendant_api.dart';
import 'station_api.dart';
import 'shift_management_api.dart';

class ApiService {
  // 单例模式
  factory ApiService() {
    return _instance;
  }

  ApiService._internal();
  static final ApiService _instance = ApiService._internal();

  late final ApiClient _apiClient;
  late final FuelTransactionApi fuelTransactionApi;
  late final EmployeeApi employeeApi;
  late final OrderApi orderApi;
  late final PromotionApi promotionApi;
  late final PaymentApi paymentApi;
  late final EnhancedReportsApi enhancedReportsApi;
  late final ShiftApi shiftApi;
  late final ShiftAttendantApi shiftAttendantApi;
  late final StationApi stationApi;
  late final ShiftManagementApi shiftManagementApi;

  // 初始化API服务
  void init({required String baseUrl}) {
    _apiClient = ApiClient(baseUrl: baseUrl);
    fuelTransactionApi = FuelTransactionApi(apiClient: _apiClient);
    employeeApi = EmployeeApi(apiClient: _apiClient);
    orderApi = OrderApi(apiClient: _apiClient);
    promotionApi = PromotionApi(apiClient: _apiClient);
    paymentApi = PaymentApi(apiClient: _apiClient);
    enhancedReportsApi = EnhancedReportsApi(apiClient: _apiClient);
    shiftApi = ShiftApi(apiClient: _apiClient);
    shiftAttendantApi = ShiftAttendantApi(apiClient: _apiClient);
    stationApi = StationApi(apiClient: _apiClient);
    shiftManagementApi = ShiftManagementApi(apiClient: _apiClient);
  }

  // 配置API客户端
  void configureClient({String? token}) {
    _apiClient.configureClient(token: token);
  }

  // 重新初始化API服务（用于更新baseUrl）
  void reinit({required String baseUrl}) {
    // 保存当前的认证token
    final String? currentToken = _apiClient.dio.options.headers['Authorization'] as String?;

    // 重新初始化
    init(baseUrl: baseUrl);

    // 恢复认证token
    if (currentToken != null) {
      configureClient(token: currentToken.replaceFirst('Bearer ', ''));
    }
  }
}
