import 'api_client.dart';
import 'employee_api.dart';
import 'fuel_transaction_api.dart';
import 'order_api.dart';
import 'promotion_api.dart';

// API客户端提供者
class ApiProvider {
  ApiProvider({required String baseUrl}) {
    _apiClient = ApiClient(baseUrl: baseUrl);
    employeeApi = EmployeeApi(apiClient: _apiClient);
    fuelTransactionApi = FuelTransactionApi(apiClient: _apiClient);
    orderApi = OrderApi(apiClient: _apiClient);
    promotionApi = PromotionApi(apiClient: _apiClient);
  }
  late final ApiClient _apiClient;
  late final EmployeeApi employeeApi;
  late final FuelTransactionApi fuelTransactionApi;
  late final OrderApi orderApi;
  late final PromotionApi promotionApi;

  void configureClient({String? token}) {
    _apiClient.configureClient(token: token);
  }
}
