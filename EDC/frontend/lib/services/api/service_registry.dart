// EDC/frontend/lib/services/api/service_registry.dart
// 多服务后端支持 - 服务注册表
// 管理不同的后端服务，每个服务有独立的 baseUrl 和配置

import '../../constants/api_constants.dart';
import 'api_client.dart';
import 'employee_api.dart';
import 'fuel_transaction_api.dart';
import 'order_api.dart';
import 'promotion_api.dart';
import 'shift_api.dart';

/// 服务注册表 - 管理多个后端服务
class ServiceRegistry {
  factory ServiceRegistry() => _instance;
  ServiceRegistry._internal();
  static final ServiceRegistry _instance = ServiceRegistry._internal();

  // 各服务的 ApiClient 实例
  final Map<BackendService, ApiClient> _apiClients =
      <BackendService, ApiClient>{};

  // 各服务的 API 实例
  final Map<BackendService, Map<String, dynamic>> _apiInstances =
      <BackendService, Map<String, dynamic>>{};

  // 当前环境
  ApiEnvironment _currentEnvironment = ApiEnvironment.local;

  // 是否已初始化
  bool _isInitialized = false;

  /// 获取初始化状态
  bool get isInitialized => _isInitialized;

  /// 初始化服务注册表
  Future<void> init(ApiEnvironment environment) async {
    _currentEnvironment = environment;
    await _initializeAllServices();
    _isInitialized = true;
    print(
        'ServiceRegistry 已初始化，环境: ${ApiConstants.getEnvironmentName(environment)}');
  }

  /// 初始化所有服务
  Future<void> _initializeAllServices() async {
    // 使用 getFinalServiceUrl 来优先使用自定义URL配置
    for (final BackendService service in BackendService.values) {
      if (ApiConstants.isServiceConfigured(service, _currentEnvironment)) {
        final String baseUrl = ApiConstants.getFinalServiceUrl(service, _currentEnvironment);
        await _initializeService(service, baseUrl);
      }
    }
  }

  /// 初始化单个服务
  Future<void> _initializeService(
      BackendService service, String baseUrl) async {
    // 创建 ApiClient
    final ApiClient apiClient = ApiClient(baseUrl: baseUrl);
    _apiClients[service] = apiClient;

    // 根据服务类型创建对应的 API 实例
    final Map<String, dynamic> apis = <String, dynamic>{};

    switch (service) {
      case BackendService.base:
        // Base 服务 - 通常包含基础的通用 API
        apis['employee'] = EmployeeApi(apiClient: apiClient);
        apis['order'] = OrderApi(apiClient: apiClient);
        apis['promotion'] = PromotionApi(apiClient: apiClient);
        apis['shift'] = ShiftApi(apiClient: apiClient);
        break;

      case BackendService.fcc:
        // FCC 服务 - 包含 FCC 相关的特定 API
        apis['fuelTransaction'] = FuelTransactionApi(apiClient: apiClient);
        break;
    }

    _apiInstances[service] = apis;

    print('已初始化服务: ${ApiConstants.getServiceName(service)} -> $baseUrl');
  }

  /// 重新配置指定服务的 URL
  Future<void> reconfigureService(
      BackendService service, String newBaseUrl) async {
    _ensureInitialized();

    print('重新配置服务: ${ApiConstants.getServiceName(service)} -> $newBaseUrl');

    // 保存当前的认证token
    final String? currentToken = _getCurrentAuthToken(service);

    await _initializeService(service, newBaseUrl);

    // 重新设置认证token
    if (currentToken != null) {
      configureServiceAuth(service, token: currentToken);
    }
  }

  /// 重新配置所有服务的URL（使用最新的自定义配置）
  Future<void> reconfigureAllServices() async {
    _ensureInitialized();

    print('重新配置所有服务以使用最新的URL配置...');

    try {
      // 保存所有服务的当前认证token
      final Map<BackendService, String?> currentTokens = <BackendService, String?>{};
      for (final BackendService service in _apiClients.keys) {
        currentTokens[service] = _getCurrentAuthToken(service);
        print('保存服务 ${ApiConstants.getServiceName(service)} 的认证token');
      }

      // 重新初始化所有服务
      await _initializeAllServices();

      // 重新设置所有服务的认证token
      for (final MapEntry<BackendService, String?> entry in currentTokens.entries) {
        if (entry.value != null) {
          configureServiceAuth(entry.key, token: entry.value);
          print('恢复服务 ${ApiConstants.getServiceName(entry.key)} 的认证token');
        }
      }

      print('所有服务重新配置完成');
    } catch (e) {
      print('重新配置服务时发生错误: $e');
      rethrow;
    }
  }

  /// 配置服务的认证令牌
  void configureServiceAuth(BackendService service, {String? token}) {
    _ensureInitialized();

    final ApiClient? apiClient = _apiClients[service];
    if (apiClient != null) {
      apiClient.configureClient(token: token);
      print('已配置服务认证: ${ApiConstants.getServiceName(service)}');
    } else {
      throw StateError('服务 ${ApiConstants.getServiceName(service)} 未初始化');
    }
  }

  /// 配置所有服务的认证令牌
  void configureAllServicesAuth({String? token}) {
    _ensureInitialized();

    for (final MapEntry<BackendService, ApiClient> entry
        in _apiClients.entries) {
      entry.value.configureClient(token: token);
    }
    print('已配置所有服务的认证令牌');
  }

  // ======================== API 访问方法 ========================

  /// 获取 Base Service 的 ApiClient
  ApiClient get baseServiceClient {
    _ensureInitialized();
    final ApiClient? client = _apiClients[BackendService.base];
    if (client == null) {
      throw StateError('Base Service 未初始化');
    }
    return client;
  }

  /// 获取 FCC Service 的 ApiClient
  ApiClient get fccServiceClient {
    _ensureInitialized();
    final ApiClient? client = _apiClients[BackendService.fcc];
    if (client == null) {
      throw StateError('FCC Service 未初始化');
    }
    return client;
  }

  /// 从 Base Service 获取员工 API
  EmployeeApi get baseEmployeeApi {
    _ensureInitialized();
    final EmployeeApi? api =
        _apiInstances[BackendService.base]?['employee'] as EmployeeApi?;
    if (api == null) {
      throw StateError('Base Service 中的 Employee API 未初始化');
    }
    return api;
  }

  /// 从 Base Service 获取订单 API
  OrderApi get baseOrderApi {
    _ensureInitialized();
    final OrderApi? api =
        _apiInstances[BackendService.base]?['order'] as OrderApi?;
    if (api == null) {
      throw StateError('Base Service 中的 Order API 未初始化');
    }
    return api;
  }

  /// 从 Base Service 获取促销 API
  PromotionApi get basePromotionApi {
    _ensureInitialized();
    final PromotionApi? api =
        _apiInstances[BackendService.base]?['promotion'] as PromotionApi?;
    if (api == null) {
      throw StateError('Base Service 中的 Promotion API 未初始化');
    }
    return api;
  }

  /// 从 Base Service 获取班次 API
  ShiftApi get baseShiftApi {
    _ensureInitialized();
    final ShiftApi? api =
        _apiInstances[BackendService.base]?['shift'] as ShiftApi?;
    if (api == null) {
      throw StateError('Base Service 中的 Shift API 未初始化');
    }
    return api;
  }

  /// 从 FCC Service 获取燃油交易 API
  FuelTransactionApi get fccFuelTransactionApi {
    _ensureInitialized();
    final FuelTransactionApi? api = _apiInstances[BackendService.fcc]
        ?['fuelTransaction'] as FuelTransactionApi?;
    if (api == null) {
      throw StateError('FCC Service 中的 FuelTransaction API 未初始化');
    }
    return api;
  }

  // ======================== 兼容性访问方法 ========================

  /// 获取员工 API - 默认使用 Base Service
  EmployeeApi get employeeApi => baseEmployeeApi;

  /// 获取订单 API - 默认使用 Base Service
  OrderApi get orderApi => baseOrderApi;

  /// 获取促销 API - 默认使用 Base Service
  PromotionApi get promotionApi => basePromotionApi;

  /// 获取燃油交易 API - 默认使用 FCC Service
  FuelTransactionApi get fuelTransactionApi => fccFuelTransactionApi;

  /// 获取班次 API - 默认使用 Base Service
  ShiftApi get shiftApi => baseShiftApi;

  // ======================== 通用访问方法 ========================

  /// 获取指定服务的 ApiClient
  ApiClient getServiceClient(BackendService service) {
    _ensureInitialized();
    final ApiClient? client = _apiClients[service];
    if (client == null) {
      throw StateError(
          '服务 ${ApiConstants.getServiceName(service)} 的 ApiClient 未初始化');
    }
    return client;
  }

  /// 获取指定服务的指定 API
  T getServiceApi<T>(BackendService service, String apiName) {
    _ensureInitialized();
    final Map<String, dynamic>? apis = _apiInstances[service];
    if (apis == null) {
      throw StateError('服务 ${ApiConstants.getServiceName(service)} 未初始化');
    }

    final api = apis[apiName] as T?;
    if (api == null) {
      throw StateError(
          '服务 ${ApiConstants.getServiceName(service)} 中的 API "$apiName" 未找到');
    }
    return api;
  }

  /// 检查服务是否已初始化
  bool isServiceInitialized(BackendService service) {
    return _apiClients.containsKey(service);
  }

  /// 获取所有已初始化的服务
  List<BackendService> getInitializedServices() {
    return _apiClients.keys.toList();
  }

  /// 获取服务状态信息
  Map<String, dynamic> getServiceStatus() {
    final Map<String, dynamic> status = <String, dynamic>{
      'environment': ApiConstants.getEnvironmentName(_currentEnvironment),
      'initialized': _isInitialized,
      'services': <String, dynamic>{},
    };

    for (final BackendService service in BackendService.values) {
      final String serviceName = ApiConstants.getServiceName(service);
      final bool isInitialized = isServiceInitialized(service);
      final String? baseUrl =
          isInitialized ? _apiClients[service]!.baseUrl : null;

      status['services'][serviceName] = <String, Object?>{
        'initialized': isInitialized,
        'baseUrl': baseUrl,
      };
    }

    return status;
  }

  // ======================== 私有方法 ========================

  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('ServiceRegistry 未初始化。请先调用 init() 方法。');
    }
  }

  /// 获取指定服务的当前认证token
  String? _getCurrentAuthToken(BackendService service) {
    final ApiClient? apiClient = _apiClients[service];
    if (apiClient != null) {
      // 从ApiClient的headers中提取Authorization token
      final String? authHeader = apiClient.dio.options.headers['Authorization'] as String?;
      if (authHeader != null && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7); // 移除 "Bearer " 前缀
      }
    }
    return null;
  }

  /// 清理所有服务（用于重新初始化）
  void _clearAll() {
    _apiClients.clear();
    _apiInstances.clear();
    _isInitialized = false;
  }

  /// 重新初始化到新环境
  Future<void> switchEnvironment(ApiEnvironment newEnvironment) async {
    print(
        '切换环境从 ${ApiConstants.getEnvironmentName(_currentEnvironment)} 到 ${ApiConstants.getEnvironmentName(newEnvironment)}');
    _clearAll();
    await init(newEnvironment);
  }
}
