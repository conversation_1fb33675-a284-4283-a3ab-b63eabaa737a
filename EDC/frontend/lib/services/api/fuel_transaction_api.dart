import 'package:dio/src/response.dart';
import 'package:flutter/foundation.dart';
import '../../models/fuel_transaction.dart';
import 'api_client.dart';

class FuelTransactionApi {
  FuelTransactionApi({required ApiClient apiClient}) : _apiClient = apiClient;
  final ApiClient _apiClient;

  // 获取燃油交易列表
  Future<FuelTransactionResponse> getFuelTransactions(
      FuelTransactionQueryParams params) async {
    debugPrint('正在获取燃油交易列表，参数: ${params.toJson()}');
    try {
      final Response response = await _apiClient.get(
        '/api/v1/fuel-transactions',
        queryParameters: params.toJson(),
      );

      // 检查HTTP状态码
      if (response.statusCode != 200) {
        final String errorMessage = response.data is Map<String, dynamic> &&
                response.data['message'] != null
            ? response.data['message'].toString()
            : 'HTTP ${response.statusCode}';
        throw Exception(errorMessage);
      }

      // 直接从response.data中解析数据，因为实际API响应不包含包装格式
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        // 检查是否是包装格式 (有code和data字段)
        if (responseData.containsKey('code') &&
            responseData.containsKey('data')) {
          // 包装格式：{code: 0, data: {items: [...], total: ...}}
          if (responseData['code'] != 0) {
            throw Exception('${responseData['message'] ?? '操作失败'}');
          }
          final Map<String, dynamic> actualData =
              responseData['data'] as Map<String, dynamic>;
          final FuelTransactionResponse result =
              FuelTransactionResponse.fromJson(actualData);
          // debugPrint('成功获取燃油交易列表（包装格式），共 ${result.total} 条记录');
          return result;
        } else {
          // 直接格式：{items: [...], total: ...}
          final FuelTransactionResponse result =
              FuelTransactionResponse.fromJson(responseData);
          // debugPrint('成功获取燃油交易列表（直接格式），共 ${result.total} 条记录');
          return result;
        }
      } else {
        throw Exception('响应数据格式错误');
      }
    } catch (e) {
      // 记录错误并重新抛出异常
      debugPrint('获取燃油交易列表失败: $e');
      rethrow;
    }
  }

  // 获取燃油交易详情
  Future<FuelTransaction> getFuelTransactionById(String id) async {
    debugPrint('正在获取燃油交易详情，ID: $id');
    try {
      final Response response =
          await _apiClient.get('/api/v1/fuel-transactions/$id');

      // 检查HTTP状态码
      if (response.statusCode != 200) {
        final String errorMessage = response.data is Map<String, dynamic> &&
                response.data['message'] != null
            ? response.data['message'].toString()
            : 'HTTP ${response.statusCode}';
        throw Exception(errorMessage);
      }

      // 直接从response.data中解析数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        // 检查是否是包装格式
        if (responseData.containsKey('code') &&
            responseData.containsKey('data')) {
          // 包装格式：{code: 0, data: {...}}
          if (responseData['code'] != 0) {
            throw Exception('${responseData['message'] ?? '操作失败'}');
          }
          final Map<String, dynamic> actualData =
              responseData['data'] as Map<String, dynamic>;
          debugPrint('成功获取燃油交易详情（包装格式），ID: $id');
          return FuelTransaction.fromJson(actualData);
        } else {
          // 直接格式：{id: ..., transaction_number: ...}
          debugPrint('成功获取燃油交易详情（直接格式），ID: $id');
          return FuelTransaction.fromJson(responseData);
        }
      } else {
        throw Exception('响应数据格式错误');
      }
    } catch (e) {
      // 记录错误并重新抛出异常
      debugPrint('获取燃油交易详情失败: $e');
      rethrow;
    }
  }

  // 获取可用的状态列表
  List<String> getAvailableStatuses() {
    return <String>['pending', 'processed', 'cancelled'];
  }

  // 获取可用的油枪列表
  List<String> getAvailablePumpIds() {
    return <String>['P01', 'P02', 'P03', 'P04', 'P05', 'P06'];
  }

  // 获取人类可读的状态描述
  String getStatusDescription(String status) {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'processed':
        return '已处理';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  }
}
