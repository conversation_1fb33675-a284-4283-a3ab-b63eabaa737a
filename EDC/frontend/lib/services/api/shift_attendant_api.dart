import 'package:dio/src/response.dart';
import 'package:flutter/foundation.dart';
import 'api_client.dart';
import '../../models/shift_attendant_model.dart';
import '../../constants/api_constants.dart';

/// 班次员工API服务
/// 
/// 专门用于获取班次中员工的详细加油情况，包括油品销售、支付方式等统计数据
/// 对应API接口：GET /api/v1/shifts/{id}/attendants
class ShiftAttendantApi {
  ShiftAttendantApi({required ApiClient apiClient}) : _apiClient = apiClient;

  /// 工厂构造函数，创建基于指定环境的实例
  factory ShiftAttendantApi.withEnvironment(ApiEnvironment env) {
    final String baseUrl = ApiConstants.getServiceUrl(BackendService.base, env);
    final ApiClient apiClient = ApiClient(baseUrl: baseUrl);
    return ShiftAttendantApi(apiClient: apiClient);
  }

  final ApiClient _apiClient;

  /// 获取指定班次中所有员工的详细加油情况
  /// 
  /// [shiftId] 班次ID
  /// [queryParams] 查询参数（可选）
  /// - attendantName: 员工姓名筛选（支持模糊匹配）
  /// - fuelGrade: 油品等级筛选
  /// - paymentMethod: 支付方式筛选
  /// 
  /// 返回值：包含员工详细信息的响应对象
  /// 
  /// 功能特点：
  /// - 智能数据源选择：已结束班次优先使用汇总表数据，进行中班次使用实时数据
  /// - 员工姓名准确性：所有员工姓名均从users表获取真实姓名
  /// - 支付数据完整性：提供详细的支付方式统计和分类
  /// - 数据一致性保证：确保支付数据与销售数据的关联准确性
  /// - 多维度筛选：支持按员工姓名、油品等级、支付方式筛选
  Future<ShiftAttendantResponse> getShiftAttendants(
    String shiftId, {
    ShiftAttendantQueryParams? queryParams,
  }) async {
    try {
      final Map<String, dynamic> params = queryParams?.toQueryParams() ?? <String, dynamic>{};

      debugPrint('ShiftAttendantApi: 获取班次员工数据 - 班次ID: $shiftId, 查询参数: $params');

      final Response response = await _apiClient.get(
        '/api/v1/shifts/$shiftId/attendants',
        queryParameters: params.isNotEmpty ? params : null,
      );

      debugPrint('ShiftAttendantApi: 班次员工数据响应状态: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = response.data as Map<String, dynamic>;
        
        // 检查响应格式
        if (responseData['success'] == true) {
          final ShiftAttendantResponse shiftAttendantResponse = 
              ShiftAttendantResponse.fromJson(responseData);

          debugPrint('ShiftAttendantApi: 班次员工数据获取成功 - 班次: ${shiftAttendantResponse.data.shiftInfo.shiftNumber}');
          debugPrint('ShiftAttendantApi: 员工数量: ${shiftAttendantResponse.data.attendants.length}');
          debugPrint('ShiftAttendantApi: 数据源: ${shiftAttendantResponse.meta.dataSource}');
          
          return shiftAttendantResponse;
        } else {
          throw _createApiException(
            message: responseData['message'] as String? ?? '获取班次员工数据失败',
            statusCode: response.statusCode,
            data: responseData,
          );
        }
      } else {
        throw _handleErrorResponse(response);
      }
    } catch (e) {
      debugPrint('ShiftAttendantApi: 获取班次员工数据失败 - $e');
      
      if (e is ApiException) {
        rethrow;
      }
      
      throw _createApiException(
        message: '获取班次员工数据请求失败: $e',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 获取指定班次中指定员工的详细加油情况
  /// 
  /// [shiftId] 班次ID
  /// [attendantName] 员工姓名（精确匹配）
  /// [queryParams] 其他查询参数（可选）
  /// 
  /// 返回值：包含指定员工详细信息的响应对象
  Future<ShiftAttendantResponse> getShiftAttendantByName(
    String shiftId,
    String attendantName, {
    ShiftAttendantQueryParams? queryParams,
  }) async {
    final ShiftAttendantQueryParams params = ShiftAttendantQueryParams(
      attendantName: attendantName,
      fuelGrade: queryParams?.fuelGrade,
      paymentMethod: queryParams?.paymentMethod,
    );

    return getShiftAttendants(shiftId, queryParams: params);
  }

  /// 获取指定班次中按油品等级筛选的员工数据
  /// 
  /// [shiftId] 班次ID
  /// [fuelGrade] 油品等级
  /// [queryParams] 其他查询参数（可选）
  /// 
  /// 返回值：包含按油品等级筛选的员工数据响应对象
  Future<ShiftAttendantResponse> getShiftAttendantsByFuelGrade(
    String shiftId,
    String fuelGrade, {
    ShiftAttendantQueryParams? queryParams,
  }) async {
    final ShiftAttendantQueryParams params = ShiftAttendantQueryParams(
      attendantName: queryParams?.attendantName,
      fuelGrade: fuelGrade,
      paymentMethod: queryParams?.paymentMethod,
    );

    return getShiftAttendants(shiftId, queryParams: params);
  }

  /// 获取指定班次中按支付方式筛选的员工数据
  /// 
  /// [shiftId] 班次ID
  /// [paymentMethod] 支付方式
  /// [queryParams] 其他查询参数（可选）
  /// 
  /// 返回值：包含按支付方式筛选的员工数据响应对象
  Future<ShiftAttendantResponse> getShiftAttendantsByPaymentMethod(
    String shiftId,
    String paymentMethod, {
    ShiftAttendantQueryParams? queryParams,
  }) async {
    final ShiftAttendantQueryParams params = ShiftAttendantQueryParams(
      attendantName: queryParams?.attendantName,
      fuelGrade: queryParams?.fuelGrade,
      paymentMethod: paymentMethod,
    );

    return getShiftAttendants(shiftId, queryParams: params);
  }

  /// 处理错误响应
  ApiException _handleErrorResponse(Response response) {
    try {
      final Map<String, dynamic> errorBody = response.data as Map<String, dynamic>;
      
      // 检查是否有标准错误格式
      if (errorBody.containsKey('error')) {
        final Map<String, dynamic> error = errorBody['error'] as Map<String, dynamic>;
        final String errorCode = error['code'] as String? ?? 'UNKNOWN_ERROR';
        final String errorMessage = error['message'] as String? ?? 'Unknown error occurred';
        final String? errorDetail = error['detail'] as String?;

        debugPrint('ShiftAttendantApi: API错误 - 状态码: ${response.statusCode}, 错误码: $errorCode, 消息: $errorMessage');
        
        if (errorDetail != null) {
          debugPrint('ShiftAttendantApi: 错误详情: $errorDetail');
        }

        String userMessage = errorMessage;
        
        // 根据错误代码提供更友好的错误消息
        switch (errorCode) {
          case 'INVALID_SHIFT_ID':
            userMessage = '班次ID格式无效';
            break;
          case 'SHIFT_NOT_FOUND':
            userMessage = '班次不存在或已被删除';
            break;
          case 'DATA_ACCESS_ERROR':
            userMessage = '数据访问错误，请稍后重试';
            break;
          case 'INTERNAL_ERROR':
            userMessage = '服务器内部错误，请稍后重试';
            break;
        }

        return _createApiException(
          message: userMessage,
          statusCode: response.statusCode,
          data: errorBody,
        );
      }

      // 如果没有标准错误格式，使用通用错误处理
      final String message = errorBody['message'] as String? ?? 'Unknown error';
      return _createApiException(
        message: message,
        statusCode: response.statusCode,
        data: errorBody,
      );
    } catch (e) {
      debugPrint('ShiftAttendantApi: 解析错误响应失败 - $e');
      
      String userMessage = '请求失败';
      
      // 根据HTTP状态码提供更友好的错误消息
      switch (response.statusCode) {
        case 400:
          userMessage = '请求参数错误';
          break;
        case 401:
          userMessage = '未授权访问，请重新登录';
          break;
        case 403:
          userMessage = '权限不足，无法访问该资源';
          break;
        case 404:
          userMessage = '班次不存在';
          break;
        case 409:
          userMessage = '班次状态冲突';
          break;
        case 500:
          userMessage = '服务器内部错误';
          break;
        case 502:
          userMessage = '网关错误，请稍后重试';
          break;
        case 503:
          userMessage = '服务暂时不可用';
          break;
        default:
          userMessage = '请求失败 (${response.statusCode})';
      }

      return _createApiException(
        message: userMessage,
        statusCode: response.statusCode,
        data: response.data,
      );
    }
  }

  /// 创建API异常
  ApiException _createApiException({
    required String message,
    required int? statusCode,
    required dynamic data,
  }) {
    return ApiException(
      message: message,
      statusCode: statusCode,
      data: data,
    );
  }

  /// 配置API认证
  void configureAuth(String? token) {
    _apiClient.configureClient(token: token);
  }

  /// 检查网络连接
  Future<bool> checkConnection() async {
    try {
      // 尝试调用一个简单的健康检查接口
      await _apiClient.get('/api/v1/health', 
          queryParameters: <String, dynamic>{'check': 'connection'});
      return true;
    } catch (e) {
      debugPrint('ShiftAttendantApi: 网络连接检查失败 - $e');
      return false;
    }
  }
}

/// 班次员工API异常类
class ShiftAttendantApiException extends ApiException {
  ShiftAttendantApiException({
    required this.operation,
    required super.message,
    super.statusCode,
    super.data,
  });

  final String operation;

  @override
  String toString() {
    return 'ShiftAttendantApiException in $operation: $message (Status code: $statusCode)';
  }
} 