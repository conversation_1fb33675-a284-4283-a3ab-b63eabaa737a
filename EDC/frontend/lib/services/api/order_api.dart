import 'package:dio/dio.dart';
import '../../models/order.dart'; // Import Order and OrderQueryCondition
import '../../models/create_order_request.dart'; // 导入创建订单请求模型
import 'api_client.dart'; // Import the base ApiClient
import 'dart:developer'; // For logging

// Define a class to hold the paginated result
// Note: 可以使用ApiResponseHandler中的PaginatedResponse<Order>替代
class PaginatedOrders {
  PaginatedOrders({
    required this.orders,
    required this.hasMore,
    required this.totalCount,
    required this.currentPage,
    required this.pageSize,
    required this.totalPages,
  });

  // 从新的PaginatedResponse转换
  factory PaginatedOrders.fromPaginatedResponse(
      PaginatedResponse<Order> response) {
    return PaginatedOrders(
      orders: response.items,
      hasMore: response.hasMore,
      totalCount: response.total,
      currentPage: response.page,
      pageSize: response.pageSize,
      totalPages: response.totalPages,
    );
  }
  final List<Order> orders;
  final bool hasMore;
  final int totalCount;
  final int currentPage;
  final int pageSize;
  final int totalPages;
}

// Helper function to map OrderStatus enum to API status string
String? _mapStatusToApiString(OrderStatus? status) {
  if (status == null) return null;
  switch (status) {
    case OrderStatus.created: // 'created' in enum maps to 'new' in API
      return 'new';
    case OrderStatus.processing:
      return 'processing';
    case OrderStatus.completed:
      return 'completed';
    case OrderStatus.cancelled:
      return 'cancelled';
    // Map other statuses if needed by API, otherwise return null or throw error
    case OrderStatus.cancelling:
    case OrderStatus.failed:
      return null; // API doesn't support these filters based on docs
  }
}

class OrderApi {
  OrderApi({required ApiClient apiClient}) : _apiClient = apiClient;
  final ApiClient _apiClient;

  /// Fetches a list of orders based on query conditions and pagination.
  ///
  /// Returns a [PaginatedOrders] object.
  Future<PaginatedOrders> getOrders({
    required OrderQueryCondition condition,
  }) async {
    // --- 1. Construct Query Parameters using snake_case ---
    final Map<String, dynamic> queryParameters = <String, dynamic>{
      'page': condition.page,
      'limit': condition.pageSize, // Use 'limit' as per API doc
    };

    // Add optional parameters with snake_case keys
    if (condition.startDate != null) {
      queryParameters['date_from'] = _safeDateFormat(condition.startDate!);
    }
    if (condition.endDate != null) {
      queryParameters['date_to'] = _safeDateFormat(condition.endDate!);
    }
    if (condition.orderId != null && condition.orderId!.isNotEmpty) {
      queryParameters['order_number'] = condition.orderId; // Use 'order_number'
    }
    // Removed commented out unsupported parameters
    // // Skipping memberPhone and operatorId for now as they don't directly map to API params in doc
    // // if (condition.memberPhone != null && condition.memberPhone!.isNotEmpty) {
    // //   queryParameters['customer_id'] = condition.memberPhone; // Or other logic?
    // // }
    // // if (condition.operatorId != null && condition.operatorId!.isNotEmpty) {
    // //   queryParameters['???'] = condition.operatorId;
    // // }
    final String? apiStatus = _mapStatusToApiString(condition.status);
    if (apiStatus != null) {
      queryParameters['status'] = apiStatus; // Pass mapped status string
    }
    if (condition.paymentMethod != null &&
        condition.paymentMethod!.isNotEmpty) {
      queryParameters['payment_method'] = condition.paymentMethod;
    }
    // Add other supported params like station_id if needed
    // queryParameters['station_id'] = yourStationId;

    log('[OrderApi] Fetching orders with params: $queryParameters');

    // --- 2. Make API Call using ApiClient ---
    try {
      final Response response = await _apiClient.get(
        '/api/v1/orders',
        queryParameters: queryParameters,
      );

      log('[OrderApi] Received response: ${response.statusCode}');

      // --- 3. Parse Response using unified parser ---
      if (response.statusCode == 200) {
        try {
          final PaginatedResponse<Order> paginatedResponse =
              ApiResponseHandler.parsePaginatedResponse<Order>(
            response,
            Order.fromJson,
          );

          log('[OrderApi] Parsed ${paginatedResponse.items.length} orders. Total: ${paginatedResponse.total}. Page: ${paginatedResponse.page}/${paginatedResponse.totalPages}. Has more: ${paginatedResponse.hasMore}');

          return PaginatedOrders.fromPaginatedResponse(paginatedResponse);
        } catch (e, stackTrace) {
          log('[OrderApi] Error parsing orders response: $e',
              error: e, stackTrace: stackTrace);
          throw ApiException(
            message: '订单列表数据解析失败: ${e.toString()}',
            statusCode: response.statusCode,
            data: response.data,
          );
        }
      } else {
        log('[OrderApi] Error: Received status code ${response.statusCode} or null data');
        // Use the structured error handling from ApiClient if available
        // Or throw a specific exception
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: 'Failed to fetch orders: Status code ${response.statusCode}',
          type: DioExceptionType.badResponse,
        );
      }
    } on DioException catch (e) {
      log('[OrderApi] DioException fetching orders: ${e.message}', error: e);
      // Let ApiClient's error handler wrap this, or handle specifically
      rethrow;
    } catch (e, stackTrace) {
      log('[OrderApi] Exception fetching orders: $e',
          error: e, stackTrace: stackTrace);
      throw Exception('An unexpected error occurred: $e');
    }
  }

  // --- 新增：获取订单详情方法 ---
  /// Fetches the details of a specific order by its ID.
  ///
  /// Requires the integer order [id].
  /// Returns the detailed [Order] object.
  Future<Order> getOrderDetail(String id) async {
    final String endpoint = '/api/v1/orders/$id'; // Construct endpoint with ID
    log('[OrderApi] Fetching order detail for ID: $id from $endpoint');

    try {
      final Response response = await _apiClient.get(endpoint);

      log('[OrderApi] Received response for order detail: ${response.statusCode}');

      // Use unified response parser
      if (response.statusCode == 200) {
        try {
          final Order orderDetail = ApiResponseHandler.parseResponse<Order>(
            response,
            Order.fromJson,
          );
          log('[OrderApi] Successfully parsed order detail for ID: $id');
          return orderDetail;
        } catch (e, stackTrace) {
          log('[OrderApi] Error parsing order detail JSON for ID: $id - ${e.toString()}',
              error: e, stackTrace: stackTrace);
          throw ApiException(
            message: '订单数据解析失败: ${e.toString()}',
            statusCode: response.statusCode,
            data: <String, String>{'error': e.toString()},
          );
        }
      } else {
        log('[OrderApi] Error fetching order detail: Status ${response.statusCode}, Data: ${response.data}');
        // Handle API error response (e.g., 404 Not Found)
        final String errorMessage =
            (response.data?['message'] as String?) ?? '获取订单详情失败';
        throw ApiException(
            message: errorMessage,
            statusCode: response.statusCode,
            data: response.data);
      }
    } on DioException catch (e) {
      log('[OrderApi] DioException fetching order detail for ID $id: ${e.message}',
          error: e);
      // Re-throw wrapped exception from ApiClient or throw custom
      rethrow; // Re-throw the original DioException
    } catch (e, stackTrace) {
      log('[OrderApi] Exception fetching order detail for ID $id: $e',
          error: e, stackTrace: stackTrace);
      throw Exception('获取订单详情时发生意外错误: $e');
    }
  }
  // --- 结束：新增方法 ---

  /// 创建新订单
  ///
  /// 接收 [CreateOrderRequest] 对象，发送POST请求创建新订单
  /// 返回创建成功的 [Order] 对象
  Future<Order> createOrder(CreateOrderRequest request) async {
    const String endpoint = '/api/v1/orders';
    log('[OrderApi] 创建新订单，请求数据: ${request.toJson()}');

    try {
      final Response response = await _apiClient.post(
        endpoint,
        data: request.toJson(),
      );

      log('[OrderApi] 创建订单响应状态码: ${response.statusCode}');

      // 修改：支持200和201状态码，因为后端实际返回200
      if ((response.statusCode == 200 || response.statusCode == 201) &&
          response.data != null) {
        try {
          final Map<String, dynamic> responseData =
              response.data as Map<String, dynamic>;

          // 检查是否是包装格式 {code, message, data}
          if (responseData.containsKey('code') &&
              responseData.containsKey('data')) {
            // 处理包装格式响应
            final int code = responseData['code'] as int? ?? -1;
            final String message = responseData['message'] as String? ?? '';

            if (code == 0) {
              final Map<String, dynamic>? orderData =
                  responseData['data'] as Map<String, dynamic>?;

              if (orderData != null) {
                final Order orderResponse = Order.fromJson(orderData);
                log('[OrderApi] 成功创建订单（包装格式），ID: ${orderResponse.id}');
                return orderResponse;
              } else {
                throw ApiException(
                    message: '订单数据为空',
                    statusCode: response.statusCode,
                    data: responseData);
              }
            } else {
              throw ApiException(
                  message: message.isNotEmpty ? message : '创建订单失败',
                  statusCode: response.statusCode,
                  data: responseData);
            }
          } else {
            // 处理直接订单对象格式（后端实际返回的格式）
            final Order orderResponse = Order.fromJson(responseData);
            log('[OrderApi] 成功创建订单（直接格式），ID: ${orderResponse.id}');
            return orderResponse;
          }
        } catch (e, stackTrace) {
          log('[OrderApi] 解析创建订单响应数据失败: $e', error: e, stackTrace: stackTrace);
          throw ApiException(
              message: '订单数据解析失败: ${e.toString()}',
              statusCode: response.statusCode,
              data: <String, String>{'error': e.toString()});
        }
      } else {
        log('[OrderApi] 创建订单失败: 状态码 ${response.statusCode}, 数据: ${response.data}');
        final String errorMessage =
            (response.data?['message'] as String?) ?? '创建订单失败';
        throw ApiException(
            message: errorMessage,
            statusCode: response.statusCode,
            data: response.data);
      }
    } on DioException catch (e) {
      log('[OrderApi] 创建订单网络异常: ${e.message}', error: e);
      
      // 详细记录400错误的响应内容
      if (e.response?.statusCode == 400) {
        log('[OrderApi] 400错误详情:');
        log('   状态码: ${e.response?.statusCode}');
        log('   响应头: ${e.response?.headers}');
        log('   响应体: ${e.response?.data}');
        log('   请求数据: ${request.toJson()}');
      }
      
      rethrow; // 重新抛出异常，由上层处理
    } catch (e, stackTrace) {
      log('[OrderApi] 创建订单时发生异常: $e', error: e, stackTrace: stackTrace);
      throw Exception('创建订单时发生意外错误: $e');
    }
  }

  /// 安全的日期格式化方法 - 将 DateTime 转换为 YYYY-MM-DD 格式
  /// 避免 substring RangeError 问题
  String _safeDateFormat(DateTime dateTime) {
    try {
      // 使用更安全的方式格式化日期，避免 substring RangeError
      final String isoString = dateTime.toIso8601String();
      if (isoString.length >= 10) {
        return isoString.substring(0, 10);
      } else {
        // 回退到手动格式化
        return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      // 如果所有方法都失败，使用当前日期作为回退
      final DateTime now = DateTime.now();
      return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
    }
  }
}
