import 'package:dio/src/response.dart';

import 'api_client.dart';
import '../../models/fuel_receivable_summary.dart';
import '../../models/nozzle_detailed_sales.dart';

class EnhancedReportsApi {
  EnhancedReportsApi({required this.apiClient});
  final ApiClient apiClient;

  /// 获取油品应收汇总
  Future<FuelReceivableSummary> getFuelReceivableSummary({
    required String startDate,
    required String endDate,
    String? siteIds,
  }) async {
    try {
      final Map<String, dynamic> queryParams = <String, dynamic>{
        'start_date': startDate,
        'end_date': endDate,
      };

      if (siteIds != null && siteIds.isNotEmpty) {
        queryParams['site_ids'] = siteIds;
      }

      final Response response = await apiClient.get(
        '/enhanced-reports/fuel-receivable',
        queryParameters: queryParams,
      );

      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        final dataMap = responseData['data'] ?? responseData;
        if (dataMap is Map<String, dynamic>) {
          return FuelReceivableSummary.fromJson(dataMap);
        } else {
          throw Exception('Invalid data format in response');
        }
      } else {
        throw Exception('Invalid response format');
      }
    } catch (e) {
      throw Exception('Error getting fuel receivable summary: $e');
    }
  }

  /// 获取油枪销售明细
  Future<NozzleDetailedSalesResponse> getNozzleSales({
    required String startDate,
    required String endDate,
    String? siteIds,
    int limit = 10,
    int offset = 0,
  }) async {
    try {
      final Map<String, dynamic> queryParams = <String, dynamic>{
        'start_date': startDate,
        'end_date': endDate,
        'limit': limit,
        'offset': offset,
      };

      if (siteIds != null && siteIds.isNotEmpty) {
        queryParams['site_ids'] = siteIds;
      }

      final Response response = await apiClient.get(
        '/enhanced-reports/nozzle-sales',
        queryParameters: queryParams,
      );

      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        return NozzleDetailedSalesResponse.fromJson(responseData);
      } else {
        throw Exception('Invalid response format');
      }
    } catch (e) {
      throw Exception('Error getting nozzle sales: $e');
    }
  }
}
