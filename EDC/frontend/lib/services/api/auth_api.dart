// EDC/frontend/lib/services/api/auth_api.dart

/// 认证API服务
/// 基于BOS认证API接口文档实现

import 'package:dio/dio.dart';
import '../../models/auth_models.dart';
import 'api_client.dart';
import '../log_service.dart';

/// 认证API服务类
class AuthApi {
  AuthApi({required this.apiClient});

  final ApiClient apiClient;

  /// 用户登录
  /// 
  /// 参数:
  /// - [request] 登录请求数据
  /// 
  /// 返回:
  /// - [LoginResponseData] 登录成功的响应数据
  /// 
  /// 异常:
  /// - [ApiException] 登录失败或网络错误
  Future<LoginResponseData> login(LoginRequest request) async {
    try {
      LogService.instance.info('AuthApi', '开始用户登录 - username: ${request.username}, system: ${request.system}, authType: ${request.authType}');

      final Response response = await apiClient.post(
        '/auth/login',
        data: request.toJson(),
      );

      LogService.instance.info('AuthApi', '登录API调用成功 - statusCode: ${response.statusCode}, hasData: ${response.data != null}');

      // 解析响应
      final LoginResponse loginResponse = LoginResponse.fromJson(
        response.data as Map<String, dynamic>,
      );

      // 检查业务状态码
      if (loginResponse.code != 0) {
        LogService.instance.error('AuthApi', '登录业务逻辑失败 - code: ${loginResponse.code}, message: ${loginResponse.message}');
        
        throw ApiException(
          message: loginResponse.message,
          statusCode: response.statusCode,
          data: {
            'code': loginResponse.code,
            'message': loginResponse.message,
            'requestId': loginResponse.requestId,
          },
        );
      }

      LogService.instance.info('AuthApi', '用户登录成功 - userId: ${loginResponse.data.user.id}, username: ${loginResponse.data.user.username}, system: ${loginResponse.data.systemAccess.system}, accessLevel: ${loginResponse.data.systemAccess.accessLevel}, stationCount: ${loginResponse.data.systemAccess.stationCount}');

      return loginResponse.data;
    } on DioException catch (e) {
      LogService.instance.error('AuthApi', '登录网络请求失败 - statusCode: ${e.response?.statusCode}', e);

      // 处理特定的HTTP状态码
      if (e.response != null) {
        final int statusCode = e.response!.statusCode ?? 0;
        final dynamic responseData = e.response!.data;

        // 尝试解析错误响应
        if (responseData is Map<String, dynamic>) {
          try {
            final AuthErrorResponse errorResponse = AuthErrorResponse.fromJson(responseData);
            
            LogService.instance.error('AuthApi', '登录业务错误 - code: ${errorResponse.code}, message: ${errorResponse.message}');

            throw ApiException(
              message: errorResponse.message,
              statusCode: statusCode,
              data: {
                'code': errorResponse.code,
                'message': errorResponse.message,
                'errors': errorResponse.errors?.map((e) => e.toJson()).toList(),
                'requestId': errorResponse.requestId,
              },
            );
          } catch (parseError) {
            LogService.instance.error('AuthApi', '解析错误响应失败', parseError);
          }
        }

        // 根据HTTP状态码提供友好的错误信息
        String errorMessage;
        switch (statusCode) {
          case 400:
            errorMessage = '请求参数错误，请检查输入信息';
            break;
          case 401:
            errorMessage = '用户名或密码错误';
            break;
          case 403:
            errorMessage = '没有访问权限，请联系管理员';
            break;
          case 404:
            errorMessage = '登录服务不可用';
            break;
          case 500:
            errorMessage = '服务器内部错误，请稍后重试';
            break;
          default:
            errorMessage = '登录失败，请检查网络连接';
        }

        throw ApiException(
          message: errorMessage,
          statusCode: statusCode,
          data: responseData,
        );
      }

      // 网络连接错误
      String errorMessage;
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
          errorMessage = '连接超时，请检查网络';
          break;
        case DioExceptionType.receiveTimeout:
          errorMessage = '接收数据超时，请重试';
          break;
        case DioExceptionType.sendTimeout:
          errorMessage = '发送数据超时，请重试';
          break;
        case DioExceptionType.connectionError:
          errorMessage = '网络连接失败，请检查网络设置';
          break;
        default:
          errorMessage = '网络错误，请稍后重试';
      }

      LogService.instance.error('AuthApi', '登录网络错误 - type: ${e.type}', e);

      throw ApiException(
        message: errorMessage,
        statusCode: null,
        data: null,
      );
    } catch (e) {
      LogService.instance.error('AuthApi', '登录未知错误', e);

      throw ApiException(
        message: '登录失败，发生未知错误',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 获取当前用户信息
  /// 
  /// 返回:
  /// - [AuthUser] 当前用户信息
  /// 
  /// 异常:
  /// - [ApiException] 获取失败或网络错误
  Future<AuthUser> getCurrentUser() async {
    try {
      LogService.instance.info('AuthApi', '开始获取当前用户信息');

      final Response response = await apiClient.get('/auth/me');

      LogService.instance.info('AuthApi', '获取用户信息API调用成功 - statusCode: ${response.statusCode}');

      // 使用统一的响应处理器
      final AuthUser user = ApiResponseHandler.parseResponse<AuthUser>(
        response,
        (data) => AuthUser.fromJson(data),
      );

      LogService.instance.info('AuthApi', '获取用户信息成功 - userId: ${user.id}, username: ${user.username}');

      return user;
    } on ApiException {
      rethrow;
    } catch (e) {
      LogService.instance.error('AuthApi', '获取用户信息失败', e);

      throw ApiException(
        message: '获取用户信息失败',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 刷新访问令牌
  /// 
  /// 参数:
  /// - [refreshToken] 刷新令牌
  /// 
  /// 返回:
  /// - [Map<String, dynamic>] 包含新的访问令牌信息
  /// 
  /// 异常:
  /// - [ApiException] 刷新失败
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      LogService.instance.info('AuthApi', '开始刷新访问令牌');

      final Response response = await apiClient.post(
        '/auth/refresh',
        data: {'refreshToken': refreshToken},
      );

      LogService.instance.info('AuthApi', '刷新令牌API调用成功 - statusCode: ${response.statusCode}');

      // 使用统一的响应处理器
      final Map<String, dynamic> tokenData = ApiResponseHandler.parseResponse<Map<String, dynamic>>(
        response,
        (data) => data,
      );

      LogService.instance.info('AuthApi', '刷新令牌成功');

      return tokenData;
    } on ApiException {
      rethrow;
    } catch (e) {
      LogService.instance.error('AuthApi', '刷新令牌失败', e);

      throw ApiException(
        message: '刷新令牌失败',
        statusCode: null,
        data: null,
      );
    }
  }

  /// 用户登出
  /// 
  /// 异常:
  /// - [ApiException] 登出失败
  Future<void> logout() async {
    try {
      LogService.instance.info('AuthApi', '开始用户登出');

      final Response response = await apiClient.post('/auth/logout');

      LogService.instance.info('AuthApi', '用户登出成功 - statusCode: ${response.statusCode}');
    } on ApiException {
      rethrow;
    } catch (e) {
      LogService.instance.error('AuthApi', '用户登出失败', e);

      throw ApiException(
        message: '登出失败',
        statusCode: null,
        data: null,
      );
    }
  }
}
