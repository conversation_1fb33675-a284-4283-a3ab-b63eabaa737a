import 'package:dio/src/response.dart';
import 'package:flutter/foundation.dart';
import 'api_client.dart';
import '../../models/station_model.dart';

/// Station API服务类
/// 用于处理站点相关的API请求
class StationApi {
  StationApi({required ApiClient apiClient}) : _apiClient = apiClient;
  final ApiClient _apiClient;

  /// 获取单个站点信息
  /// 调用 BOS API: GET /api/v1/stations/{id}
  Future<Station?> getStationById(int stationId) async {
    debugPrint('正在获取站点信息，站点ID: $stationId');
    try {
      final Response response = await _apiClient.get('/api/v1/stations/$stationId');

      // 判断响应是否成功
      if (response.statusCode == 200 && response.data['code'] == 0) {
        final dynamic data = response.data['data'];
        if (data != null) {
          debugPrint('获取站点信息成功: ${data['site_name']}');
          return Station.fromJson(data as Map<String, dynamic>);
        } else {
          debugPrint('站点不存在: $stationId');
          return null;
        }
      } else {
        // 处理业务逻辑错误
        final errorCode = response.data['code'] ?? 'UNKNOWN_ERROR';
        final errorMessage = response.data['message'] ?? '未知错误';

        throw ApiException(
          message: errorMessage.toString(),
          statusCode: response.statusCode,
          data: <String, dynamic>{'code': errorCode},
        );
      }
    } catch (e) {
      // 记录错误并重新抛出
      debugPrint('获取站点信息失败: $e');
      if (e is ApiException) {
        rethrow;
      } else {
        throw ApiException(
          message: '获取站点信息失败',
          statusCode: 500,
          data: <String, String>{'error': e.toString()},
        );
      }
    }
  }

  /// 获取多个站点信息
  /// 调用 BOS API: GET /api/v1/stations
  Future<List<Station>> getStations({
    int? page,
    int? pageSize,
    String? search,
  }) async {
    debugPrint('正在获取站点列表...');
    try {
      final Map<String, dynamic> queryParams = <String, dynamic>{};
      if (page != null) queryParams['page'] = page;
      if (pageSize != null) queryParams['page_size'] = pageSize;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final Response response = await _apiClient.get(
        '/api/v1/stations',
        queryParameters: queryParams,
      );

      // 判断响应是否成功
      if (response.statusCode == 200 && response.data['code'] == 0) {
        final dynamic data = response.data['data'];
        if (data != null && data is List) {
          debugPrint('获取站点列表成功，共 ${data.length} 个站点');
          return data.map((dynamic item) => Station.fromJson(item as Map<String, dynamic>)).toList();
        } else {
          debugPrint('站点列表为空');
          return <Station>[];
        }
      } else {
        // 处理业务逻辑错误
        final errorCode = response.data['code'] ?? 'UNKNOWN_ERROR';
        final errorMessage = response.data['message'] ?? '未知错误';

        throw ApiException(
          message: errorMessage.toString(),
          statusCode: response.statusCode,
          data: <String, dynamic>{'code': errorCode},
        );
      }
    } catch (e) {
      // 记录错误并重新抛出
      debugPrint('获取站点列表失败: $e');
      if (e is ApiException) {
        rethrow;
      } else {
        throw ApiException(
          message: '获取站点列表失败',
          statusCode: 500,
          data: <String, String>{'error': e.toString()},
        );
      }
    }
  }
} 