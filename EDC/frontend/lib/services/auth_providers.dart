// EDC/frontend/lib/services/auth_providers.dart

/// 认证相关的Provider
/// 为现有代码提供简化的认证状态访问接口

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/auth_models.dart';
import 'new_auth_service.dart';

/// 便捷的认证状态Provider
final isLoggedInProvider = Provider<bool>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.isLoggedIn;
});

/// 便捷的当前用户Provider
final currentUserProvider = Provider<AuthUser?>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.currentUser;
});

/// 便捷的当前用户名Provider
final currentUsernameProvider = Provider<String?>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.currentUser?.username;
});

/// 便捷的当前用户全名Provider
final currentUserFullNameProvider = Provider<String?>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.currentUser?.fullName;
});

/// 便捷的当前用户ID Provider
final currentUserIdProvider = Provider<String?>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.currentUser?.id;
});

/// 便捷的默认加油站ID Provider
final defaultStationIdProvider = Provider<int?>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  final defaultStation = newAuthService.defaultStation;
  return defaultStation?.id;
});

/// 便捷的管理权限Provider
final hasManagePermissionProvider = Provider<bool>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  final systemAccess = newAuthService.systemAccess;
  if (systemAccess != null) {
    return systemAccess.accessLevel == 'admin' || 
           systemAccess.accessLevel == 'manager';
  }
  return false;
});

/// 便捷的可访问加油站ID列表Provider
final accessibleStationIdsProvider = Provider<List<int>>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.accessibleStationIds;
});

/// 权限检查Provider工厂
Provider<bool> hasPermissionProvider(String permission) {
  return Provider<bool>((ref) {
    final newAuthService = ref.watch(newAuthServiceProvider);
    return newAuthService.hasPermission(permission);
  });
}

/// 角色检查Provider工厂
Provider<bool> hasRoleProvider(String role) {
  return Provider<bool>((ref) {
    final newAuthService = ref.watch(newAuthServiceProvider);
    return newAuthService.hasRole(role);
  });
}

/// 兼容性：员工编号Provider（映射到用户名）
final currentEmployeeNoProvider = Provider<String?>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.currentUser?.username;
});

/// 兼容性：员工姓名Provider（映射到全名）
final currentEmployeeNameProvider = Provider<String?>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.currentUser?.fullName;
});

/// 系统访问信息Provider
final systemAccessProvider = Provider<SystemAccess?>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.systemAccess;
});

/// 用户站点列表Provider
final userStationsProvider = Provider<List<UserStationInfo>>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.currentUser?.stations ?? [];
});

/// 用户站点信息Provider
final userSitesProvider = Provider<List<SiteInfo>>((ref) {
  final newAuthService = ref.watch(newAuthServiceProvider);
  return newAuthService.currentUser?.sites ?? [];
});
