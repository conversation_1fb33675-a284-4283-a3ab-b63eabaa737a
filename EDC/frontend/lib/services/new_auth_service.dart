// EDC/frontend/lib/services/new_auth_service.dart

/// 新的认证服务
/// 基于BOS认证API v2实现，与现有系统兼容

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/auth_models.dart';
import '../constants/api_constants.dart';
import '../main.dart'; // 导入storageServiceProvider
import 'api/api_client.dart';
import 'api/auth_api.dart';
import 'auth_storage_service.dart';
import 'shared/storage_service.dart';
import 'log_service.dart';

/// 新认证服务类
class NewAuthService extends ChangeNotifier {
  NewAuthService({required this.storageService}) {
    _init();
  }

  // 依赖注入
  final StorageService storageService;

  // API客户端和服务
  late ApiClient _apiClient;
  late AuthApi _authApi;

  // 存储服务
  final AuthStorageService _storage = AuthStorageService.instance;

  // 状态管理
  bool _isLoggedIn = false;
  bool _isInitialized = false;
  AuthUser? _currentUser;
  SystemAccess? _systemAccess;

  // Getters
  bool get isLoggedIn => _isLoggedIn;
  bool get isInitialized => _isInitialized;
  AuthUser? get currentUser => _currentUser;
  SystemAccess? get systemAccess => _systemAccess;

  /// 初始化服务
  Future<void> _init() async {
    try {
      LogService.instance.info('NewAuthService', '开始初始化新认证服务');

      // 初始化存储服务
      await _storage.init();

      // 初始化API客户端
      await _initApiClient();

      // 恢复登录状态
      await _restoreLoginState();

      _isInitialized = true;
      notifyListeners();

      LogService.instance.info('NewAuthService', '新认证服务初始化完成 - isLoggedIn: $_isLoggedIn, hasUser: ${_currentUser != null}');
    } catch (e) {
      LogService.instance.error('NewAuthService', '新认证服务初始化失败', e);
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// 初始化API客户端
  Future<void> _initApiClient() async {
    // 获取服务器配置的baseURL
    final String baseUrl = await _getConfiguredBaseUrl();

    _apiClient = ApiClient(baseUrl: '$baseUrl/api/v1');
    _authApi = AuthApi(apiClient: _apiClient);

    // 如果有存储的令牌，配置到API客户端
    final String? accessToken = await _storage.getAccessToken();
    if (accessToken != null) {
      _apiClient.configureClient(token: accessToken);
    }

    LogService.instance.info('NewAuthService', 'API客户端初始化完成 - baseUrl: $baseUrl, hasToken: ${accessToken != null}');
  }

  /// 获取配置的baseURL
  Future<String> _getConfiguredBaseUrl() async {
    try {
      // 1. 优先使用自定义BOS服务器地址
      final String? customBosUrl = await storageService.getCustomBosUrl();
      if (customBosUrl != null && customBosUrl.isNotEmpty) {
        LogService.instance.info('NewAuthService', '使用自定义BOS服务器地址: $customBosUrl');
        return customBosUrl;
      }

      // 2. 使用当前API环境的默认配置
      final ApiEnvironment currentEnv = await storageService.getApiEnvironment();
      final String defaultUrl = ApiConstants.getServiceUrl(BackendService.base, currentEnv);

      LogService.instance.info('NewAuthService', '使用默认BOS服务器地址: $defaultUrl (环境: $currentEnv)');
      return defaultUrl;
    } catch (e) {
      // 3. 如果出错，使用本地环境作为后备
      LogService.instance.error('NewAuthService', '获取服务器配置失败，使用本地环境', e);
      return ApiConstants.getServiceUrl(BackendService.base, ApiEnvironment.local);
    }
  }

  /// 恢复登录状态
  Future<void> _restoreLoginState() async {
    try {
      final bool isLoggedIn = await _storage.isLoggedIn();
      if (!isLoggedIn) {
        LogService.instance.info('NewAuthService', '用户未登录');
        return;
      }

      // 检查令牌是否过期
      final bool isExpired = await _storage.isTokenExpired();
      if (isExpired) {
        LogService.instance.info('NewAuthService', '令牌已过期，尝试刷新');
        final bool refreshed = await _refreshTokenIfNeeded();
        if (!refreshed) {
          LogService.instance.info('NewAuthService', '令牌刷新失败，清除登录状态');
          await _clearLoginState();
          return;
        }
      }

      // 恢复用户数据
      _currentUser = await _storage.getUserData();
      if (_currentUser != null) {
        LogService.instance.info('NewAuthService', '🔍 恢复用户数据: ${_currentUser!.fullName}, stations: ${_currentUser!.stations.length}');
      }
      _systemAccess = await _storage.getSystemAccess();

      if (_currentUser != null && _systemAccess != null) {
        _isLoggedIn = true;
        LogService.instance.info('NewAuthService', '登录状态恢复成功 - userId: ${_currentUser!.id}, username: ${_currentUser!.username}');
      } else {
        LogService.instance.warning('NewAuthService', '用户数据不完整，清除登录状态');
        await _clearLoginState();
      }
    } catch (e) {
      LogService.instance.error('NewAuthService', '恢复登录状态失败', e);
      await _clearLoginState();
    }
  }

  /// 用户登录
  Future<bool> login(String username, String password) async {
    try {
      LogService.instance.info('NewAuthService', '开始用户登录 - username: $username');

      // 创建登录请求
      final LoginRequest request = LoginRequest(
        username: username,
        password: password,
        system: 'EDC', // 固定为EDC系统
        authType: 'local',
        rememberMe: true,
      );

      // 调用登录API
      final LoginResponseData loginData = await _authApi.login(request);

      // 保存登录数据
      await _storage.saveLoginData(loginData);

      // 更新API客户端令牌
      _apiClient.configureClient(token: loginData.accessToken);

      // 更新内存状态
      _currentUser = loginData.user;
      LogService.instance.info('NewAuthService', '🔍 登录成功，用户stations数量: ${_currentUser!.stations.length}');
      _systemAccess = loginData.systemAccess;
      _isLoggedIn = true;

      notifyListeners();

      LogService.instance.info('NewAuthService', '用户登录成功 - userId: ${_currentUser!.id}, username: ${_currentUser!.username}, system: ${_systemAccess!.system}, stationCount: ${_systemAccess!.stationCount}');
      
      // 🔍 详细验证存储的数据
      LogService.instance.info('NewAuthService', '🔍 用户stations详细信息:');
      for (int i = 0; i < _currentUser!.stations.length; i++) {
        final station = _currentUser!.stations[i];
        LogService.instance.info('NewAuthService', '  Station $i: stationId=${station.stationId}, siteName=${station.siteName}, isDefault=${station.isDefault}');
      }
      
      // 验证defaultStation
      final defaultStation = this.defaultStation;
      if (defaultStation != null) {
        LogService.instance.info('NewAuthService', '🔍 defaultStation验证: stationId=${defaultStation.stationId}, siteName=${defaultStation.siteName}');
      } else {
        LogService.instance.error('NewAuthService', '🔍 defaultStation为null！');
      }

      return true;
    } catch (e) {
      LogService.instance.error('NewAuthService', '用户登录失败 - username: $username', e);
      
      await _clearLoginState();
      rethrow;
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      LogService.instance.info('NewAuthService', '开始用户登出');

      // 调用登出API（可选，即使失败也要清除本地状态）
      try {
        await _authApi.logout();
      } catch (e) {
        LogService.instance.warning('NewAuthService', '登出API调用失败，继续清除本地状态');
      }

      // 清除本地状态
      await _clearLoginState();

      LogService.instance.info('NewAuthService', '用户登出完成');
    } catch (e) {
      LogService.instance.error('NewAuthService', '用户登出失败', e);
      // 即使出错也要清除本地状态
      await _clearLoginState();
    }
  }

  /// 刷新令牌
  Future<bool> _refreshTokenIfNeeded() async {
    try {
      final String? refreshToken = await _storage.getRefreshToken();
      if (refreshToken == null) {
        LogService.instance.warning('NewAuthService', '没有刷新令牌');
        return false;
      }

      LogService.instance.info('NewAuthService', '开始刷新访问令牌');

      final Map<String, dynamic> tokenData = await _authApi.refreshToken(refreshToken);
      
      final String newAccessToken = tokenData['accessToken'] as String;
      final int expiresIn = tokenData['expiresIn'] as int;

      // 更新存储的令牌
      await _storage.updateAccessToken(newAccessToken, expiresIn);

      // 更新API客户端令牌
      _apiClient.configureClient(token: newAccessToken);

      LogService.instance.info('NewAuthService', '访问令牌刷新成功');
      return true;
    } catch (e) {
      LogService.instance.error('NewAuthService', '刷新访问令牌失败', e);
      return false;
    }
  }

  /// 清除登录状态
  Future<void> _clearLoginState() async {
    try {
      await _storage.clearAuthData();
      
      _currentUser = null;
      _systemAccess = null;
      _isLoggedIn = false;
      
      // 清除API客户端令牌
      _apiClient.configureClient(token: null);
      
      notifyListeners();
      
      LogService.instance.info('NewAuthService', '登录状态已清除');
    } catch (e) {
      LogService.instance.error('NewAuthService', '清除登录状态失败', e);
    }
  }

  /// 获取当前用户的默认加油站
  UserStationInfo? get defaultStation {
    if (_currentUser == null) return null;

    // 由于UserStationInfo结构改变，现在直接返回第一个站点
    // 如果需要默认站点逻辑，需要在API响应中添加相应字段
    final UserStationInfo? defaultStation = _currentUser!.stations.isNotEmpty ? _currentUser!.stations.first : null;
      print('🔍 [NewAuthService] defaultStation查询: 用户有${_currentUser!.stations.length}个stations, defaultStation: ${defaultStation?.stationId}');
      return defaultStation;
  }

  /// 检查用户是否有特定权限
  bool hasPermission(String permission) {
    if (_currentUser == null) return false;
    return _currentUser!.permissions.contains(permission);
  }

  /// 检查用户是否有特定角色
  bool hasRole(String role) {
    if (_currentUser == null) return false;
    return _currentUser!.roles.contains(role);
  }

  /// 获取用户可访问的加油站ID列表
  List<int> get accessibleStationIds {
    if (_systemAccess == null) return [];
    return _systemAccess!.stationIds;
  }

  /// 检查令牌是否即将过期（30分钟内）
  Future<bool> isTokenExpiringSoon() async {
    final int remainingTime = await _storage.getTokenRemainingTime();
    return remainingTime < 1800; // 30分钟
  }

  /// 主动刷新令牌（如果需要）
  Future<void> refreshTokenIfExpiring() async {
    if (await isTokenExpiringSoon()) {
      await _refreshTokenIfNeeded();
    }
  }

  /// 更新服务器配置
  /// 当用户在设置中更改服务器地址时调用此方法
  Future<void> updateServerConfiguration() async {
    try {
      LogService.instance.info('NewAuthService', '开始更新服务器配置');

      // 重新初始化API客户端以使用新的baseURL
      await _initApiClient();

      LogService.instance.info('NewAuthService', '服务器配置更新完成');
    } catch (e) {
      LogService.instance.error('NewAuthService', '更新服务器配置失败', e);
      rethrow;
    }
  }
}

/// Riverpod Provider
final newAuthServiceProvider = ChangeNotifierProvider<NewAuthService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return NewAuthService(storageService: storageService);
});


