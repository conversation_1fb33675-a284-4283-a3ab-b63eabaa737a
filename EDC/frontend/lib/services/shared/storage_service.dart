import 'package:shared_preferences/shared_preferences.dart';
import '../../constants/api_constants.dart'; // 引入我们刚创建的常量

// 服务类，用于管理本地存储
class StorageService {
  // SharedPreferences 实例，使用 late 关键字延迟初始化
  late SharedPreferences _prefs;

  // 初始化方法，获取 SharedPreferences 实例
  // 这个方法应该在应用启动时调用一次
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // 获取当前选择的API环境
  // 如果没有存储过，则默认返回本地环境 (ApiEnvironment.local)
  Future<ApiEnvironment> getApiEnvironment() async {
    // 读取存储的值，如果不存在则返回 null
    final String? storedValue =
        _prefs.getString(ApiConstants.environmentPreferenceKey);
    if (storedValue == null) {
      // 如果没有存储过，返回默认值：本地环境
      return ApiEnvironment.local;
    }
    // 根据存储的字符串值返回对应的枚举成员
    // 使用 try-catch 避免因存储了无效值而导致崩溃
    try {
      return ApiEnvironment.values
          .firstWhere((ApiEnvironment e) => e.toString() == storedValue);
    } catch (e) {
      // 如果存储的值无效，也返回默认值
      print(
          'Error reading API environment preference: $e. Defaulting to local.'); // 添加日志方便调试
      return ApiEnvironment.local;
    }
  }

  // 设置（保存）选择的API环境
  Future<void> setApiEnvironment(ApiEnvironment env) async {
    // 将枚举值转换为字符串进行存储
    await _prefs.setString(
        ApiConstants.environmentPreferenceKey, env.toString());
  }

  // --- 其他可能需要的存储方法 ---
  // 例如，存储和获取认证 Token
  Future<String?> getAuthToken() async {
    return _prefs.getString('auth_token'); // 假设 Token 的 key 是 'auth_token'
  }

  Future<void> setAuthToken(String? token) async {
    if (token != null) {
      await _prefs.setString('auth_token', token);
    } else {
      await _prefs.remove('auth_token'); // 如果 token 为 null，则移除
    }
  }

  Future<void> clearAuthToken() async {
    await _prefs.remove('auth_token');
  }

  // --- 自定义服务器地址配置方法 ---
  
  /// 保存自定义BOS服务器地址
  Future<void> setCustomBosUrl(String url) async {
    await _prefs.setString('custom_bos_url', url);
  }

  /// 获取自定义BOS服务器地址
  Future<String?> getCustomBosUrl() async {
    return _prefs.getString('custom_bos_url');
  }

  /// 保存自定义FCC服务器地址
  Future<void> setCustomFccUrl(String url) async {
    await _prefs.setString('custom_fcc_url', url);
  }

  /// 获取自定义FCC服务器地址
  Future<String?> getCustomFccUrl() async {
    return _prefs.getString('custom_fcc_url');
  }

  /// 清除所有自定义服务器地址配置
  Future<void> clearCustomUrls() async {
    await _prefs.remove('custom_bos_url');
    await _prefs.remove('custom_fcc_url');
  }

  /// 检查是否有自定义配置
  Future<bool> hasCustomConfiguration() async {
    final bosUrl = await getCustomBosUrl();
    final fccUrl = await getCustomFccUrl();
    return bosUrl != null || fccUrl != null;
  }
  // -----------------------------
}

// 可以考虑使用 Riverpod 提供 StorageService 的实例
// final storageServiceProvider = Provider<StorageService>((ref) {
//   // 注意：这里直接实例化可能不是最佳实践，
//   // 因为 init() 是异步的。通常在 main.dart 中初始化，然后提供实例。
//   // 更好的方式是在 main.dart 中初始化，然后通过 override 提供实例。
//   return StorageService();
// });
