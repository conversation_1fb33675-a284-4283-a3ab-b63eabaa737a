import 'package:flutter/material.dart';
import '../../constants/customer_type_constants.dart';
import '../../models/member_model.dart';
import '../member_cache_service.dart';

/// 获取会员信息的冗余数据
///
/// 该函数返回一个包含会员信息的冗余数据的Map
/// 可以在创建订单时调用此函数获取会员信息
///
/// [member] 会员对象，包含会员的基本信息
/// 返回一个包含会员信息的冗余数据的Map
Map<String, dynamic> getMemberData(Member? member) {
  if (member == null) {
    return <String, dynamic>{};
  }

  // 构建会员数据
  return <String, dynamic>{
    'member_id': member.memberId,
    'phone': member.phone,
    'name': member.name ?? '',
    'plate_numbers': member.plateNumbers,
    'member_level': member.memberLevel,
    'register_date': member.registrationDate.toIso8601String(),
    'status': member.status.toString().split('.').last,
    'account_balance': member.accountBalance,
    // 添加扩展信息
    ...member.metadata,
  };
}

/// 根据交易数据获取会员信息
///
/// 该函数从交易数据中提取会员ID，并返回对应的会员数据
/// 如果交易数据中没有会员ID，或者找不到对应的会员，则返回空Map
///
/// [transactionData] 交易数据，包含交易的基本信息
/// 返回一个包含会员信息的冗余数据的Map
Future<Map<String, dynamic>> getMemberDataFromTransaction(
    Map<String, dynamic>? transactionData) async {
  debugPrint('transactionData: $transactionData');

  if (transactionData == null) {
    return <String, dynamic>{};
  }

  // 获取会员ID - 检查两种可能的数据结构
  String? memberId;

  // 检查是否有直接的memberId字段
  if (transactionData['memberId'] != null) {
    memberId = transactionData['memberId'].toString();
  }
  // 检查是否有嵌套的member.id字段
  else if (transactionData['member'] != null &&
      transactionData['member']['id'] != null) {
    memberId = transactionData['member']['id'].toString();
  }

  // 如果没有找到会员ID，返回空Map
  if (memberId == null) {
    debugPrint('未找到会员ID');
    return <String, dynamic>{};
  }

  debugPrint('找到会员ID: $memberId');

  // 在实际应用中，这里应该从API或本地数据库获取会员数据
  // 这里使用模拟数据作为示例
  final List<Member> dummyMembers = Member.getDummyMembers();
  final Member member = dummyMembers.firstWhere(
    (Member m) => m.memberId == memberId,
    orElse: () => dummyMembers.first, // 默认返回第一个会员作为示例
  );

  // 获取会员冗余数据
  final Map<String, dynamic> memberData = getMemberData(member);
  debugPrint('会员数据: $memberData');
  return memberData;
}

/// 根据会员信息Map获取会员数据
///
/// 该函数从会员信息Map中提取会员数据，并返回对应的会员数据Map
/// 主要用于会员支付页面等已经有会员信息的场景
///
/// [memberInfo] 会员信息Map
/// 返回一个包含会员信息的冗余数据的Map
Map<String, dynamic> getMemberDataFromInfo(Map<String, dynamic>? memberInfo) {
  if (memberInfo == null) {
    return <String, dynamic>{};
  }

  // 构建会员数据
  return <String, dynamic>{
    'member_id': memberInfo['id'],
    'name': memberInfo['name'],
    'type': memberInfo['type'],
    'balance': memberInfo['balance'],
    'points': memberInfo['points'],
    // 可以根据需要添加更多字段
  };
}

/// 从缓存获取会员数据
///
/// 优先使用内存缓存中的会员信息，用于支付页面
/// 返回一个包含会员信息的冗余数据的Map
Map<String, dynamic> getMemberDataFromCache() {
  final Member? cachedMember = memberCacheService.cachedMember;

  if (cachedMember == null) {
    debugPrint('💾 缓存中没有会员信息');
    return <String, dynamic>{};
  }

  debugPrint('💾 从缓存获取会员信息: ${cachedMember.name} (${cachedMember.id})');

  // 构建会员数据，重点关注车牌、车型等关键信息
  final dynamic plateNumbers = cachedMember.metadata['plateNumbers'] ?? <dynamic>[];
  final String vehicleType = (cachedMember.metadata['vehicle'] ?? 'Unknown Vehicle').toString();
  final CustomerType customerType = CustomerTypeUtils.detectCustomerType(cachedMember.metadata, memberId: cachedMember.id);

  debugPrint('💾 构建缓存会员数据:');
  debugPrint('   会员ID: ${cachedMember.id}');
  debugPrint('   会员姓名: ${cachedMember.name}');
  debugPrint('   车牌号: $plateNumbers');
  debugPrint('   车型: $vehicleType');
  debugPrint('   客户类型: ${customerType.value}');

  return <String, dynamic>{
    'member_id': cachedMember.id,
    'phone': cachedMember.phone,
    'name': cachedMember.name,
    'member_level': cachedMember.levelDisplayName,
    'register_date': cachedMember.registrationDate.toIso8601String(),
    'status': cachedMember.statusDisplayName,
    'account_balance': cachedMember.balance,
    'points': cachedMember.points,
    // 重点信息：车牌、车型、客户类型 - 使用多种字段名确保兼容性
    'plate_numbers': plateNumbers,
    'vehicle_plate': plateNumbers, // 添加 vehicle_plate 字段以兼容 order_detail_widget
    'vehicle': vehicleType,
    'vehicle_type': vehicleType, // 添加 vehicle_type 字段以兼容 order_detail_widget
    'customer_type': customerType.value,
    'customerType': customerType.value, // 添加 customerType 字段以兼容 order_detail_widget
    'is_from_cache': true,
    'cache_time': memberCacheService.cacheTime?.toIso8601String(),
    // 添加扩展信息
    ...cachedMember.metadata,
  };
}

/// 优先使用缓存的会员数据获取策略
///
/// 1. 首先检查内存缓存
/// 2. 如果缓存中没有，则从交易数据中获取
/// 3. 如果都没有，返回空Map
///
/// [transactionData] 交易数据（备用数据源）
/// 返回一个包含会员信息的冗余数据的Map
Future<Map<String, dynamic>> getMemberDataWithCachePriority(
    Map<String, dynamic>? transactionData) async {
  // 优先使用缓存
  final Map<String, dynamic> cachedData = getMemberDataFromCache();
  if (cachedData.isNotEmpty) {
    debugPrint('✅ 使用缓存的会员信息进行支付');
    return cachedData;
  }

  // 如果缓存为空，则从交易数据获取（保持向后兼容）
  if (transactionData != null) {
    debugPrint('⚠️ 缓存为空，尝试从交易数据获取会员信息');
    return await getMemberDataFromTransaction(transactionData);
  }

  debugPrint('❌ 没有找到任何会员信息');
  return <String, dynamic>{};
}

/// 订单创建成功后消费会员缓存
///
/// 在支付成功后调用此方法清除缓存，避免重复使用
///
/// [orderCreated] 是否订单创建成功
/// [memberData] 使用的会员数据（用于日志记录）
/// 返回被消费的会员信息（如果有）
///
/// @deprecated 请直接使用 memberCacheService.consumeCache() 方法
@Deprecated('Use memberCacheService.consumeCache() directly instead')
Member? consumeMemberCacheAfterOrder(
    bool orderCreated, Map<String, dynamic>? memberData) {
  if (!orderCreated) {
    debugPrint('⚠️ 订单创建失败，不消费会员缓存');
    return null;
  }

  // 检查是否使用了缓存的会员信息
  final bool isFromCache = memberData?['is_from_cache'] == true;
  if (!isFromCache) {
    debugPrint('ℹ️ 未使用缓存会员信息，无需消费缓存');
    return null;
  }

  // 消费缓存
  final Member? consumedMember = memberCacheService.consumeCache();
  if (consumedMember != null) {
    debugPrint('🍽️ 订单创建成功，已消费会员缓存: ${consumedMember.name}');
    debugPrint('   车牌号: ${consumedMember.metadata['plateNumbers']}');
    debugPrint('   车型: ${consumedMember.metadata['vehicle']}');
  }

  return consumedMember;
}

/// 检查是否有可用的会员信息（缓存或交易数据）
///
/// 用于支付页面判断是否显示会员相关功能
///
/// [transactionData] 交易数据
/// 返回是否有会员信息可用
bool hasMemberInfoAvailable(Map<String, dynamic>? transactionData) {
  // 检查缓存
  if (memberCacheService.hasCachedMember) {
    return true;
  }

  // 检查交易数据
  if (transactionData != null) {
    final bool hasDirectMemberId = transactionData['memberId'] != null;
    final bool hasNestedMemberId = transactionData['member'] != null &&
        transactionData['member']['id'] != null;
    return hasDirectMemberId || hasNestedMemberId;
  }

  return false;
}
