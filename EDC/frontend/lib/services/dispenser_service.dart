import 'package:flutter/foundation.dart';

import '../models/dispenser_model.dart';
import '../models/fcc_device.dart';
import 'fcc_device_service.dart'; // 使用FCC设备服务
import 'api/api_client.dart';
import 'adapters/fcc_device_adapter_v2.dart';

class DispenserService {
  // 保留以备后续实际API调用使用

  DispenserService({
    required FCCDeviceService fccDeviceService, // 修改参数
    required ApiClient apiClient,
  })  : _fccDeviceService = fccDeviceService, // 使用FCC设备服务
        _apiClient = apiClient;
  final FCCDeviceService _fccDeviceService; // 替换为FCC设备服务
  // ignore: unused_field
  final ApiClient _apiClient;

  /// 获取所有分液器
  Future<List<Dispenser>> getDispensers() async {
    try {
      // 使用 FCCDeviceService 获取设备数据并转换为 Dispenser
      final List<FCCDevice> fccDevices = await _fccDeviceService.getFccDevices();
      final List<PumpGroup> allPumpGroups = FCCDeviceAdapterV2.fccDevicesToPumpGroups(fccDevices);
      final List<Dispenser> dispensers = FCCDeviceAdapterV2.pumpGroupsToDispensers(allPumpGroups);

      return dispensers;
    } catch (e) {
      throw Exception('Failed to get dispensers: $e');
    }
  }

  /// 获取指定分液器的油泵组
  Future<List<PumpGroup>> getPumpGroupsByDispenser(String dispenserId) async {
    try {
      // 使用 FCCDeviceService 获取数据
      final List<Dispenser> dispensers = await getDispensers();
      final Dispenser dispenser =
          dispensers.firstWhere((Dispenser d) => d.id == dispenserId);
      return dispenser.pumpGroups;
    } catch (e) {
      throw Exception('Failed to get pump groups: $e');
    }
  }

  /// 获取油泵组的喷枪
  Future<List<Nozzle>> getNozzlesByPumpGroup(String pumpGroupId) async {
    try {
      // 使用 FCCDeviceService 获取数据
      final List<Dispenser> dispensers = await getDispensers();
      for (final Dispenser dispenser in dispensers) {
        for (final PumpGroup pumpGroup in dispenser.pumpGroups) {
          if (pumpGroup.id == pumpGroupId) {
            return pumpGroup.nozzles;
          }
        }
      }
      return <Nozzle>[];
    } catch (e) {
      throw Exception('Failed to get nozzles: $e');
    }
  }

  /// 更新喷枪状态（最小粒度）
  Future<Nozzle> updateNozzleStatus(
      String nozzleId, NozzleStatus status) async {
    try {
      // 使用 FCCDeviceService 获取数据
      final List<Dispenser> dispensers = await getDispensers();
      for (final Dispenser dispenser in dispensers) {
        final Nozzle? nozzle = dispenser.getNozzleById(nozzleId);
        if (nozzle != null) {
          return nozzle.copyWith(
            status: status,
            lastUpdateTime: DateTime.now(),
          );
        }
      }

      throw Exception('Nozzle not found: $nozzleId');
    } catch (e) {
      throw Exception('Failed to update nozzle status: $e');
    }
  }

  /// 授权指定喷枪
  Future<bool> authorizeNozzle(
      String nozzleId, AuthorizationRequest authRequest) async {
    try {
      debugPrint('🔧 DispenserService: 使用FCC服务授权 nozzle $nozzleId');
      debugPrint('   模式: ${authRequest.mode.name}, 值: ${authRequest.value}');

      // 直接使用FCCDeviceService的authorizeNozzle方法
      await _fccDeviceService.authorizeNozzle(
        nozzleId,
        mode: _getPresetType(authRequest.mode),
        value: authRequest.value,
        employeeId: authRequest.staffId, // 使用AuthRequest中的staffId作为employeeId
      );

      // 如果没有抛出异常，认为授权成功
      debugPrint('✅ DispenserService: 授权成功');
      return true;
    } catch (e) {
      debugPrint('❌ DispenserService: 授权异常: $e');
      throw Exception('Failed to authorize nozzle: $e');
    }
  }

  /// 获取实时喷枪状态
  Future<List<Nozzle>> refreshNozzleStatus() async {
    try {
      // 使用 FCCDeviceService 获取数据
      final List<Dispenser> dispensers = await getDispensers();
      final List<Nozzle> allNozzles = <Nozzle>[];

      for (final Dispenser dispenser in dispensers) {
        for (final PumpGroup pumpGroup in dispenser.pumpGroups) {
          allNozzles.addAll(pumpGroup.nozzles);
        }
      }

      return allNozzles;
    } catch (e) {
      throw Exception('Failed to refresh nozzle status: $e');
    }
  }

  /// 获取指定喷枪的详细信息
  Future<Nozzle?> getNozzleDetails(String nozzleId) async {
    try {
      // 使用 FCCDeviceService 获取数据
      final List<Dispenser> dispensers = await getDispensers();
      for (final Dispenser dispenser in dispensers) {
        final Nozzle? nozzle = dispenser.getNozzleById(nozzleId);
        if (nozzle != null) {
          return nozzle;
        }
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get nozzle details: $e');
    }
  }

  /// 停止喷枪操作
  Future<bool> stopNozzle(String nozzleId) async {
    try {

      return true;
    } catch (e) {
      throw Exception('Failed to stop nozzle: $e');
    }
  }




  /// 转换授权模式到API格式
  String _getPresetType(AuthMode mode) {
    switch (mode) {
      case AuthMode.amount:
        return 'amount';
      case AuthMode.volume:
        return 'volume';
      case AuthMode.full:
        return 'full';
    }
  }
}

/// Dispenser服务异常类
class DispenserServiceException implements Exception {
  const DispenserServiceException(
    this.message, {
    this.code,
    this.originalError,
  });
  final String message;
  final String? code;
  final dynamic originalError;

  @override
  String toString() {
    return 'DispenserServiceException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}
