import 'package:flutter/foundation.dart';
import '../models/dispenser_model.dart';

/// 油枪授权缓存服务
/// 用于存储和检索最近的授权信息，特别是Staff ID
class NozzleAuthorizationCacheService {
  static final NozzleAuthorizationCacheService _instance = 
      NozzleAuthorizationCacheService._internal();
  
  factory NozzleAuthorizationCacheService() => _instance;
  
  NozzleAuthorizationCacheService._internal();

  // 授权信息缓存 - 按nozzle ID存储
  final Map<String, AuthorizationRequest> _authorizationCache = {};

  /// 存储授权信息
  void storeAuthorization(String nozzleId, AuthorizationRequest authRequest) {
    _authorizationCache[nozzleId] = authRequest;
    debugPrint('💾 NozzleAuthorizationCache: 存储授权信息');
    debugPrint('   Nozzle ID: $nozzleId');
    debugPrint('   Staff ID: ${authRequest.staffId}');
    debugPrint('   Mode: ${authRequest.mode.name}');
    debugPrint('   Value: ${authRequest.value}');
    debugPrint('   Request Time: ${authRequest.requestTime}');
  }

  /// 获取授权信息
  AuthorizationRequest? getAuthorization(String nozzleId) {
    final AuthorizationRequest? authRequest = _authorizationCache[nozzleId];
    if (authRequest != null) {
      debugPrint('📖 NozzleAuthorizationCache: 读取授权信息');
      debugPrint('   Nozzle ID: $nozzleId');
      debugPrint('   Staff ID: ${authRequest.staffId}');
    } else {
      debugPrint('📖 NozzleAuthorizationCache: 未找到授权信息 for nozzle $nozzleId');
    }
    return authRequest;
  }

  /// 清除特定油枪的授权信息
  void clearAuthorization(String nozzleId) {
    final AuthorizationRequest? removed = _authorizationCache.remove(nozzleId);
    if (removed != null) {
      debugPrint('🗑️ NozzleAuthorizationCache: 清除授权信息 for nozzle $nozzleId');
    }
  }

  /// 清除所有授权信息
  void clearAll() {
    final int count = _authorizationCache.length;
    _authorizationCache.clear();
    debugPrint('🗑️ NozzleAuthorizationCache: 清除所有授权信息 ($count 项)');
  }

  /// 检查是否有授权信息
  bool hasAuthorization(String nozzleId) {
    return _authorizationCache.containsKey(nozzleId);
  }

  /// 获取所有已授权的油枪ID
  List<String> getAuthorizedNozzleIds() {
    return _authorizationCache.keys.toList();
  }

  /// 清理过期的授权信息（超过30分钟）
  void cleanupExpiredAuthorizations() {
    final DateTime now = DateTime.now();
    final List<String> expiredKeys = [];

    for (final MapEntry<String, AuthorizationRequest> entry in _authorizationCache.entries) {
      final Duration difference = now.difference(entry.value.requestTime);
      if (difference.inMinutes > 30) {
        expiredKeys.add(entry.key);
      }
    }

    for (final String key in expiredKeys) {
      _authorizationCache.remove(key);
      debugPrint('🕐 NozzleAuthorizationCache: 清理过期授权信息 for nozzle $key');
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint('🕐 NozzleAuthorizationCache: 清理了 ${expiredKeys.length} 个过期授权');
    }
  }
}

// 全局实例
final NozzleAuthorizationCacheService nozzleAuthorizationCache = 
    NozzleAuthorizationCacheService(); 