import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../models/fuel_transaction.dart';
import '../services/api/api_service.dart';
import '../services/api/fuel_transaction_api.dart';
import '../services/shift_service.dart';

/// BOS交易轮询服务
/// 定期轮询BOS系统获取待处理交易，并独立轮询班次状态变化
class BosTransactionPollingService {
  BosTransactionPollingService({
    ApiService? apiService,
    Duration pollingInterval = const Duration(seconds: 5), // BOS交易轮询间隔为5秒
    Duration shiftPollingInterval = const Duration(seconds: 15), // 班次状态轮询间隔为15秒
    bool enableTransactionPolling = true, // 是否启用交易轮询
    bool enableShiftPolling = true, // 是否启用班次状态轮询
  })  : _apiService = apiService ?? ApiService(),
        _pollingInterval = pollingInterval,
        _shiftPollingInterval = shiftPollingInterval,
        _enableTransactionPolling = enableTransactionPolling,
        _enableShiftPolling = enableShiftPolling;
        
  final ApiService _apiService;
  final Duration _pollingInterval;
  final Duration _shiftPollingInterval;
  final bool _enableTransactionPolling;
  final bool _enableShiftPolling;
  
  Timer? _pollingTimer; // 交易轮询定时器
  Timer? _shiftPollingTimer; // 班次状态轮询定时器
  bool _isPolling = false;

  /// 已处理的BOS交易ID集合，避免重复处理
  final Set<String> _processedTransactionIds = <String>{};

  /// 上次轮询发现的pending交易集合，用于检测交易消失
  final Set<String> _lastPendingTransactionIds = <String>{};

  /// 上次已知的班次状态，用于检测班次变化
  ShiftInfo? _lastKnownShift;

  /// 回调函数
  Function(List<FuelTransaction>)? onTransactionsFound;
  Function(List<String>)? onTransactionsDisappeared; // 交易消失回调
  Function(ShiftInfo?)? onShiftStatusChanged; // 班次状态变化回调
  Function(String)? onError;

  /// 启动轮询
  void startPolling() {
    if (_isPolling) {
      debugPrint('⚠️ BOS轮询已在运行中');
      return;
    }

    try {
      _isPolling = true;

      // 根据配置启动交易轮询定时器
      if (_enableTransactionPolling) {
        _pollingTimer = Timer.periodic(_pollingInterval, (Timer timer) {
          _pollPendingTransactions();
        });
        debugPrint('✅ BOS交易轮询已启动，间隔: ${_pollingInterval.inSeconds}秒');
      } else {
        debugPrint('ℹ️ BOS交易轮询已禁用');
      }

      // 根据配置启动独立的班次状态轮询定时器
      if (_enableShiftPolling) {
        _shiftPollingTimer = Timer.periodic(_shiftPollingInterval, (Timer timer) {
          _pollShiftStatus();
        });

        // 立即执行一次班次状态检查
        _pollShiftStatus();
        debugPrint('✅ 班次状态轮询已启动，间隔: ${_shiftPollingInterval.inSeconds}秒');
      } else {
        debugPrint('ℹ️ 班次状态轮询已禁用');
      }
    } catch (e) {
      debugPrint('❌ BOS轮询启动失败: $e');
      onError?.call('Failed to start BOS polling: $e');
    }
  }

  /// 停止轮询
  void stopPolling() {
    try {
      _pollingTimer?.cancel();
      _pollingTimer = null;
      
      _shiftPollingTimer?.cancel();
      _shiftPollingTimer = null;
      
      _isPolling = false;
      debugPrint('⏹️ BOS交易轮询已停止');
      debugPrint('⏹️ 班次状态轮询已停止');
    } catch (e) {
      debugPrint('❌ 停止BOS轮询失败: $e');
    }
  }

  /// 轮询待处理的交易
  Future<void> _pollPendingTransactions() async {
    try {
      final FuelTransactionApi fuelTransactionApi =
          _apiService.fuelTransactionApi;

      // 获取查询时间范围
      final Map<String, String> timeRange = _getTransactionQueryTimeRange();

      // 查询待处理交易
      final FuelTransactionQueryParams queryParams = FuelTransactionQueryParams(
        status: 'pending', // 查询待处理的交易
        dateFrom: timeRange['dateFrom'],
        dateTo: timeRange['dateTo'],
        page: 1,
        limit: 20, // 最多查询20条
        sortBy: 'created_at',
        sortDir: 'desc',
      );

      final FuelTransactionResponse response =
          await fuelTransactionApi.getFuelTransactions(queryParams);

      // 获取当前pending交易ID集合
      final Set<String> currentPendingIds = response.items
          .map((FuelTransaction tx) => tx.transactionNumber)
          .toSet();

      // 检测消失的交易（上次有，这次没有）
      final Set<String> disappearedTransactionIds = 
          _lastPendingTransactionIds.difference(currentPendingIds);

      if (disappearedTransactionIds.isNotEmpty) {
        debugPrint('🔍 检测到 ${disappearedTransactionIds.length} 条交易已消失');
        debugPrint('   消失的交易ID: $disappearedTransactionIds');
        
        // 通知回调处理消失的交易
        onTransactionsDisappeared?.call(disappearedTransactionIds.toList());
      }

      // 更新上次pending交易集合
      _lastPendingTransactionIds.clear();
      _lastPendingTransactionIds.addAll(currentPendingIds);

      if (response.items.isNotEmpty) {
        debugPrint('🔄 发现 ${response.items.length} 条pending交易');
        debugPrint('   查询时间范围: ${timeRange['dateFrom']} ~ ${timeRange['dateTo']}');
        debugPrint('   数据来源: ${timeRange['source']}');
        
        // 对于pending交易，我们需要持续处理以保持nozzle的complete状态
        // 不再过滤"已处理"的交易，因为pending状态需要持续维护
        final List<FuelTransaction> pendingTransactions = response.items;
        
        for (final FuelTransaction transaction in pendingTransactions) {
          debugPrint('   - 交易 ${transaction.transactionNumber}: nozzle ${transaction.nozzleId}, volume=${transaction.volume}L, amount=${transaction.amount}');
        }

        // 通知回调处理所有pending交易（保持状态锁定）
        onTransactionsFound?.call(pendingTransactions);
      } else {
        debugPrint('📭 未发现pending交易');
      }
    } catch (e) {
      debugPrint('⚠️ BOS交易轮询失败: $e');
      onError?.call('BOS polling failed: $e');
    }
  }

  /// 获取交易查询时间范围
  Map<String, String> _getTransactionQueryTimeRange() {
    final DateTime now = DateTime.now();
    final ShiftService shiftService = ShiftService();
    final ShiftInfo? currentShift = shiftService.currentShift;

    String dateFrom;
    String dateTo;
    String source;

    if (currentShift != null && currentShift.status == ShiftStatus.active) {
      // 使用当前班次时间范围
      dateFrom = _formatDateForQuery(currentShift.startTime);
      dateTo = _formatDateForQuery(now);
      source = '班次时间 (${currentShift.shiftId})';
    } else {
      // 回退策略：查询最近6小时
      final DateTime sixHoursAgo = now.subtract(const Duration(hours: 6));
      dateFrom = _formatDateForQuery(sixHoursAgo);
      dateTo = _formatDateForQuery(now);
      source = '最近6小时 (无活跃班次)';
    }

    return <String, String>{
      'dateFrom': dateFrom,
      'dateTo': dateTo,
      'source': source,
    };
  }

  /// 格式化日期用于查询
  String _formatDateForQuery(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }

  /// 获取服务状态
  Map<String, dynamic> getServiceStatus() {
    return <String, dynamic>{
      'is_polling': _isPolling,
      'transaction_polling_interval_seconds': _pollingInterval.inSeconds,
      'shift_polling_interval_seconds': _shiftPollingInterval.inSeconds,
      'processed_transactions_count': _processedTransactionIds.length,
      'last_pending_transactions_count': _lastPendingTransactionIds.length,
      'has_transaction_callback': onTransactionsFound != null,
      'has_disappeared_callback': onTransactionsDisappeared != null,
      'has_shift_callback': onShiftStatusChanged != null,
      'has_error_callback': onError != null,
      'last_known_shift': _lastKnownShift?.shiftId,
      'last_known_shift_status': _lastKnownShift?.status.name,
    };
  }

  /// 清理已处理交易记录（避免内存无限增长）
  void clearProcessedTransactions() {
    final int previousProcessedCount = _processedTransactionIds.length;
    final int previousPendingCount = _lastPendingTransactionIds.length;
    
    _processedTransactionIds.clear();
    _lastPendingTransactionIds.clear();
    
    debugPrint('🧹 清理交易记录: 已处理 $previousProcessedCount -> 0, 上次pending $previousPendingCount -> 0');
  }

  /// 手动触发一次轮询（用于测试或立即检查）
  Future<void> pollOnce() async {
    debugPrint('🔄 手动触发BOS交易轮询...');
    await _pollPendingTransactions();
    await _pollShiftStatus();
  }

  /// 🎯 查询指定nozzle的pending交易
  /// 用于精确验证特定nozzle是否有pending交易
  Future<List<FuelTransaction>> queryNozzlePendingTransactions(String nozzleId) async {
    try {
      debugPrint('🔍 [BOS轮询] 查询nozzle $nozzleId 的pending交易');

      final FuelTransactionApi fuelTransactionApi = _apiService.fuelTransactionApi;

      // 获取查询时间范围（最近1小时）
      final DateTime now = DateTime.now();
      final DateTime oneHourAgo = now.subtract(const Duration(hours: 1));

      final FuelTransactionQueryParams queryParams = FuelTransactionQueryParams(
        nozzleId: nozzleId, // 使用新增的nozzle_id筛选
        status: 'pending',
        dateFrom: oneHourAgo.toIso8601String().split('T')[0], // 格式: 2025-07-22
        dateTo: now.toIso8601String().split('T')[0],
        page: 1,
        limit: 10, // 限制数量，避免过多数据
        sortBy: 'created_at',
        sortDir: 'desc',
      );

      debugPrint('   📤 查询参数: ${queryParams.toJson()}');

      final FuelTransactionResponse response =
          await fuelTransactionApi.getFuelTransactions(queryParams);

      if (response.items.isNotEmpty) {
        debugPrint('   ✅ 找到 ${response.items.length} 条pending交易');
        for (final FuelTransaction transaction in response.items) {
          debugPrint('     - 交易 ${transaction.transactionNumber}: volume=${transaction.volume}L, amount=${transaction.amount}');
        }
      } else {
        debugPrint('   📭 未找到pending交易');
      }

      return response.items;

    } catch (e) {
      debugPrint('   ❌ 查询nozzle $nozzleId pending交易失败: $e');
      return <FuelTransaction>[];
    }
  }

  /// 🎯 批量查询多个nozzle的pending交易
  /// 用于批量验证多个nozzle的状态
  Future<Map<String, List<FuelTransaction>>> queryMultipleNozzlesPendingTransactions(
    List<String> nozzleIds,
  ) async {
    final Map<String, List<FuelTransaction>> results = <String, List<FuelTransaction>>{};

    debugPrint('🔍 [BOS轮询] 批量查询 ${nozzleIds.length} 个nozzle的pending交易');

    // 并发查询所有nozzle
    final List<Future<MapEntry<String, List<FuelTransaction>>>> futures =
        nozzleIds.map((String nozzleId) async {
      final List<FuelTransaction> transactions = await queryNozzlePendingTransactions(nozzleId);
      return MapEntry<String, List<FuelTransaction>>(nozzleId, transactions);
    }).toList();

    final List<MapEntry<String, List<FuelTransaction>>> entries = await Future.wait(futures);

    for (final MapEntry<String, List<FuelTransaction>> entry in entries) {
      results[entry.key] = entry.value;
    }

    final int totalTransactions = results.values.fold(0, (int sum, List<FuelTransaction> list) => sum + list.length);
    debugPrint('🎯 [BOS轮询] 批量查询完成: 总共找到 $totalTransactions 条pending交易');

    return results;
  }

  /// 轮询班次状态变化
  Future<void> _pollShiftStatus() async {
    try {
      final ShiftService shiftService = ShiftService();

      // 确保ShiftService已初始化
      if (!shiftService.isInitialized) {
        debugPrint('🔄 ShiftService未初始化，正在初始化...');
        await shiftService.initialize();

        // 初始化后再次检查
        if (!shiftService.isInitialized) {
          debugPrint('❌ ShiftService初始化失败，跳过班次状态轮询');
          return;
        }
        debugPrint('✅ ShiftService初始化完成');
      }

      // 检查是否有有效的station_id
      if (shiftService.currentStationId == null) {
        debugPrint('⚠️ 班次状态轮询跳过: 无有效的station_id（用户可能未登录）');
        debugPrint('   - ShiftService已初始化: ${shiftService.isInitialized}');
        debugPrint('   - 当前station_id: ${shiftService.currentStationId}');
        return;
      }

      debugPrint('🔄 开始班次状态轮询 (station_id: ${shiftService.currentStationId})');

      // 获取当前班次状态
      await shiftService.refreshCurrentShift();
      final ShiftInfo? currentShift = shiftService.currentShift;

      // 详细记录轮询结果
      if (currentShift != null) {
        debugPrint('📊 轮询结果: 找到活跃班次 ${currentShift.shiftId} (${currentShift.status.name})');
      } else {
        debugPrint('📊 轮询结果: 当前无活跃班次（API返回has_active_shift: false）');
      }

      // 检查班次状态是否发生变化
      if (_hasShiftChanged(currentShift)) {
        debugPrint('🎯 班次状态发生变化！');
        debugPrint('   之前状态: ${_lastKnownShift?.shiftId ?? '无班次'} (${_lastKnownShift?.status.name ?? '无状态'})');
        debugPrint('   当前状态: ${currentShift?.shiftId ?? '无班次'} (${currentShift?.status.name ?? '无状态'})');

        // 更新已知班次状态
        _lastKnownShift = currentShift;

        // 通知回调处理班次状态变化
        onShiftStatusChanged?.call(currentShift);
        debugPrint('✅ 班次状态变化回调已触发');
      } else {
        debugPrint('ℹ️ 班次状态无变化 (${currentShift?.shiftId ?? '无班次'})');
      }
    } catch (e) {
      debugPrint('❌ 班次状态轮询失败: $e');
      debugPrint('   错误详情: ${e.toString()}');
      // 班次状态轮询失败不影响交易轮询，只记录错误
    }
  }

  /// 检查班次状态是否发生变化
  bool _hasShiftChanged(ShiftInfo? currentShift) {
    // 如果都为null，没有变化
    if (_lastKnownShift == null && currentShift == null) {
      debugPrint('🔍 状态检查: 都为null，无变化');
      return false;
    }

    // 一个为null一个不为null，发生变化
    if (_lastKnownShift == null || currentShift == null) {
      debugPrint('🔍 状态检查: 一个为null一个不为null，有变化');
      debugPrint('   之前: ${_lastKnownShift?.shiftId ?? 'null'}');
      debugPrint('   当前: ${currentShift?.shiftId ?? 'null'}');
      return true;
    }

    // 班次ID或状态发生变化
    final bool hasChanged = _lastKnownShift!.shiftId != currentShift.shiftId ||
                           _lastKnownShift!.status != currentShift.status;

    if (hasChanged) {
      debugPrint('🔍 状态检查: 班次ID或状态发生变化');
      debugPrint('   班次ID变化: ${_lastKnownShift!.shiftId} -> ${currentShift.shiftId}');
      debugPrint('   状态变化: ${_lastKnownShift!.status.name} -> ${currentShift.status.name}');
    } else {
      debugPrint('🔍 状态检查: 班次ID和状态都无变化');
    }

    return hasChanged;
  }
}

/// BOS交易轮询服务Provider
final Provider<BosTransactionPollingService>
    bosTransactionPollingServiceProvider =
    Provider<BosTransactionPollingService>(
        (ProviderRef<BosTransactionPollingService> ref) {
  return BosTransactionPollingService();
});
