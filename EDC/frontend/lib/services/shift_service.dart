import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/shift_model.dart';
import '../models/end_shift_validation.dart';
import '../constants/api_constants.dart';
import 'api/api_service.dart';
import 'shared/storage_service.dart';
import 'new_auth_service.dart';

/// 班次状态枚举（保持兼容性）
enum ShiftStatus {
  notStarted, // 未开班
  active, // 班次进行中
  ended, // 已结班
}

/// 班次信息模型（兼容旧版本）
class ShiftInfo {
  ShiftInfo({
    required this.shiftId,
    required this.operatorId,
    required this.startTime,
    required this.startingCash,
    required this.status,
    this.endTime,
    this.endingCash,
  });

  /// 从新的ShiftModel转换
  factory ShiftInfo.fromShiftModel(ShiftModel model,
      {String? operatorId, double? startingCash, double? endingCash}) {
    return ShiftInfo(
      shiftId: model.shiftNumber,
      operatorId: operatorId ?? 'UNKNOWN',
      startTime: model.startTime,
      startingCash: startingCash ?? 0.0,
      status: model.isActive
          ? ShiftStatus.active
          : (model.isClosed ? ShiftStatus.ended : ShiftStatus.notStarted),
      endTime: model.endTime,
      endingCash: endingCash,
    );
  }
  final String shiftId;
  final String operatorId;
  final DateTime startTime;
  final double startingCash;
  final ShiftStatus status;
  final DateTime? endTime;
  final double? endingCash;

  ShiftInfo copyWith({
    String? shiftId,
    String? operatorId,
    DateTime? startTime,
    double? startingCash,
    ShiftStatus? status,
    DateTime? endTime,
    double? endingCash,
  }) {
    return ShiftInfo(
      shiftId: shiftId ?? this.shiftId,
      operatorId: operatorId ?? this.operatorId,
      startTime: startTime ?? this.startTime,
      startingCash: startingCash ?? this.startingCash,
      status: status ?? this.status,
      endTime: endTime ?? this.endTime,
      endingCash: endingCash ?? this.endingCash,
    );
  }

  /// 获取班次运行时间
  String get elapsedTime {
    final DateTime now = DateTime.now();
    final Duration duration = now.difference(startTime);
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  /// 格式化开班时间
  String get formattedStartTime {
    return '${startTime.day.toString().padLeft(2, '0')}/${startTime.month.toString().padLeft(2, '0')}/${startTime.year} ${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化结班时间
  String? get formattedEndTime {
    if (endTime == null) return null;
    return '${endTime!.day.toString().padLeft(2, '0')}/${endTime!.month.toString().padLeft(2, '0')}/${endTime!.year} ${endTime!.hour.toString().padLeft(2, '0')}:${endTime!.minute.toString().padLeft(2, '0')}';
  }
}

/// 班次管理服务
class ShiftService extends ChangeNotifier {
  factory ShiftService({NewAuthService? authService}) {
    if (authService != null) {
      _instance._authService = authService;
    }
    return _instance;
  }
  ShiftService._internal();
  static final ShiftService _instance = ShiftService._internal();

  /// API服务实例 - 使用统一的ApiService
  final ApiService _apiService = ApiService();

  /// 当前站点ID（从登录用户获取）
  int? _currentStationId;

  /// 认证服务实例 - 延迟初始化
  NewAuthService? _authService;
  
  /// 设置全局认证服务实例
  static void setGlobalAuthService(NewAuthService authService) {
    _instance._authService = authService;
    debugPrint('🔍 [ShiftService] 全局认证服务已设置');
  }

  /// 当前班次信息
  ShiftInfo? _currentShift;
  ShiftModel? _currentShiftModel;

  /// 当前操作员信息
  String? _currentOperatorId;
  double? _currentStartingCash;
  double? _currentEndingCash;

  /// 是否已初始化
  bool _initialized = false;

  /// 获取初始化状态
  bool get isInitialized => _initialized;

  /// 班次状态流控制器
  final StreamController<ShiftInfo?> _shiftStatusController = 
      StreamController<ShiftInfo?>.broadcast();

  /// 初始化服务
  Future<void> initialize({
    ApiEnvironment? env,
    int? stationId,
  }) async {
    if (_initialized) return;

    try {
      // 如果没有指定环境，尝试从StorageService获取当前环境
      ApiEnvironment currentEnv = env ?? ApiEnvironment.local;
      if (env == null) {
        try {
          final StorageService storageService = StorageService();
          await storageService.init();
          currentEnv = await storageService.getApiEnvironment();
        } catch (e) {
          debugPrint('Failed to get current environment, using local: $e');
        }
      }

      // 初始化认证服务
      if (_authService == null) {
        final StorageService storageService = StorageService();
        await storageService.init();
        _authService = NewAuthService(storageService: storageService);
        
        // 🔧 等待认证服务完全初始化
        debugPrint('🔍 [ShiftService] 等待认证服务初始化...');
        int retryCount = 0;
        const int maxRetries = 10;
        const Duration retryDelay = Duration(milliseconds: 500);
        
        while (!_authService!.isInitialized && retryCount < maxRetries) {
          await Future<void>.delayed(retryDelay);
          retryCount++;
          debugPrint('🔍 [ShiftService] 认证服务初始化等待中... ($retryCount/$maxRetries)');
        }
        
        if (_authService!.isInitialized) {
          debugPrint('✅ [ShiftService] 认证服务初始化完成');
        } else {
          debugPrint('⚠️ [ShiftService] 认证服务初始化超时，继续尝试获取station_id');
        }
      }

      // 获取station_id：优先使用参数，然后从认证服务获取
      if (stationId != null) {
        _currentStationId = stationId;
        debugPrint('🔍 [ShiftService] 使用参数提供的station_id: $stationId');
      } else {
        // 从认证服务获取当前用户的默认站点ID
        debugPrint('🔍 [ShiftService] 尝试从认证服务获取station_id...');
        _currentStationId = await _getStationIdFromAuthWithRetry();
      }

      // 只有在有有效station_id时才尝试获取当前活跃班次
      if (_currentStationId != null) {
        await _loadCurrentShift();
      } else {
        debugPrint('⚠️ ShiftService: 无有效的station_id，跳过班次加载');
      }

      _initialized = true;
      debugPrint('ShiftService initialized successfully with environment: $currentEnv, station_id: $_currentStationId');
    } catch (e) {
      debugPrint('Failed to initialize ShiftService: $e');
      // 即使初始化失败，也标记为已初始化，以便后续重试
      _initialized = true;
    }
  }

  /// 从认证服务获取station_id（带重试机制）
  Future<int?> _getStationIdFromAuthWithRetry() async {
    const int maxRetries = 3;
    const Duration retryDelay = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      debugPrint('🔍 [ShiftService] 获取station_id尝试 $attempt/$maxRetries');

      final int? stationId = _getStationIdFromAuth();
      if (stationId != null) {
        debugPrint('✅ [ShiftService] 成功获取station_id: $stationId');
        return stationId;
      }

      if (attempt < maxRetries) {
        debugPrint('⚠️ [ShiftService] 获取station_id失败，${retryDelay.inSeconds}秒后重试...');
        await Future<void>.delayed(retryDelay);
      }
    }

    debugPrint('❌ [ShiftService] 经过$maxRetries次尝试仍无法获取station_id');
    return null;
  }

  /// 从认证服务获取station_id
  int? _getStationIdFromAuth() {
    try {
      debugPrint('🔍 [ShiftService] 开始获取station_id...');
      debugPrint('🔍 [ShiftService] 认证服务实例: ${_authService.runtimeType}');
      debugPrint('🔍 [ShiftService] 认证服务初始化状态: ${_authService?.isInitialized}');
      debugPrint('🔍 [ShiftService] 认证服务登录状态: ${_authService?.isLoggedIn}');
      debugPrint('🔍 [ShiftService] 当前用户是否存在: ${_authService?.currentUser != null}');
      debugPrint('🔍 [ShiftService] 认证服务状态: authService=${_authService != null}, isLoggedIn=${_authService?.isLoggedIn}');
      if (_authService?.currentUser != null) {
        debugPrint('🔍 [ShiftService] 当前用户: ${_authService!.currentUser!.username}, stations数量: ${_authService!.currentUser!.stations.length}');
      }
      
      if (_authService == null || !_authService!.isLoggedIn) {
        debugPrint('⚠️ ShiftService: 用户未登录，无法获取station_id');
        return null;
      }

      final defaultStation = _authService!.defaultStation;
      debugPrint('🔍 [ShiftService] defaultStation获取结果: ${defaultStation != null ? "非null" : "null"}');
      
      if (defaultStation != null) {
        debugPrint('🔍 [ShiftService] defaultStation详细信息:');
        debugPrint('  - stationId: ${defaultStation.stationId}');
        debugPrint('  - station.id: ${defaultStation.station.id}');
        debugPrint('  - siteName: ${defaultStation.station.siteName}');
        debugPrint('  - siteCode: ${defaultStation.station.siteCode}');
        debugPrint('  - isDefault: ${defaultStation.isDefault}');
        
        // 使用stationId字段，而不是station.id
        debugPrint('✅ ShiftService: 从认证服务获取station_id: ${defaultStation.stationId}');
        debugPrint('   站点详情: ${defaultStation.station.siteName} (${defaultStation.station.siteCode})');
        return defaultStation.stationId;
      } else {
        debugPrint('⚠️ ShiftService: 用户没有关联的站点');
        debugPrint('🔍 [ShiftService] 当前用户stations数量: ${_authService!.currentUser?.stations.length ?? 0}');
        if (_authService!.currentUser?.stations.isNotEmpty == true) {
          debugPrint('🔍 [ShiftService] 第一个station信息: ${_authService!.currentUser!.stations.first.stationId}');
        }
        return null;
      }
    } catch (e) {
      debugPrint('❌ ShiftService: 获取station_id失败: $e');
      return null;
    }
  }

  /// 加载当前班次
  Future<void> _loadCurrentShift() async {
    // 检查是否有有效的station_id
    if (_currentStationId == null) {
      debugPrint('⚠️ ShiftService: 无有效的station_id，跳过班次加载');
      return;
    }

    try {
      _currentShiftModel = await _apiService.shiftManagementApi.getCurrentShift(_currentStationId!);
      if (_currentShiftModel != null) {
        _currentShift = ShiftInfo.fromShiftModel(
          _currentShiftModel!,
          operatorId: _currentOperatorId,
          startingCash: _currentStartingCash,
          endingCash: _currentEndingCash,
        );
        debugPrint('✅ 加载到活跃班次: ${_currentShift!.shiftId} (${_currentShift!.status.name})');
      } else {
        _currentShift = null;
        debugPrint('ℹ️ 当前无活跃班次（API返回has_active_shift: false）');
      }
      notifyListeners();

      // 发送状态到流中
      if (!_shiftStatusController.isClosed) {
        _shiftStatusController.add(_currentShift);
      }
    } catch (e) {
      debugPrint('❌ 加载当前班次失败: $e');
      // API调用失败时，保持当前状态不变
      // 新接口不会返回404，所以这里只处理真正的错误
    }
  }

  /// 获取当前班次信息
  ShiftInfo? get currentShift => _currentShift;

  /// 获取当前班次模型
  ShiftModel? get currentShiftModel => _currentShiftModel;

  /// 获取班次状态流
  Stream<ShiftInfo?> get shiftStatusStream => _shiftStatusController.stream;

  /// 获取当前班次状态
  ShiftStatus get currentStatus =>
      _currentShift?.status ?? ShiftStatus.notStarted;

  /// 是否有活跃班次
  bool get hasActiveShift => _currentShift?.status == ShiftStatus.active;

  /// 是否可以开班
  bool get canStartShift => _currentShift?.status != ShiftStatus.active;

  /// 是否可以结班
  bool get canEndShift => _currentShift?.status == ShiftStatus.active;

  /// 设置当前站点ID
  void setStationId(int stationId) {
    _currentStationId = stationId;
    debugPrint('✅ ShiftService: 手动设置station_id: $stationId');
  }

  /// 获取当前站点ID
  int? get currentStationId => _currentStationId;

  /// 开始班次
  Future<bool> startShift({
    required String operatorId,
    required double startingCash,
    String? notes,
  }) async {
    if (!_initialized) {
      await initialize();
    }

    // 检查是否有有效的station_id
    if (_currentStationId == null) {
      debugPrint('❌ ShiftService: 无有效的station_id，无法开班');
      return false;
    }

    try {
      final StartShiftRequest request = StartShiftRequest(
        stationId: _currentStationId!,
        operatorId: operatorId,
        startingCash: startingCash,
        notes: notes,
      );

      _currentShiftModel = await _apiService.shiftManagementApi.startShift(request);
      _currentOperatorId = operatorId;
      _currentStartingCash = startingCash;

      _currentShift = ShiftInfo.fromShiftModel(
        _currentShiftModel!,
        operatorId: operatorId,
        startingCash: startingCash,
      );

      notifyListeners();
      
      // 发送状态到流中
      if (!_shiftStatusController.isClosed) {
        _shiftStatusController.add(_currentShift);
      }

      debugPrint('Shift started successfully: ${_currentShift!.shiftId}');
      return true;
    } catch (e) {
      debugPrint('Failed to start shift: $e');
      return false;
    }
  }

  /// 班结预检查
  Future<EndShiftValidationResponse?> validateEndShift() async {
    if (!_initialized) {
      await initialize();
    }

    if (_currentShift == null || _currentShift!.status != ShiftStatus.active) {
      return null;
    }

    // 检查是否有有效的station_id
    if (_currentStationId == null) {
      debugPrint('❌ ShiftService: 无有效的station_id，无法验证结班');
      return null;
    }

    try {
      return await _apiService.shiftManagementApi.validateEndShift(_currentStationId!);
    } catch (e) {
      debugPrint('Failed to validate end shift: $e');
      return null;
    }
  }

  /// 结束班次
  Future<bool> endShift({
    required double endingCash,
  }) async {
    if (!_initialized) {
      await initialize();
    }

    if (_currentShift == null || _currentShift!.status != ShiftStatus.active) {
      return false;
    }

    // 检查是否有有效的station_id
    if (_currentStationId == null) {
      debugPrint('❌ ShiftService: 无有效的station_id，无法结班');
      return false;
    }

    try {
      _currentShiftModel = await _apiService.shiftManagementApi.endShift(_currentStationId!);
      _currentEndingCash = endingCash;

      _currentShift = ShiftInfo.fromShiftModel(
        _currentShiftModel!,
        operatorId: _currentOperatorId,
        startingCash: _currentStartingCash,
        endingCash: endingCash,
      );

      notifyListeners();

      // 发送状态到流中
      if (!_shiftStatusController.isClosed) {
        _shiftStatusController.add(_currentShift);
      }

      debugPrint('Shift ended successfully: ${_currentShift!.shiftId}');
      return true;
    } catch (e) {
      debugPrint('Failed to end shift: $e');
      return false;
    }
  }

  /// 获取班次详情
  Future<ShiftModel?> getShiftDetails(String shiftId) async {
    if (!_initialized) {
      await initialize();
    }

    try {
      return await _apiService.shiftManagementApi.getShiftById(shiftId);
    } catch (e) {
      debugPrint('Failed to get shift details: $e');
      return null;
    }
  }

  /// 刷新当前班次状态
  Future<void> refreshCurrentShift() async {
    if (!_initialized) {
      await initialize();
    }

    // 重新获取station_id（以防用户登录状态发生变化）
    if (_currentStationId == null) {
      _currentStationId = _getStationIdFromAuth();
    }

    await _loadCurrentShift();
  }

  /// 重置班次状态（仅本地，不调用API）
  void resetShift() {
    _currentShift = null;
    _currentShiftModel = null;
    _currentOperatorId = null;
    _currentStartingCash = null;
    _currentEndingCash = null;
    notifyListeners();
    
    // 发送状态到流中
    if (!_shiftStatusController.isClosed) {
      _shiftStatusController.add(_currentShift);
    }
    
    debugPrint('Shift reset locally');
  }

  /// 获取班次状态描述
  String getStatusDescription() {
    switch (currentStatus) {
      case ShiftStatus.notStarted:
        return 'No Active Shift';
      case ShiftStatus.active:
        return 'Shift Active';
      case ShiftStatus.ended:
        return 'Shift Ended';
    }
  }

  /// 获取班次状态颜色
  String getStatusColorHex() {
    switch (currentStatus) {
      case ShiftStatus.notStarted:
        return '#666666'; // 灰色
      case ShiftStatus.active:
        return '#00A650'; // BP绿色
      case ShiftStatus.ended:
        return '#FFD903'; // BP黄色
    }
  }

  /// 切换API环境
  void switchEnvironment(ApiEnvironment env) {
    // ApiService会在全局配置更新时自动重新配置，无需手动切换
    debugPrint('Environment switch requested: ${env.name} (handled by global config)');
  }

  /// 检查网络连接
  Future<bool> checkConnection() async {
    if (!_initialized) {
      await initialize();
    }

    try {
      return await _apiService.shiftManagementApi.checkConnection();
    } catch (e) {
      debugPrint('Connection check failed: $e');
      return false;
    }
  }

  /// 获取错误信息（用于UI显示）
  String getLastError() {
    // TODO: 实现错误状态管理
    return 'No error information available';
  }

  /// 配置认证token
  void configureAuth(String? token) {
    if (_initialized) {
      _apiService.configureClient(token: token);
    }
  }

  /// 释放资源
  @override
  void dispose() {
    _shiftStatusController.close();
    super.dispose();
  }
}
