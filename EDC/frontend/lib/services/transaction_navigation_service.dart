import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

import '../constants/bp_colors.dart';
import '../controllers/fcc_status_controller.dart'; // 添加这个导入以获取providers
import '../controllers/dispenser_controller.dart'; // 添加dispenser controller导入
import '../models/dispenser_model.dart';
import '../models/fuel_transaction.dart';
import '../models/member_model.dart'; // 添加Member模型导入
import '../models/payment_transaction_data.dart';
import 'auth_providers.dart';
import '../services/api/api_service.dart';
import 'fcc_device_service.dart';
import '../services/api/fuel_transaction_api.dart';
import '../services/nozzle_authorization_cache_service.dart';
import '../services/transaction_state_sync_service.dart';
import '../services/shift_service.dart';
import '../widgets/app_loading_indicator.dart';
import '../services/member_cache_service.dart'; // 添加member cache service导入
import '../services/api/fcc_device_api.dart'; // 添加 FCCDeviceAPI 导入

/// 交易导航服务
/// 负责处理nozzle点击后的导航逻辑
class TransactionNavigationService {
  TransactionNavigationService({
    required this.context,
    required this.ref,
  })  : _apiService = ApiService(),
        _fccDeviceApi = FCCDeviceApi(), // 添加 FCCDeviceAPI 实例
        _syncService = TransactionStateSyncService(
          ref,
          fccDeviceService: ref.read(fccDeviceServiceProvider),
          pollingService: ref.read(fccPollingServiceProvider),
        );

  final BuildContext context;
  final WidgetRef ref;
  final ApiService _apiService;
  final FCCDeviceApi _fccDeviceApi; // 添加 FCCDeviceAPI 实例变量
  final TransactionStateSyncService _syncService;

  // 用于取消正在进行的请求
  final Map<String, Completer<void>> _activeRequests = {};

  /// 处理nozzle点击事件
  Future<void> handleNozzleTap(Nozzle nozzle) async {
    debugPrint(
        '🔄 Nozzle ${nozzle.name} (ID: ${nozzle.id}) 被点击，状态: ${nozzle.status.name}');


    // 优先通过API检查是否有交易数据
    final bool hasTransaction = await _checkTransactionDataViaApi(nozzle);

    if (hasTransaction) {
      debugPrint('✅ Nozzle ${nozzle.name} 通过API查询到交易数据，进入结算页面');
      await _navigateToSettlementWithSavedTransaction(nozzle);
      return;
    }


    // 优先检查是否有有效的预授权信息
    if (nozzle.preauthInfo != null && !nozzle.preauthInfo!.isExpired) {
      debugPrint('🔐 检测到有效预授权信息，显示预授权详情');
      await _showPreauthDetails(nozzle);
      return;
    }


    // 没有交易数据时，根据状态进行处理
    switch (nozzle.status) {
      case NozzleStatus.idle:
        await _showAuthorizationDialog(nozzle);
        break;

      case NozzleStatus.auth:
        await _showAuthorizedNozzleDetails(nozzle);
        break;

      case NozzleStatus.fuelling:
        await _showFuellingDetails(nozzle);
        break;

      case NozzleStatus.complete:
        // complete状态但API查询无数据时，仍尝试结算（可能有延迟）
        debugPrint('✅ Nozzle ${nozzle.name} 完成状态，尝试结算');
        await _navigateToSettlementWithSavedTransaction(nozzle);
        break;

      case NozzleStatus.offline:
        await _showNozzleError(nozzle, nozzle.errorMessage);
        break;
    }
  }

  /// 通过API检查nozzle是否有交易数据
  Future<bool> _checkTransactionDataViaApi(Nozzle nozzle) async {
    debugPrint('🔍 通过API检查 Nozzle ${nozzle.name} 的交易数据...');

    try {
      // 查询BOS API
      final FuelTransactionApi fuelTransactionApi =
          _apiService.fuelTransactionApi;
      final String pumpId = _syncService.extractPumpIdFromNozzle(nozzle);
      final Map<String, String> timeRange = _getTransactionQueryTimeRange();

      final FuelTransactionQueryParams pendingParams =
          FuelTransactionQueryParams(
        pumpId: pumpId,
        status: 'pending',
        dateFrom: timeRange['dateFrom']!,
        dateTo: timeRange['dateTo']!,
        page: 1,
        limit: 10, // 减少查询数量，只需确认是否有数据
        sortBy: 'created_at',
        sortDir: 'desc',
      );

      debugPrint('📤 BOS查询参数: ${pendingParams.toJson()}');

      final FuelTransactionResponse pendingResponse =
          await fuelTransactionApi.getFuelTransactions(pendingParams);

      // 查找FCC已完成的交易
      for (final FuelTransaction transaction in pendingResponse.items) {
        final String? fccStatus =
            transaction.metadata['fcc_status']?.toString();
        final String? fccCompleted =
            transaction.metadata['fcc_completed_at']?.toString();
        if (fccStatus == 'completed' || fccCompleted != null) {
          if (transaction.nozzleId == nozzle.id) {
            debugPrint('✅ 找到匹配的交易: ${transaction.transactionNumber}');
            return true;
          }
        }
      }

      debugPrint('ℹ️ 未找到交易数据');
      return false;
    } catch (e) {
      debugPrint('❌ API查询交易数据失败: $e');
      return false;
    }
  }

  /// 使用保存的交易数据进入结算页面（推荐方式）
  Future<void> _navigateToSettlementWithSavedTransaction(Nozzle nozzle) async {
    final bool foundTransaction =
        await _navigateToSettlementWithBosData(nozzle);

    // 如果BOS查询也没有找到交易，前往授权页面
    if (!foundTransaction) {
      debugPrint('💡 BOS查询未找到交易，前往授权页面');
      await _showAuthorizationDialog(nozzle);
    }
  }

  /// 使用BOS数据进入结算页面（备选方式）
  /// 返回 true 表示找到了交易，false 表示没有找到交易
  Future<bool> _navigateToSettlementWithBosData(Nozzle nozzle) async {
    debugPrint('🔍 正在查询BOS交易数据进行结算...');

    // 创建请求标识符
    final String requestId = 'settlement_${nozzle.id}_${DateTime.now().millisecondsSinceEpoch}';
    final Completer<void> requestCompleter = Completer<void>();
    _activeRequests[requestId] = requestCompleter;

    // 显示可取消的加载遮罩
    CancellableLoadingOverlay.show(
      context,
      message: 'Fetching transaction data...',
      timeout: const Duration(seconds: 30), // 30秒超时
      onCancel: () {
        debugPrint('🚫 用户取消了交易数据查询');
        _cancelRequest(requestId);
      },
    );

    try {
      final FuelTransactionApi fuelTransactionApi =
          _apiService.fuelTransactionApi;

      final String pumpId = _syncService.extractPumpIdFromNozzle(nozzle);
      final Map<String, String> timeRange = _getTransactionQueryTimeRange();

      // 查询pending状态中已完成的交易
      final FuelTransactionQueryParams pendingParams =
          FuelTransactionQueryParams(
        pumpId: pumpId,
        status: 'pending',
        dateFrom: timeRange['dateFrom']!,
        dateTo: timeRange['dateTo']!,
        page: 1,
        limit: 20,
        sortBy: 'created_at',
        sortDir: 'desc',
      );

      debugPrint('📤 BOS查询参数（pending+completed）: ${pendingParams.toJson()}');

      // 检查请求是否被取消
      if (_activeRequests[requestId]?.isCompleted == true) {
        debugPrint('🚫 请求已被取消，停止处理');
        return false;
      }

      final FuelTransactionResponse pendingResponse =
          await fuelTransactionApi.getFuelTransactions(pendingParams);

      // 再次检查请求是否被取消
      if (_activeRequests[requestId]?.isCompleted == true) {
        debugPrint('🚫 请求已被取消，停止处理');
        return false;
      }

      // 查找所有FCC已完成的交易
      final List<FuelTransaction> completedTransactions = <FuelTransaction>[];

      for (final FuelTransaction transaction in pendingResponse.items) {
        final String? fccStatus =
            transaction.metadata['fcc_status']?.toString();
        final String? fccCompleted =
            transaction.metadata['fcc_completed_at']?.toString();

        if (fccStatus == 'completed' || fccCompleted != null) {
          if (transaction.nozzleId == nozzle.id) {
            completedTransactions.add(transaction);
          }
        }
      }

      // 安全隐藏加载遮罩
      CancellableLoadingOverlay.hide();
      _activeRequests.remove(requestId);

      if (completedTransactions.isEmpty) {
        debugPrint('⚠️ 未找到待支付交易 for nozzle ${nozzle.name}');
        return false;
      }

      debugPrint('✅ 找到 ${completedTransactions.length} 笔待支付交易');

      // 直接结算第一笔交易（不再显示选择列表）
      final FuelTransaction transaction = completedTransactions.first;
      await _navigateToSingleTransaction(nozzle, transaction);
      return true;

    } catch (e) {
      debugPrint('❌ BOS查询失败: $e');
      
      // 安全隐藏加载遮罩
      CancellableLoadingOverlay.hide();
      _activeRequests.remove(requestId);

      // 检查是否是取消导致的错误
      if (e.toString().contains('cancelled') || e.toString().contains('aborted')) {
        debugPrint('🚫 请求被取消');
        return false;
      }

      // 显示错误信息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to fetch transaction data: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      return false;
    }
  }

  /// 取消指定的请求
  void _cancelRequest(String requestId) {
    final Completer<void>? completer = _activeRequests[requestId];
    if (completer != null && !completer.isCompleted) {
      completer.complete();
      _activeRequests.remove(requestId);
      debugPrint('✅ 请求 $requestId 已取消');
    }
  }

  /// 取消所有活跃的请求
  void cancelAllRequests() {
    for (final String requestId in _activeRequests.keys.toList()) {
      _cancelRequest(requestId);
    }
    CancellableLoadingOverlay.hide();
    debugPrint('✅ 已取消所有活跃请求');
  }

  /// 导航到单笔交易的支付页面
  Future<void> _navigateToSingleTransaction(Nozzle nozzle, FuelTransaction transaction) async {
    debugPrint('📱 单笔交易结算: ${transaction.transactionNumber}');

    // 获取员工信息 - 使用新认证服务
    final String? currentUserId = ref.read(currentUserIdProvider);
    final String? currentUsername = ref.read(currentUsernameProvider);

    final String employeeId = currentUserId ?? 'unknown';
    final String employeeNo = currentUsername ?? 'unknown';

    debugPrint('🔍 新认证服务员工信息:');
    debugPrint('  - employeeId: $employeeId (${employeeId.runtimeType})');
    debugPrint('  - employeeNo: $employeeNo (${employeeNo.runtimeType})');

    // 获取会员信息 - 优先从缓存，如果有的话
    Member? memberInfo;
    final bool hasCachedMember = memberCacheService.hasCachedMember;
    if (hasCachedMember) {
      memberInfo = memberCacheService.cachedMember;
      debugPrint('💾 找到缓存的会员信息: ${memberInfo?.name} (车辆类型: ${memberInfo?.metadata['vehicle']})');
    } else {
      debugPrint('⚠️ 没有缓存的会员信息');
    }

    // 使用新的工厂方法创建PaymentTransactionData，并传递会员信息
    debugPrint('🔄 开始创建PaymentTransactionData，交易ID: ${transaction.id}');
    try {
      final PaymentTransactionData paymentData =
          PaymentTransactionData.fromFuelTransaction(
        transaction: transaction,
        employeeId: employeeId,
        employeeNo: employeeNo,
        memberInfo: memberInfo, // 传递会员信息以确保车辆类型被包含
      );
      debugPrint('✅ PaymentTransactionData创建成功');
      
      // 输出车辆类型信息用于调试
      if (paymentData.metadata['vehicle_type'] != null) {
        debugPrint('🚗 PaymentTransactionData包含车辆类型: ${paymentData.metadata['vehicle_type']}');
      } else {
        debugPrint('⚠️ PaymentTransactionData不包含车辆类型信息');
      }
    } catch (e) {
      debugPrint('❌ PaymentTransactionData创建失败: $e');
      debugPrint(
          '❌ 交易数据详情: id=${transaction.id}, transactionNumber=${transaction.transactionNumber}');
      debugPrint('❌ 员工数据: employeeId=$employeeId, employeeNo=$employeeNo');
      rethrow;
    }

    final PaymentTransactionData paymentData =
        PaymentTransactionData.fromFuelTransaction(
      transaction: transaction,
      employeeId: employeeId,
      employeeNo: employeeNo,
      memberInfo: memberInfo, // 确保传递会员信息
    );

    if (context.mounted) {
      await context.push('/payment', extra: paymentData);
    }
  }

  /// 导航到交易选择列表页面
  Future<void> _navigateToTransactionSelectionList(
      Nozzle nozzle, List<FuelTransaction> transactions) async {
    debugPrint('📋 显示交易选择列表页面');

    final Map<String, dynamic> pageData =
        _buildTransactionListData(nozzle, transactions);

    if (context.mounted) {
      await context.push('/fuel/nozzle-transactions', extra: pageData);
    }
  }

  /// 构建单笔交易数据 - 使用扁平结构与燃油交易详情页面保持一致
  Map<String, dynamic> _buildTransactionData(
      Nozzle nozzle, FuelTransaction transaction) {
    return <String, dynamic>{
      // ✅ 扁平结构，与fuel_transaction_detail_page.dart保持一致
      'id': transaction.id, // BOS数字ID (用于API)
      'transactionNumber': transaction.transactionNumber, // FCC编号 (显示用)
      'stationId': transaction.stationId, // Station ID
      'status': transaction.status,
      'payment': <String, String>{
        'totalAmount': transaction.amount.toString(), // 保持字符串格式以兼容现有代码
        'unitPrice': transaction.unitPrice.toString(),
        'volume': transaction.volume.toString(),
      },
      // 燃油信息直接放在顶级
      'pumpId': transaction.pumpId,
      'nozzleId': transaction.nozzleId,
      'fuelType': transaction.fuelType,
      'fuelGrade': transaction.fuelGrade,

      // 额外信息（向后兼容）
      'nozzle': <String, Object>{
        'id': nozzle.id,
        'name': nozzle.name,
        'pumpGroupId': nozzle.pumpGroupId,
        'fuelType': nozzle.fuelType,
        'fuelGrade': nozzle.fuelGrade,
        'price': nozzle.price,
      },
      'transaction': <String, Object?>{
        'id': transaction.id,
        'transactionNumber': transaction.transactionNumber,
        'bosId': transaction.id,
        'amount': transaction.amount,
        'volume': transaction.volume,
        'unitPrice': transaction.unitPrice,
        'fuelType': transaction.fuelType,
        'fuelGrade': transaction.fuelGrade,
        'pumpId': transaction.pumpId,
        'nozzleId': transaction.nozzleId,
        'startTime': transaction.createdAt.toIso8601String(),
        'endTime': transaction.metadata['fcc_completed_at']?.toString() ??
            DateTime.now().toIso8601String(),
        'status': transaction.status,
        'staffId': transaction.employeeId?.toString() ?? 'BOS_AUTO',
        'fccTransactionId': transaction.fccTransactionId,
        'memberCardId': transaction.memberCardId,
        'memberId': transaction.memberId,
      },
      'settlement': <String, Object>{
        'needsPayment': true,
        'totalAmount': transaction.amount,
        'currency': 'IDR',
        'transactionId': transaction.id,
      }
    };
  }

  /// 构建交易列表数据
  Map<String, dynamic> _buildTransactionListData(
      Nozzle nozzle, List<FuelTransaction> transactions) {
    return <String, dynamic>{
      'nozzle': <String, Object>{
        'id': nozzle.id,
        'name': nozzle.name,
        'pumpGroupId': nozzle.pumpGroupId,
        'fuelType': nozzle.fuelType,
        'fuelGrade': nozzle.fuelGrade,
        'price': nozzle.price,
      },
      'transactions': transactions
          .map((FuelTransaction tx) => <String, Object?>{
                'id': tx.id, // ✅ BOS数字ID (用于API)
                'transactionNumber': tx.transactionNumber, // ✅ FCC编号 (显示用)
                'bosId': tx.id, // 保持向后兼容
                'amount': tx.amount,
                'volume': tx.volume,
                'unitPrice': tx.unitPrice,
                'fuelType': tx.fuelType,
                'fuelGrade': tx.fuelGrade,
                'pumpId': tx.pumpId,
                'nozzleId': tx.nozzleId,
                'startTime': tx.createdAt.toIso8601String(),
                'endTime': tx.metadata['fcc_completed_at']?.toString() ??
                    DateTime.now().toIso8601String(),
                'status': tx.status,
                'staffId': tx.employeeId?.toString() ?? 'BOS_AUTO',
                'fccTransactionId': tx.fccTransactionId,
                'memberCardId': tx.memberCardId,
                'memberId': tx.memberId,
                'createdAt': tx.createdAt.toIso8601String(),
                'metadata': tx.metadata,
              })
          .toList(),
    };
  }

  /// 显示授权对话框
  Future<void> _showAuthorizationDialog(Nozzle nozzle) async {
    // 检查班次状态
    final ShiftService shiftService = ShiftService();
    if (!shiftService.hasActiveShift) {
      _showErrorMessage(
          'Cannot authorize nozzle: No active shift. Please start a shift first.');
      debugPrint('❌ 授权被拒绝: 没有活跃班次');
      return;
    }

    debugPrint('✅ 班次状态检查通过，允许授权');

    final AuthorizationRequest? authRequest = await context
        .push<AuthorizationRequest>('/nozzle-authorization',
            extra: <String, Nozzle>{
          'selectedNozzle': nozzle,
        });

    // ✅ 修复：移除重复的授权调用，因为授权页面已经处理了授权
    // 如果返回了authRequest，说明授权已经成功完成
    if (authRequest != null && context.mounted) {
      debugPrint('✅ 授权页面返回成功，授权已完成');
      // 不需要再次调用授权接口，也不需要显示成功消息
      // 因为授权页面已经处理了这些
    }
  }

  /// 显示预授权详情
  Future<void> _showPreauthDetails(Nozzle nozzle) async {
    await showDialog<void>(
      context: context,
      builder: (BuildContext context) => _PreauthDetailsDialog(nozzle: nozzle),
    );
  }

  /// 显示已授权nozzle的详细信息
  Future<void> _showAuthorizedNozzleDetails(Nozzle nozzle) async {
    await showDialog<void>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF00A650).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.check_circle,
                color: Color(0xFF00A650),
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    nozzle.name,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const Text(
                    'Authorized',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.normal,
                      color: Color(0xFF00A650),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildInfoRow('Nozzle ID', nozzle.id),
              _buildInfoRow('Status', nozzle.status.name.toUpperCase()),
              _buildInfoRow('Pump Group', nozzle.pumpGroupId.toString()),
              _buildInfoRow('Fuel Type', nozzle.fuelType),
              _buildInfoRow('Fuel Grade', nozzle.fuelGrade),
              _buildInfoRow(
                  'Unit Price', 'IDR ${nozzle.price.toStringAsFixed(0)}'),
              if (nozzle.currentAuth != null) ...<Widget>[
                _buildInfoRow('Authorized At',
                    _formatDateTime(nozzle.currentAuth!.requestTime)),
                _buildInfoRow(
                    'Auth Mode', nozzle.currentAuth!.mode.name.toUpperCase()),
                if (nozzle.currentAuth!.value != null)
                  _buildInfoRow(
                      'Auth Value',
                      nozzle.currentAuth!.mode == AuthMode.amount
                          ? 'IDR ${nozzle.currentAuth!.value!.toStringAsFixed(0)}'
                          : '${nozzle.currentAuth!.value!.toStringAsFixed(2)} L'),
                _buildInfoRow('Staff ID', _getCorrectStaffId(nozzle)),
              ],
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF666666),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            ),
            child: const Text(
              'Close',
              style: TextStyle(fontSize: 16),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () => _handleCancelAuthorization(nozzle),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            ),
            icon: const Icon(Icons.cancel, size: 18),
            label: const Text(
              'Cancel Auth',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示加注中nozzle的详细信息
  Future<void> _showFuellingDetails(Nozzle nozzle) async {
    // 尝试获取当前交易信息
    final Map<String, dynamic>? currentTransaction =
        await _getCurrentTransactionForNozzle(nozzle);

    await showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Row(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFFFFD903).withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.local_gas_station,
                color: Color(0xFFFFD903),
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    nozzle.name,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const Text(
                    'Fuelling in Progress',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.normal,
                      color: Color(0xFFFFD903),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildInfoRow('Nozzle ID', nozzle.id),
              _buildInfoRow('Status', nozzle.status.name.toUpperCase()),
              _buildInfoRow('Pump Group', nozzle.pumpGroupId.toString()),
              _buildInfoRow('Fuel Type', nozzle.fuelType),
              _buildInfoRow('Fuel Grade', nozzle.fuelGrade),
              _buildInfoRow(
                  'Unit Price', 'IDR ${nozzle.price.toStringAsFixed(0)}'),

              // 如果有当前交易信息，显示交易详情
              if (currentTransaction != null) ...<Widget>[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                const Text(
                  'Current Transaction',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                    'Transaction ID',
                    (currentTransaction['transactionNumber'] ?? 'N/A')
                        .toString()),
                _buildInfoRow('Current Volume',
                    '${(currentTransaction['volume'] as num?)?.toStringAsFixed(2) ?? '0.00'} L'),
                _buildInfoRow('Current Amount',
                    'IDR ${(currentTransaction['amount'] as num?)?.toStringAsFixed(0) ?? '0'}'),
                if (currentTransaction['startTime'] != null)
                  _buildInfoRow(
                      'Started At',
                      _formatDateTime(DateTime.parse(
                          currentTransaction['startTime'].toString()))),
              ],

              if (nozzle.currentAuth != null)
                _buildInfoRow('Authorized At',
                    _formatDateTime(nozzle.currentAuth!.requestTime)),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF666666),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            ),
            child: const Text(
              'Close',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示nozzle错误信息
  Future<void> _showNozzleError(Nozzle nozzle, [String? customMessage]) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Row(
          children: <Widget>[
            const Icon(Icons.error, color: Colors.red, size: 24),
            const SizedBox(width: 8),
            Text('${nozzle.name} - Error'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(customMessage ??
                nozzle.errorMessage ??
                'Unknown error occurred'),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// 获取交易查询时间范围
  Map<String, String> _getTransactionQueryTimeRange() {
    final DateTime now = DateTime.now();
    final ShiftService shiftService = ShiftService();
    final ShiftInfo? currentShift = shiftService.currentShift;

    String dateFrom;
    String dateTo;
    String source;

    if (currentShift != null && currentShift.status == ShiftStatus.active) {
      dateFrom = _formatDateForQuery(currentShift.startTime);
      dateTo = _formatDateForQuery(now);
      source = '班次时间 (${currentShift.shiftId})';
    } else {
      final DateTime sixHoursAgo = now.subtract(const Duration(hours: 6));
      dateFrom = _formatDateForQuery(sixHoursAgo);
      dateTo = _formatDateForQuery(now);
      source = '最近6小时 (无活跃班次)';
    }

    return <String, String>{
      'dateFrom': dateFrom,
      'dateTo': dateTo,
      'source': source,
    };
  }

  /// 格式化日期用于查询
  String _formatDateForQuery(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }

  /// 创建加载覆盖层
  OverlayEntry _createLoadingOverlay(String message) {
    return OverlayEntry(
      builder: (BuildContext context) => Material(
        color: Colors.black54,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  message,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 显示成功消息
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 显示错误消息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 构建信息行小部件
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
                color: Color(0xFF666666),
              ),
            ),
          ),
          const Text(
            ': ',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF666666),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
                color: Color(0xFF333333),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期时间显示
  String _formatDateTime(DateTime dateTime) {
    final DateTime now = DateTime.now();
    final Duration diff = now.difference(dateTime);

    if (diff.inDays > 0) {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}h ${diff.inMinutes % 60}m ago';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// 获取指定nozzle的当前交易信息
  Future<Map<String, dynamic>?> _getCurrentTransactionForNozzle(
      Nozzle nozzle) async {
    try {
      final FuelTransactionApi fuelTransactionApi =
          _apiService.fuelTransactionApi;
      final String pumpId = _syncService.extractPumpIdFromNozzle(nozzle);
      final String nozzleId = _syncService.extractNozzleIdFromNozzle(nozzle);
      final Map<String, String> timeRange = _getTransactionQueryTimeRange();

      // 查询进行中的交易
      final FuelTransactionQueryParams activeParams =
          FuelTransactionQueryParams(
        pumpId: pumpId,
        status: 'active', // 查询活跃状态的交易
        dateFrom: timeRange['dateFrom']!,
        dateTo: timeRange['dateTo']!,
        page: 1,
        limit: 5,
        sortBy: 'created_at',
        sortDir: 'desc',
      );

      final FuelTransactionResponse activeResponse =
          await fuelTransactionApi.getFuelTransactions(activeParams);

      // 查找匹配的交易
      for (final FuelTransaction transaction in activeResponse.items) {
        if (transaction.nozzleId == nozzle.id) {
          return <String, dynamic>{
            'transactionNumber': transaction.transactionNumber,
            'volume': transaction.volume,
            'amount': transaction.amount,
            'startTime': transaction.createdAt.toIso8601String(),
            'fuelType': transaction.fuelType,
            'fuelGrade': transaction.fuelGrade,
          };
        }
      }
  
      return null;
    } catch (e) {
      debugPrint('❌ 获取当前交易信息失败: $e');
      return null;
    }
  }

  /// 获取正确的Staff ID - 优先使用缓存的授权信息
  String _getCorrectStaffId(Nozzle nozzle) {
    // 首先尝试从授权缓存中获取
    final AuthorizationRequest? cachedAuth = 
        nozzleAuthorizationCache.getAuthorization(nozzle.id);
    
    if (cachedAuth != null) {
      debugPrint('✅ 使用缓存的Staff ID: ${cachedAuth.staffId} for nozzle ${nozzle.id}');
      return cachedAuth.staffId;
    }
    
    // 如果缓存中没有，使用nozzle自带的授权信息
    if (nozzle.currentAuth != null) {
      debugPrint('⚠️ 使用FCC返回的Staff ID: ${nozzle.currentAuth!.staffId} for nozzle ${nozzle.id}');
      return nozzle.currentAuth!.staffId;
    }
    
    // 兜底返回
    debugPrint('❌ 未找到Staff ID信息 for nozzle ${nozzle.id}');
    return 'UNKNOWN';
  }

  /// 取消授权nozzle
  Future<void> _handleCancelAuthorization(Nozzle nozzle) async {
    debugPrint('🚫 用户点击取消授权按钮，尝试取消 nozzle ${nozzle.id} 的授权');

    // 显示确认对话框
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Cancel Authorization'),
        content: Text('Are you sure you want to cancel authorization for ${nozzle.name}?'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );

    if (confirmed != true) {
      debugPrint('🚫 用户取消了取消授权操作');
      return;
    }

    try {
      debugPrint('🔧 开始调用 Preauth Cancel API 取消授权');
      
      // 构建 device_id，格式：device_com7_pump + pumpGroupId（补零到2位）
      final String pumpId = nozzle.pumpGroupId.toString().padLeft(2, '0');
      final String deviceId = 'device_com7_pump$pumpId';
      
      debugPrint('📤 Device ID: $deviceId, Nozzle ID: ${nozzle.id}');
      
      // 使用 FCCDeviceAPI 调用 Cancel Preauth API
      final Map<String, dynamic> result = await _fccDeviceApi.cancelPreauth(
        deviceId,
        nozzle.id,
        reason: 'User cancelled authorization',
      );

      debugPrint('✅ Cancel Preauth API调用成功: nozzle ${nozzle.id}');
      debugPrint('📥 API响应: ${json.encode(result)}');
      
      // 清除授权缓存
      nozzleAuthorizationCache.clearAuthorization(nozzle.id);
      
      _showSuccessMessage('Authorization cancelled successfully.');
      Navigator.of(context).pop(); // 关闭当前授权对话框
      
      // 手动刷新 nozzle 状态
      final DispenserController controller = ref.read(dispenserControllerProvider.notifier);
      await controller.refreshNozzleStatus();
      
    } catch (e) {
      debugPrint('❌ Cancel Preauth API调用异常: $e');
      _showErrorMessage('Failed to cancel authorization: ${e.toString()}');
    }
  }
}

/// 预授权详情对话框
class _PreauthDetailsDialog extends StatefulWidget {
  const _PreauthDetailsDialog({required this.nozzle});

  final Nozzle nozzle;

  @override
  State<_PreauthDetailsDialog> createState() => _PreauthDetailsDialogState();
}

class _PreauthDetailsDialogState extends State<_PreauthDetailsDialog> {
  Timer? _countdownTimer;
  int _remainingSeconds = 0;
  bool _isExpired = false;

  @override
  void initState() {
    super.initState();
    _updateCountdown();
    _startCountdownTimer();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _updateCountdown() {
    final PreauthInfo? preauth = widget.nozzle.preauthInfo;
    if (preauth != null) {
      final int seconds = preauth.expiresAt.difference(DateTime.now()).inSeconds;
      setState(() {
        _remainingSeconds = seconds;
        _isExpired = seconds <= 0;
      });
    }
  }

  void _startCountdownTimer() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      _updateCountdown();
      if (_isExpired) {
        timer.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final PreauthInfo? preauth = widget.nozzle.preauthInfo;
    if (preauth == null) {
      return AlertDialog(
        title: const Text('No Authorization'),
        content: const Text('No preauth information available.'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      );
    }

    return AlertDialog(
      title: Row(
        children: <Widget>[
          Icon(
            Icons.local_gas_station,
            color: _isExpired ? BPColors.error : BPColors.primary,
            size: 24,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Authorization Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: _isExpired ? BPColors.error : BPColors.primary,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Nozzle 信息
          _buildInfoRow('Nozzle', widget.nozzle.name),
          _buildInfoRow('Fuel Type', widget.nozzle.fuelType),

          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),

          // 预授权信息
          _buildInfoRow('Authorization Type', _getAuthTypeDisplay(preauth.type)),
          _buildInfoRow('Preset Value', preauth.displayValue),
          _buildInfoRow('Created At', _formatDateTime(preauth.createdAt)),
          _buildInfoRow('Expires At', _formatDateTime(preauth.expiresAt)),

          const SizedBox(height: 12),

          // 状态显示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _isExpired
                  ? BPColors.error.withValues(alpha: 0.1)
                  : BPColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isExpired ? BPColors.error : BPColors.primary,
                width: 1,
              ),
            ),
            child: Column(
              children: <Widget>[
                Text(
                  _isExpired ? 'EXPIRED' : 'ACTIVE',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _isExpired ? BPColors.error : BPColors.primary,
                  ),
                ),
                if (!_isExpired) ...<Widget>[
                  const SizedBox(height: 4),
                  Text(
                    'Expires in ${_remainingSeconds}s',
                    style: TextStyle(
                      fontSize: 14,
                      color: _remainingSeconds < 10
                          ? BPColors.error
                          : BPColors.primary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
      actions: <Widget>[
        if (!_isExpired) ...<Widget>[
          TextButton(
            onPressed: () => _cancelPreauth(context),
            style: TextButton.styleFrom(
              foregroundColor: BPColors.error,
            ),
            child: const Text('Cancel Auth'),
          ),
        ],
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getAuthTypeDisplay(String type) {
    switch (type) {
      case 'preset_amount':
        return 'Preset Amount';
      case 'preset_volume':
        return 'Preset Volume';
      case 'full_tank':
        return 'Full Tank';
      default:
        return type;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
           '${dateTime.month.toString().padLeft(2, '0')}/'
           '${dateTime.year} '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// 取消预授权
  Future<void> _cancelPreauth(BuildContext context) async {
    try {
      // 显示确认对话框
      final bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: const Text('Cancel Authorization'),
          content: Text(
            'Are you sure you want to cancel the authorization for ${widget.nozzle.name}?',
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: BPColors.error,
              ),
              child: const Text('Yes, Cancel'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // 调用 FCC 服务取消预授权
        final FCCDeviceService fccService = FCCDeviceService();
        await fccService.cancelNozzlePreauth(widget.nozzle.id);

        if (context.mounted) {
          // 显示成功消息
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Authorization cancelled successfully'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 2),
            ),
          );

          // 关闭详情对话框
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      debugPrint('❌ Cancel preauth failed: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel authorization: $e'),
            backgroundColor: BPColors.error,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
