# SUNMI PAY SDK V2 Development Document

**Shanghai Sunmi Tech Co.,Ltd**

## Table of Contents

1. [Revision History](#1-revision-history)
2. [Abstract](#2-abstract)
   - 2.1 [Introduction](#21-introduction)
   - 2.2 [SDK Integration](#22-sdk-integration)
3. [API](#3-api)
   - 3.1 [SunmiPayKernel SDK Operation Object](#31-sunmipaykernel-sdk-operation-object)
   - 3.2 [Public member variable](#32-public-member-variable)
   - 3.3 [Card operation module](#33-card-operation-module)
4. [Error Code Definition](#4-error-code-definition)
5. [Access permission](#5-access-permission)
   - 5.1 [Permission location](#51-permission-location)
   - 5.2 [Permission definition](#52-permission-definition)
6. [Appendix](#6-appendix)
   - 6.1 [Aidl constants class](#61-aidl-constants-class)

## 1. Revision History

| Version | Release date | Changes | The appropriate SDK version | Author |
|---------|-------------|---------|----------------------------|--------|
| 3.2.45 | 2019/06/05 | Initialize version | SunmiPaySDKService v1.0.07 | Cui weihai |
| 3.2.46 | 2020/09/08 | Add ETC module(only used in China) | SunmiPaySDKService v1.0.19 | Cui weihai |
| 3.2.47 | 2021/08/21 | Add ETC trade interfaces, add M112 ID card module(only used in China) | SunmiPaySDKService v1.0.30 | Cui weihai |
| 3.2.48 | 2022/02/15 | 1.Update interface transmitApdu()<br>2.Add interface transmitApduEx()、checkCardEx()、transmitApduExx() | SunmiPayHardwareService_v1.0.43 | Cui weihai |

## 2. Abstract

### 2.1 Introduction

SunmiPaySDK is a set of interface which is based on firmware encapsulation and is close to Java developer to call hardware. Through this SDK, developer can quickly call the corresponding firmware interface of Sunmi financial instruments and realize the development of its own business logic. SDK mainly includes: terminal information basic module, card operation module, password keyboard module, EMV module, security module.

This document is the SunmiPaySDK V2 interface documentation. Compared to the V1 interface, the V2 interface makes it easier to develop this understanding and call.

### 2.2 SDK Integration

#### 2.2.1 Quick integration

This document is an integrated development guide for the SUNMI's POS. To guide the use method of SDK, It requires the reader to have already familiar with the basic use method of IDE, have certain Android programming basis, familiar with financial related specifications, processes and related concepts (key, pinblock, pan,emv, MAC, etc.). At present, SDK only supports the development of Sunmi P1N, P1-4G, P2 Pro, P2 Lite. Please read this document carefully before using SDK. Check the following prerequisites before use.

1. Model number is P1N or P1-4G or P2 Pro or P2 Lite.(Setting-About device-Model number)

2. Go to Setting--->APP--->top right cornor--->show system

SunmiPayHardwareService_v3.3.0_release.apk or the latest version.

3. Android studio quick integrate，Put the PayLib-release-xxx.aar package under the libs folder.

```gradle
repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    ...
    compile(name: 'PayLib-release-xxx', ext: 'aar')
}
```

Build the Project again when finished.

#### 2.2.2 Android version and IDE version supported by the SDK

SDK only support API-19(Android 4.4) or the latest version.

SDK only support Android studio、Intellij.

## 3. API

### 3.1 SunmiPayKernel SDK Operation Object

#### 3.1.1 Get an instance of SunmiPayKernel

| Prototype | `SunmiPayKernel getInstance()` |
|-----------|--------------------------------|
| Feature | Get kernel instance object of SDK |
| Parameter | [in] None |
| Return | SunmiPayKernel |
| Comment | None |

#### 3.1.2 Connect to PaySDK

| Prototype | `initPaySDK (Context context,ConnectCallback connectCallback)` |
|-----------|---------------------------------------------------------------|
| Feature | Initialize Pay SDK. |
| Parameter | context[in] Context<br>connectCallback[in] Callback of connection status. Refer to [ConnectCallback](#314-connectcallback) |
| Return | None |
| Comment | It is recommended to call this method in your app's Application class, if failure to call this method, any invoking of SDK API method may throw NullPointerException. |

#### 3.1.3 Disconnect to PaySDK

| Prototype | `destroyPaySDK ()` |
|-----------|-------------------|
| Feature | Destroy the Pay SDK. |
| Parameter | [in] None |
| Return | None |
| Comment | This method will disconnect your app to Pay SDK. After calling this method, any function module in SunmiPayKernel will be set to **null**, and any invoke of SDK API method will throw NullPointerException. To prevent a memory leak, call this methos in Activity's onDestroy() is best practice. |

#### 3.1.4 ConnectCallback

##### ******* Connect to PaySDK callback

| Prototype | `onConnectPaySDK ()` |
|-----------|---------------------|
| Feature | Callback method, indicate connect to PaySDK success |
| Parameter | [in] None |
| Return | None |
| Comment | Any SDK API method calls must be made after receiving this callback. After initializing the SDK, developers should pay attention to this method. When this method called, it indicates the SDK was initialized successfully, and the SDK related API can be invoked rightly. |

##### ******* Disconnect to PaySDK callback

| Prototype | `onDisconnectPaySDK ()` |
|-----------|------------------------|
| Feature | Disconnect the PaySDK |
| Parameter | [in] None |
| Return | None |
| Comment | When this method called, it indicates your app has disconnected to Pay SDK, any invoke of SDK API method may produce Exception. To use SDK API, please call method InitPaySDK() firstly. |

### 3.2 Public member variable

After connected to PaySDK, in SunmiPayKernel, there are some function modules can be used to interactive with PaySDK, as following table showing:

| Variable name | Description | Comment |
|---------------|-------------|---------|
| mReadCardOptV2 | Read card module | Read card |
| mETCOptV2 | ETC operation module | Provide ETC related interfaces |
| mRFIDOptV2 | RFID operation moudle | Provide RFID related interfaces |

### 3.3 Card operation module

#### 3.3.1 ReadCardOpt

##### ******* Check card

| Prototype | `void checkCard(int cardType, CheckCardCallbackV2 callback, int timeout)` |
|-----------|-------------------------------------------------------------------------|
| Feature | For card checking, magnetic stripe card, IC card and NFC card are supported. After checking, the card type will be put into CheckCardCallbackV2. |
| Parameter | cardType[in] Card type, only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue()<br><br>callback[in] Check card callback. Refer to [CheckCardCallbackV2](#332-checkcardcallbackv2)<br><br>timeout[in] Timeout (unit: second). Effective time range: 1-120s |
| Return | None |
| Comment | No distinction between bank card and non bank card. |

##### ******* Cancel check card

Interface instructions: Artificial return must call cancelCheckCard(), terminate the underlying blocking thread, otherwise the next execution function will fail (for example, click the physical return key, click the interface navigation bar return key to call the function)

| Prototype | `void cancelCheckCard();` |
|-----------|-------------------------|
| Feature | Cancel check card |
| Parameter | [in] None |
| Return | None |
| Comment | The function needs to be called when the CheckCard is not returned (CheckCardCallbackV2 Success or Failed Interface is not callback) and leaves the interface. |

##### 3.3.1.3 APDU command exchange

| Prototype | `int apduCommand(int cardType, ApduSendV2 send, ApduRecvV2 recv)` |
|-----------|------------------------------------------------------------------|
| Feature | IC card operation function. This function support the general interface protocol for IC card.(T=0 and T=1)<br><br>This support the general interface protocol for contactless card. (T=CL) |
| Parameter | cardType[in] Card type currently in operation, only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue()<br><br>send[in] class ApduSendV2 {<br>&nbsp;&nbsp;byte[] command; // Command[] = {CLA，INS，P1，P2}.<br>&nbsp;&nbsp;short lc; // Length of dataIn (0~256)<br>&nbsp;&nbsp;byte[] dataIn; // Data of the IC card which you want to send, max length is 256 bytes.<br>&nbsp;&nbsp;short le; // Expected length of the returned data (0~256)<br>}<br><br>recv[out] class ApduRecvV2 {<br>&nbsp;&nbsp;short outLen; //Actual data length returned from IC card (0~256)<br>&nbsp;&nbsp;byte[] outData; // Data returned from IC card. max length is 256 bytes.<br>&nbsp;&nbsp;byte swa; // Status byte 1<br>&nbsp;&nbsp;byte swb; // Status byte 2<br>} |
| Return | 0: Success<br><br>Other value: Fail |
| Comment | For variable format of ApduSendV2, refer to the doc **apdu format and implement in sunmi way** |

##### 3.3.1.4 APDU command exchange (not recommend)

| Prototype | `int smartCardExchange(int cardType, byte[] apduSend, byte[] apduRecv)` |
|-----------|------------------------------------------------------------------------|
| Feature | IC card operation function. This function support the general interface protocol for IC card.(T=0 and T=1)<br><br>This support the general interface protocol for contactless card. (T=CL) |
| Parameter | cardType[in] Card type currently in operation, only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue()<br><br>apduSend[in] The APDU command which to send, format is:<br>Command(4B)+LC(1B, value is **len**)+inData(**len** B)+LE(1B)<br><br>LC is unsigned, range is 0~255<br><br>apduRecv[out] The out data buffer,<br>apduRecv.length>=260，receive data format is：<br><br>outLen(2B, Big endian,value is **len**)+outData(**len** B)+SWA(1B)+SWB(1B) |
| Return | 0: Success<br><br>Other value: Fail |
| Comment | None |

##### 3.3.1.5 Transmit APDU command to card

| Prototype | `int transmitApdu(int cardType, byte[] sendBuff, byte[] recvBuff)` |
|-----------|-------------------------------------------------------------------|
| Feature | Transmit the **sendBuff** to card directly.<br><br>This function support the general interface protocol for IC card.(T=0 and T=1), and also support the general interface protocol for contactless card. (T=CL) |
| Parameter | cardType[in] Card type currently in operation, only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue()<br><br>sendBuff[in] Data which want to transmit to card. Max length less than 2048B<br><br>recvBuff[out] Data received from card. The max receive data length less than 2048B,<br><br>recvBuff.lenght>=2048 |
| Return | >=0: The valid data length in recvBuff<br><br><0: Error code |
| Comment | None |

##### 3.3.1.6 Close contact and contactless module

| Prototype | `int cardOff(int cardType)` |
|-----------|----------------------------|
| Feature | Power off contact IC card or turn off carrier of contactless card. |
| Parameter | cardType[in] Card type, only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue() |
| Return | 0: Success<br><br>Other value: Fail |
| Comment | None |

##### 3.3.1.7 Check if card exist on slot

| Prototype | `int getCardExistStatus(int cardType)` |
|-----------|---------------------------------------|
| Feature | Check if card exist on slot |
| Parameter | cardType[in] Card type (non-composite type), only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue() |
| Return | AidlConstantsV2.CardExistStatus<br>CARD_ABSENT = 0x01; // Card don't exist.<br>CARD_PRESENT = 0x02; // Card exist. |
| Comment |  |

##### 3.3.1.8 Transmit APDU command to card(extended method)

| Prototype | `int transmitApduEx(int cardType, byte[] sendBuff, out byte[] recvBuff)` |
|-----------|-------------------------------------------------------------------------|
| Feature | Transmit the **sendBuff** to card directly.<br><br>This function support the general interface protocol for IC card.(T=0 and T=1), and also support the general interface protocol for contactless card. (T=CL) |
| Parameter | cardType[in] Card type currently in operation, only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue()<br><br>sendBuff[in] Data which want to transmit to card. Max length less than 2048B<br><br>recvBuff[out] Data received from card. The max receive data length less than 2048B,<br><br>recvBuff.lenght>=2048 |
| Return | >=0: The valid data length in recvBuff<br><br><0: Error code |
| Comment | This method is the same as **transmitApdu**() when cardType is **PSAM0/SAM1/SAM2/SAM3** |

##### ******* Check card(extended method)

| Prototype | `void checkCardEx(int cardType, int ctrCode, int stopOnError, CheckCardCallbackV2 checkCardCallback, int timeout)` |
|-----------|-------------------------------------------------------------------------------------------------------------------|
| Feature | For card checking, magnetic stripe card, IC card and NFC card are supported. After checking, the card type will be put into CheckCardCallbackV2. |
| Parameter | cardType[in] Card type, only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue()<br><br>ctrCode[in] Card active control code<br><br>Bit0-bit1: Contact card working voltage<br>0: VCC_3000mV<br>1: VCC_1800mV<br>2: VCC_5000mV<br>3: Reserved<br><br>Bit2: The speed of reset on Contact CPU card or SAM card<br>0: SPD_1X<br>1: SPD_4X<br><br>Bit3: support PPS or not<br>0: NOT SUPPORT<br>1: SUPPORT<br><br>Bit4: The procedure of reach agreement on contact CPU card or SAM card<br>0: ICC_SPEC<br>1: ICC_EMV<br><br>Bit5: whether select the second protocol of card supported or not<br>0: not select<br>1: select<br><br>stopOnError[in] Whether to stop immediately when checking special card type error<br><br>callback[in] Check card callback. Refer to [CheckCardCallbackV2](#332-checkcardcallbackv2)<br><br>timeout[in] Timeout (unit: second). Effective time range: 1-120s |
| Return | None |
| Comment | No distinction between bank card and non bank card. |

##### *******0 Transmit APDU command to card( second time extended method)

| Prototype | `int transmitApduExx(int cardType, int ctrCode, byte[] sendBuff, byte[] recvBuff);` |
|-----------|------------------------------------------------------------------------------------|
| Feature | Transmit the **sendBuff** to card directly.<br><br>This function support the general interface protocol for IC card.(T=0 and T=1), and also support the general interface protocol for contactless card. (T=CL) |
| Parameter | cardType[in] Card type currently in operation, only support following values:<br>AidlConstantsV2.CardType. PSAM0.getValue()<br>AidlConstantsV2.CardType. SAM1.getValue()<br>AidlConstantsV2.CardType. SAM2.getValue()<br>AidlConstantsV2.CardType. SAM3.getValue()<br><br>ctrCode[in] Card data exchange control code:<br><br>Bit0-Bit3: set contactless CPU card apdu fragment APDU wait time(fwi)<br><br>\| fwi (bit0--bit3) \| Wait time (unit: ms) \| fwi(bit0--bit3) \| Wait time (unit: ms) \|<br>\|-----------------|-------------------|-----------------|-------------------\|<br>\| 0x0-0x3 \| card specified time \| 0xA \| 309 \|<br>\| 0x4 \| 4.832 \| 0xB \| 618.5 \|<br>\| 0x5 \| 9.664 \| 0xC \| 1237 \|<br>\| 0x6 \| 19.3 \| 0xD \| 2474 \|<br>\| 0x7 \| 38.7 \| 0xE \| 4948 \|<br>\| 0x8 \| 77.3 \| Other \| card specified time \|<br>\| 0x9 \| 154.3 \| \| \|<br><br>Bit4-bit5：contactless CPU card APDU repeat times<br>0-Not repeat<br>1-Repeat 1 time<br>2-Repeat 2 times<br>3-Reserved<br><br>sendBuff[in] Data to transmit to card. Max length less than 2048B<br><br>recvBuff[out] Data received from card. The max receive data length less than 2048B,<br><br>recvBuff.lenght>=2048 |
| Return | >=0: The valid data length in recvBuff<br><br><0: Error code |
| Comment |  |

#### 3.3.2 CheckCardCallbackV2

##### ******* Find magnetic card

| Prototype | `void findMagCard(Bundle info)` |
|-----------|--------------------------------|
| Feature | Find magnetic stripe card. |
| Parameter | info[in] Contains the following data:<br><br>cardType: int, the card type<br><br>TRACK1: String, track1 data<br><br>TRACK2: String, track2 data<br><br>TRACK3: String, track3 data<br><br>pan: String, PAN data (return by checkCardEnc() )<br><br>name: String, cardhold name (return by checkCardEnc() )<br><br>expire: String, card expire date (return by checkCardEnc() )<br><br>servicecode: String, card service code (return by checkCardEnc() )<br><br>track1ErrorCode: int, track1 error code<br><br>track2ErrorCode: int, track2 error code<br><br>track3ErrorCode: int, track3 error code<br><br>The track error code can be one of the following values:<br><br>-1: Track no data<br><br>-2: Track parity check error<br><br>-3: Track LRC check error |
| Return | None |
| Comment | None |

##### ******* Find IC card

| Prototype | `void findICCard(String atr)` |
|-----------|-------------------------------|
| Feature | Find IC card |
| Parameter | atr[in] The card ATR |
| Return | None |
| Comment | None |

##### 3.3.2.3 Find radio frequency card

| Prototype | `void findRFCard(String uuid)` |
|-----------|--------------------------------|
| Feature | Find RF Card |
| Parameter | uuid[in] The card UUID |
| Return | None |
| Comment | None |

##### 3.3.2.4 Check card error

| Prototype | `void onError(int code, string message)` |
|-----------|------------------------------------------|
| Feature | Check card error callback. |
| Parameter | code[in] Error code<br><br>message[in] Error message |
| Return | None |
| Comment | None |

##### 3.3.2.5 Find IC card(extended method)

| Prototype | `void findICCardEx(Bundle info)` |
|-----------|----------------------------------|
| Feature | Find IC card |
| Parameter | info[in] Contains the following data:<br>cardType: int, the checked card type<br>atr: String, the card ATR |
| Return | None |
| Comment | Compare with **findICCard()**, This method provide more detail info in param [info], Both this method and **findICCard()** will be called in check card process. Client app can implement one of the two to do process. |

##### 3.3.2.6 Find radio frequency card(extended method)

| Prototype | `void findRFCardEx(Bundle info)` |
|-----------|----------------------------------|
| Feature | Find RF Card |
| Parameter | info[in] Contains the following data:<br>cardType: int, the checked card type<br><br>uuid: String, the card UUID<br><br>ats: String, the card ATS<br><br>cardCategory: int, the card category, 'A'or 'B'<br><br>atqa: byte[], the card ATQA |
| Return | None |
| Comment | Compare with **findRFCard()**, This method provide more detail info in param [info], Both this method and **findRFCard()** will be called in check card process. Client app can implement one of the two to do process. |

##### 3.3.2.7 Check card error(extended method)

| Prototype | `void onErrorEx(Bundle info)` |
|-----------|-------------------------------|
| Feature | Check card error callback. |
| Parameter | info[in] Contains the following data:<br>cardType: int, the failed card type<br><br>code: int, the error code<br><br>message: String, the error message |
| Return | None |
| Comment | Compare with **onError()**, This method provide more detail info in param [info], Both this method and **onError()** will be called in check card process. Client app can implement one of the two to do process. |

## 4. Error Code Definition

| Error code | Error description |
|------------|------------------|
| -100 | Incorrect number of parameters or length |
| -101 | Unsupported command |
| -1000 | System parameter error |
| -1001 | Feature not supported |
| -1002 | Initialization failed |
| -1003 | System time year error |
| -1004 | System time month error |
| -1005 | System time day error |
| -1006 | System time hour error |
| -1007 | System time minute error |
| -1008 | System time second error |
| -1009 | Hardware failure |
| -1010 | Buffer length error |
| -2000 | Card parameter error |
| -2001 | Have no card |
| -2002 | Multiple cards |
| -2100 | Magnetic card data decoding |
| -2500 | Module detection failed |
| -2501 | Drive core data structure error |
| -2502 | Module is not powered |
| -2503 | Carrier is not turned on |
| -2520 | Communication timeout |
| -2521 | Internal FIFO operation failed |
| -2522 | Communication frame error |
| -2523 | Communication character check error |
| -2524 | Communication conflict |
| -2525 | Signal in communication does not comply with the protocol |
| -2526 | CRC check error in communication |
| -2527 | M1 card password authentication error |
| -2528 | Mifare authentication parameters are incorrect |
| -2529 | Card exists |
| -2540 | The number of data in the A card communication response does not match the expected |
| -2541 | A card communication replies to the first character of the WUPA/REQA command is illegal |
| -2542 | Card number checksum error of A card communication response |
| -2543 | The first character of the card number answered by the A card communication is wrong. |
| -2544 | A card communication response ATS TL byte is illegal |
| -2545 | The A0 T0 byte of the A card communication response is illegal. |
| -2546 | The A1 TA1 byte of the ATS communication is illegal. |
| -2547 | The ATB TB1 byte of the A-card communication response is illegal. |
| -2548 | A card communication response ATS TC1 byte is illegal |
| -2550 | The number of data in the B card communication response does not match the expected |
| -2551 | The first character of the B card communication response WUBP/REQB command is not 0x50 |
| -2552 | The fourth bit of the protocol type byte in ATQB is not '0' |
| -2553 | The B-card communication responds to the difference in channel coding and setting in the ATTRIB command. |
| -2554 | B card communication response HLTB command response non 0x00 error |
| -2560 | Retransmitted to the limit if the correct reception is received |
| -2561 | Block type coding error |
| -2562 | I block PCB error or subsequent data length error |
| -2563 | PICC uses I block response link block |
| -2564 | Received I block serial number is incorrect |
| -2565 | R block PCB error or subsequent data length error |
| -2566 | PICC response NAK block |
| -2567 | The received R block serial number is incorrect. |
| -2568 | S block PCB error or subsequent data length error |
| -2569 | S block non-S-WTX request sent by PICC |
| -2570 | WTX parameter error requested (=0) |
| -2571 | Card return data exceeds FSD |
| -2800 | Verify error |
| -2801 | Communication timeout |
| -2802 | Module is not powered |
| -2803 | ATR error |
| -2804 | Communication error |
| -3000 | Security parameter error |
| -3001 | Root key error |
| -3002 | Security system is locked |
| -3003 | Security file read and write error |
| -3004 | Key index error |
| -3005 | Key verification error |
| -3006 | No PIN entry |
| -3007 | PIN input canceled |
| -3008 | PIN input timeout |
| -3009 | PIN input interval is too short |
| -3010 | KCV mode error |
| -3011 | KCV check error |
| -3012 | KCV ODD check error |
| -3013 | No matching key |
| -3014 | Error of key type |
| -3015 | Key length error |
| -3016 | Key exponent length error |
| -3017 | Destination key index error |
| -3018 | Source key index error |
| -3019 | Source key type error |
| -3020 | Group index error |
| -3022 | No KCV |
| -3023 | DUKPT overflow |
| -3024 | DUKPT key type error |
| -3025 | DUKPT KSN needs to add 1 |
| -3026 | Try to use the key outside the scope of the key. |
| -3027 | Incorrect use of the key, restricting only decrypted keys to encrypt data, such as calculate the Mac with the master key |
| -3028 | Feature not yet supported |
| -3029 | Feature key attribute does not match |
| -3030 | Not certified |
| -4000 | Transaction refuse |
| -4001 | Please use other interfaces |
| -4002 | Transaction terminated |
| -4005 | Final select data error |
| -4100 | Transaction terminated (command send and receive error) |
| -4101 | Transaction terminated (command receipt timeout) |
| -4102 | Transaction terminated (command receipt timeout) |
| -4103 | Transaction terminated (status code error) |
| -4104 | Transaction terminated (card locked) |
| -4105 | Transaction terminated (application locked) |
| -4106 | Transaction terminated (terminal not applied) |
| -4107 | Transaction terminated (applications with no support for the terminal and card) |
| -4108 | Transaction terminated (card return data error) |
| -4109 | Transaction terminated (card return data element duplicate) |
| -4110 | Transaction terminated (transaction not received) |
| -4111 | Transaction terminated (card expired) |
| -4112 | The list of preprocessing parameters is empty |
| -4113 | Transaction terminated (L1 read card timeout) |
| -4114 | Transaction terminated (L1 transmission error) |
| -4115 | Transaction terminated (L1 protocol error) |
| -4116 | Transaction terminated (L2 mandatory data error) |
| -4117 | Transaction terminated (L2 card authentication failed (offline data authentication failed)) |
| -4118 | Transaction terminated (L2 status word error) |
| -4119 | Transaction terminated (L2 data parsing failed) |
| -4120 | Transaction terminated (L2 transaction amount exceeds the contactless transaction limit) |
| -4121 | Transaction terminated (L2 card data error) |
| -4122 | Transaction terminated (L2 does not support magnetic stripe card mode) |
| -4123 | Transaction terminated (L2 card without PPSE) |
| -4124 | Transaction terminated (L2 PPSE processing error) |
| -4125 | Transaction terminated (L2 candidate list is empty) |
| -4126 | Transaction terminated (L2 IDS read error) |
| -4127 | Transaction terminated (L2 IDS write error) |
| -4128 | Transaction terminated (L2 IDS data error) |
| -4129 | Transaction terminated (L2 IDS has no matching AC) |
| -4130 | Transaction terminated (L2 terminal data error) |
| -4131 | Transaction terminated (L3 timeout) |
| -4132 | Transaction terminated (L3 cancel) |
| -4133 | Transaction terminated (L3 transaction amount does not exist) |
| -4134 | Transaction terminated (re-presentation of the card) |
| -4135 | Transaction terminated (using other cards (with Data Record)) |
| -4136 | Transaction terminated (using other cards) |
| -4137 | Transaction terminated (GPO response error) |
| -4138 | Transaction terminated (final selection of card data error) |
| -4139 | Transaction terminated (L3 no DET data) |
| -4140 | Kernel type is not supported |
| -4141 | Contactless transaction limit exceeded |
| -4142 | The amount is 0 |
| -4144 | Please use another interface (pre-processing failed) |
| -4500 | Invalid parameter |
| -4501 | Checksum error when downloading public key |
| -4502 | Terminal parameters do not exist. |
| -4503 | Error with terminal parameter data |
| -4504 | The transaction log does not exist |
| -4505 | Transaction log data error |
| -4506 | EMV data does not exist |
| -4507 | PBOC LOG format does not exist |
| -4825 | Two present card |
| -4854 | Complete command with empty |
| -4855 | Complete command with ODOL |
| -4856 | Complete command after reselect app |
| -4857 | Read record command after reselect app |
| -7001 | Printer error |
| -7002 | Low battery voltage |
| -7003 | Out of paper |
| -7004 | Temperature is too high |
| -7005 | Printer data error |
| -7006 | Invalid print parameters |
| -7007 | Device not open or device operation error |
| -7008 | Print buffer overflow |
| -8001 | Write data fail for tax control module |
| -8002 | Read data fail for tax control module |
| -10100 | Serial port closed |
| -10101 | Serial port overtime |
| -10102 | LRC check error |
| -10103 | SP Out of sequence |
| -10104 | SP is initializing |
| -10105 | SP is rebooting |
| -10106 | SP is reconnecting. |
| -10107 | SP is busy |
| -10108 | SP is sleep |
| -10200 | Read OS file package error |
| -10201 | SP is updating |
| -10202 | Connect SP failed |
| -10203 | Failed to open upgrade file |
| -10204 | Packet timeout |
| -10205 | Packet processing error |
| -10206 | Upgrade string is too long |
| -10207 | Upgrade unsuccessful |
| -10208 | Did not get the sdk version number of this machine |
| -10209 | The version is the same as the target upgrade version. |
| -10210 | Query default information failed |
| -10211 | Firmware version does not allow downgrade |
| -10212 | Upgrade cancelled |
| -10300 | Input parameter error |
| -10301 | The length of the response packet data area is illegal. |
| -10302 | Answer packet data parsing error |
| -10400 | The kernel has been rebooted |
| -20001 | Repeated call |
| -20002 | Firmware is being upgraded |
| -20003 | Parameter error |
| -20004 | Thread was aborted |
| -20005 | Firmware upgrade failed |
| -20006 | Firmware verification failed |
| -30001 | Failure to read card (unknown cause card failure, recommended to re-read the card operation) |
| -30002 | Unknown card type |
| -30003 | Failure of NFC check card |
| -30004 | Failure of IC check card |
| -30005 | Read card timeout |
| -30013 | This card is a chip card and can not fallback. |
| -30014 | Create candidate list timeout |
| -30015 | Card interaction failure |
| -30016 | Wrong card interaction parameters |
| -40002 | Key length error |
| -40003 | The check value error |
| -40004 | Store key fail |
| -40005 | Calculate MAC error |
| -40006 | Encryption error |
| -40007 | Return array data length error |
| -40008 | The MAC algorithm type not support |
| -40009 | Length of check value error |
| -40010 | Key index error |
| -40011 | Decrypt error |
| -40012 | Length of key error |
| -40013 | Get random error |
| -40014 | Key does not exist |
| -40016 | Verify signature fail |
| -40017 | Failed to get alarm information code |
| -40018 | Key partition has run out |
| -40019 | Inject BDK error |
| -50002 | Transaction preprocess fail |
| -50003 | Transaction process fail |
| -50004 | EMV kernel process fail |
| -50005 | PAN format error |
| -50006 | Call PINPAD fail |
| -50007 | None kernel data |
| -50008 | PINPAD parameter error |
| -50009 | EMV process not finish |
| -50010 | The transaction type not support |
| -50011 | Checking card information fail or timeout. |
| -50012 | CVM error |
| -50013 | Database operation fail |
| -50014 | No matching CAPK |
| -50015 | Save terminal parameter error |
| -50016 | No matching AID |
| -50017 | Check card fail |
| -50018 | Call interface order error |
| -50019 | Transaction data invalid |
| -50020 | PIN entry cancel |
| -50021 | PIN entry error |
| -50022 | The index of app select error |
| -50023 | Cert verify error |
| -50024 | Online process error |
| -50025 | App final select timeout |
| -50026 | App final select error |
| -50027 | Signature error |
| -50028 | Unknown CVM type |
| -60001 | Input PIN timeout |
| -60002 | Keyboard failed to activate password |
| -60003 | PinPadType type error (when the incoming keyboard type is not 1 and 2, the error is returned) |
| -60004 | Getting PinBlock failed |
| -60005 | PIN status query thread is interrupted |
| -70001 | Miss permisstion **com.sunmi.perm.MSR** |
| -70002 | Miss permission **com.sunmi.perm.ICC** |
| -70003 | Miss permission **com.sunmi.perm. CONTACTLESS_CARD** |
| -70004 | Miss permission **com.sunmi.perm. PINPAD** |
| -70005 | Miss permission **com.sunmi.perm. SECURITY** |
| -70006 | Miss permission **com.sunmi.perm. LED** |

## 5. Access permission

### 5.1 Permission location

The appropriate permissions should be declared in the AndroidManifest file before using each interface.

### 5.2 Permission definition

#### 5.2.1 Magnetic stripe card permission

```xml
<uses-permission android:name="com.sunmi.perm.MSR"/>
```

#### 5.2.2 Contact IC card permission

```xml
<uses-permission android:name="com.sunmi.perm.ICC"/>
```

#### 5.2.3 Contactless IC card permission

```xml
<uses-permission android:name="com.sunmi.perm.CONTACTLESS_CARD"/>
```

#### 5.2.4 Pinpad permission

```xml
<uses-permission android:name="com.sunmi.perm.PINPAD"/>
```

#### 5.2.5 Security permission

```xml
<uses-permission android:name="com.sunmi.perm.SECURITY"/>
```

#### 5.2.6 LED permission

```xml
<uses-permission android:name="com.sunmi.perm.LED" />
```

#### 5.2.7 Printer permission (not supported)

```xml
<uses-permission android:name="com.sunmi.perm.PRINTER" />
```

#### 5.2.8 Serial port permission (not supported)

```xml
<uses-permission android:name="com.sunmi.perm.SERIAL"/>
```

#### 5.2.9 Customer display permission (not supported)

```xml
<uses-permission android:name="com.sunmi.perm.CUSTOMER_DISPLAY"/>
```

#### 5.2.10 ID card permission (not supported)

```xml
<uses-permission android:name="com.sunmi.perm.IDCard"/>
```

#### 5.2.11 Money box permission (not supported)

```xml
<uses-permission android:name="com.sunmi.perm.MONEYBOX"/>
```

#### 5.2.12 Finger print permission (not supported)

```xml
<uses-permission android:name="com.sunmi.perm.FINGERPRINT"/>
```

## 6. Appendix

### 6.1 Aidl constants class

`com.sunmi.pay.hardware.aidl.AidlConstants`

#### 6.1.1 Card type constant definition

```java
// Magnetic
public static final int MAGNETIC= 1<<0;

// IC
public static final int IC = 1<<1;

// RFC
public static final int NFC = 1<<2;

// Mifare
public static final int MIFARE= 1<<3;

// PSAM,slot 0
public static final int PSAM0= 1<<4;

// Felica
public static final int FELICA= 1<<5;

// SAM1
public static final int SAM1= 1<<6;

// Mifare plus
public static final int MIFARE_PLUS= 1<<7;

// Mifare desfire
public static final int MIFARE_DESFIRE= 1<<8;

// AT24C01
public static final int AT24C01= 1<<9;

// AT24C02
public static final int AT24C02= 1<<10;

// AT24C04
public static final int AT24C04= 1<<11;

// AT24C08
public static final int AT24C08= 1<<12;

// AT24C16
public static final int AT24C16= 1<<13;

// AT24C32
public static final int AT24C32= 1<<14;

// AT24C64
public static final int AT24C64= 1<<15;

// AT24C128
public static final int AT24C128= 1<<16;

// AT24C256
public static final int AT24C256= 1<<17;

// AT24C512
public static final int AT24C512= 1<<18;

// SLE4442
public static final int SLE4442= 1<<19;

// SLE4428
public static final int SLE4428= 1<<20;

// AT88SC1608
public static final int AT88SC1608= 1<<21;

// CTX512B
public static final int CTX512B= 1<<22;

// SAM2
public static final int SAM2= 1<<23;

// SAM3
public static final int SAM3= 1<<24;
```