# CardModule 20003错误修复总结

## 问题描述
CardModule在读卡操作时出现20003错误，根据商米SDK文档，20003代表"参数错误"。

## 修复内容

### 1. 增强了16进制字符串转换的参数验证
- 验证输入字符串不为空
- 验证字符串长度为偶数
- 验证字符串只包含有效的16进制字符(0-9, A-F)
- 添加了详细的错误信息

### 2. M1卡认证方法(m1Auth)参数验证
- 扇区号验证：必须在0-15之间
- 密钥类型验证：必须是0(KeyA)或1(KeyB)
- 密钥长度验证：必须是12位16进制字符(6字节)
- 添加了详细的日志记录

### 3. M1卡读取块方法(m1ReadBlock)参数验证
- 扇区号验证：必须在0-15之间
- 块号验证：必须在0-3之间
- 绝对块号验证：必须在0-63之间
- 增强了错误代码处理

### 4. M1卡写入块方法(m1WriteBlock)参数验证
- 扇区号验证：必须在0-15之间
- 块号验证：必须在0-3之间
- 防止写入控制块：不允许写入每个扇区的第3块
- 数据长度验证：必须是32位16进制字符(16字节)
- 绝对块号验证：必须在0-63之间

### 5. 钱包操作方法参数验证
创建了通用的钱包参数验证函数`validateWalletParams()`：
- 扇区号验证：必须在0-15之间
- 块号验证：钱包只能使用0-2块(不能使用控制块)
- 绝对块号验证：必须在0-63之间

应用到以下方法：
- `m1InitWallet()` - 初始化钱包
- `m1GetBalance()` - 获取余额
- `m1IncreaseValue()` - 增加金额(额外验证金额必须大于0)
- `m1DecreaseValue()` - 减少金额(额外验证金额必须大于0)
- `m1Restore()` - 恢复操作

### 6. 增强了错误代码处理
为所有方法添加了针对20003和其他常见错误代码的特定处理：
- `-20003`: 参数错误
- `-2000`: 卡参数错误
- `-2001`: 没有卡片
- `-2002`: 检测到多张卡片
- `-30001`: 读卡失败(未知原因)

### 7. 改进了日志记录
- 添加了详细的参数日志
- 添加了SDK调用前的参数验证日志
- 改进了错误信息的可读性

## 常见20003错误原因及解决方案

1. **扇区号超出范围**: 确保扇区号在0-15之间
2. **块号超出范围**: 确保块号在0-3之间(钱包操作为0-2)
3. **密钥格式错误**: 确保密钥是12位有效的16进制字符
4. **数据格式错误**: 确保写入数据是32位有效的16进制字符
5. **尝试写入控制块**: 不要写入每个扇区的第3块
6. **金额参数错误**: 确保金额大于0

## 使用建议

1. 在调用任何M1卡操作前，先进行卡片认证
2. 确保所有参数都在有效范围内
3. 使用正确格式的16进制字符串
4. 检查日志以获取详细的错误信息
5. 对于钱包操作，避免使用控制块(每个扇区的第3块)

## 测试建议

1. 测试边界值：扇区0和15，块0和3
2. 测试无效参数：负数、超出范围的值
3. 测试无效的16进制字符串
4. 测试钱包操作的金额验证
