include: package:flutter_lints/flutter.yaml

# 静态分析配置
analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/generated/**"
    - "build/**"
  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true

# 自定义lint规则
linter:
  rules:
    # 错误检测
    - avoid_types_as_parameter_names
    - no_duplicate_case_values
    - unrelated_type_equality_checks
    - valid_regexps
    
    # 样式规范
    - camel_case_types
    - file_names
    - non_constant_identifier_names
    - constant_identifier_names
    
    # 最佳实践
    - prefer_const_constructors
    - prefer_const_declarations
    - prefer_const_literals_to_create_immutables
    - prefer_final_fields
    - prefer_final_locals
    - unnecessary_const
    - unnecessary_new
    
    # 类型安全
    - always_declare_return_types
    - always_specify_types
    - avoid_return_types_on_setters
    - type_annotate_public_apis
    
    # Flutter特定
    - use_build_context_synchronously
    - avoid_web_libraries_in_flutter
    - sized_box_for_whitespace
    
    # 性能相关
    - prefer_collection_literals
    - prefer_spread_collections
    - unnecessary_lambdas
    
    # 可读性
    - prefer_single_quotes
    - sort_child_properties_last
    - sort_constructors_first
    - sort_unnamed_constructors_first 