import 'package:flutter_test/flutter_test.dart';
import 'package:edc_app/models/create_order_request.dart';

void main() {
  group('CreateOrderRequest', () {
    test('should include terminal_id in JSON when provided', () {
      // Arrange
      final request = CreateOrderRequest(
        fuelTransactionId: 'test-transaction-123',
        paymentMethod: 1,
        stationId: 101,
        paymentType: 'CASH',
        allocatedAmount: 100.0,
        customerId: 1001,
        customerName: 'Test Customer',
        customerPhone: '081234567890',
        vehicleType: 'Car',
        licensePlate: 'B1234ABC',
        employeeNo: 'EMP001',
        terminalId: 'terminal-device-uuid-123',
        metadata: {'test': 'data'},
      );

      // Act
      final json = request.toJson();

      // Assert
      expect(json['terminal_id'], equals('terminal-device-uuid-123'));
      expect(json['fuel_transaction_id'], equals('test-transaction-123'));
      expect(json['payment_method'], equals(1));
      expect(json['station_id'], equals(101));
      expect(json['payment_type'], equals('CASH'));
      expect(json['allocated_amount'], equals(100.0));
      expect(json['customer_id'], equals(1001));
      expect(json['customer_name'], equals('Test Customer'));
      expect(json['customer_phone'], equals('081234567890'));
      expect(json['vehicle_type'], equals('Car'));
      expect(json['license_plate'], equals('B1234ABC'));
      expect(json['employee_no'], equals('EMP001'));
      expect(json['metadata'], isA<Map<String, dynamic>>());
    });

    test('should not include terminal_id in JSON when null', () {
      // Arrange
      final request = CreateOrderRequest(
        fuelTransactionId: 'test-transaction-123',
        paymentMethod: 1,
        stationId: 101,
        terminalId: null, // Explicitly set to null
      );

      // Act
      final json = request.toJson();

      // Assert
      expect(json.containsKey('terminal_id'), isFalse);
      expect(json['fuel_transaction_id'], equals('test-transaction-123'));
      expect(json['payment_method'], equals(1));
      expect(json['station_id'], equals(101));
    });

    test('should validate required fields correctly', () {
      // Test valid request
      final validRequest = CreateOrderRequest(
        fuelTransactionId: 'test-transaction-123',
        paymentMethod: 1,
        stationId: 101,
      );
      expect(validRequest.validate(), isNull);

      // Test empty fuel transaction ID
      final invalidRequest1 = CreateOrderRequest(
        fuelTransactionId: '',
        paymentMethod: 1,
        stationId: 101,
      );
      expect(invalidRequest1.validate(), contains('fuel_transaction_id不能为空'));

      // Test invalid payment method
      final invalidRequest2 = CreateOrderRequest(
        fuelTransactionId: 'test-transaction-123',
        paymentMethod: 0,
        stationId: 101,
      );
      expect(invalidRequest2.validate(), contains('payment_method必须大于0'));

      // Test invalid station ID
      final invalidRequest3 = CreateOrderRequest(
        fuelTransactionId: 'test-transaction-123',
        paymentMethod: 1,
        stationId: 0,
      );
      expect(invalidRequest3.validate(), contains('station_id必须大于0'));
    });

    test('should handle B2B payment type correctly', () {
      // Arrange
      final request = CreateOrderRequest(
        fuelTransactionId: 'test-transaction-123',
        paymentMethod: 1,
        stationId: 101,
        paymentType: 'B2B',
        terminalId: 'terminal-device-uuid-123',
      );

      // Act
      final json = request.toJson();

      // Assert
      expect(json['payment_type'], equals('B2B'));
      expect(json['is_b2b'], isTrue);
      expect(json['metadata'], isA<Map<String, dynamic>>());
      expect(json['metadata']['is_b2b_payment'], isTrue);
      expect(json['terminal_id'], equals('terminal-device-uuid-123'));
    });
  });
}
